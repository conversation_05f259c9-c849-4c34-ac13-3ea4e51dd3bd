[versions]
lifecycleViewmodelKtx = "2.9.1"
rxjava2 = "2.2.12"
rxandroid2 = "2.1.1"
activity = "1.9.3"
agp = "8.9.0"
androidautosize = "v1.2.1"
androidStandaloneScrollBar = "1.1.3"
androidTools = "31.9.0"
activityCompose = "1.9.0"
agentwebCore = "v5.1.1-androidx"
androidGifDrawable = "1.2.26"
apng = "3.0.1"
appcompat = "1.6.1"
androidxComposeBom = "2025.02.00"
androidxTracing = "1.3.0-alpha02"
banner = "2.2.3"
constraintlayout = "2.1.4"
coreKtx = "1.9.0"
coreSplashscreen = "1.0.1"
espressoCore = "3.6.1"
floatingx = "2.3.4"
fragment = "1.4.0"
glide = "4.12.0"
gson = "2.8.9"
hilt = "2.56"
hiltExt = "1.2.0"
kotlinxCoroutinesAndroid = "1.6.4"
kotlinxCoroutinesCore = "1.6.4"
leanback = "1.0.0"
lifecycleCommonJava8 = "2.0.0"
lifecycleCompiler = "2.0.0"
lifecycleExtensions = "2.0.0"
lifecycleRuntime = "2.0.0"
lifecycleViewmodelCompose = "2.6.1"
lottie = "6.0.0"
material = "1.11.0"
mmkvStaticVersion = "2.2.2"
multidex = "2.0.1"
multitype = "4.3.0"
navigationFragmentKtx = "2.6.0"
okhttp = "4.9.0"
overscrollDecorAndroid = "1.1.1"
refreshLayoutKernel = "2.1.0"
retrofit = "2.9.0"
retrofit2KotlinCoroutinesAdapter = "0.9.2"
room = "2.6.1"
shadowview = "1.0.1"
timber = "4.5.1"
mpandroidchart = "3.1.0"
kotlin = "2.1.10"
ksp = "2.1.10-1.0.31"
junit = "4.13.2"
junitVersion = "1.2.1"
unpeekLivedata = "7.8.0"
utilcodex = "1.31.1"
versionName = "1.1.19"
versionCode = "23"
viewpager2 = "1.1.0"

# Test dependencies
mockk = "1.13.8"
kotlinx-coroutines-test = "1.7.3"
junit-jupiter = "5.10.1"
assertj = "3.24.2"
turbine = "1.0.0"

[libraries]
agentweb-core = { module = "io.github.justson:agentweb-core", version.ref = "agentwebCore" }
android-gif-drawable = { module = "pl.droidsonroids.gif:android-gif-drawable", version.ref = "androidGifDrawable" }
android-standalone-scroll-bar = { module = "com.github.I3eyonder:android-standalone-scroll-bar", version.ref = "androidStandaloneScrollBar" }
androidautosize = { module = "com.github.JessYanCoding:AndroidAutoSize", version.ref = "androidautosize" }
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "activityCompose" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom-alpha", version.ref = "androidxComposeBom" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtx" }
androidx-core-splashscreen = { module = "androidx.core:core-splashscreen", version.ref = "coreSplashscreen" }
androidx-fragment = { module = "androidx.fragment:fragment", version.ref = "fragment" }
androidx-leanback = { module = "androidx.leanback:leanback", version.ref = "leanback" }
androidx-lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycleViewmodelKtx" }
androidx-lifecycle-runtime = { module = "androidx.lifecycle:lifecycle-runtime", version.ref = "lifecycleRuntime" }
androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycleViewmodelKtx" }
androidx-lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycleViewmodelCompose" }
androidx-lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycleViewmodelKtx" }
androidx-lifecycle-viewmodel-savedstate = { module = "androidx.lifecycle:lifecycle-viewmodel-savedstate", version.ref = "lifecycleViewmodelKtx" }
androidx-multidex = { module = "androidx.multidex:multidex", version.ref = "multidex" }
androidx-navigation-fragment-ktx = { module = "androidx.navigation:navigation-fragment-ktx", version.ref = "navigationFragmentKtx" }
androidx-navigation-ui-ktx = { module = "androidx.navigation:navigation-ui-ktx", version.ref = "navigationFragmentKtx" }
androidx-tracing-ktx = { group = "androidx.tracing", name = "tracing-ktx", version.ref = "androidxTracing" }
androidx-viewpager2 = { module = "androidx.viewpager2:viewpager2", version.ref = "viewpager2" }
apng = { module = "com.github.penfeizhou.android.animation:apng", version.ref = "apng" }
banner = { module = "io.github.youth5201314:banner", version.ref = "banner" }
floatingx = { module = "io.github.petterpx:floatingx", version.ref = "floatingx" }
glide-compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "glide" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "hilt" }
hilt-android-compiler = { module = "com.google.dagger:hilt-android-compiler", version.ref = "hilt" }
hilt-android-testing = { module = "com.google.dagger:hilt-android-testing", version.ref = "hilt" }
hilt-compiler = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "hilt" }
hilt-core = { module = "com.google.dagger:hilt-core", version.ref = "hilt"}
hilt-ext-compiler = { group = "androidx.hilt", name = "hilt-compiler", version.ref = "hiltExt" }
hilt-ext-work = { group = "androidx.hilt", name = "hilt-work", version.ref = "hiltExt" }
jetbrains-kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinxCoroutinesAndroid" }
kotlin-test = { group = "org.jetbrains.kotlin", name = "kotlin-test", version.ref = "kotlin" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinxCoroutinesCore" }
lifecycle-common-java8 = { module = "androidx.lifecycle:lifecycle-common-java8", version.ref = "lifecycleCommonJava8" }
lifecycle-compiler = { module = "androidx.lifecycle:lifecycle-compiler", version.ref = "lifecycleCompiler" }
lifecycle-extensions = { module = "androidx.lifecycle:lifecycle-extensions", version.ref = "lifecycleExtensions" }
lottie = { module = "com.airbnb.android:lottie", version.ref = "lottie" }
material = { module = "com.google.android.material:material", version.ref = "material" }
mmkv-static = { module = "com.tencent:mmkv-static", version.ref = "mmkvStaticVersion" }
multitype = { module = "com.drakeet.multitype:multitype", version.ref = "multitype" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
mpandroidchart = { module = "com.github.PhilJay:MPAndroidChart", version.ref = "mpandroidchart" }
overscroll-decor-android = { module = "io.github.everythingme:overscroll-decor-android", version.ref = "overscrollDecorAndroid" }
refresh-header-classics = { module = "io.github.scwang90:refresh-header-classics", version.ref = "refreshLayoutKernel" }
refresh-layout-kernel = { module = "io.github.scwang90:refresh-layout-kernel", version.ref = "refreshLayoutKernel" }
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
retrofit2-converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit" }
retrofit2-kotlin-coroutines-adapter = { module = "com.jakewharton.retrofit:retrofit2-kotlin-coroutines-adapter", version.ref = "retrofit2KotlinCoroutinesAdapter" }
shadowview = { module = "com.github.amikoj:ShadowView", version.ref = "shadowview" }
timber = { module = "com.jakewharton.timber:timber", version.ref = "timber" }
androidx-activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }

# Dependencies of the included build-logic
android-gradlePlugin = { group = "com.android.tools.build", name = "gradle", version.ref = "agp" }
android-tools-common = { group = "com.android.tools", name = "common", version.ref = "androidTools" }
compose-gradlePlugin = { module = "org.jetbrains.kotlin:compose-compiler-gradle-plugin", version.ref = "kotlin" }
kotlin-gradlePlugin = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin", version.ref = "kotlin" }
ksp-gradlePlugin = { group = "com.google.devtools.ksp", name = "com.google.devtools.ksp.gradle.plugin", version.ref = "ksp" }
room-gradlePlugin = { group = "androidx.room", name = "room-gradle-plugin", version.ref = "room" }
unpeek-livedata = { module = "com.kunminx.arch:unpeek-livedata", version.ref = "unpeekLivedata" }
utilcodex = { module = "com.blankj:utilcodex", version.ref = "utilcodex" }

rxjava2-rxjava = { group = "io.reactivex.rxjava2", name = "rxjava", version.ref = "rxjava2" }
rxjava2-rxandroid = { group = "io.reactivex.rxjava2", name = "rxandroid", version.ref = "rxandroid2" }

# Test libraries
mockk = { module = "io.mockk:mockk", version.ref = "mockk" }
mockk-android = { module = "io.mockk:mockk-android", version.ref = "mockk" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "kotlinx-coroutines-test" }
junit-jupiter-api = { module = "org.junit.jupiter:junit-jupiter-api", version.ref = "junit-jupiter" }
junit-jupiter-engine = { module = "org.junit.jupiter:junit-jupiter-engine", version.ref = "junit-jupiter" }
junit-jupiter-params = { module = "org.junit.jupiter:junit-jupiter-params", version.ref = "junit-jupiter" }
assertj-core = { module = "org.assertj:assertj-core", version.ref = "assertj" }
turbine = { module = "app.cash.turbine:turbine", version.ref = "turbine" }




[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
#jetbrainsCompose = { id = "org.jetbrains.compose", version.ref = "kotlin-plugin" }
compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }

# Plugins of the included build-logic
hms-android-application = { id = "hms.android.application" }
hms-android-library = { id = "hms.android.library" }
hms-hilt = { id = "hms.hilt" }

