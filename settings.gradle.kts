pluginManagement {
    includeBuild("build-logic")
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        jcenter()
        mavenCentral()
        maven { url = uri("https://jitpack.io") }
//        maven {
//            isAllowInsecureProtocol = true
//            url = uri("https://nexus.gwm.cn/repository/maven-public/")
//        }
//        maven {
//            isAllowInsecureProtocol = true
//            url = uri("https://nexus.gwm.cn/repository/maven-gdc-releases/")
//        }
//        maven {
//            isAllowInsecureProtocol = true
//            url = uri("https://nexus.gwm.cn/repository/maven-snapshots/")
//        }
    }
//    versionCatalogs {
//        create("version") {
//            from(files("./gradle/libs.versions.toml"))
//        }
//    }
}

rootProject.name = "hms"
include(":app")
// feature
include(":feature:setting")

// core
include(":core:model")
include(":core:network")
include(":core:common")
include(":core:data")



check(JavaVersion.current().isCompatibleWith(JavaVersion.VERSION_17)) {
    """
    HMS requires JDK 17+ but it is currently using JDK ${JavaVersion.current()}.
    Java Home: [${System.getProperty("java.home")}]
    https://developer.android.com/build/jdks#jdk-config-in-studio
    """.trimIndent()
}
include(":core:ui")
include(":feature:health-report")
