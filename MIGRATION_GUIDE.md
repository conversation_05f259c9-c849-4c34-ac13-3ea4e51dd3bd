# MMKVUtil 到 DataStore Repository 迁移指南

## 概述

本指南将帮助您从 `MMKVUtil` 迁移到基于 DataStore 的分层 Repository 架构。新架构提供了更好的类型安全、协程支持和模块化设计。

## 架构对比

### 旧架构 (MMKVUtil)
```kotlin
// 直接使用工具类
MMKVUtil.storeUserId("123")
val userId = MMKVUtil.getUserId()
```

### 新架构 (DataStore Repository)
```kotlin
// 通过依赖注入使用Repository
@Inject
lateinit var userRepository: UserRepository

// 异步操作
viewModelScope.launch {
    userRepository.storeUserId("123")
}

// 响应式数据流
userRepository.getUserId().collect { userId ->
    // 处理用户ID变化
}
```

## 功能映射表

### 用户相关数据

| MMKVUtil 方法 | 新 Repository 方法 | 说明 |
|---------------|-------------------|------|
| `storeUserId(String)` | `UserRepository.storeUserId(String)` | 存储用户ID |
| `getUserId()` | `UserRepository.getUserId(): Flow<String>` | 获取用户ID (响应式) |
| `storeUserToken(String)` | `UserRepository.storeToken(String)` | 存储用户Token |
| `getUserToken()` | `UserRepository.getToken(): Flow<String>` | 获取用户Token |
| `isVisitorMode()` | `UserRepository.isVisitorMode(): Flow<Boolean>` | 判断访客模式 |
| `storeUserinfo(...)` | `UserRepository.storeUserBasicInfo(UserBasicInfo)` | 存储用户基本信息 |
| `getUserinfo()` | `UserRepository.getUserBasicInfo(): Flow<UserBasicInfo>` | 获取用户基本信息 |
| `clearAllExceptSome()` | `UserRepository.clearAllUserData()` | 清除用户数据 |

### 健康权限相关

| MMKVUtil 方法 | 新 Repository 方法 | 说明 |
|---------------|-------------------|------|
| `storeHeartRateAuthority(Boolean)` | `HealthAuthorityRepository.storeHeartRatePermission(Boolean)` | 心率权限 |
| `getHeartRateAuthority()` | `HealthAuthorityRepository.getHeartRatePermission(): Flow<Boolean>` | 获取心率权限 |
| `storeSleepAuthority(Boolean)` | `HealthAuthorityRepository.storeSleepPermission(Boolean)` | 睡眠权限 |
| `getSleepAuthority()` | `HealthAuthorityRepository.getSleepPermission(): Flow<Boolean>` | 获取睡眠权限 |
| `storeStressAuthority(Boolean)` | `HealthAuthorityRepository.storeStressPermission(Boolean)` | 压力权限 |
| `getStressAuthority()` | `HealthAuthorityRepository.getStressPermission(): Flow<Boolean>` | 获取压力权限 |
| `storeBloodOxygenAuthority(Boolean)` | `HealthAuthorityRepository.storeHealthDataPermission(Boolean)` | 血氧权限 |
| `getBloodOxygenAuthority()` | `HealthAuthorityRepository.getHealthDataPermission(): Flow<Boolean>` | 获取血氧权限 |

### 系统设置相关

| MMKVUtil 方法 | 新 Repository 方法 | 说明 |
|---------------|-------------------|------|
| `storeLastThemeMode(Int)` | `SystemRepository.storeThemeMode(String)` | 主题设置 |
| `getLastThemeMode()` | `SystemRepository.getThemeMode(): Flow<String>` | 获取主题设置 |
| `storeIsFirstLaunchAppAfterBoot(Boolean)` | `SystemRepository.storeFirstLaunch(Boolean)` | 首次启动标识 |
| `getIsFirstLaunchAppAfterBoot()` | `SystemRepository.isFirstLaunch(): Flow<Boolean>` | 获取首次启动标识 |

### 导航相关

| MMKVUtil 方法 | 新 Repository 方法 | 说明 |
|---------------|-------------------|------|
| `storeNavigationGuideStatus(Boolean)` | `NavigationRepository.storeCurrentBottomNavIndex(Int)` | 导航状态 |
| `getNavigationGuideStatus()` | `NavigationRepository.getCurrentBottomNavIndex(): Flow<Int>` | 获取导航状态 |
| `storeNavigationInfo(String)` | `NavigationRepository.storeNavigationHistory(List<String>)` | 导航信息 |
| `getNavigationInfo()` | `NavigationRepository.getNavigationHistory(): Flow<List<String>>` | 获取导航信息 |

## 迁移步骤

### 1. 添加依赖注入

在需要使用数据存储的类中，注入相应的 Repository：

```kotlin
@AndroidEntryPoint
class MainActivity : AppCompatActivity() {
    
    @Inject
    lateinit var userRepository: UserRepository
    
    @Inject
    lateinit var systemRepository: SystemRepository
    
    // 其他Repository...
}
```

### 2. 替换同步调用为异步调用

**旧代码：**
```kotlin
// 同步操作
MMKVUtil.storeUserId("123")
val userId = MMKVUtil.getUserId()
```

**新代码：**
```kotlin
// 异步操作
lifecycleScope.launch {
    userRepository.storeUserId("123")
}

// 响应式数据流
lifecycleScope.launch {
    userRepository.getUserId().collect { userId ->
        // 处理用户ID变化
    }
}
```

### 3. 使用数据模型替代原始类型

**旧代码：**
```kotlin
MMKVUtil.storeUserinfo(
    nickname = "张三",
    birthYear = 1990,
    birthMonth = 5,
    height = 175,
    weight = 70.5f,
    gender = 1
)
```

**新代码：**
```kotlin
val userInfo = UserBasicInfo(
    nickname = "张三",
    birthYear = 1990,
    birthMonth = 5,
    height = 175,
    weight = 70.5f,
    gender = 1
)

lifecycleScope.launch {
    userRepository.storeUserBasicInfo(userInfo)
}
```

### 4. 批量操作优化

**旧代码：**
```kotlin
// 多次单独调用
MMKVUtil.storeHeartRateAuthority(true)
MMKVUtil.storeSleepAuthority(true)
MMKVUtil.storeStressAuthority(false)
```

**新代码：**
```kotlin
// 批量更新
val permissions = HealthAuthorityPreferences(
    privacyPolicyAccepted = true,
    heartRatePermission = true,
    sleepPermission = true,
    stressPermission = false,
    healthDataPermission = true,
    locationPermission = false,
    notificationPermission = true
)

lifecycleScope.launch {
    healthAuthorityRepository.updateHealthAuthorityPreferences(permissions)
}
```

## ViewModel 中的使用示例

```kotlin
@HiltViewModel
class UserViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val systemRepository: SystemRepository
) : ViewModel() {
    
    // 用户ID状态
    val userId = userRepository.getUserId()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = ""
        )
    
    // 访客模式状态
    val isVisitorMode = userRepository.isVisitorMode()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = true
        )
    
    // 存储用户信息
    fun saveUserInfo(userInfo: UserBasicInfo) {
        viewModelScope.launch {
            userRepository.storeUserBasicInfo(userInfo)
        }
    }
    
    // 登出
    fun logout() {
        viewModelScope.launch {
            userRepository.clearAllUserData()
        }
    }
}
```

## Compose 中的使用示例

```kotlin
@Composable
fun UserProfileScreen(
    viewModel: UserViewModel = hiltViewModel()
) {
    val userId by viewModel.userId.collectAsState()
    val isVisitorMode by viewModel.isVisitorMode.collectAsState()
    
    if (isVisitorMode) {
        GuestModeContent()
    } else {
        UserProfileContent(
            userId = userId,
            onSaveUserInfo = viewModel::saveUserInfo,
            onLogout = viewModel::logout
        )
    }
}
```

## 注意事项

### 1. 异步操作
- 所有存储操作都是异步的，需要在协程中执行
- 读取操作返回 `Flow`，支持响应式编程

### 2. 类型安全
- 使用强类型的数据模型替代原始类型
- 编译时检查，减少运行时错误

### 3. 模块化
- 不同功能的数据存储在不同的 Repository 中
- 便于测试和维护

### 4. 性能优化
- DataStore 基于 Protocol Buffers，性能更好
- 支持数据迁移和版本管理

### 5. 测试友好
- Repository 接口便于 Mock 测试
- 依赖注入支持测试替换

## 常见问题

### Q: 如何处理数据迁移？
A: 可以创建一个迁移工具类，在应用启动时将 MMKV 数据迁移到 DataStore：

```kotlin
@Singleton
class DataMigrationHelper @Inject constructor(
    private val userRepository: UserRepository,
    // 其他Repository...
) {
    suspend fun migrateFromMMKV() {
        // 从 MMKV 读取数据并存储到 DataStore
        val userId = MMKVUtil.getUserId()
        if (!userId.isNullOrEmpty()) {
            userRepository.storeUserId(userId)
        }
        // 迁移其他数据...
    }
}
```

### Q: 如何处理同步需求？
A: 如果确实需要同步操作，可以使用 `runBlocking`，但不推荐在主线程使用：

```kotlin
// 不推荐，仅在特殊情况下使用
val userId = runBlocking {
    userRepository.getUserId().first()
}
```

### Q: 如何处理默认值？
A: 在 Repository 实现中设置合理的默认值：

```kotlin
override fun getUserId(): Flow<String> {
    return userDataStore.data
        .map { preferences ->
            preferences[DataStorePreKeysConfig.USER_ID] ?: "" // 默认值
        }
}
```

## 总结

新的 DataStore Repository 架构提供了：
- 更好的类型安全
- 响应式数据流
- 模块化设计
- 更好的测试支持
- 现代化的异步编程模式

迁移过程需要将同步调用改为异步，并充分利用 Flow 的响应式特性来构建更健壮的应用。