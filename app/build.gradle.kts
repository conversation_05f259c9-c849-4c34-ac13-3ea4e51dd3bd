
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

plugins {
    alias(libs.plugins.hms.android.application)
    alias(libs.plugins.hms.hilt)
    alias(libs.plugins.compose)
}
// 允许数据类使用@HiltViewModel注解
//kapt {
//    correctErrorTypes = true
//}

android {
    aaptOptions.cruncherEnabled = false // 是否开启PNG图片优化检查
    namespace = "com.healthlink.hms"
//    compileSdk = 34
//    ndkVersion = "26.1.10909125"
    val timestamp = SimpleDateFormat("yyMMdd", Locale.US).run {
//        setTimeZone(TimeZone.getTimeZone("UTC"))
        format(Date())
    }

    defaultConfig {
        applicationId = "com.healthlink.hms"
        minSdk = 29
        targetSdk = 33
        versionCode =  libs.versions.versionCode.get().toInt()
        versionName = libs.versions.versionName.get()
        multiDexEnabled = true
        manifestPlaceholders["VERSIONNAME"] = "${versionName}"
//        ndk {
//            abiFilters("armeabi-v7a", "arm64-v8a", "x86") // 指定支持的ABI
//        }

    }
    // 签名配置
    signingConfigs {
        create("V4") {
            storeFile = file("../../platform.keystore")
            storePassword = "android"
            keyAlias = "platform"
            keyPassword = "android"
            enableV1Signing = true
            enableV2Signing = true
        }

        create("V35") {
            storeFile = file("../../platform.keystore")
            storePassword = "android"
            keyAlias = "platform"
            keyPassword = "android"
            enableV1Signing = true
            enableV2Signing = true
        }
    }
    //环境配置
    buildTypes {

        debug {
            isDebuggable = true
            isMinifyEnabled = false
            resValue("string","app_name","数字健康")
            buildConfigField("String", "BASE_DOMAIN_NAME", "\"gwm-cockpit-test.healthlinkiot.com\"")
            buildConfigField("String", "BASE_URL", "\"https://gwm-cockpit-test.healthlinkiot.com\"")
            buildConfigField("String", "BASE_H5_URL", "\"https://gwm-cockpit-test.healthlinkiot.com:48087/\"")
            buildConfigField("String", "BUILD_TIME", "\"${timestamp}\"")
            buildConfigField("String", "HUAWEI_APP_ID", "\"110740871\"")
            buildConfigField("String", "SIP_URL", "\"https://iectest.healthlinkiot.com:5049/ymdata/init/v2\"")
            buildConfigField("String", "SIP_CHANNEL_ID", "\"HEALTH-EC-BJS2221001C026-TEST\"")
            signingConfig = signingConfigs.getByName("V4")

//            buildConfigField("String", "HUAWEI_APP_ID", "\"111798819\"")
//            buildConfigField("String", "BASE_DOMAIN_NAME", "\"gwm-cockpit.healthlinkiot.com\"")
//            buildConfigField("String", "BASE_URL", "\"https://gwm-cockpit.healthlinkiot.com\"")

        }

        // 预发环境
        create("preRelease") {
            isDebuggable = true
            isMinifyEnabled = false
            resValue("string","app_name","数字健康")
            buildConfigField("String", "BASE_DOMAIN_NAME", "\"gwm-cockpit-test.healthlinkiot.com\"")
            buildConfigField("String", "BASE_URL", "\"https://gwm-cockpit-test.healthlinkiot.com\"")
            buildConfigField("String", "BASE_H5_URL", "\"https://gwm-cockpit-test.healthlinkiot.com:48087/\"")
            buildConfigField("String", "BUILD_TIME", "\"${timestamp}\"")
            buildConfigField("String", "HUAWEI_APP_ID", "\"111798819\"")
            buildConfigField("String", "SIP_URL", "\"https://iectest.healthlinkiot.com:5049/ymdata/init/v2\"")
            buildConfigField("String", "SIP_CHANNEL_ID", "\"GWM-EC-BJS2221001C026-TEST\"")
//            buildConfigField("String", "SIP_URL", "\"https://iec.healthlinkiot.com:5049/ymdata/init/v2\"")
            signingConfig = signingConfigs.getByName("V4")
        }

        release {
            isMinifyEnabled = true
            isShrinkResources = true
//            isDebuggable = true
            resValue("string","app_name","数字健康")
            buildConfigField("String", "BASE_DOMAIN_NAME", "\"gwm-cockpit.healthlinkiot.com\"")
            buildConfigField("String", "BASE_URL", "\"https://gwm-cockpit.healthlinkiot.com\"")
            buildConfigField("String", "BASE_H5_URL", "\"https://hms-test.healthlinkiot.com\"")
            buildConfigField("String", "BUILD_TIME", "\"${timestamp}\"")
//            buildConfigField("String", "HUAWEI_APP_ID", "\"111693185\"")
            buildConfigField("String", "HUAWEI_APP_ID", "\"111798819\"")
//            buildConfigField("String", "SIP_URL", "\"https://iectest.healthlinkiot.com:5049/ymdata/init/v2\"")
            buildConfigField("String", "SIP_URL", "\"https://iec.healthlinkiot.com:5049/ymdata/init/v2\"")
            buildConfigField("String", "SIP_CHANNEL_ID", "\"GWM-EC-BJS2221001C026-PRO\"")
            buildConfigField("boolean", "DEBUG", "false")
            signingConfig = signingConfigs.getByName("V4")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )

        }
    }
    //渠道配置
    flavorDimensions += "version"
    productFlavors {

        create("V4") {
            buildConfigField("String", "PLATFORM_CODE", "\"V4\"")
//            signingConfig = signingConfigs.getByName("V4")
        }

        create("V35") {
            buildConfigField("String", "PLATFORM_CODE", "\"V35\"")
//            signingConfig = signingConfigs.getByName("V4")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
//    buildFeatures {
//        viewBinding = true
//        buildConfig = true
//        compose = true
//        dataBinding = true
//    }

    applicationVariants.all {
        val variant = this
        val dateFormat = SimpleDateFormat("yyyyMMddHHmm")
        val currentDateTime = dateFormat.format(Date())
        variant.outputs
            .map { it as com.android.build.gradle.internal.api.BaseVariantOutputImpl }
            .forEach {
                it.outputFileName = "hms_v${variant.versionName}-${variant.flavorName}-${variant.buildType.name}-${currentDateTime}.apk"
            }
    }

//    composeOptions {
//        kotlinCompilerExtensionVersion = "1.5.2"
//    }
}

dependencies {
    implementation(fileTree("libs"))
    implementation(libs.androidx.activity)

    // compose 依赖配置
    val composeBom = platform("androidx.compose:compose-bom:2025.02.00") // libs.androidx.compose.bom
    implementation(composeBom)
    androidTestImplementation(composeBom)

    implementation("androidx.work:work-runtime-ktx:2.7.1")
    // Choose one of the following:
    // Material Design 3
    implementation("androidx.compose.material3:material3")
    // or Material Design 2
    implementation("androidx.compose.material:material")
    // or skip Material Design and build directly on top of foundational components
    implementation("androidx.compose.foundation:foundation")
    // or only import the main APIs for the underlying toolkit systems,
    // such as input and measurement/layout
    implementation("androidx.compose.ui:ui")

    // Android Studio Preview support
    implementation("androidx.compose.ui:ui-tooling-preview")
    debugImplementation("androidx.compose.ui:ui-tooling")

    // UI Tests
    androidTestImplementation("androidx.compose.ui:ui-test-junit4")
    debugImplementation("androidx.compose.ui:ui-test-manifest")

    // Optional - Included automatically by material, only add when you need
    // the icons but not the material library (e.g. when using Material3 or a
    // custom design system based on Foundation)
    implementation("androidx.compose.material:material-icons-core")
    // Optional - Add full set of material icons
    implementation("androidx.compose.material:material-icons-extended")
    // Optional - Add window size utils
    implementation("androidx.compose.material3:material3-window-size-class")

    // Optional - Integration with activities
    implementation(libs.androidx.activity.compose)
    // Optional - Integration with ViewModels
    implementation(libs.androidx.lifecycle.viewmodel.compose)

    ////////////////////android//////////////////////
//    implementation (libs.androidx.core.splashscreen)

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.leanback)
    implementation(libs.material)
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.fragment)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)
    implementation(libs.androidx.viewpager2)
    // lifecycle
    implementation(libs.androidx.lifecycle.runtime)
    implementation(libs.lifecycle.extensions)
    implementation(libs.lifecycle.common.java8)
    implementation(libs.lifecycle.compiler)

    // 加入协程
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.jetbrains.kotlinx.coroutines.android)

    // retrofit
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    implementation("com.jakewharton.retrofit:retrofit2-kotlin-coroutines-adapter:0.9.2")
    //日志拦截器
    implementation("com.squareup.okhttp3:logging-interceptor:4.9.0")
    implementation("com.squareup.retrofit2:adapter-rxjava2:2.4.0")
    // rxjava
    implementation(libs.rxjava2.rxandroid)
    implementation(libs.rxjava2.rxjava)
    // Glide 图片加载框架
    implementation(libs.glide)
    ksp(libs.glide.compiler)

    implementation(libs.gson)
    //网络组件
    implementation(libs.okhttp)
    //图表组件库
    implementation(libs.mpandroidchart)
    //agentWeb  webView
    implementation(libs.agentweb.core)

    // 下拉刷新
    implementation(libs.refresh.layout.kernel)        //核心必须依赖
    implementation(libs.refresh.header.classics)    //经典刷新头
    implementation(libs.overscroll.decor.android)

    //TTS SDK
    implementation(files("libs/tts.commonlib-1.2.2.aar"))
    implementation(files("libs/tts.client-1.2.2.aar"))
    //MAP SDK
    implementation(files("libs/GWMMapSDK-2.0.4.aar"))
    //GwmAdapterClient SDK
    implementation(files("libs/both-1.1.3.aar"))
    implementation(files("libs/libVrWidget-2.0.8.jar"))
    //NumberPicker修改版
    implementation(files("libs/NumberPicker-2.4.13.aar"))
    implementation(files("libs/libWidgetCux-1.1.5-SOP-20240816.031318-1.aar"))
    implementation(libs.lottie)
    //Gif 解码库
    implementation(libs.android.gif.drawable)
    // apng
    implementation(libs.apng)
    // walle
//    implementation(libs.library)
    // 界面适配
//    implementation(libs.androidautosize)

    // 列表多item
    implementation(libs.multitype)
    // 通用工具类
//    implementation(libs.utilcodex)
    // 腾讯MMKV本地存储
//    implementation(libs.mmkv.static)
    //引入埋点sdk
    implementation(files("libs/GWMDataTrackSDK-2.5.10.aar"))

    // 判断小憩模式是否可用
//    compileOnly(files("libs/gwmhmiframework-0.4.0.jar"))
    // SIP电话
//    compileOnly(files("libs/reduce-release-sit-20250422v1.aar"))

    // 滚动条
//    implementation (libs.android.standalone.scroll.bar)

    implementation(libs.floatingx)
//    implementation ("com.vmadalin:easypermissions-ktx:1.0.0")

//    implementation(libs.shadowview)

    implementation("com.kyleduo.switchbutton:library:2.1.0")
    // unpeek livedata数据回流
//    implementation(libs.unpeek.livedata)

    //banner组件
    implementation (libs.banner)

    implementation(libs.hilt.android)
    ksp(libs.hilt.android.compiler)

    // core:model 基础模型
    implementation(project(":core:model"))
    // core:common 通用模块
//    implementation(project(":core:common"))
    // core:network 网络模块
    implementation(project(":core:network"))
    // core:data 数据模块
    implementation(project(":core:data"))
    // feature:setting 设置模块
    implementation(project(":feature:setting"))

    // debug 添加内存泄露检查工具
//    debugImplementation ("com.squareup.leakcanary:leakcanary-android:2.14")
}
