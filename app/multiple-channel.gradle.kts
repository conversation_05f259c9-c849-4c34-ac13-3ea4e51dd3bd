//apply(plugin = "walle")
//
//walle {
//    // 指定渠道包输出路径
//    apkOutputFolder = file("${project.buildDir}/outputs/channels")
//    // 渠道配置文件
//    apkFilePath = "${project.projectDir}/app/channel"
//    // 渠道配置文件名称
//    channelFileNames = ["official"]
//    // 定制渠道包的apk文件名称
//    variantFilter { variant ->
//        variant.outputs.all {
//            outputFileName = "app-${variant.flavorName}-${variant.buildType.name}.apk"
//        }
//    }
}