<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">
    <aapt:attr name="android:drawable">
        <vector
            android:width="80dp"
            android:height="80dp"
            android:viewportWidth="80"
            android:viewportHeight="80">
            <group android:name="_R_G">
                <group
                    android:name="_R_G_L_1_G"
                    android:scaleX="0.96"
                    android:scaleY="0.96"
                    android:translateX="39.878"
                    android:translateY="39.878">
                    <path
                        android:name="_R_G_L_1_G_D_0_P_0"
                        android:pathData=" M0.13 -37.81 C21.06,-37.81 38.06,-20.81 38.06,0.13 C38.06,21.06 21.06,38.06 0.13,38.06 C-20.81,38.06 -37.81,21.06 -37.81,0.13 C-37.81,-20.81 -20.81,-37.81 0.13,-37.81c "
                        android:strokeWidth="5.5"
                        android:strokeAlpha="0.2"
                        android:strokeColor="#ffffff"
                        android:strokeLineCap="round"
                        android:strokeLineJoin="round" />
                </group>
                <group
                    android:name="_R_G_L_0_G"
                    android:scaleX="0.96"
                    android:scaleY="0.96"
                    android:translateX="40"
                    android:translateY="40">
                    <path
                        android:name="_R_G_L_0_G_D_0_P_0"
                        android:pathData=" M0 -38 C20.97,-38 38,-20.97 38,0 C38,20.97 20.97,38 0,38 C-20.97,38 -38,20.97 -38,0 C-38,-20.97 -20.97,-38 0,-38c "
                        android:strokeWidth="5.5"
                        android:strokeAlpha="0.8"
                        android:strokeColor="#ffffff"
                        android:strokeLineCap="round"
                        android:strokeLineJoin="round"
                        android:trimPathStart="0"
                        android:trimPathEnd="0.01"
                        android:trimPathOffset="0" />
                </group>
            </group>
            <group android:name="time_group" />
        </vector>
    </aapt:attr>
    <target android:name="_R_G_L_0_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="417"
                    android:propertyName="trimPathStart"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.4,0 0.2,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="1033"
                    android:propertyName="trimPathStart"
                    android:startOffset="417"
                    android:valueFrom="0"
                    android:valueTo="0.99"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.4,0 0.2,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="1033"
                    android:propertyName="trimPathEnd"
                    android:startOffset="0"
                    android:valueFrom="0.01"
                    android:valueTo="1"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.4,0 0.2,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="1450"
                    android:propertyName="trimPathOffset"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="1.0138888888888888"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="time_group">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="1450"
                    android:propertyName="translateX"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="1"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
</animated-vector>