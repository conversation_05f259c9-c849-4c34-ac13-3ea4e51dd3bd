<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="Theme.HMS" parent="Base.Theme.HMS">
        <!-- Transparent system bars for edge-to-edge. -->
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">?attr/isLightTheme</item>

    </style>

    <!--    新增主题SplashUITheme 给 SplashActivity 用-->
<!--    <style name="SplashUITheme" parent="Theme.HMS">-->
<!--        <item name="android:windowIsTranslucent">true</item>-->
<!--        <item name="android:windowNoTitle">true</item>-->
<!--    </style>-->
</resources>