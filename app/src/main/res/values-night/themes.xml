<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.HMS" parent="Theme.MaterialComponents.DayNight.NoActionBar.Bridge">
        <!-- Customize your dark theme here. -->
        <!-- <item name="colorPrimary">@color/my_dark_primary</item> -->
        <item name="android:statusBarColor">@color/hms_status_bar_color</item>
    </style>

    <style name="Theme.HMS" parent="Base.Theme.HMS">
        <item name="android:windowDisablePreview">true</item>
    </style>

    <!--    新增主题SplashUITheme 给 SplashActivity 用-->
<!--    <style name="SplashUITheme" parent="Theme.HMS">-->
<!--        <item name="android:windowIsTranslucent">true</item>-->
<!--        <item name="android:windowNoTitle">true</item>-->
<!--    </style>-->

    <style name="Theme.MyApp.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/view_color_bg</item>
<!--        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_launcher</item>-->
<!--        <item name="windowSplashScreenAnimatedIcon">@drawable/news_avd_v02</item>-->
        <item name="windowSplashScreenAnimationDuration">1000</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="postSplashScreenTheme">@style/Theme.HMS</item>
    </style>

</resources>