<resources>
    <!--  有数据 卡片样式设置-->
<!--    卡片异常背景样式-->
    <style name="car_exception_style">
        <item name="android:layout_width">@dimen/card_exception_width</item>
        <item name="android:layout_height">@dimen/card_exception_height</item>
        <item name="android:layout_gravity">top|end</item>
        <item name="android:background">@drawable/bg_card_bink_anim</item>
    </style>
    <!--    卡片内容边界-->
    <style name="hms_card_container_bound">
        <item name="android:layout_marginTop">24dp</item>
        <item name="android:layout_marginStart">24dp</item>
        <item name="android:layout_marginEnd">24dp</item>
        <!--        <item name="android:layout_marginBottom">32dp</item>-->
    </style>
    <!--    卡片图标-->
    <style name="hms_card_icon">
        <item name="android:layout_width">38dp</item>
        <item name="android:layout_height">38dp</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_alignParentStart">true</item>
    </style>
    <!--    卡片标题-->
    <style name="hms_card_title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:layout_marginStart">13dp</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textSize">26sp</item>
        <item name="android:textColor">@color/text_color_333</item>
    </style>
    <!--    卡片标题状态-->
    <style name="hms_card_title_status">
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:paddingStart">14dp</item>
        <item name="android:paddingEnd">14dp</item>
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@color/hms_white_color_100</item>
        <item name="android:text">正常</item>
        <item name="android:background">@drawable/health_index_status_nice_bg_fill</item>
    </style>

    <!--    卡片值容器-->
    <style name="hms_card_value_container">
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>
    <!--    卡片值容器 UI中压力是特殊的-->
    <style name="hms_card_value_container_pressure">
        <item name="android:layout_marginTop">11dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>
    <!--    卡片值容器 - 私密模式样式 -->
    <style name="hms_card_value_container_secrect_mode">
        <item name="android:layout_marginTop">20dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>
    <!--    卡片值-->
    <style name="hms_data_card_main_data">
        <item name="android:textSize">46sp</item>
        <item name="android:textColor">@color/text_color_333</item>
    </style>

    <style name="hms_data_card_main_data_privacy">
        <item name="android:textSize">46sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_color_333</item>
    </style>

    <style name="hms_data_car_data_unit">
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@color/text_color_666</item>
        <item name="android:gravity">bottom</item>
        <item name="android:paddingBottom">10dp</item>
    </style>

    <style name="hms_data_car_data_unit_privacy">
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@color/text_color_fc_80</item>
        <item name="android:layout_marginTop">10dp</item>
    </style>
    <!--    卡片图表-->
    <!--    卡片最后更新日期-->
    <style name="hms_card_last_update_text">
        <item name="android:layout_marginEnd">24dp</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/text_color_666</item>
        <item name="android:layout_gravity">end|bottom</item>
        <item name="android:gravity">center</item>
    </style>

    <!--  无数据卡片样式设置  -->
    <style name="hms_card_no_data_image">
        <item name="android:layout_width">180dp</item>
        <item name="android:layout_height">160dp</item>
        <item name="android:layout_gravity">bottom|end</item>
    </style>

    <style name="hms_card_no_data_desc">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">24dp</item>
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@color/text_color_666</item>
        <item name="android:layout_gravity">start</item>

    </style>
    <!--    tabLayout内 tab 样式-->
    <style name="TabLayoutTextSelected">
        <item name="android:textColor">@color/text_color_fc_100</item>
        <item name="android:textSize">30sp</item>
    </style>

    <style name="TabLayoutTextUnSelected">
        <item name="android:textColor">@color/text_color_fc_60</item>
        <item name="android:textSize">28sp</item>
    </style>
    <!--    tabLayout 点击样式-->
    <style name="TimeTabLayout" parent="Widget.Design.TabLayout">
        <!-- 移除点击效果 -->
        <!--        <item name="tabBackground">?android:attr/selectableItemBackground</item>-->
        <item name="tabRippleColor">@android:color/transparent</item>
    </style>

    <style name="EditTextStyle" parent="Widget.AppCompat.EditText">
        <item name="android:background">@null</item>
    </style>
    <!--二级dialog切换-->
    <style name="TransparentDialogActivity" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.8</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style>
    <!--个人信息性别radio样式-->
    <style name="CustomRadioButton" parent="Widget.AppCompat.CompoundButton.RadioButton">
        <item name="android:button">@drawable/radio_button_selector</item>
    </style>

    <!-- 自定义Dialog样式 -->
    <style name="CustomDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
<!--        <item name="android:windowIsTranslucent">true</item>-->
<!--        &lt;!&ndash; 内容区域在状态栏和导航栏后面显示 &ndash;&gt;-->
<!--        <item name="android:fitsSystemWindows">false</item>-->
    </style>
    <style name="MyDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@color/dialog_mask_color</item>
        <!--        <item name="android:windowIsTranslucent">true</item>-->
        <!--        &lt;!&ndash; 内容区域在状态栏和导航栏后面显示 &ndash;&gt;-->
        <!--        <item name="android:fitsSystemWindows">false</item>-->
    </style>

    <!-- res/values/styles.xml -->
    <style name="TransparentDialog" parent="Theme.AppCompat.Dialog">
        <!-- 设置背景为透明 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <!-- 使窗口内容布局延伸到状态栏和导航栏 -->
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>
<!--二级页面样式提取-->
<!--    二级页面容器-->
    <style name="fragment_card_container_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingStart">@dimen/fragment_card_container_pad_start</item>
        <item name="android:paddingEnd">@dimen/fragment_card_container_pad_end</item>
        <item name="android:orientation">vertical</item>
    </style>
<!--    二级页面指标解读卡片容器-->
    <style name="fragment_card_metrics_intro_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">horizontal</item>
        <item name="layout_constraintBottom_toTopOf">@+id/card_advice_container</item>
        <item name="layout_constraintEnd_toEndOf">parent</item>
        <item name="layout_constraintStart_toStartOf">parent</item>
        <item name="layout_constraintTop_toBottomOf">@+id/ll_chart_container</item>
    </style>

<!--    二级界面三张卡片样式-->
    <style name="card_3_style">
        <item name="android:layout_width">@dimen/card_3_style_width</item>
        <item name="android:layout_height">@dimen/card_3_style_height</item>
        <item name="android:background">@drawable/card_bg_selector</item>
    </style>

    <style name="card_4_style">
        <item name="android:layout_width">@dimen/card_4_width</item>
        <item name="android:layout_height">@dimen/card_4_height</item>
        <item name="android:background">@drawable/card_bg_selector</item>
    </style>

    <!-- 其他样式 -->
    <style name="FullScreenDialogStyle" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowIsFloating">false</item>
<!--        <item name="android:windowBackground">@android:color/transparent</item>-->
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.8</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>

</resources>