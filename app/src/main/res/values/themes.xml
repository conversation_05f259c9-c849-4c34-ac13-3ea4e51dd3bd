<resources>
    <!-- Base application theme. -->
    <style name="Base.Theme.HMS" parent="Theme.MaterialComponents.DayNight.NoActionBar.Bridge">
        <!-- Customize your light theme here. -->
        <!-- <item name="colorPrimary">@color/my_light_primary</item> -->
    </style>

    <style name="Theme.HMS" parent="Base.Theme.HMS">
        <item name="android:windowDisablePreview">true</item>
    </style>

<!--    新增主题SplashUITheme 给 SplashActivity 用-->
    <style name="SplashUITheme" parent="Theme.HMS">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <!-- 定义一个透明主题 -->
    <style name="TransparentTheme" parent="Theme.HMS">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

<!--    SplashScreen Theme-->
    <style name="Theme.MyApp.Splash" parent="Theme.SplashScreen">
        <!-- 自定义启动画面背景色 -->
        <item name="windowSplashScreenBackground">@color/view_color_bg</item>
        <!-- 自定义启动画面图标 -->
<!--        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_launcher</item>-->
<!--        <item name="windowSplashScreenAnimatedIcon">@drawable/news_avd_v02</item>-->
        <!-- 自定义启动画面图标的动画样式 -->
        <item name="windowSplashScreenAnimationDuration">1000</item>
        <!-- 控制启动画面是否淡入淡出 -->
        <item name="postSplashScreenTheme">@style/Theme.HMS</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

</resources>