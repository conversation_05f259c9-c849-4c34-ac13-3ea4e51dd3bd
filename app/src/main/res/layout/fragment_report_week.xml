<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingBottom="80dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="152dp"
                    android:layout_marginRight="152dp"
                    android:orientation="vertical">



                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:layout_marginTop="80dp"
                        android:layout_marginBottom="26dp">
                        <TextView
                            android:id="@+id/week_report_card_2_icon"
                            android:layout_width="8dp"
                            android:layout_height="30dp"
                            android:layout_centerVertical="true"
                            android:layout_alignParentLeft="true"
                            android:background="#229A90"
                            />

                        <TextView
                            android:layout_toRightOf="@id/week_report_card_2_icon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_centerVertical="true"
                            android:text="综合健康状况：低风险"
                            android:textSize="36sp"
                            android:textColor="@color/text_color_fc_100"
                            />

                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="788dp"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingTop="28dp"
                            >

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginBottom="40dp"
                                >

                                <ImageView
                                    android:layout_width="300dp"
                                    android:layout_height="300dp"
                                    android:layout_marginLeft="142dp"
                                    android:src="@drawable/week_report_health_normal" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:layout_marginStart="60dp"
                                    android:layout_gravity="center"
                                    android:gravity="center"
                                    >
                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="前晚睡眠不足"
                                        android:textSize="22sp"
                                        android:textColor="@color/text_color_fc_60"
                                        />
                                    <RelativeLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        >
                                        <TextView
                                            android:id="@+id/tv_value1"
                                            style="@style/hms_data_card_main_data"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:textSize="56sp"
                                            android:textColor="@color/text_color_fc_80"
                                            android:text="3" />

                                        <TextView
                                            style="@style/hms_data_car_data_unit"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_toEndOf="@+id/tv_value1"
                                            android:layout_alignBaseline="@+id/tv_value1"
                                            android:textColor="@color/text_color_fc_60"
                                            android:textSize="22sp"
                                            android:text="次"
                                            />
                                    </RelativeLayout>


                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="心动过速"
                                        android:textSize="22sp"
                                        android:textColor="@color/text_color_fc_60"
                                        />
                                    <RelativeLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        >
                                        <TextView
                                            android:id="@+id/tv_value2"
                                            style="@style/hms_data_card_main_data"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:textSize="56sp"
                                            android:textColor="@color/text_color_fc_80"
                                            android:text="6" />

                                        <TextView
                                            style="@style/hms_data_car_data_unit"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_toEndOf="@+id/tv_value2"
                                            android:layout_alignBaseline="@+id/tv_value2"
                                            android:textColor="@color/text_color_fc_60"
                                            android:textSize="22sp"
                                            android:text="次"
                                            />
                                    </RelativeLayout>
                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="396dp"
                                android:orientation="vertical"
                                android:paddingLeft="32dp"
                                android:paddingRight="40dp"
                                android:background="@drawable/card_bg"
                                >

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="20dp"
                                    >

                                    <ImageView
                                        android:layout_width="48dp"
                                        android:layout_height="48dp"
                                        android:src="@drawable/ic_interest_width"
                                        />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="健康评估"
                                        android:textSize="32sp"
                                        android:textColor="@color/text_color_fc_100"
                                        android:layout_marginLeft="20dp"
                                        >

                                    </TextView>

                                </LinearLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="19dp"
                                    android:text="@string/evalue_1"
                                    android:textSize="26sp"
                                    android:lineHeight="42dp"
                                    android:textColor="@color/text_color_fc_80"
                                    />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="32dp"
                                    android:text="@string/evalue_2"
                                    android:textSize="26sp"
                                    android:lineHeight="42dp"
                                    android:textColor="@color/text_color_fc_80"
                                    />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="788dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.5"
                            android:layout_marginLeft="40dp"
                            android:orientation="vertical"
                            >

                            <TextView
                                android:layout_toRightOf="@id/week_report_card_2_icon"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="20dp"
                                android:layout_centerVertical="true"
                                android:text="健康建议"
                                android:textSize="32sp"
                                android:textColor="@color/text_color_fc_100"
                                android:layout_marginBottom="40dp"
                                />
                            <LinearLayout
                                android:layout_width="788dp"
                                android:layout_height="320dp"
                                android:background="@drawable/health_advice_hr_too_fast"
                                >
                                <!--                                <ImageView-->
                                <!--                                    android:layout_width="788dp"-->
                                <!--                                    android:layout_height="320dp"-->
                                <!--                                    android:src="@drawable/health_advice_hr_too_fast"-->
                                <!--                                    />-->
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:orientation="horizontal">

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:orientation="vertical"
                                        >

                                        <TextView
                                            android:layout_toRightOf="@id/week_report_card_2_icon"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginLeft="32dp"
                                            android:layout_centerVertical="true"
                                            android:text="心率过速"
                                            android:textSize="30sp"
                                            android:textColor="@color/text_color_fc_100"
                                            android:layout_marginTop="30dp"
                                            />

                                        <TextView
                                            android:layout_toRightOf="@id/week_report_card_2_icon"
                                            android:layout_width="312dp"
                                            android:layout_height="wrap_content"
                                            android:layout_marginLeft="32dp"
                                            android:layout_marginTop="30dp"
                                            android:layout_centerVertical="true"
                                            android:lineHeight="42dp"
                                            android:text="可尝试深呼吸、舒展身体、影响等放松技巧"
                                            android:textSize="26sp"
                                            android:textColor="@color/text_color_fc_60"
                                            />
                                    </LinearLayout>


                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:orientation="horizontal"
                                android:layout_marginTop="40dp"
                                >

                                <LinearLayout
                                    android:layout_width="374dp"
                                    android:layout_height="320dp"
                                    android:orientation="vertical"
                                    android:layout_weight="0.5"
                                    android:background="@drawable/health_advice_music"
                                    >

                                    <TextView
                                        android:layout_toRightOf="@id/week_report_card_2_icon"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="32dp"
                                        android:layout_centerVertical="true"
                                        android:text="音乐疗法"
                                        android:textSize="30sp"
                                        android:textColor="@color/text_color_fc_100"
                                        android:layout_marginTop="30dp"
                                        />

                                    <TextView
                                        android:layout_toRightOf="@id/week_report_card_2_icon"
                                        android:layout_width="209dp"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="32dp"
                                        android:layout_marginTop="30dp"
                                        android:layout_centerVertical="true"
                                        android:text="音乐能辅助睡眠，改善睡眠质量"
                                        android:lineHeight="42dp"
                                        android:textSize="26sp"
                                        android:textColor="@color/text_color_fc_60"
                                        />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="374dp"
                                    android:layout_height="320dp"
                                    android:orientation="vertical"
                                    android:layout_weight="0.5"
                                    android:layout_marginLeft="40dp"
                                    android:background="@drawable/health_advice_env"
                                    >

                                    <TextView
                                        android:layout_toRightOf="@id/week_report_card_2_icon"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="32dp"
                                        android:layout_centerVertical="true"
                                        android:text="环境疗法"
                                        android:textSize="30sp"
                                        android:textColor="@color/text_color_fc_100"
                                        android:layout_marginTop="30dp"
                                        />

                                    <TextView
                                        android:layout_toRightOf="@id/week_report_card_2_icon"
                                        android:layout_width="209dp"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="32dp"
                                        android:layout_marginTop="30dp"
                                        android:layout_centerVertical="true"
                                        android:text="卧室维持安静，   保持关灯睡觉"
                                        android:lineHeight="42dp"
                                        android:textSize="26sp"
                                        android:textColor="@color/text_color_fc_60"
                                        />
                                </LinearLayout>


                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:layout_marginBottom="40dp">

                        <TextView
                            android:id="@+id/week_report_card_1_icon"
                            android:layout_width="8dp"
                            android:layout_height="30dp"
                            android:layout_centerVertical="true"
                            android:layout_alignParentLeft="true"
                            android:background="#229A90"
                            />

                        <TextView
                            android:layout_toRightOf="@id/week_report_card_1_icon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_centerVertical="true"
                            android:text="行程分析"
                            android:textSize="36sp"
                            android:textColor="@color/text_color_fc_100"
                            />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_alignParentRight="true"
                            android:layout_marginRight="20dp"
                            android:textSize="20sp"
                            android:text="06月17日-06月23日"
                            android:textColor="@color/text_color_fc_60"
                            />

                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.5"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="164dp"
                                    android:layout_weight="0.5"
                                    android:background="@drawable/card_bg"
                                    android:orientation="horizontal"
                                    android:layout_marginRight="40dp"
                                    android:layout_gravity="center_horizontal">
                                    <ImageView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:layout_marginLeft="53dp"
                                        android:src="@drawable/week_report_drive_miles">

                                    </ImageView>
                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:layout_marginLeft="32dp"
                                        android:orientation="vertical">
                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="累计驾驶时长"
                                            android:textSize="26sp"
                                            android:textColor="@color/text_color_fc_60"
                                            />

                                        <LinearLayout
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:orientation="horizontal"
                                            >

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="9"
                                                android:textColor="@color/text_color_fc_80"
                                                android:textSize="64sp" />
                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="小时"
                                                android:textSize="24sp"
                                                android:textColor="@color/text_color_fc_60"
                                                />
                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="28"
                                                android:textSize="64sp"
                                                android:textColor="@color/text_color_fc_80"
                                                />
                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="分"
                                                android:textSize="24sp"
                                                android:textColor="@color/text_color_fc_60"
                                                />
                                        </LinearLayout>
                                    </LinearLayout>
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="0.5"

                                    android:background="@drawable/card_bg">
                                    <ImageView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="53dp"
                                        android:layout_gravity="center_vertical"
                                        android:src="@drawable/week_report_drive_times">

                                    </ImageView>
                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginLeft="32dp"
                                        android:orientation="vertical">
                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="累计驾驶里程"
                                            android:textSize="26sp"
                                            android:textColor="@color/text_color_fc_60"
                                            />

                                        <LinearLayout
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:orientation="horizontal"
                                            >
                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="938"
                                                android:textSize="64sp"
                                                android:textColor="@color/text_color_fc_80"
                                                />
                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="km"
                                                android:textSize="24sp"
                                                android:textColor="@color/text_color_fc_60"
                                                />
                                        </LinearLayout>
                                    </LinearLayout>
                                </LinearLayout>

                            </LinearLayout>
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="186dp"
                                android:background="@drawable/card_bg"
                                android:layout_marginTop="40dp"
                                android:padding="32dp"
                                android:gravity="center"
                                >

                                <TextView
                                    android:id="@+id/tv_journey_analyse"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/journey_analyse"
                                    android:lineHeight="78dp"
                                    android:textSize="26sp"
                                    android:textColor="@color/text_color_fc_60"
                                    />

                                <!--<TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="9"
                                    android:textSize="36sp"
                                    android:textColor="#229A90"
                                    />
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="小时"
                                    android:textSize="36sp"
                                    android:textColor="#63686E"
                                    />
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="28"
                                    android:textSize="36sp"
                                    android:textColor="#229A90"
                                    />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="33dp"
                                    android:text="分，"
                                    android:textColor="#63686E"
                                    android:textSize="36sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="其中有20分中指标存在异常，"
                                    android:textSize="36sp"
                                    android:textColor="#63686E"
                                    />
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="您的健康状态击败了40%的长城车主。"
                                    android:textSize="36sp"
                                    android:textColor="#63686E"
                                    />-->

                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.5"
                            android:layout_marginLeft="40dp"
                            android:background="@drawable/card_bg"
                            android:orientation="vertical">
                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="健康风险行程占比"
                                android:layout_marginTop="32dp"
                                android:layout_marginLeft="32dp"
                                android:textColor="@color/text_color_fc_100"
                                android:textSize="36sp"
                                />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center"
                                >
                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="32dp"
                                    android:src="@drawable/health_percent_sample"
                                   />
                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="32dp"
                                    android:orientation="vertical"
                                    >
                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="正常行程"
                                        android:textSize="22sp"
                                        android:textColor="@color/text_color_fc_60"
                                        android:drawableLeft="@drawable/point_green"
                                        android:drawablePadding="8dp"
                                        />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="32dp"
                                        android:drawableLeft="@drawable/point_red"
                                        android:text="异常行程"
                                        android:textColor="@color/text_color_fc_60"
                                        android:drawablePadding="8dp"
                                        android:textSize="22sp" />
                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </ScrollView>
    </layout>
