<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/hms_view_bg_color">

    <LinearLayout
        android:layout_marginTop="106dp"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        >
        <ImageView
            android:layout_width="240dp"
            android:layout_height="240dp"
            android:src="@drawable/img_no_data_auth"
            />
        <TextView
            android:id="@+id/tv_auth"
            android:layout_marginTop="24dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="需要先开启您华为健康APP中该项数据的自动监测"
            android:textSize="22sp"
            android:textColor="@color/text_color_fc_80"
            />
    </LinearLayout>

</FrameLayout>