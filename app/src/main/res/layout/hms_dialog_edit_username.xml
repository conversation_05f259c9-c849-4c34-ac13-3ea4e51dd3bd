<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/fl_container">

    <LinearLayout
        android:id="@+id/dialog_content"
        android:background="@drawable/dialog_bg"
        android:layout_gravity="center"
        android:layout_width="960dp"
        android:layout_height="362dp"
        android:orientation="vertical">
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="90dp">
    <TextView
        android:id="@+id/hms_dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="40dp"
        android:text="昵称"
        android:textStyle="bold"
        android:textColor="@color/text_color_fc_100"
        android:textSize="26sp" />
</LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="184dp">
            <RelativeLayout
                android:layout_width="800dp"
                android:layout_height="72dp"
                android:layout_gravity="center"
                android:background="@drawable/setting_item_selector">

                <EditText
                    android:id="@+id/et_username"
                    android:layout_width="660dp"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="40dp"
                    android:text=""
                    android:inputType="text"
                    android:textColor="@color/personal_setting_text_100"
                    android:textSize="26sp"
                    android:focusable="true"
                    android:hint="请输入昵称"
                    android:textColorHint="@color/personal_setting_edit_hint"
                    android:focusableInTouchMode="true"
                    android:longClickable="false"
                    style="@style/EditTextStyle"/>
                <ImageView
                    android:id="@+id/img_clear_all_text"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="37dp"
                    android:layout_toRightOf="@+id/et_username"
                    android:layout_centerVertical="true"
                    android:src="@drawable/btn_search_clean"/>

            </RelativeLayout>
        </FrameLayout>


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="88dp">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="88dp"
                android:layout_alignParentBottom="true"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/positiveButton"
                    android:layout_width="match_parent"
                    android:layout_height="88dp"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_left_bg_fill_selector"
                    android:text="确定"
                    android:textColor="@color/btn_left_text_color"
                    android:textSize="26sp" />
                <Button
                    android:id="@+id/negativeButton"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_right_bg_fill_selector"
                    android:text="取消"
                    android:textColor="@color/text_color_fc_60"
                    android:textSize="26sp" />
            </LinearLayout>
            <View
                android:layout_gravity="center_horizontal"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/dialog_divider" />
        </FrameLayout>


    </LinearLayout>

</FrameLayout>