<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/main_data_card_width"
    android:layout_height="@dimen/main_data_card_height"
    android:background="@drawable/card_bg_selector"
    android:layout_gravity="center"
    android:gravity="center"
    >
        <TextView
            android:layout_marginTop="24dp"
            android:layout_marginStart="32dp"
            style="@style/hms_card_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="演示"
            />
        <LinearLayout
            android:layout_marginTop="24dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
            android:layout_gravity="center"
            android:gravity="center"
        >
        <Button
            android:id="@+id/tv_scene_welcome"
            android:layout_width="146dp"
            android:layout_height="40dp"
            android:layout_marginEnd="16dp"
            android:text="开机迎宾"
            android:background="@drawable/card_scene_bg"
            android:textColor="@color/text_color_fc_100"
            android:gravity="center"
            android:textSize="18sp" />

        <Button
            android:id="@+id/tv_scene_for_off_office"
            android:layout_width="146dp"
            android:layout_height="40dp"
            android:layout_marginStart="16dp"
            android:background="@drawable/card_scene_bg"
            android:text="下班关怀"
            android:textColor="@color/text_color_fc_100"
            android:gravity="center"
            android:textSize="18sp" />
        </LinearLayout>

</FrameLayout>