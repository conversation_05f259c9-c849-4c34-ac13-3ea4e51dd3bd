<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <FrameLayout
        android:id="@+id/fl_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
        android:background="#CC000000">
    <RelativeLayout
        android:id="@+id/rl_phone_bind_container"
        android:background="@drawable/dialog_bg"
        android:layout_gravity="center"
        android:layout_width="960dp"
        android:layout_height="560dp">

        <TextView
            android:id="@+id/hms_dialog_title"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_alignParentTop="true"
            android:gravity="center"
            android:text="绑定电话医生服务手机号"
            android:textColor="@color/text_color_fc_100"
            android:textSize="26sp" />
        <ScrollView
            android:id="@+id/sv_container"
            android:layout_below="@+id/hms_dialog_title"
            android:layout_marginTop="24dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:id="@+id/ll_phone_1"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="72dp"
                    android:layout_marginStart="115dp"
                    android:layout_marginEnd="158dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="手机号1"
                        android:textSize="26sp"
                        android:textColor="@color/text_color_fc_80"
                        />
                    <com.healthlink.hms.views.ClearableEditText
                        android:id="@+id/phone_1"
                        android:padding="16dp"
                        android:drawablePadding="8dp"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="32dp"
                        android:background="@drawable/setting_item_selector"
                        android:text=""
                        android:textColor="@color/personal_setting_text_100"
                        android:textSize="26sp"
                        android:selectAllOnFocus="true"
                        android:inputType="number"
                        android:singleLine="true"
                        android:hint="请输入手机号"
                        android:textColorHint="@color/text_color_fc_30"
                        android:maxLength="11"
                        style="@style/EditTextStyle"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/ll_phone_2"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="72dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginStart="115dp"
                    android:layout_marginEnd="158dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="手机号2"
                        android:textSize="26sp"
                        android:textColor="@color/text_color_fc_80"
                        />
                    <com.healthlink.hms.views.ClearableEditText
                        android:id="@+id/phone_2"
                        android:padding="16dp"
                        android:drawablePadding="8dp"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="32dp"
                        android:background="@drawable/setting_item_selector"
                        android:text=""
                        android:textColor="@color/personal_setting_text_100"
                        android:textSize="26sp"
                        android:selectAllOnFocus="true"
                        android:inputType="number"
                        android:singleLine="true"
                        android:hint="请输入手机号"
                        android:textColorHint="@color/text_color_fc_30"
                        android:maxLength="11"
                        style="@style/EditTextStyle"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_marginTop="24dp"
                    android:id="@+id/ll_phone_3"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="72dp"
                    android:layout_marginStart="115dp"
                    android:layout_marginEnd="158dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="手机号3"
                        android:gravity="center"
                        android:textSize="26sp"
                        android:textColor="@color/text_color_fc_80"
                        />
                    <com.healthlink.hms.views.ClearableEditText
                        android:id="@+id/phone_3"
                        android:padding="16dp"
                        android:drawablePadding="8dp"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="32dp"
                        android:background="@drawable/setting_item_selector"
                        android:text=""
                        android:textColor="@color/personal_setting_text_100"
                        android:textSize="26sp"
                        android:selectAllOnFocus="true"
                        android:inputType="number"
                        android:singleLine="true"
                        android:hint="请输入手机号"
                        android:textColorHint="@color/text_color_fc_30"
                        android:maxLength="11"
                        style="@style/EditTextStyle"/>
                </LinearLayout>
            </LinearLayout>

        </ScrollView>

        <TextView
            android:layout_below="@+id/sv_container"
            android:id="@+id/tv_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:textSize="18sp"
            android:gravity="center"
            android:textColor="@color/text_color_fc_60"
            android:text="使用电话医生服务，请先至少绑定一个手机号，需注意绑定后不可更改"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="88dp"
            android:layout_alignParentBottom="true"
            android:orientation="horizontal">

            <Button
                android:id="@+id/positiveButton"
                android:layout_width="match_parent"
                android:layout_height="88dp"
                android:layout_marginRight="1dp"
                android:layout_weight="0.5"
                android:background="@drawable/dialog_btn_left_bg_fill_selector"
                android:text="绑定"
                android:enabled="false"
                android:textColor="@color/btn_left_text_unEnabled_color"
                android:textSize="26sp" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#19FFFFFF" />

            <Button
                android:id="@+id/negativeButton"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="1dp"
                android:layout_weight="0.5"
                android:background="@drawable/dialog_btn_right_bg_fill_selector"
                android:text="取消"
                android:textColor="@color/text_color_fc_60"
                android:textSize="26sp" />
        </LinearLayout>

    </RelativeLayout>

</FrameLayout>

</layout>
