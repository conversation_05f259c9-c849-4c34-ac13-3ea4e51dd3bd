<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_head_check_tips_container"
    android:layout_width="wrap_content"
    android:layout_height="68dp"
    android:layout_centerInParent="true"
    android:paddingStart="30dp"
    android:paddingEnd="30dp"
    android:background="@drawable/shape_main_header_check_gradient_stroken"
    tools:showIn="@layout/activity_main">
    <ImageView
        android:id="@+id/iv_main_head_check_tips"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_gravity="center_vertical"
        android:src="@drawable/ic_tick_circle"
        />
    <TextView
        android:id="@+id/main_head_check_tips_text"
        android:layout_marginStart="5dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:textSize="26sp"
        android:gravity="center_vertical"
        android:textColor="@color/text_color_fc_100"
        android:text="@string/main_head_check_tips">
    </TextView>

    <Button
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:textSize="18sp"
        android:textColor="@color/text_color_fc_100"
        android:layout_gravity="center_vertical"
        android:background="@drawable/card_bg_selector"
        android:text="@string/main_header_check_report"
        />
<!--      android:background="@drawable/card_bg_selector" -->
</LinearLayout>