<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.healthlink.hms.viewmodels.MainViewModel" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/personal_setting_right">

        <LinearLayout
            android:id="@+id/lo_setting"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_left_content"
                android:layout_width="680dp"
                android:layout_height="match_parent"
                android:background="@color/personal_setting_left_bg">

                <View
                    android:id="@+id/left_view"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:background="#00000000" />

                <LinearLayout
                    android:id="@+id/setting_back"
                    android:layout_width="wrap_content"
                    android:layout_height="96dp"
                    android:layout_below="@+id/left_view"
                    android:layout_marginLeft="48dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginLeft="16dp"
                        android:src="@drawable/ic_arrow_left" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="40dp"
                        android:text="设置"
                        android:textColor="@color/personal_setting_text_100"
                        android:textSize="30sp" />
                </LinearLayout>

                <ImageView
                    android:id="@+id/setting_avatar"
                    android:layout_width="160dp"
                    android:layout_height="160dp"
                    android:layout_below="@+id/setting_back"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="77dp"
                    android:src="@mipmap/img_touxiang" />

                <TextView
                    android:id="@+id/setting_username"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/setting_avatar"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="40dp"
                    android:text=""
                    android:textColor="@color/personal_setting_text_100"
                    android:textSize="26sp" />

                <com.gwm.widget.GwmButton
                    android:id="@+id/setting_logout"
                    android:layout_width="280dp"
                    android:layout_height="64dp"
                    android:layout_below="@+id/setting_username"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="180dp"
                    android:background="@drawable/bg_exit_button"
                    android:gravity="center"
                    android:text="退出登录"
                    android:accessibilityPaneTitle=""
                    android:textColor="@color/personal_setting_text_100"
                    android:textSize="22sp" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/personal_setting_right"
                android:orientation="vertical">
                <View
                    android:id="@+id/right_view"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:background="#00000000"/>

                <com.healthlink.hms.views.StretchScrollView
                    android:id="@+id/sc_right_content"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/personal_setting_right"
                    android:scrollbars="none">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="736dp"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="64dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="36dp"
                                android:layout_marginTop="36dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:text="信息"
                                    android:textColor="@color/personal_setting_text_60"
                                    android:textSize="22sp" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="22dp"
                                android:background="@drawable/bg_setting_item_normal"
                                android:orientation="vertical">

                                <RelativeLayout
                                    android:id="@+id/setting_personal_info"
                                    android:layout_width="match_parent"
                                    android:layout_height="104dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentStart="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginStart="32dp"
                                        android:text="个人信息"
                                        android:textColor="@color/personal_setting_text_100"
                                        android:textSize="26sp" />

                                    <ImageView
                                        android:layout_width="32dp"
                                        android:layout_height="32dp"
                                        android:layout_alignParentRight="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginEnd="36dp"
                                        android:src="@drawable/ic_arrow_right" />

                                </RelativeLayout>

                                <View
                                    android:layout_width="672dp"
                                    android:layout_height="1dp"
                                    android:layout_gravity="center_horizontal"
                                    android:background="@color/bg_setting_item_line_color"
                                    android:visibility="gone" />

                                <RelativeLayout
                                    android:id="@+id/setting_bind_doctor_phone_number"
                                    android:layout_width="match_parent"
                                    android:layout_height="104dp"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/tv_doctor_number"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentStart="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginStart="32dp"
                                        android:text="电话医生绑定号码"
                                        android:textColor="@color/personal_setting_text_100"
                                        android:textSize="26sp" />

                                    <TextView
                                        android:id="@+id/tv_doctor_service_phone"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentRight="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginRight="80dp"
                                        android:text="未绑定"
                                        android:textColor="@color/personal_setting_text_60"
                                        android:textSize="22sp" />

                                    <ImageView
                                        android:layout_width="32dp"
                                        android:layout_height="32dp"
                                        android:layout_alignParentRight="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginEnd="36dp"
                                        android:src="@drawable/ic_arrow_right" />
                                </RelativeLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="36dp"
                                android:layout_marginTop="38dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:text="通知"
                                    android:textColor="@color/personal_setting_text_60"
                                    android:textSize="22sp" />
                            </LinearLayout>


                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="168dp"
                                android:layout_marginTop="22dp"
                                android:background="@drawable/bg_setting_item_normal">

<!--                                app:kswBackDrawable="@drawable/ios_back_drawable"-->
<!--                                app:kswThumbDrawable="@drawable/ios_thumb_selector"
app:kswBackColor = "@color/btn_left_text_color"
#428293A3
-->
                                <com.kyleduo.switchbutton.SwitchButton
                                    android:layout_marginLeft="32dp"
                                    android:layout_alignParentLeft="true"
                                    android:layout_centerVertical="true"
                                    android:id="@+id/setting_noti_switcher"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:kswAnimationDuration="200"
                                    app:kswBackColor="@color/notification_switch_off_bg"
                                    app:kswThumbWidth="32dp"
                                    app:kswThumbHeight="32dp"
                                    app:kswThumbMarginBottom="4dp"
                                    app:kswThumbMarginLeft="4dp"
                                    app:kswThumbMarginRight="4dp"
                                    app:kswThumbMarginTop="4dp"
                                    app:kswThumbColor="#fff"
                                    app:kswThumbRangeRatio="2.25"/>


<!--                                <com.gwm.widget.GwmSwitchButton-->
<!--                                    android:id="@+id/setting_noti_switcher"-->
<!--                                    android:layout_width="80dp"-->
<!--                                    android:layout_height="40dp"-->
<!--                                    android:layout_alignParentLeft="true"-->
<!--                                    android:layout_centerVertical="true"-->
<!--                                    android:layout_marginLeft="32dp"-->
<!--                                    android:background="@drawable/img_switch_off" />-->

                                <LinearLayout
                                    android:layout_width="568dp"
                                    android:layout_height="wrap_content"
                                    android:layout_centerVertical="true"
                                    android:layout_marginLeft="24dp"
                                    android:layout_toRightOf="@+id/setting_noti_switcher"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/tv_noti_switcher_title"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@string/setting_scene_engine_switcher_title"
                                        android:textColor="@color/personal_setting_text_100"
                                        android:textSize="26sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="8dp"
                                        android:lineSpacingExtra="7dp"
                                        android:text="关闭后，此应用中的健康提醒、服务通知、关怀服务等消息将不再发送通知"
                                        android:textColor="@color/personal_setting_text_60"
                                        android:textSize="22sp" />
                                </LinearLayout>


                            </RelativeLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="36dp"
                                android:layout_marginTop="38dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:text="关于"
                                    android:textColor="@color/personal_setting_text_60"
                                    android:textSize="22sp" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="314dp"
                                android:layout_marginTop="22dp"
                                android:background="@drawable/bg_setting_item_normal"
                                android:orientation="vertical">

                                <RelativeLayout
                                    android:id="@+id/rl_data_use_intro"
                                    android:layout_width="match_parent"
                                    android:layout_height="104dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentStart="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginStart="32dp"
                                        android:text="数据使用说明"
                                        android:textColor="@color/personal_setting_text_100"
                                        android:textSize="26sp" />

                                    <ImageView
                                        android:layout_width="32dp"
                                        android:layout_height="32dp"
                                        android:layout_alignParentRight="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginEnd="36dp"
                                        android:src="@drawable/ic_arrow_right" />
                                </RelativeLayout>

                                <View
                                    android:layout_width="672dp"
                                    android:layout_height="1dp"
                                    android:layout_gravity="center_horizontal"
                                    android:background="@color/bg_setting_item_line_color" />

                                <RelativeLayout
                                    android:id="@+id/setting_unbind_account"
                                    android:layout_width="match_parent"
                                    android:layout_height="104dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentStart="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginStart="32dp"
                                        android:text="@string/setting_fun_unbind_huawei_label"
                                        android:textColor="@color/personal_setting_text_100"
                                        android:textSize="26sp" />

                                    <ImageView
                                        android:layout_width="48dp"
                                        android:layout_height="48dp"
                                        android:layout_alignParentRight="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginRight="80dp"
                                        android:src="@mipmap/icon_huawei_health_kit" />

                                    <ImageView
                                        android:layout_width="32dp"
                                        android:layout_height="32dp"
                                        android:layout_alignParentRight="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginEnd="36dp"
                                        android:src="@drawable/ic_arrow_right" />
                                </RelativeLayout>

                                <View
                                    android:layout_width="672dp"
                                    android:layout_height="1dp"
                                    android:layout_gravity="center_horizontal"
                                    android:background="@color/bg_setting_item_line_color" />

                                <RelativeLayout
                                    android:id="@+id/setting_logoff_account"
                                    android:layout_width="match_parent"
                                    android:layout_height="104dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentStart="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginStart="32dp"
                                        android:text="注销"
                                        android:textColor="@color/personal_setting_text_100"
                                        android:textSize="26sp" />

                                    <ImageView
                                        android:layout_width="32dp"
                                        android:layout_height="32dp"
                                        android:layout_alignParentRight="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginEnd="36dp"
                                        android:src="@drawable/ic_arrow_right" />
                                </RelativeLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="36dp"
                                android:layout_marginTop="40dp"
                                android:gravity="center"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/version"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/app_version_name"
                                    android:textColor="@color/personal_setting_text_40"
                                    android:textSize="22sp" />

                                <TextView
                                    android:id="@+id/icp_license_no"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/icp_license_no"
                                    android:textColor="@color/personal_setting_text_40"
                                    android:textSize="22sp" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="36dp"
                                android:layout_marginTop="40dp"
                                android:layout_marginBottom="60dp"
                                android:gravity="center"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="48dp"
                                    android:text=""
                                    android:textColor="@color/personal_setting_text_40"
                                    android:textSize="22sp" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>
                </com.healthlink.hms.views.StretchScrollView>
            </LinearLayout>

        </LinearLayout>
    </FrameLayout>
</layout>