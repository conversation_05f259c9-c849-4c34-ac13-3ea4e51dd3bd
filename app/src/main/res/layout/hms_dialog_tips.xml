<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"

    >
<RelativeLayout
    android:id="@+id/dialog_content"
    android:layout_gravity="center"
    android:layout_width="960dp"
    android:layout_height="560dp"
    android:background="@drawable/dialog_bg">
    <LinearLayout
        android:id="@+id/lo_tips_title"
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:layout_alignParentTop="true">
        <TextView
            android:id="@+id/tv_tips_title_small"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:textAlignment="center"
            android:textStyle="bold"
            android:text=""
            android:textColor="@color/text_color_fc_100"
            android:lineHeight="42dp"
            android:textSize="26sp" />
    </LinearLayout>
<ScrollView
    android:layout_width="match_parent"
    android:layout_height="350dp"
    android:layout_marginLeft="48dp"
    android:layout_marginRight="16dp"
    android:layout_below="@+id/lo_tips_title">
    <TextView
        android:id="@+id/textView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="32dp"
        android:layout_gravity="center_vertical"
        android:text=""
        android:textColor="@color/text_color_fc_80"
        android:lineHeight="36sp"
        android:textSize="22sp" />
</ScrollView>
    <Button
        android:id="@+id/positiveButton"
        android:layout_width="match_parent"
        android:layout_height="88dp"
        android:layout_alignParentBottom="true"
        android:textColor="@color/hms_color_primary"
        android:textSize="26sp"
        android:background="@drawable/shape_hms_dialog_btn_corners"
        android:text="知道了"/>
</RelativeLayout>

</FrameLayout>