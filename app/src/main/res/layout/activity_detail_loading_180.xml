<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center_horizontal"
    android:background="@color/hms_view_bg_color"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginTop="180dp"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_loading_amin"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:gravity="center"
            android:src="@drawable/loading_80x80" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:text="加载中..."
            android:textColor="@color/text_color_fc_100"
            android:textSize="30sp" />
    </LinearLayout>


</LinearLayout>