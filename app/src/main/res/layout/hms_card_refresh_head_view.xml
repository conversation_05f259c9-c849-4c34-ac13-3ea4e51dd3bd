<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:orientation="horizontal"
    android:gravity="center"
    android:layout_gravity="center_vertical"
    android:layout_height="76dp">
        <ImageView
            android:id="@+id/iv_loading_amin"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:gravity="center"
            android:src="@drawable/loading_dark"
            />
<!--    <pl.droidsonroids.gif.GifImageView-->
<!--        android:id="@+id/iv_loading_amin"-->
<!--        android:layout_width="24dp"-->
<!--        android:layout_height="24dp"-->
<!--        android:gravity="center"-->
<!--        android:src="@drawable/loading_dark" />-->

    <TextView
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:gravity="center"
        android:text="@string/loading_bottom_text"
        android:textColor="@color/text_color_fc_100"
        android:textSize="22sp" />

</LinearLayout>