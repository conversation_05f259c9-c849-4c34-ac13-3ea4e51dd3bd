<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout
    android:id="@+id/fl_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"

    >

    <RelativeLayout
        android:id="@+id/dialog_content"
        android:layout_width="640dp"
        android:layout_height="328dp"
        android:background="@drawable/dialog_bg"
        android:layout_gravity="center"
        >


        <TextView
            android:id="@+id/hms_dialog_message"
            android:layout_width="match_parent"
            android:layout_height="240dp"
            android:gravity="center"
            android:text="@string/logout_app_content"
            android:textColor="@color/text_color_fc_80"
            android:textSize="22sp"
            android:layout_alignParentTop="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentEnd="true"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="88dp"
            android:layout_alignParentBottom="true"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal">
            <Button
                android:id="@+id/positiveButton"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="0.5"
                android:layout_marginRight="1dp"
                android:background="@drawable/dialog_btn_left_bg_fill_selector"
                android:text="知道了"
                android:textColor="@color/hms_color_primary"
                android:textSize="26sp"
                />
        </LinearLayout>

</RelativeLayout>

</FrameLayout>