<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.healthlink.hms.views.StretchScrollView
            android:visibility="invisible"
            android:id="@+id/report_scroll"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="94dp"
                android:paddingTop="42dp"
                android:paddingRight="94dp">

                <TextView
                    android:id="@+id/tv_health_report_conclusion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"
                    android:layout_centerVertical="true"
                    android:text="综合健康状况："
                    android:textColor="@color/text_color_333"
                    android:textStyle="bold"
                    android:textSize="30sp" />

                <TextView
                    android:id="@+id/tv_health_report_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/tv_health_report_conclusion"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:text=""
                    android:textColor="@color/text_color_666"
                    android:textSize="22sp" />

                <RelativeLayout
                    android:id="@+id/lo_health_pg"
                    android:layout_width="854dp"
                    android:layout_height="380dp"
                    android:layout_below="@+id/tv_health_report_conclusion"
                    android:layout_marginTop="40dp"
                    android:background="@drawable/card_bg"
                    android:orientation="horizontal">

                    <FrameLayout
                        android:id="@+id/lo_health_report_image"
                        android:layout_width="260dp"
                        android:layout_height="260dp"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="50dp">

                        <com.healthlink.hms.views.HealthStatusGradientRingView
                            android:id="@+id/hs_view"
                            android:layout_width="260dp"
                            android:layout_height="260dp"
                            android:visibility="gone" />

                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="424dp"
                        android:layout_height="316dp"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="60dp"
                        android:layout_toRightOf="@+id/lo_health_report_image"
                        android:orientation="horizontal">

                        <com.healthlink.hms.views.MiddleEllipsesTextView
                            android:id="@+id/tv_health_summarize"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:lineHeight="36dp"
                            android:maxLines="9"
                            android:text=""
                            android:textColor="@color/text_color_333"
                            android:textSize="22sp" />

                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/lo_health_summarize_more"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:layout_alignParentRight="true"
                        android:layout_alignParentBottom="true"
                        android:layout_marginRight="60dp"
                        android:layout_marginBottom="28dp"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/tv_health_report_see_more_1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:text="查看更多"
                            android:textColor="@color/card_more"
                            android:textSize="22sp" />
                    </LinearLayout>


                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="830dp"
                    android:layout_height="380dp"
                    android:layout_below="@+id/tv_health_report_conclusion"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="40dp"
                    android:background="@drawable/card_bg">
                <com.healthlink.hms.views.StatisticsView
                    android:id="@+id/statistics_view"
                    android:layout_width="718dp"
                    android:layout_height="match_parent"
                    android:layout_centerHorizontal="true"/>

                </RelativeLayout>
<!--同期对比-->
                <RelativeLayout
                    android:id="@+id/rl_contrast"
                    android:layout_width="match_parent"
                    android:layout_height="410dp"
                    android:layout_below="@+id/lo_health_pg"
                    android:layout_alignParentStart="true"
                    android:layout_marginTop="40dp"
                    android:background="@drawable/card_bg">

                    <LinearLayout
                        android:id="@+id/lo_contrast_left_more"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentLeft="true"
                        android:layout_marginLeft="692dp"
                        android:layout_marginTop="118dp"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/tv_health_report_see_more_5"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="查看更多"
                            android:textColor="@color/card_more"
                            android:textSize="22sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/lo_contrast"
                        android:layout_width="735dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_marginLeft="60dp"
                        android:layout_marginTop="32dp"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="42dp"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center_vertical"
                                android:src="@drawable/fragment_report_contrast" />

                            <TextView
                                android:id="@+id/tv_contrast_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="20dp"
                                android:text="同期对比"
                                android:textColor="@color/text_color_333"
                                android:textStyle="bold"
                                android:textSize="26sp" />
                        </LinearLayout>

                        <com.healthlink.hms.views.MiddleEllipsesTextView
                            android:id="@+id/tv_contrast_content"
                            android:layout_width="735dp"
                            android:layout_height="72dp"
                            android:lineHeight="36dp"
                            android:layout_marginTop="8dp"
                            android:maxLines="2"
                            android:text=""
                            android:textColor="@color/text_color_333"
                            android:textSize="22sp" />


                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="44dp"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tv_score_today"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_alignParentLeft="true"
                                android:text=""
                                android:textColor="@color/text_color_333"
                                android:textSize="36sp" />

                            <TextView
                                android:id="@+id/tv_score_today_unit"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignBaseline="@+id/tv_score_today"
                                android:layout_toRightOf="@+id/tv_score_today"
                                android:text="分"
                                android:textColor="@color/text_color_666"
                                android:textSize="22sp" />
                        </RelativeLayout>

                        <FrameLayout
                            android:id="@+id/fl_today_score_bg"
                            android:layout_width="match_parent"
                            android:layout_marginTop="6dp"
                            android:layout_height="40dp">

                            <View
                                android:id="@+id/score_today_bar"
                                android:layout_width="65dp"
                                android:layout_height="match_parent"
                                android:background="@drawable/img_health_report_score_today" />

                            <TextView
                                android:id="@+id/tv_score_current_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginLeft="10dp"
                                android:text=""
                                android:textColor="@color/text_color_333"
                                android:textSize="22sp" />
                        </FrameLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:layout_marginTop="22dp"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tv_score_yesterday"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentLeft="true"
                                android:layout_centerVertical="true"
                                android:text=""
                                android:textColor="@color/text_color_333"
                                android:textSize="36sp" />

                            <TextView
                                android:id="@+id/tv_score_yesterday_unit"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignBaseline="@+id/tv_score_yesterday"
                                android:layout_toRightOf="@+id/tv_score_yesterday"
                                android:text="分"
                                android:textColor="@color/text_color_666"
                                android:textSize="22sp" />
                        </RelativeLayout>

                        <FrameLayout
                            android:id="@+id/fl_yesterday_score_bg"
                            android:layout_width="match_parent"
                            android:layout_height="40dp">

                            <View
                                android:id="@+id/score_yesterday_bar"
                                android:layout_width="65dp"
                                android:layout_height="match_parent"
                                android:background="@drawable/img_health_report_score_yesterday" />

                            <TextView
                                android:id="@+id/tv_score_last_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginLeft="10dp"
                                android:text=""
                                android:textColor="@color/text_color_fc_100"
                                android:textSize="22sp" />
                        </FrameLayout>
                    </LinearLayout>

                    <com.healthlink.hms.views.CalHiddenTextView
                        android:id="@+id/tv_hidden_text"
                        android:layout_width="726dp"
                        android:layout_height="410dp"
                        android:layout_alignParentEnd="true"
                        android:layout_marginTop="77dp"
                        android:layout_marginEnd="60dp"
                        android:lineHeight="36dp"
                        android:maxLines="7"
                        android:textColor="@color/text_color_333"
                        android:textSize="22sp" />
                    <com.healthlink.hms.views.MiddleEllipsesTextView
                        android:id="@+id/tv_more_text"
                        android:layout_width="726dp"
                        android:layout_height="410dp"
                        android:layout_below="@+id/tv_hidden_text"
                        android:layout_alignStart="@+id/tv_hidden_text"
                        android:textSize="22sp"
                        android:lineHeight="36dp"
                        android:maxLines="7"
                        android:textColor="@color/text_color_333"
                        />
                    <LinearLayout
                        android:id="@+id/lo_contrast_more"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentBottom="true"
                        android:layout_marginEnd="210dp"
                        android:layout_marginBottom="40dp"
                        android:orientation="horizontal"
                        android:visibility="gone">
                        <TextView
                            android:id="@+id/tv_health_report_see_more_2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:text="查看更多"
                            android:textColor="@color/card_more"
                            android:textSize="22sp" />
                    </LinearLayout>


                    <ImageView
                        android:visibility="gone"
                        android:layout_width="130dp"
                        android:layout_height="130dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentBottom="true"
                        android:layout_marginEnd="60dp"
                        android:layout_marginBottom="20dp"
                        android:src="@drawable/img_compare"
                        />
                </RelativeLayout>
<!--健康评估-->
                <RelativeLayout
                    android:id="@+id/rl_health_estimate"
                    android:layout_width="match_parent"
                    android:layout_height="220dp"
                    android:layout_below="@+id/rl_contrast"
                    android:layout_alignParentStart="true"
                    android:layout_marginTop="40dp"
                    android:background="@drawable/card_bg">

                    <LinearLayout
                        android:id="@+id/lo_health_estimate_title"
                        android:layout_width="match_parent"
                        android:layout_height="42dp"
                        android:layout_alignParentTop="true"
                        android:layout_marginTop="32dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="60dp"
                            android:src="@drawable/ic_interest" />

                        <TextView
                            android:id="@+id/tv_health_report_estimate_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="20dp"
                            android:text="健康评估"
                            android:textColor="@color/text_color_333"
                            android:textStyle="bold"
                            android:textSize="26sp" />
                    </LinearLayout>

                    <com.healthlink.hms.views.MiddleEllipsesTextView
                        android:id="@+id/tv_health_estimate_content"
                        android:layout_width="match_parent"
                        android:layout_height="252dp"
                        android:layout_below="@+id/lo_health_estimate_title"
                        android:layout_marginLeft="60dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginRight="60dp"
                        android:lineHeight="36dp"
                        android:maxLines="7"
                        android:text=""
                        android:textColor="@color/text_color_333"
                        android:textSize="22sp" />

                    <LinearLayout
                        android:id="@+id/lo_health_estimate_more"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:layout_alignParentRight="true"
                        android:layout_alignParentBottom="true"
                        android:layout_marginRight="60dp"
                        android:layout_marginBottom="32dp"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/tv_health_report_see_more_3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:text="查看更多"
                            android:textColor="@color/card_more"
                            android:textSize="22sp" />
                    </LinearLayout>

                </RelativeLayout>
<!--健康建议-->
                <RelativeLayout
                    android:id="@+id/rl_health_advice"
                    android:layout_width="match_parent"
                    android:layout_height="380dp"
                    android:layout_below="@+id/rl_health_estimate"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="40dp"
                    android:background="@drawable/card_bg">

                    <LinearLayout
                        android:id="@+id/lo_health_advice_title"
                        android:layout_width="match_parent"
                        android:layout_height="42dp"
                        android:layout_alignParentTop="true"
                        android:layout_marginTop="32dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="60dp"
                            android:src="@drawable/ic_bike" />

                        <TextView
                            android:id="@+id/tv_health_report_advice_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="20dp"
                            android:text="健康建议"
                            android:textColor="@color/text_color_333"
                            android:textStyle="bold"
                            android:textSize="26sp" />
                    </LinearLayout>

                    <com.healthlink.hms.views.MiddleEllipsesTextView
                        android:id="@+id/tv_health_advice_content"
                        android:layout_width="match_parent"
                        android:layout_height="252dp"
                        android:layout_below="@+id/lo_health_advice_title"
                        android:layout_marginLeft="60dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginRight="60dp"
                        android:lineHeight="36dp"
                        android:maxLines="7"
                        android:text=""
                        android:textColor="@color/text_color_333"
                        android:textSize="22sp" />

                    <LinearLayout
                        android:id="@+id/health_advice_more"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:layout_alignParentRight="true"
                        android:layout_alignParentBottom="true"
                        android:layout_marginRight="60dp"
                        android:layout_marginBottom="32dp"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/tv_health_report_see_more_4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:text="查看更多"
                            android:textColor="@color/card_more"
                            android:textSize="22sp" />
                    </LinearLayout>

                </RelativeLayout>

                <TextView
                    android:visibility="gone"
                    android:id="@+id/tv_trip_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/rl_health_advice"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="60dp"
                    android:text="行程分析"
                    android:textColor="@color/health_report_normal_100"
                    android:textSize="30sp" />

                <LinearLayout
                    android:visibility="gone"
                    android:id="@+id/lo_trip_content"
                    android:layout_width="match_parent"
                    android:layout_height="142dp"
                    android:layout_below="@+id/tv_trip_title"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="40dp"
                    android:background="@drawable/card_bg"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="430dp"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="3dp"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_trip_card1_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="本日行程数"
                            android:lineHeight="42dp"
                            android:layout_marginTop="30dp"
                            android:textColor="@color/text_color_fc_60"
                            android:textSize="22sp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/tv_trip_card1_content"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="36sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/tv_trip_card1_unit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignBaseline="@+id/tv_trip_card1_content"
                                    android:layout_toRightOf="@+id/tv_trip_card1_content"
                                    android:text="次"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="22sp" />
                            </RelativeLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="2dp"
                        android:layout_height="80dp"
                        android:layout_gravity="center_vertical"
                        android:background="@color/health_report_trip_divider" />

                    <LinearLayout
                        android:layout_width="430dp"
                        android:layout_height="match_parent"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_trip_card2_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="状态欠佳行程数"
                            android:lineHeight="42dp"
                            android:layout_marginTop="30dp"
                            android:textColor="@color/text_color_fc_60"
                            android:textSize="22sp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/tv_trip_card2_content"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="36sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/tv_trip_card2_unit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignBaseline="@+id/tv_trip_card2_content"
                                    android:layout_toRightOf="@+id/tv_trip_card2_content"
                                    android:text="次"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="22sp" />
                            </RelativeLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="2dp"
                        android:layout_height="80dp"
                        android:layout_gravity="center_vertical"
                        android:background="@color/health_report_trip_divider" />

                    <LinearLayout
                        android:layout_width="430dp"
                        android:layout_height="match_parent"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_trip_card3_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="累计驾驶时长"
                            android:lineHeight="42dp"
                            android:layout_marginTop="30dp"
                            android:textColor="@color/text_color_fc_60"
                            android:textSize="22sp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/tv_trip_card3_hour_value"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="36sp"
                                    android:textStyle="bold" />
                                <TextView
                                    android:id="@+id/tv_trip_card3_hour_unit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignBaseline="@+id/tv_trip_card3_hour_value"
                                    android:layout_toRightOf="@+id/tv_trip_card3_hour_value"
                                    android:text="小时"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="22sp" />

                                <TextView
                                    android:id="@+id/tv_trip_card3_min_value"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_toRightOf="@+id/tv_trip_card3_hour_unit"
                                    android:text="0"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="36sp"
                                    android:textStyle="bold" />
                                <TextView
                                    android:id="@+id/tv_trip_card3_min_unit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignBaseline="@+id/tv_trip_card3_min_value"
                                    android:layout_toRightOf="@+id/tv_trip_card3_min_value"
                                    android:text="分钟"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="22sp" />
                            </RelativeLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="2dp"
                        android:layout_height="80dp"
                        android:layout_gravity="center_vertical"
                        android:background="@color/health_report_trip_divider" />

                    <LinearLayout
                        android:id="@+id/sleep_day_data_center"
                        android:layout_width="430dp"
                        android:layout_height="match_parent"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_sleep_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="累计驾驶里程"
                            android:lineHeight="42dp"
                            android:layout_marginTop="30dp"
                            android:textColor="@color/text_color_fc_60"
                            android:textSize="22sp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/tv_trip_distance_all"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="338"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="36sp"
                                    android:textStyle="bold" />


                                <TextView
                                    android:id="@+id/sleep_hour_unit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignBaseline="@+id/tv_trip_distance_all"
                                    android:layout_toRightOf="@+id/tv_trip_distance_all"
                                    android:text="km"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="22sp" />
                            </RelativeLayout>
                        </LinearLayout>
                    </LinearLayout>


                </LinearLayout>

                <LinearLayout
                    android:visibility="gone"
                    android:id="@+id/lo_trip_image_data"
                    android:layout_width="wrap_content"
                    android:layout_height="314dp"
                    android:layout_below="@+id/lo_trip_content"
                    android:layout_marginTop="26dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <com.healthlink.hms.views.TripAniView
                        android:id="@+id/trip_image"
                        android:layout_width="400dp"
                        android:layout_height="250dp"
                        android:layout_gravity="center_vertical" />

                    <LinearLayout
                        android:id="@+id/lo_trip_data"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="驾驶状态占比："
                            android:textColor="@color/health_report_normal_80"
                            android:textSize="22sp" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:orientation="horizontal">

                            <com.healthlink.hms.views.HMSCircleView
                                android:layout_width="10dp"
                                android:layout_height="10dp"
                                android:layout_gravity="center"
                                app:circleColor="@color/health_report_trip_normal" />

                            <TextView
                                android:id="@+id/tv_normal_trip_per"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:text="状态良好--%"
                                android:textColor="@color/health_report_normal_60"
                                android:textSize="22sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:orientation="horizontal">

                            <com.healthlink.hms.views.HMSCircleView
                                android:layout_width="10dp"
                                android:layout_height="10dp"
                                android:layout_gravity="center"
                                app:circleColor="@color/health_report_trip_abnormal" />

                            <TextView
                                android:id="@+id/tv_un_normal_trip_per"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:text="状态欠佳--%"
                                android:textColor="@color/health_report_normal_60"
                                android:textSize="22sp" />
                        </LinearLayout>

                    </LinearLayout>
                </LinearLayout>

                <RelativeLayout
                    android:visibility="gone"
                    android:id="@+id/last_info"
                    android:layout_width="1000dp"
                    android:layout_height="286dp"
                    android:layout_below="@+id/lo_trip_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="40dp"
                    android:background="@drawable/card_bg">

                    <TextView
                        android:id="@+id/tv_drive_time_conclusion"
                        android:layout_width="700dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_alignParentTop="true"
                        android:layout_marginLeft="60dp"
                        android:layout_marginTop="120dp"
                        android:lineSpacingExtra="19dp"
                        android:text=""
                        android:textColor="@color/health_report_normal_80"
                        android:textSize="22sp" />

                    <ImageView
                        android:layout_width="130dp"
                        android:layout_height="130dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="106dp"
                        android:layout_marginRight="60dp"
                        android:src="@drawable/week_report_drive_times" />
                </RelativeLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/rl_health_advice"
                    android:layout_marginVertical="40dp"
                    android:layout_centerHorizontal="true"
                    android:text="@string/tips_health_info"
                    android:textColor="@color/personal_setting_text_40"
                    android:textSize="22sp" />
            </RelativeLayout>
        </com.healthlink.hms.views.StretchScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
