<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <RelativeLayout
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="top|center"
        android:fitsSystemWindows="true"
        android:background="@android:color/transparent"
        android:id="@+id/container"
        tools:context=".activity.HuaweiOAuthActivity">

        <FrameLayout
            android:soundEffectsEnabled="false"
            android:id="@+id/login_dialog_container"
            android:layout_gravity="center"
            android:layout_width="1476dp"
            android:layout_height="840dp"
            android:orientation="vertical"
            android:background="@drawable/drawable_bg_login_color"
            >
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:background="@android:color/transparent"
                >
                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="96dp">
                    <ImageView
                        android:visibility="gone"
                        android:id="@+id/hw_oauth_webview_back"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginStart="53dp"
                        android:layout_centerVertical="true"
                        android:background="@drawable/ic_arrow_left"
                        />
                    <TextView
                        android:id="@+id/tv_webview_title"
                        android:gravity="center"
                        android:layout_width="match_parent"
                        android:layout_height="106dp"
                        android:textSize="28sp"
                        android:textColor="@color/text_color_fc_100"
                        android:text="帐号登录"
                        />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_intro"
                    android:visibility="visible"
                    android:paddingStart="70dp"
                    android:paddingEnd="70dp"
                    android:paddingTop="0dp"
                    android:layout_width="wrap_content"
                    android:layout_height="122dp"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:lineHeight="36dp"
                        android:textColor="@color/text_color_fc_60"
                        android:textSize="22sp"
                        android:text="1、请勾选全部的数据项，确保您的健康分析更加准确。\n2、请您打开华为健康APP刷新数据，以确保您的华为健康数据可以及时更新。"
                        />
                </RelativeLayout>

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@android:color/transparent"
                    >

<!--                    <com.hieupt.android.standalonescrollbar.view.ScrollView2-->
<!--                        android:id="@+id/scroll_web_privacy1"-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:layout_marginStart="68dp"-->
<!--                        android:layout_marginTop="40dp"-->
<!--                        android:layout_marginEnd="68dp">-->
                    <LinearLayout
                        android:id="@+id/wv_huawei_oauth_parent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        />
<!--                    </com.hieupt.android.standalonescrollbar.view.ScrollView2>-->
                </FrameLayout>
            </LinearLayout>
<!--            <com.hieupt.android.standalonescrollbar.StandaloneScrollBar-->
<!--                android:layout_marginTop="40dp"-->
<!--                app:scrollbarAlwaysShow="true"-->
<!--                app:scrollbarThumbLength="160dp"-->
<!--                android:layout_gravity="end"-->
<!--                app:scrollbarThumbDrawable="@drawable/scrollbar_ver_thumb"-->
<!--                android:id="@+id/scrollbar"-->
<!--                android:layout_marginEnd="16dp"-->
<!--                android:layout_width="4dp"-->
<!--                android:layout_marginBottom="5dp"-->
<!--                android:layout_height="match_parent"-->
<!--                />-->

        </FrameLayout>


    </RelativeLayout>


</layout>