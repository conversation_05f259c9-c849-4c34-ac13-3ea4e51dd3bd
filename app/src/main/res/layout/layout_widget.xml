<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_card_health_status"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="0dp"
    android:background="@drawable/transparent"
    >

    <ImageView
        android:id="@+id/widget_iv_health_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/widget_health_status_disable" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="32px"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:orientation="vertical"
        android:gravity="center_vertical"
        >
        <LinearLayout
            android:id="@+id/widget_tv_health_status_summary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="58px"
            android:orientation="horizontal"
            android:visibility="gone"
            android:gravity="center_vertical"
            >
            <TextView
                android:id="@+id/widget_tv_health_status_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_fc_100"
                android:textSize="29px"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:text="@string/hms_widget_health_status_label" />
            <TextView
                android:id="@+id/widget_tv_health_status_code_name"
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_fc_100"
                android:textSize="34.58px"
                android:textFontWeight="700"
                android:gravity="center_vertical"/>
        </LinearLayout>
        <TextView
            android:id="@+id/widget_tv_login_status"
            android:layout_width="150dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10px"
            android:layout_marginEnd="10dp"
            android:textColor="@color/text_color_fc_100"
            android:textSize="12sp"
            android:lineHeight="@dimen/px_to_dp_28px"
            android:text="@string/hms_widget_tip_for_no_login"/>
        <TextView
            android:id="@+id/widget_tv_health_status"
            android:layout_width="150dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="15px"
            android:layout_marginEnd="10dp"
            android:textColor="@color/text_color_fc_100"
            android:textSize="12sp"
            android:lineHeight="@dimen/px_to_dp_28px"
            android:visibility="gone"
            android:text="@string/hms_widget_tip_for_no_login"/>
    </LinearLayout>

    <RelativeLayout
        android:layout_width="212px"
        android:layout_height="212px"
        android:layout_alignParentRight="true"
        android:layout_marginRight="11px"
        android:padding="0dp"
        android:layout_centerVertical="true"
        >
        <RelativeLayout
            android:layout_width="212px"
            android:layout_height="212px">
            <ImageView
                android:id="@+id/widget_health_status_image_bg"
                android:layout_width="212px"
                android:layout_height="212px"
                android:visibility="gone"
                android:src="@drawable/widget_health_status_basic_normal"
                />
            <ImageView
                android:id="@+id/widget_health_status_image"
                android:layout_width="212px"
                android:layout_height="212px"
                android:visibility="gone"
                android:src="@drawable/widget_health_status_basic_normal"
                />
        </RelativeLayout>
        <LinearLayout
            android:id="@+id/widget_heath_score_ll"
            android:layout_width="wrap_content"
            android:layout_height="146px"
            android:orientation="horizontal"
            android:layout_marginTop="42px"
            android:layout_centerHorizontal="true"
            android:gravity="center_vertical"
            android:visibility="gone"
            >
            <TextView
                android:id="@+id/widget_health_score_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15px"
                android:textSize="32px"
                android:text="98"
                android:textFontWeight="700"
                android:textColor="@color/text_color_fc_100"
                />

            <TextView
                android:id="@+id/widget_health_score_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15px"
                android:textSize="26px"
                android:text="分"
                android:textColor="@color/text_color_fc_100"
                />

        </LinearLayout>
    </RelativeLayout>

</RelativeLayout>