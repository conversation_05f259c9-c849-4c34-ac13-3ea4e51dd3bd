<?xml version="1.0" encoding="utf-8"?><!-- res/layout/view_dial_pad.xml -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingHorizontal="72dp"
    tools:layout_height="504dp"
    tools:layout_width="560dp">

    <HorizontalScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="18dp"
        android:scrollbars="none"
        android:fillViewport="true">

        <TextView
            android:id="@+id/tv_input"
            android:layout_width="wrap_content"
            android:layout_height="64dp"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:lines="1"
            android:scrollHorizontally="true"
            android:textColor="@color/doctor_call_name"
            android:textSize="44sp"
            tools:text="231" />
    </HorizontalScrollView>

    <GridLayout
        android:id="@+id/grid_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:layout_marginTop="16dp"
        android:layout_weight="1"
        android:alignmentMode="alignMargins"
        android:columnCount="3"
        android:rowCount="4">

        <com.healthlink.hms.business.doctorcall.view.DoctorCallDialButton
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_row="0"
            android:layout_rowWeight="1"
            android:layout_column="0"
            android:layout_columnWeight="1"
            android:gravity="center"
            app:dc_number="1" />

        <com.healthlink.hms.business.doctorcall.view.DoctorCallDialButton
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_row="0"
            android:layout_rowWeight="1"
            android:layout_column="1"
            android:layout_columnWeight="1"
            android:gravity="center"
            app:dc_letter="ABC"
            app:dc_number="2" />

        <com.healthlink.hms.business.doctorcall.view.DoctorCallDialButton
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_row="0"
            android:layout_rowWeight="1"
            android:layout_column="2"
            android:layout_columnWeight="1"
            android:gravity="center"
            app:dc_letter="DEF"
            app:dc_number="3" />

        <com.healthlink.hms.business.doctorcall.view.DoctorCallDialButton
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_row="1"
            android:layout_rowWeight="1"
            android:layout_column="0"
            android:layout_columnWeight="1"
            android:gravity="center"
            app:dc_letter="GHI"
            app:dc_number="4" />

        <com.healthlink.hms.business.doctorcall.view.DoctorCallDialButton
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_row="1"
            android:layout_rowWeight="1"
            android:layout_column="1"
            android:layout_columnWeight="1"
            android:gravity="center"
            app:dc_letter="JKL"
            app:dc_number="5" />

        <com.healthlink.hms.business.doctorcall.view.DoctorCallDialButton
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_row="1"
            android:layout_rowWeight="1"
            android:layout_column="2"
            android:layout_columnWeight="1"
            android:gravity="center"
            app:dc_letter="MNO"
            app:dc_number="6" />

        <com.healthlink.hms.business.doctorcall.view.DoctorCallDialButton
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_row="2"
            android:layout_rowWeight="1"
            android:layout_column="0"
            android:layout_columnWeight="1"
            android:gravity="center"
            app:dc_letter="PQRS"
            app:dc_number="7" />

        <com.healthlink.hms.business.doctorcall.view.DoctorCallDialButton
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_row="2"
            android:layout_rowWeight="1"
            android:layout_column="1"
            android:layout_columnWeight="1"
            android:gravity="center"
            app:dc_letter="TUV"
            app:dc_number="8" />

        <com.healthlink.hms.business.doctorcall.view.DoctorCallDialButton
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_row="2"
            android:layout_rowWeight="1"
            android:layout_column="2"
            android:layout_columnWeight="1"
            android:gravity="center"
            app:dc_letter="WXYZ"
            app:dc_number="9" />

        <com.healthlink.hms.business.doctorcall.view.DoctorCallDialButton
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_row="3"
            android:layout_rowWeight="1"
            android:layout_column="0"
            android:layout_columnWeight="1"
            android:gravity="center"
            app:dc_letter=""
            app:dc_number="*" />

        <com.healthlink.hms.business.doctorcall.view.DoctorCallDialButton
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_row="3"
            android:layout_rowWeight="1"
            android:layout_column="1"
            android:layout_columnWeight="1"
            android:gravity="center"
            app:dc_number="0" />

        <com.healthlink.hms.business.doctorcall.view.DoctorCallDialButton
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_row="3"
            android:layout_rowWeight="1"
            android:layout_column="2"
            android:layout_columnWeight="1"
            android:gravity="center"
            app:dc_letter=""
            app:dc_number="#" />

    </GridLayout>
</LinearLayout>

