<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/dialog_content"
        android:layout_width="640dp"
        android:layout_height="560dp"
        android:layout_gravity="center"
        android:background="@drawable/dialog_bg">

        <LinearLayout
            android:id="@+id/lo_dialog_title"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_alignParentTop="true">
            <TextView
                android:id="@+id/hms_dialog_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_marginTop="32dp"
                android:gravity="center"
                android:text="健康建议"
                android:lineHeight="42dp"
                android:textColor="@color/text_color_fc_100"
                android:textSize="26sp" />
        </LinearLayout>

        <FrameLayout
            android:id="@+id/lo_data_usage"
            android:layout_width="match_parent"
            android:layout_height="334dp"
            android:layout_below="@+id/lo_dialog_title"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/lo_loading"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone" />

            <com.hieupt.android.standalonescrollbar.view.ScrollView2
                android:id="@+id/data_usage_container_scroll"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="48dp"
                android:layout_marginEnd="48dp">

                <TextView
                    android:id="@+id/hms_dialog_message"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:lineHeight="36dp"
                    android:text=""
                    android:textColor="@color/text_color_fc_80"
                    android:textSize="22sp" />
            </com.hieupt.android.standalonescrollbar.view.ScrollView2>
        </FrameLayout>

        <com.hieupt.android.standalonescrollbar.StandaloneScrollBar
            android:id="@+id/scrollbar"
            android:layout_width="4dp"
            android:layout_height="334dp"
            android:layout_alignTop="@+id/lo_data_usage"
            android:layout_alignRight="@+id/lo_data_usage"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="5dp"
            app:scrollbarAlwaysShow="false"
            app:scrollbarThumbDrawable="@drawable/scrollbar_ver_thumb"
            app:scrollbarThumbLength="64dp" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="88dp"
            android:layout_alignParentBottom="true"
            android:background="@drawable/bg_dialog_w_960_button"
            android:orientation="horizontal">

            <Button
                android:id="@+id/positiveButton"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginRight="1dp"
                android:layout_weight="0.5"
                android:background="#00000000"
                android:text="知道了"
                android:textColor="@color/hms_color_primary"
                android:textSize="26sp" />
        </LinearLayout>

    </RelativeLayout>

</FrameLayout>