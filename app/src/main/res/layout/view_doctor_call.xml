<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:gravity="center_vertical"
    android:layout_height="match_parent">

    <cn.enjoytoday.shadow.ShadowLayout
        android:orientation="vertical"
        android:id="@+id/shadowLayout"
        android:gravity="center"
        app:shadowRadius="16dp"
        app:shadowColor="#26000000"
        app:bgColor="@color/doctor_call_dial_shadow"
        android:background="@drawable/bg_doctor_call_dial"
        app:xOffset="0dp"
        app:yOffset="0dp"
        app:blurRadius="40dp"
        android:layout_marginHorizontal="12dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
<!--      paddingBottom  186dp -->
        <LinearLayout
            android:layout_width="560dp"
            android:layout_height="876dp"
            android:paddingBottom="90dp"
            android:gravity="center_horizontal"
            android:elevation="3dp"
            android:orientation="vertical">

            <View
                android:layout_width="72dp"
                android:layout_height="3dp"
                android:layout_marginTop="22dp"
                android:layout_marginBottom="23dp"
                android:background="@drawable/bg_doctor_call_bar" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <LinearLayout
                    android:id="@+id/doctor_call_content_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    tools:visibility="visible">

                    <ImageView
                        android:layout_width="116dp"
                        android:layout_height="116dp"
                        android:layout_margin="10dp"
                        android:src="@drawable/bg_doctor_avtar" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="36dp"
                        android:lineHeight="72dp"
                        android:text="@string/telephone_doctor_module_name"
                        android:textColor="@color/doctor_call_name"
                        android:textSize="36sp" />

                    <TextView
                        android:id="@+id/tv_doctor_call_duration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="18dp"
                        android:alpha="0.6"
                        android:lineHeight="42dp"
                        android:textColor="@color/doctor_call_text"
                        android:textSize="26sp"
                        tools:text="00:00" />
                </LinearLayout>

                <com.healthlink.hms.business.doctorcall.view.DoctorCallDialPan
                    android:id="@+id/doctor_call_dial_pan"
                    android:layout_width="match_parent"
                    android:layout_height="504dp"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="8dp"
                    android:visibility="gone" />

            </FrameLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="horizontal">
                <LinearLayout
                    android:id="@+id/btn_doctor_call_mute"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_doctor_call_mute"
                        android:layout_width="88dp"
                        android:layout_height="88dp"
                        android:src="@drawable/bg_btn_mute_disable"
                        tools:src="@drawable/bt_doctor_call_mute_selector" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:textSize="22sp"
                        android:lineHeight="36sp"
                        android:textColor="@color/doctor_call_text"
                        android:text="@string/doctor_call_mute" />

                </LinearLayout>
                <LinearLayout
                    android:id="@+id/btn_doctor_call_hang_up"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:layout_marginStart="52dp"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_doctor_call_hang_up"
                        android:layout_width="88dp"
                        android:layout_height="88dp"
                        android:src="@drawable/bt_doctor_call_hangup_selector" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:textSize="22sp"
                        android:textColor="@color/doctor_call_text"
                        android:text="@string/doctor_call_hang_up" />

                </LinearLayout>
                <LinearLayout
                    android:id="@+id/btn_doctor_call_keyboard"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:layout_marginStart="52dp"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_doctor_call_keyboard"
                        android:layout_width="88dp"
                        android:layout_height="88dp"
                        android:src="@drawable/bg_btn_dtmf_keyboard_disable"
                        tools:src="@drawable/bt_doctor_call_keyboard_normal_selector" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:textSize="22sp"
                        android:lineHeight="36dp"
                        android:textColor="@color/doctor_call_text"
                        android:text="@string/doctor_call_keyboard" />

                </LinearLayout>
            </LinearLayout>

            <TextView
                android:layout_marginTop="80dp"
                android:layout_gravity="center|bottom"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="18sp"
                android:textColor="@color/text_color_fc_60"
                android:text="@string/doctor_declare"
                />
        </LinearLayout>
    </cn.enjoytoday.shadow.ShadowLayout>
</FrameLayout>

