<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

<LinearLayout
    android:layout_width="872dp"
    android:layout_height="860dp"
    android:background="@drawable/card_bg"
    android:layout_marginRight="40dp"
    android:layout_marginBottom="28dp"
    android:orientation="vertical"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:orientation="horizontal"
        >
        <Button
            android:id="@+id/hw_oauth_webview_back"
            android:text="后退"
            android:layout_width="80dp"
            android:layout_height="40dp"
            android:layout_marginTop="30dp"
            android:layout_marginLeft="24dp"
            android:layout_centerVertical="true"
            android:background="@drawable/text_bg_fill_selector"
            android:textColor="@color/btn_text_color"
            android:textSize="18sp"
            />

        <TextView
            android:id="@+id/tv_intro"
            android:layout_toRightOf="@id/hw_oauth_webview_back"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="24dp"
            android:textColor="@color/text_color_fc_40"
            android:textSize="22sp"
            android:visibility="visible"

            android:text="1、请勾选全部的数据项，确保您的健康分析更加准确。\n2、请您打开华为健康APP刷新数据，以确保您的华为健康数据可以及时更新。"
            />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="712dp">

        <LinearLayout
            android:id="@+id/wv_huawei_oauth_parent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        >
        <View
            android:layout_width="match_parent"
            android:layout_height="24dp"/>
    </LinearLayout>

</LinearLayout>

</layout>