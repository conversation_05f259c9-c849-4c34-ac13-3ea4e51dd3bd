<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_card_container_doctor"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="0dp"
    android:background="@drawable/transparent"
    >

    <ImageView
        android:id="@+id/widget_iv_health_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/widget_doctor" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="32px"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:orientation="vertical"
        android:gravity="center_vertical"
        >
        <LinearLayout
            android:id="@+id/widget_tv_health_status_summary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="39px"
            android:orientation="horizontal"
            android:visibility="visible"
            android:gravity="center_vertical"
            >
            <TextView
                android:id="@+id/widget_tv_health_status_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_fc_100"
                android:textSize="14.5sp"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:text="@string/hms_widget_health_ask_label" />
            <ImageView
                android:layout_marginLeft="6dp"
                android:id="@+id/widget_image_call"
                android:layout_gravity="center"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_calling"
                android:gravity="center_vertical"/>
        </LinearLayout>
        <TextView
            android:id="@+id/widget_tv_health_status"
            android:layout_width="150dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10px"
            android:layout_marginEnd="10dp"
            android:textColor="@color/text_color_fc_60"
            android:textSize="12sp"
            android:lineHeight="@dimen/px_to_dp_28px"
            android:text="@string/hms_widget_health_ask_content"/>
    </LinearLayout>

</RelativeLayout>