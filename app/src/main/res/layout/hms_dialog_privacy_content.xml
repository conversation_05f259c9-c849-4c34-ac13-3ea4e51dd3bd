<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00000000">

    <RelativeLayout
        android:id="@+id/privacy_agree_content"
        android:layout_width="1280dp"
        android:layout_height="640dp"
        android:layout_gravity="center"
        android:background="@drawable/dialog_bg"
        android:visibility="visible">

        <LinearLayout
            android:id="@+id/lo_content_dialog_title"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_alignParentTop="true"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:background="@drawable/dialog_title_radius">

            <TextView
                android:id="@+id/content_dialog_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:gravity="center"
                android:lineHeight="42dp"
                android:text=""
                android:textColor="@color/text_color_fc_100"
                android:textSize="30sp" />
        </LinearLayout>

        <FrameLayout
            android:id="@+id/privacy_agree_scroll"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="90dp"
            android:orientation="vertical">
            <com.hieupt.android.standalonescrollbar.view.ScrollView2
                android:id="@+id/scroll_web_privacy1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="68dp"
                android:layout_marginEnd="68dp">
                <LinearLayout
                    android:id="@+id/web_privacy1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="32dp"
                    android:orientation="horizontal"/>
            </com.hieupt.android.standalonescrollbar.view.ScrollView2>
        </FrameLayout>


        <com.hieupt.android.standalonescrollbar.StandaloneScrollBar
            android:layout_alignRight="@+id/privacy_agree_scroll"
            android:layout_alignTop="@+id/privacy_agree_scroll"
            android:layout_marginTop="40dp"
            app:scrollbarAlwaysShow="true"
            app:scrollbarThumbLength="64dp"
            app:scrollbarThumbDrawable="@drawable/scrollbar_ver_thumb"
            android:id="@+id/scrollbar"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="16dp"
            android:layout_width="4dp"
            android:layout_marginBottom="5dp"
            android:layout_height="match_parent"
            />
    </RelativeLayout>

</FrameLayout>