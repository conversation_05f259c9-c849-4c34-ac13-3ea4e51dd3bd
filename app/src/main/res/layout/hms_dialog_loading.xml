<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@android:color/transparent"
    >
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="600dp"
        android:layout_height="wrap_content"
        >


        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/iv_loading_amin"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:src="@drawable/loading_dark"
            app:layout_constraintBottom_toBottomOf="@+id/textView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/textView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/loading_bottom_text"
            android:textColor="@color/text_color_fc_100"
            android:textSize="30sp"
            android:layout_marginTop="28dp"
            app:layout_constraintTop_toBottomOf="@+id/iv_loading_amin"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>