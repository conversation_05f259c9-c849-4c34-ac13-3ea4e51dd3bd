<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/privacy_agree_home"
        android:layout_width="960dp"
        android:layout_height="560dp"
        android:layout_gravity="center"
        android:background="@drawable/dialog_bg"
        android:visibility="visible">

        <TextView
            android:id="@+id/hms_dialog_title"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_alignParentTop="true"
            android:gravity="center"
            android:text="出生年月"
            android:textColor="@color/text_color_fc_100"
            android:textSize="26sp" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/hms_dialog_title"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="42dp">

            <RelativeLayout
                android:id="@+id/lo_year_piker"
                android:layout_width="wrap_content"
                android:layout_height="280dp">

                <com.shawnlin.numberpicker.NumberPicker
                    android:id="@+id/birthday_year_piker"
                    android:layout_width="wrap_content"
                    android:layout_height="280dp"
                    android:layout_alignParentLeft="true"
                    app:np_dividerColor="@color/colorAccent"
                    app:np_dividerType="underline"
                    app:np_fadingEdgeEnabled="false"
                    app:np_orientation="vertical"
                    app:np_secondTextColor="@color/personal_setting_text_40"
                    app:np_secondTextSize="30sp"
                    app:np_selectedTextColor="@color/personal_setting_text_100"
                    app:np_selectedTextSize="36sp"
                    app:np_textColor="@color/personal_setting_text_30"
                    app:np_textSize="22sp"
                    app:np_wheelItemCount="6"
                    app:np_wrapSelectorWheel="false" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="118dp"
                    android:layout_toRightOf="@+id/birthday_year_piker"
                    android:text="年"
                    android:textColor="@color/personal_setting_text_100"
                    android:textSize="30sp" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/lo_month_piker"
                android:layout_width="wrap_content"
                android:layout_height="280dp"
                android:layout_marginLeft="100dp"
                android:layout_toRightOf="@+id/lo_year_piker">

                <com.shawnlin.numberpicker.NumberPicker
                    android:id="@+id/birthday_month_piker"
                    android:layout_width="wrap_content"
                    android:layout_height="280dp"
                    android:layout_alignParentLeft="true"
                    app:np_accessibilityDescriptionEnabled="true"
                    app:np_dividerColor="@color/colorAccent"
                    app:np_dividerType="underline"
                    app:np_fadingEdgeEnabled="false"
                    app:np_orientation="vertical"
                    app:np_secondTextColor="@color/personal_setting_text_40"
                    app:np_secondTextSize="30sp"
                    app:np_selectedTextColor="@color/personal_setting_text_100"
                    app:np_selectedTextSize="36sp"
                    app:np_textColor="@color/personal_setting_text_30"
                    app:np_textSize="22sp"
                    app:np_wheelItemCount="6"
                    app:np_wrapSelectorWheel="true" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="118dp"
                    android:layout_toRightOf="@+id/birthday_month_piker"
                    android:text="月"
                    android:textColor="@color/personal_setting_text_100"
                    android:textSize="30sp" />
            </RelativeLayout>

        </RelativeLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="88dp"
            android:layout_alignParentBottom="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="88dp"
                android:layout_alignParentBottom="true"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/positiveButton"
                    android:layout_width="match_parent"
                    android:layout_height="88dp"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_left_bg_fill_selector"
                    android:text="确定"
                    android:textColor="@color/btn_left_text_color"
                    android:textSize="26sp" />

                <Button
                    android:id="@+id/negativeButton"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_right_bg_fill_selector"
                    android:text="取消"
                    android:textColor="@color/text_color_fc_60"
                    android:textSize="26sp" />
            </LinearLayout>

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal"
                android:background="@color/dialog_divider" />
        </FrameLayout>
    </RelativeLayout>

</FrameLayout>