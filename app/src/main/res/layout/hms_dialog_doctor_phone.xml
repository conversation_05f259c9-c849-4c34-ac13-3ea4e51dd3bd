<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/fl_container"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"

    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_content"
        android:layout_gravity="center"
        android:background="@drawable/dialog_bg"
        android:layout_width="640dp"
        android:layout_height="328dp">


        <TextView
            android:id="@+id/hms_dialog_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="手机号绑定成功，现在您可以呼叫资深全科医生团队的优质电话问诊服务。"
            android:textColor="@color/text_color_fc_80"
            android:textSize="22sp"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/hms_dialog_tip"/>

        <TextView
            android:id="@+id/hms_dialog_tip"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:gravity="center"
            android:text="已绑定手机号："
            android:textColor="@color/text_color_fc_60"
            android:textSize="18sp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="88dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/hms_dialog_message"
            app:layout_constraintVertical_chainStyle="packed"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="88dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal">

            <Button
                android:id="@+id/positiveButton"
                android:layout_width="match_parent"
                android:layout_height="88dp"
                android:layout_weight="0.5"
                android:layout_marginRight="1dp"
                android:background="@drawable/dialog_btn_left_bg_fill_selector"
                android:text="确定"
                android:textColor="@color/btn_left_text_color"
                android:textSize="26sp"
                />
            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#19FFFFFF"
                />
            <Button
                android:id="@+id/negativeButton"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="0.5"
                android:layout_marginLeft="1dp"
                android:background="@drawable/dialog_btn_right_bg_fill_selector"
                android:text="取消"
                android:textColor="@color/text_color_fc_60"
                android:textSize="26sp"
                />
        </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>