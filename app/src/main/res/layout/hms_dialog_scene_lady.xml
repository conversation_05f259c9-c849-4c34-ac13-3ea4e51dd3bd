<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout
    android:id="@+id/fl_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/dialog_bg"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="420dp"
        android:layout_height="420dp">

        <WebView
            android:id="@+id/scene_lady_dialog_webview"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/textView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textView"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:layout_marginBottom="60dp"
            android:gravity="center"
            android:text="@string/dialog_scene_lady_content"
            android:textColor="#CCFCFCFC"
            android:textSize="24sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/scene_lady_dialog_webview" />


</androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>