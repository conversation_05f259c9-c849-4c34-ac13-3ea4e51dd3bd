<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.healthlink.hms.viewmodels.MainViewModel" />
    </data>

    <!--数据容器 android:background="@drawable/bg_app"-->
    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/hms_view_bg_color"
        android:fitsSystemWindows="true"
        android:orientation="vertical">
        <!--    导航头部-->
        <LinearLayout
            android:id="@+id/ll_nav_back_container"
            android:layout_width="300dp"
            android:layout_height="80dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="64dp"
                android:src="@drawable/ic_arrow_left" />

            <TextView
                android:id="@+id/tv_nav_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="40dp"
                android:text="返回"
                android:textColor="@color/text_color_333"
                android:textSize="30sp" />
        </LinearLayout>

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            style="@style/TimeTabLayout"
            android:layout_width="440dp"
            android:layout_height="92dp"
            android:layout_below="@+id/ll_nav_back_container"
            android:layout_marginStart="68dp"
            android:layout_marginBottom="6dp"
            android:background="@android:color/transparent"
            android:textSize="30sp"
            app:tabGravity="fill"
            app:tabIndicator="@drawable/tab_indicator"
            app:tabIndicatorFullWidth="false"
            app:tabMode="fixed"
            app:tabSelectedTextColor="@color/text_color_333"
            app:tabTextAppearance="@style/TabLayoutTextUnSelected"
            app:tabTextColor="@color/text_color_666" />

        <FrameLayout
            android:id="@+id/fl_viewpager_parent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/tabLayout">
            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/contentViewPager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                />
        </FrameLayout>

    </RelativeLayout>
</layout>