<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/main_data_card_width"
    android:layout_height="@dimen/main_data_card_height"
    android:orientation="vertical"
    android:background="@drawable/card_bg_selector">
    <!--    卡片正常数据界面-->
    <FrameLayout
        android:visibility="gone"
        android:id="@+id/card_normal_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <View
            android:id="@+id/iv_bg_card_exception"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_card_bink_anim"
            android:visibility="gone"/>

        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <!--卡片标题 及 卡片状态文字：正常，过速登-->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    style="@style/hms_card_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:gravity="center_vertical"
                    android:text="血糖"
                    />
                <TextView
                    style="@style/hms_card_title_status"
                    android:layout_width="wrap_content"
                    android:layout_height="28dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/health_index_status_nice_bg_fill"
                    android:gravity="center"
                    android:text="正常"
                />
            </RelativeLayout>
            <LinearLayout
                style="@style/hms_card_value_container"
                >
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/tv_main_blood_sugar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="7.54"
                        android:gravity="bottom"
                        style="@style/hms_data_card_main_data"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/hms_data_car_data_unit"
                        android:text="mmol/L"
                        android:layout_toEndOf="@+id/tv_main_blood_sugar"
                        android:layout_alignBaseline="@+id/tv_main_blood_sugar"/>
                </RelativeLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="105dp"
                android:orientation="vertical">
                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@mipmap/bg_blood_sugar_chart"/>
            </LinearLayout>
        </LinearLayout>
        <TextView
            style="@style/hms_card_last_update_text"
            android:id="@+id/tv_data_year_or_day"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:text="2024-05-11"
            />
    </FrameLayout>
    <!--    卡片无数据界面-->
    <FrameLayout
        android:visibility="visible"
        android:id="@+id/card_no_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@mipmap/card_blood_sugar_no_data"
            android:visibility="visible"
            android:contentDescription="@string/card_bg_blood_sugar_no_data" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:text="血糖" />

            <TextView
                style="@style/hms_card_no_data_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="血糖检测 饮食运动分析" />

        </LinearLayout>

    </FrameLayout>
</LinearLayout>