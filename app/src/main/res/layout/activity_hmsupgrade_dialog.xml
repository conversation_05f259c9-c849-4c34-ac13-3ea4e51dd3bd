<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.healthlink.hms.viewmodels.MainViewModel" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/dialog_upgrade_container"
        android:background="@android:color/transparent">
        <RelativeLayout
            android:id="@+id/dialog_content"
            android:layout_width="640dp"
            android:layout_height="328dp"
            android:layout_gravity="center"
            android:background="@drawable/dialog_bg">

            <LinearLayout
                android:id="@+id/lo_dialog_title"
                android:layout_width="match_parent"
                android:layout_height="90dp"
                android:layout_alignParentTop="true">
                <TextView
                    android:id="@+id/hms_dialog_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_marginTop="32dp"
                    android:gravity="center"
                    android:text=""
                    android:lineHeight="42dp"
                    android:textColor="@color/text_color_fc_100"
                    android:textSize="26sp"
                    android:textFontWeight="700"/>
            </LinearLayout>

            <FrameLayout
                android:id="@+id/lo_data_usage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/lo_dialog_title"
                android:layout_marginTop="35dp"
                android:orientation="vertical">

                <com.hieupt.android.standalonescrollbar.view.ScrollView2
                    android:id="@+id/data_usage_container_scroll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="48dp"
                    android:layout_marginEnd="48dp">

                    <TextView
                        android:id="@+id/hms_dialog_message"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:lineHeight="36dp"
                        android:text=""
                        android:textColor="@color/text_color_fc_80"
                        android:textSize="22sp" />
                </com.hieupt.android.standalonescrollbar.view.ScrollView2>
            </FrameLayout>

            <com.hieupt.android.standalonescrollbar.StandaloneScrollBar
                android:visibility="gone"
                android:id="@+id/scrollbar"
                android:layout_width="4dp"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/lo_data_usage"
                android:layout_alignRight="@+id/lo_data_usage"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="5dp"
                app:scrollbarAlwaysShow="false"
                app:scrollbarThumbDrawable="@drawable/scrollbar_ver_thumb"
                app:scrollbarThumbLength="64dp" />


            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="88dp"
                android:layout_marginTop="48dp"
                android:layout_alignParentBottom="true"
                android:layout_alignParentEnd="true">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="88dp"
                    android:orientation="horizontal"
                    >
                    <Button
                        android:id="@+id/positiveButton"
                        android:layout_width="match_parent"
                        android:layout_height="88dp"
                        android:layout_weight="0.5"
                        android:background="@drawable/dialog_btn_left_bg_fill_selector"
                        android:text="去升级"
                        android:textColor="@color/hms_color_primary"
                        android:textSize="26sp" />


                    <Button
                        android:id="@+id/negativeButton"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="0.5"
                        android:background="@drawable/dialog_btn_right_bg_fill_selector"
                        android:text="取消"
                        android:textColor="@color/text_color_fc_60"
                        android:textSize="26sp" />
                </LinearLayout>

                <View
                    android:layout_gravity="center_horizontal"
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/dialog_divider" />
            </FrameLayout>

        </RelativeLayout>

    </FrameLayout>
</layout>