<?xml version="1.0" encoding="utf-8"?>
<cn.enjoytoday.shadow.ShadowLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="640dp"
    android:layout_height="96dp"
    android:orientation="horizontal"
    android:gravity="center"
    app:shadowRadius="16dp"
    app:blurRadius="16dp"
    app:bgColor="@color/toast_bg_color"
    app:xOffset="0dp"
    app:shadowColor="@color/toast_shader">

    <TextView
        android:id="@+id/toast_text"
        android:layout_width="640dp"
        android:layout_height="96dp"
        android:textSize="26sp"
        android:gravity="center"
        android:textColor="@color/text_color_fc_100"
        />

</cn.enjoytoday.shadow.ShadowLayout>