package com.healthlink.hms.application

import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityManager
import android.app.Application
import android.app.Dialog
import android.app.NotificationChannel
import android.app.NotificationManager
import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.os.Process
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.FrameLayout
import android.widget.TextView
import androidx.annotation.LayoutRes
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.view.WindowCompat
import com.bumptech.glide.Glide
import com.gwm.datatrack.client.DataTrackManager
import com.healthlink.hms.R
import com.healthlink.hms.activity.HuaweiOAuthActivity
import com.healthlink.hms.activity.MainActivity
import com.healthlink.hms.core.common.utils.AppContext
import com.healthlink.hms.core.common.utils.BaseContext
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.core.common.utils.MMKVUtil.getLastThemeMode
import com.healthlink.hms.core.common.utils.MMKVUtil.storeLastThemeMode
import com.healthlink.hms.core.network.NetworkApi
import com.healthlink.hms.core.network.NetworkRequiredInfo
import com.healthlink.hms.helper.ApplicationHelper
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.sdks.map.gwm.GWMMapManagerKotCoroutines
import com.healthlink.hms.utils.PrivacyModeObserver
import com.healthlink.hms.utils.isDarkModeEnabled
import com.healthlink.hms.views.ImmersiveDialog
import com.healthlink.hms.widget.HMSWidgetProvider
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.lang.IllegalArgumentException
import java.lang.ref.WeakReference
import javax.inject.Inject

@HiltAndroidApp
class HmsApplication : Application() {
    @Inject
    lateinit var applicationHelper: ApplicationHelper

    override fun onCreate() {
        super.onCreate()
        appContext = applicationContext
        // 1.先初始化
        // TODO: 这种方式是否合适
        AppContext.initialize(AppContextProvider())
        //初始化获取私密模式
//        isPrivacyMode= isPrivacyModeEnabled()
        // 初始化埋点全局SDK
        try {
            DataTrackManager.getInstance().initManager(appContext)
        }catch (ex:Exception){
            Log.i(TAG,"init DataTrackManager fail。")
        }
        // 初始化本地存储 MMKVUtil
        val cryptKey: String? = "Hms123456gwm" // 可以替换成实际的密钥，也可以传 null
        MMKVUtil.initialize(cryptKey)
        // 跟随系统设置的主题模式
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM)
        // 2. 初始化
        NetworkApi.init(NetworkRequiredInfo())
        // 仅在主进程中初始化地图单例对象
        getCurProcessName(this)?.let {
            Log.i(TAG, "getCurProcessName is $it and packageName is ${this.packageName}")
            if (it == this.packageName) {
                GWMMapManagerKotCoroutines.initMapManager(this)
                Log.i(TAG, "${GWMMapManagerKotCoroutines.toString()}")

                GlobalScope.launch {
                    GwmAdapterManagerKotCoroutines.initGwmAdapter(appContext)
                }
            }
        }
        registerActivityLifecycle()
        // 仅在主进程监听私密模式
        getCurProcessName(this)?.let {
            if (it == this.packageName) {
                registPrivacyModeListener()
            }
        }

//        // 获取coffee os 版本
//        val version = GwmAdapterManagerKotCoroutines.getCoffeeOSVersion(this)
//        LogUtils.i("HmsApplication version: $version")
        prepareNotification()
        judgeIsDarkMode(appContext)
    }

    private fun judgeIsDarkMode(context: Context) {
        val nightModeFlags: Int =
            context.resources.configuration.uiMode and
                    Configuration.UI_MODE_NIGHT_MASK
        when (nightModeFlags) {
            Configuration.UI_MODE_NIGHT_YES -> {
                isDarkMode = true
                storeLastThemeMode(Configuration.UI_MODE_NIGHT_YES)
            }
            Configuration.UI_MODE_NIGHT_NO -> {
                isDarkMode = false
                storeLastThemeMode(Configuration.UI_MODE_NIGHT_NO)
            }
            Configuration.UI_MODE_NIGHT_UNDEFINED -> {
                isDarkMode = false
                storeLastThemeMode(Configuration.UI_MODE_NIGHT_UNDEFINED)
            }
        }
    }

    /**
     * 初始化通知Channel
     */
    private fun prepareNotification() {
        val channel = NotificationChannel(channelId, channelName, importance)
        // 创建渠道
        notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channel)
    }

    override fun onTerminate() {
        super.onTerminate()
        //unregist
        unregistPrivacyModeListener()
    }

    private fun getCurProcessName(context: Context): String? {
        // 获取此进程的标识符
        val pid = Process.myPid()
        // 获取活动管理器
        val activityManager = context.getSystemService(ACTIVITY_SERVICE) as ActivityManager
        // 从应用程序进程列表找到当前进程，是：返回当前进程名
        try {
            if(activityManager.runningAppProcesses != null && activityManager.runningAppProcesses.isNotEmpty()){
                for (appProcess in activityManager.runningAppProcesses) {
                    if (appProcess.pid == pid) {
                        return appProcess.processName
                    }
                }
            }
        } catch (e: Exception) {
            Log.i(TAG, "getCurProcessName: " + e.message)
        }
        return null
    }

    /**
     * 应用前后台判断 isInForeground true 前台 false 后台
     */
    private fun registerActivityLifecycle() {
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityPaused(activity: Activity) {
                Log.d(TAG, "onActivityPaused , ${activity}")
                isInForeground = false
//                setCurrentActivity(null)
            }

            override fun onActivityResumed(activity: Activity) {
                Log.d(TAG, "onActivityResumed , ${activity}")
                isInForeground = true
                setCurrentActivity(activity)
            }

            override fun onActivityStarted(activity: Activity) {
                Log.d(TAG, "onActivityStarted , ${activity}")
            }
            override fun onActivityDestroyed(activity: Activity) {
                Log.d(TAG, "onActivityDestroyed , ${activity}")
//                unregisterActivityLifecycleCallbacks(activity)
//                if (getCurrentActivity() == activity) {
//                    setCurrentActivity(null)
//                }
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
                Log.d(TAG, "onActivitySaveInstanceState , ${activity}")
            }
            override fun onActivityStopped(activity: Activity) {
                Log.d(TAG, "onActivityStopped , ${activity}")
            }
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                Log.d(TAG, "onActivityCreated , ${activity}")
            }
        })


    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        Log.i(TAG, "onTrimMemory $level")
        if (level == TRIM_MEMORY_UI_HIDDEN) {
            // mainThread
            Glide.get(this).clearMemory()
        }
        // mainThread
        Glide.get(this).trimMemory(level)
    }

    override fun onLowMemory() {
        super.onLowMemory()
        Log.d(TAG, "onLowMemory")
        Glide.get(this).clearMemory()
    }
    
    companion object {
        // 通知相关
        //渠道Id
        val channelId = "HMS_001"
        //渠道名
        private val channelName = "数字健康"

        //渠道重要级
        private val importance = NotificationManager.IMPORTANCE_HIGH

        //通知管理者
        private lateinit var notificationManager: NotificationManager

        private lateinit var privacyModeObserver: PrivacyModeObserver

        var isDarkMode = false

        // 是否显示了用户政策与隐私协议 Toast
        private var isShowUserPrivacyToast = false

        @SuppressLint("StaticFieldLeak")
        const val TAG = "HmsApplication"
        lateinit var appContext: Context
        var isPrivacyMode = false
            private set
        var isInForeground = false
            private set

//        private var currentActivity: WeakReference<Activity>? = null
    private var currentActivity: Activity? = null
        /**
         * 记录STR退出时间，如果再收到STR退出事件时，如果不超过10秒，不处理部分业务逻辑。
         * 默认为0
         */
        var STR_EXIT_TIMESTAMP = 0L;

        fun setCurrentActivity(activity: Activity?) {
            Log.i(TAG,"set current activity to $activity")
            currentActivity = activity
        }

        fun getCurrentActivity(): Activity? {

            if(currentActivity!=null){
                Log.i(TAG,"getCurrentActivity is $currentActivity")
//                return  currentActivity
                return WeakReference(currentActivity).get()
            }

//            else{
//                try {
//                    val manager =
//                        appContext.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
//                    val tasks = manager.getRunningTasks(1)
//                    var act =  tasks?.get(0)?.topActivity
//                    return act as Activity
//                }catch (ex: Exception){
//                    ex.printStackTrace()
//                }
            Log.i(TAG,"getCurrentActivity is null")
            return null
//            }
        }
//        @SuppressLint("StaticFieldLeak")
//        var currentActivity: Activity? = null
//            private set
        // 是否调用初始化接口成功
        var isInitSuccess: Boolean = false

        /**
         * 是否打开对话框
         */
        var isDialogShow = false

        fun isChangeTheme(): Boolean {
            val nightModeFlags: Int =
                appContext.resources.configuration.uiMode and
                        Configuration.UI_MODE_NIGHT_MASK
            val lasThemeModel = getLastThemeMode()
            Log.i(TAG, "lasThemeModel = $lasThemeModel nightModeFlags = $nightModeFlags")
            if (lasThemeModel != nightModeFlags) {
                storeLastThemeMode(nightModeFlags)
                return true
            } else {
                return false
            }
        }

        /**
         * 调用初始化接口
         * @param reqFrom 调用方
         * @param respCallback 成功失败回调
         */
        fun sendInitInfoReq(reqFrom: String = "HmsApplication", respCallback: ((isSuccess: Boolean) -> Unit)?) {
            (appContext as? HmsApplication)?.let { application ->
                application.applicationHelper.let { helper ->
                    GlobalScope.launch {
                        helper.sendInitInfoReq(reqFrom) { isSuccess ->
                            respCallback?.invoke(isSuccess)
                        }
                    }
                }
            }
        }

        /**
         * 私密模式是否可用
         * @param context
         * @return true 开启 false 关闭
         */
        fun isPrivacyModeEnabled(): Boolean {
            appContext?.let {
                val contentResolver: ContentResolver = appContext.contentResolver
                return try {
                    val privateMode: Int =
                        Settings.System.getInt(contentResolver, "private_mode_status")
                    return privateMode == 1
                } catch (e: Settings.SettingNotFoundException) {
                    false
                }
            }
            return false
        }

        fun showGlobalDialog(title: String, message: String, needToLogout: Boolean) {
            getCurrentActivity()?.let {
                showHuaweiAuthHMSDialog(
                    it,
                    R.layout.hms_dialog_huawei_auth,
                    message,
                    "知道了"
                )
                { isPositive ->
                    if (isPositive) {
                        if (needToLogout) {
                            doLogout()
                        }
                    }
                }
            }
        }

        /**
         * 退出登录，回到首页
         */
        private fun doLogout() {
            MMKVUtil.clearAllExceptSome()
            getCurrentActivity()?.let {
                if (it is MainActivity) {
                    var mainActivity = it
                    mainActivity.doResume()
                } else {
                    val mainIntent = Intent(it, MainActivity::class.java)
                    mainIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                    it.startActivity(mainIntent)
                    it.finish()
                    it.overridePendingTransition(
                        R.anim.activity_enter_slide_in_left,
                        R.anim.activity_enter_slide_out_right
                    )
                }
            }
            // 退出登录，更新桌面小卡片
            updateWidget()
        }

        /**
         * 触发桌面小卡片更新
         */
        private fun updateWidget() {
            // 使用 context 而不是 requireContext
            val context = HmsApplication.appContext  // 或者传递 context 参数
            // 获取 AppWidgetManager 实例
            val appWidgetManager = AppWidgetManager.getInstance(context)
            // 获取当前小部件的所有 AppWidgetId
            val appWidgetIds = appWidgetManager.getAppWidgetIds(
                ComponentName(context, HMSWidgetProvider::class.java)
            )
            var provider = HMSWidgetProvider()
            provider.updateHmsWidget(HmsApplication.appContext,appWidgetIds)
        }

        /**
         * 打开电话医生备案界面
         */
        fun openBindDoctorServiceAcInApp() {
//            getCurrentActivity()?.let {
//                val intent = Intent(it, BindPhoneCallDoctorActivity::class.java)
//                it.startActivity(intent)
//                it.overridePendingTransition(R.anim.activity_enter_dialog, R.anim.activity_stay)
//            }
        }

        fun registPrivacyModeListener() {
            val contentResolver: ContentResolver = appContext.contentResolver
            privacyModeObserver = PrivacyModeObserver(Handler())
            contentResolver.registerContentObserver(
                Settings.System.getUriFor("private_mode_status"),
                true,
                privacyModeObserver
            )
        }

        fun unregistPrivacyModeListener() {
            val contentResolver: ContentResolver = appContext.contentResolver
            privacyModeObserver?.let {
                contentResolver.unregisterContentObserver(it)
            }
        }

        /**
         * 打开登录界面
         */
        fun showLoginDialog() {
            // 打开华为授权登录界面
            getCurrentActivity()?.let {
                val intent = Intent(it, HuaweiOAuthActivity::class.java)
                it.startActivity(intent)
              //  it.overridePendingTransition(0,0)
                it.overridePendingTransition(R.anim.dialog_activity_enter_anim, R.anim.activity_stay)
            } ?: Log.i(TAG, "showLoginDialog: activity is null")
        }

        /**
         * 网络是否连接
         */
        fun isNetworkConn() : Boolean {
            return BaseContext.checkNetWork()
        }

        /**
         * 黑夜模式 statusBar 白色 对话框弹出时 statusBar 白色
         * 白天模式 statusBar 黑色 对话框弹出时 statusBar 白色
         */
        fun setStatusBarColor(isLight : Boolean) {
            getCurrentActivity()?.let {
                val window = it.window
                val windowInsetsControllerCompat = WindowCompat.getInsetsController(window,window.decorView)// ViewCompat.getWindowInsetsController(window.decorView)
                if (!isDarkModeEnabled(it)){
                    windowInsetsControllerCompat.isAppearanceLightStatusBars = isLight
                }
            } ?: Log.i(TAG, "setStatusBarColor: activity is null")
        }

        private var globalDialog: Dialog? = null
        /**
         * 显示华为授权相关对话框
         * @param layoutId 布局必须包含 hms_dialog_message TextView positiveButton Button
         * @param msg 显示消息
         * @param positiveBtnTitle 底部按钮文字
         */
        private fun showHuaweiAuthHMSDialog(
            context: Activity,
            @LayoutRes layoutId: Int,
            msg: String,
            positiveBtnTitle: String,
            btnClickCallback: (isPositive: Boolean) -> Unit
        ) {
            Log.i(TAG," --------globalDialog is $globalDialog")
            // 如果对话框已经显示，不再显示
            globalDialog?.let {
                if (it.isShowing && !context.isFinishing && !context.isDestroyed) {
                    try {
                        it.dismiss()
                        Log.i(TAG," --------isShowing is dismiss")
                    } catch (e: IllegalArgumentException){
                        Log.i(TAG," --------catch  $e")
                    }
                }
            }

            Log.i(TAG," --------isShowing is no return")
            // 创建新的对话框
            globalDialog = ImmersiveDialog(
                context,
                R.style.MyDialogStyle
            )
            Log.i(TAG," --------globalDialog = $globalDialog")
            var view: View = LayoutInflater.from(context).inflate(layoutId, null)
            val contentView = view.findViewById<ViewGroup>(R.id.dialog_content)
            var layout = view.findViewById<FrameLayout>(R.id.fl_container)
            layout.setOnClickListener {
                // 用户点击空白区域
            }

            Log.i(TAG," --------设置对话框标题 = $globalDialog")
            // 设置对话框标题
            val titleTextView: TextView = view.findViewById(R.id.hms_dialog_message)
            titleTextView.text = msg

            // 设置确认按钮
            val positiveButton: Button = view.findViewById(R.id.positiveButton)
            positiveButton.text = positiveBtnTitle
            positiveButton.setOnClickListener {
                btnClickCallback(true)
                globalDialog?.dismiss()
            }

            Log.i(TAG," --------setContentView = $view")
            globalDialog?.setContentView(view)
            globalDialog?.setOnShowListener {
                view.setOnTouchListener { v: View?, event: MotionEvent ->
                    if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                        globalDialog?.dismiss()
                    }
                    false
                }
            }
            // 使对话框全屏
            // globalDialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)

            globalDialog?.window?.setLayout(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            Log.i(TAG," --------globalDialog?.window = ${globalDialog?.window}")

            //globalDialog?.show()

            Log.i(TAG," --------context = $context")
            Log.i(TAG," --------context = ${context is Activity}  ")
            if (context is Activity){

                Log.i(TAG," --------isFinishing = ${context.isFinishing}  ")
                Log.i(TAG," --------isDestroyed = ${context.isDestroyed}  ")
            }
            if(context is Activity && !context.isFinishing && !context.isDestroyed){
                Log.i(TAG," --------show!! = $globalDialog  ")
                globalDialog?.show()
            } else {
                Log.i("HMSDialogUtils", "showHMSDialog context is not activity")
            }

        }

        /**
         * 是否同意用户政策与隐私协议
         */
        public fun isAgreePrivacy() : Boolean{
            return MMKVUtil.getPrivacyPolicy()
        }

        /**
         * 设置是否同意用户政策与隐私协议
         */
        public fun storeShowUserPrivacyToast(showUserPrivacyToast: Boolean){
            isShowUserPrivacyToast = showUserPrivacyToast
        }

        /**
         * 获取是否提示过用户协议与隐私政策
         * true 提示过
         * false 没有提示
         */
        public fun getIsShowUserPrivacyToast(): Boolean {
            return isShowUserPrivacyToast
        }
    }




}

