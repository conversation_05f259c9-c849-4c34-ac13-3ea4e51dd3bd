package com.healthlink.hms.business.doctorcall.view

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.widget.RelativeLayout
import android.widget.TextView
import com.healthlink.hms.R

internal class DoctorCallDialButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {

    var numberTextView: TextView
        private set
    private var letterTextView: TextView

    init {
        LayoutInflater.from(context).inflate(R.layout.view_dial_button, this, true)
        numberTextView = findViewById(R.id.tv_number)
        letterTextView = findViewById(R.id.tv_letter)

        val attributes = context.obtainStyledAttributes(attrs, R.styleable.Doctor<PERSON>allDialButton)
        val number = attributes.getString(R.styleable.DoctorCallDialButton_dc_number)
        val letter = attributes.getString(R.styleable.DoctorCallDialButton_dc_letter)
        attributes.recycle()

        numberTextView.text = number
        if (TextUtils.isEmpty(letter)){
            letterTextView.visibility = GONE
        }else{
            letterTextView.text = letter
        }
       val param = numberTextView.layoutParams as MarginLayoutParams
        if(numberTextView.text == "*"){
            param.topMargin += 17
        }

    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> {
                alpha = 0.7f
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                alpha = 1.0f
            }
        }
        return super.onTouchEvent(event)
    }
}