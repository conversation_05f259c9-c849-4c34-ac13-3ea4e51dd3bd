package com.healthlink.hms.business.doctorcall.view

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.GridLayout
import android.widget.HorizontalScrollView
import android.widget.LinearLayout
import android.widget.TextView
import com.healthlink.hms.R

internal class DoctorCallDialPan @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private var inputListener: ((Int) -> Unit)? = null
    private var inputTextView: TextView

    private val minTextSize = 32f
    private val maxTextSize = 44f

    private val scrollView: HorizontalScrollView

    init {
        LayoutInflater.from(context).inflate(R.layout.view_dial_pad, this, true)
        scrollView = findViewById<HorizontalScrollView>(R.id.scroll_view)
        inputTextView = findViewById(R.id.tv_input)

        val gridLayout = findViewById<GridLayout>(R.id.grid_layout)

        for (i in 0 until gridLayout.childCount) {
            val child = gridLayout.getChildAt(i)
            if (child is DoctorCallDialButton) {
                child.setOnClickListener { button ->
                    val text = (button as DoctorCallDialButton).numberTextView.text
                    val number = input2Key(text.toString())
                    if (number != null) {
                        inputListener?.invoke(number)
                    }
                }
            }
        }

        inputTextView.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                adjustTextSize()
            }

            override fun afterTextChanged(s: Editable?) {}
        })
    }

    fun setInputListener(listener: (Int) -> Unit) {
        inputListener = listener
    }

    fun setText(string: String) {
        inputTextView.text = string
        scrollView.post { scrollView.fullScroll(HorizontalScrollView.FOCUS_RIGHT) }
    }

    private fun adjustTextSize() {
        val textLength = inputTextView.text.length
        val newSize = maxTextSize - (textLength * 0.5f)
        inputTextView.textSize = if (newSize < minTextSize) minTextSize else newSize
    }

    /**
     *  int：二次拨号键 键值：
     *  1(1),2(2),3(3),4(4),5(5),6(6),
     *  7(7),8(8),9(9),10(*),0(0),11(#),
     *  12(A),13(B),14(C),15(D)
     */
    private fun input2Key(input: String): Int? {
        return when (input) {
            "1" -> 1
            "2" -> 2
            "3" -> 3
            "4" -> 4
            "5" -> 5
            "6" -> 6
            "7" -> 7
            "8" -> 8
            "9" -> 9
            "*" -> 10
            "0" -> 0
            "#" -> 11
            "A" -> 12
            "B" -> 13
            "C" -> 14
            "D" -> 15
            else -> null
        }
    }
}