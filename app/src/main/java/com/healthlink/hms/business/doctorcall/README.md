# 健康医生网络电话功能

## 综述
健康医生网络电话功能是在首页拨打医生电话的功能，用户可以通过网络电话与医生进行沟通。

## 结构
- `DoctorCallManager` 管理类，负责网络电话的初始化、拨打电话、挂断电话等操作。同时也应该是唯一对外暴露的类。
- `DoctorCallModel` 健康医生网络电话功能中所用到的数据模型，每次的通话会话都会生成一个新的实例以防止数据混乱
- `DoctorCallService` 健康医生通话功能中用来采集车机录音用以发送自定义音频给 SDK 的服务
- `DoctorCallUtils` 健康医生网络电话功能中用到的工具方法
- view
  - `DoctorCallView` 健康医生网络电话功能中拨打电话期间显示在 App 内的悬浮窗，会用 FloatingX 来管理其移动和跨页面显示
  - `DoctorCallDialPan` 拨打电话时的拨号盘
  - `DoctorCallDialButton` 拨打电话时的拨号按钮

## 用法
1. 初始化网络电话功能
```kotlin
DoctorCallManager.init(context, needShowError, onSuccess)
```
2. 拨打电话
```kotlin
DoctorCallManager.call(context)
```
拨打电话，在方法中会判断 SDK 是否初始化，必要权限是否授予，通话焦点是否被占有等情况，如果不满足条件会弹出相应的提示。
在检查完毕后会正式调用 SDK 的拨打电话方法，这里需要使用 SDK 自采集音频的呼出方法，并且弹出通话悬浮窗。

3. 主动挂断电话
```kotlin
DoctorCallManager.hangUp()
```
在收到 SDK 通话结束的回调时（对面挂断），内部会自己调用该方法，无需手动调用。
并且因为挂断操作其实也是内部完成，这个方法暂时没有外部调用的时机。

## 关于悬浮窗
悬浮窗的界面是由 `DoctorCallView` 实现，而其移动，跨页面显示等功能是由第三方库 `FloatingX` 实现的。
FloatingX 具体介绍参见：https://github.com/Petterpx/FloatingX

## 输出日志
在 debug 模式下才会输出日志。
在 Log 中筛选 Tag 为 `DoctorCall` 可以查看本功能相关的日志输出。
