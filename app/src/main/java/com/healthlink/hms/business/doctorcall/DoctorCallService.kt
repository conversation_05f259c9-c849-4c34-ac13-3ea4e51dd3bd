package com.healthlink.hms.business.doctorcall

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioRecord
import android.os.Binder
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.ActivityCompat
import com.cincc.reduce.CallCtrl
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.business.doctorcall.DoctorCallManager.TAG
import com.healthlink.hms.business.doctorcall.DoctorCallManager.log
import com.healthlink.hms.utils.ToastUtil
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException

/**
 * 健康医生通话功能中用来采集车机录音用以发送自定义音频给 SDK 的服务
 * 因为通话需要在 App 处于后台时候也能正常通话，所以需要一个独立的服务来处理录音
 *
 * WARNING： 确保在绑定 service 并启动录音前的流程中已经申请了录音权限（RECORD_AUDIO），
 */
class DoctorCallService : Service() {

    val mainHandler = Handler(Looper.getMainLooper())

    private val audioManager
        get() = HmsApplication.appContext.getSystemService(Service.AUDIO_SERVICE) as? AudioManager

    private var audioFocusRequest: AudioFocusRequest? = null

    private val recordThread by lazy {
        object : Thread() {

            override fun run() {
                DoctorCallManager.log("[record-service] start to record audio ")
                // 申请焦点
//                requestAudioFocusForUpStream(this@DoctorCallService)
                // 最小读取缓存 - 7680
                var bufferSize = AudioRecord.getMinBufferSize(DoctorCallManager.AUDIO_SAMPLE_RATE,
                    DoctorCallManager.DEFAULT_CHANNEL_COUNT, AudioFormat.ENCODING_PCM_16BIT);
                bufferSize = 800
                // 权限判断
                val audioRecord = if (ActivityCompat.checkSelfPermission(
                        this@DoctorCallService,
                        Manifest.permission.RECORD_AUDIO
                    ) == PackageManager.PERMISSION_GRANTED
                ) {
                    AudioRecord(
                        DoctorCallManager.UPSTREAM_MIC_SOURCE,  // 2019   2014/2015 --> MediaRecorder.AudioSource.MIC
                        DoctorCallManager.AUDIO_SAMPLE_RATE,  // 8000
                        DoctorCallManager.DEFAULT_CHANNEL_COUNT,   // 1
                        AudioFormat.ENCODING_PCM_16BIT,   //  2
                        bufferSize   // 7680
                    )
                }else {
                    DoctorCallManager.log("[record-service] Record ERROR: 未获取录音权限, 自定义采集的录音未其启动")
                    return
                }
                // 如果初始化失败 TODO 应该终止弹窗，通知前台挂断
                if (audioRecord.state != AudioRecord.STATE_INITIALIZED) {
                    log("[record-service] AudioRecord initialization failed")
                    // 挂断以释放焦点
                    if (Looper.myLooper() == Looper.getMainLooper()) {
                        log("[record-service] invoke hangup from main thread")
                        DoctorCallManager.hangup()
                    } else {
                        mainHandler.post {
                            log("[record-service] invoke hangup from other thread")
                            DoctorCallManager.hangup()
                        }
                    }
                    return
                }

                // 开始录音
//                audioRecord.startRecording()
                val outputStream: FileOutputStream?
                val buffer = ByteArray(bufferSize)
                try {
                    val timeMillis = System.currentTimeMillis()
                    // 录音文件
                    val pmcFile = File(getExternalFilesDir(null), "record_$timeMillis.pcm")
                    DoctorCallManager.log("[record-service] Record LOG: 开始录制，录音文件路径: ${pmcFile.absolutePath}")
                    outputStream = FileOutputStream(pmcFile)
                    // 开始录音
                    audioRecord.startRecording()
                    var bytTotal: Long = 0
                    while (!isInterrupted) {
                        val timestamp = System.currentTimeMillis()
                        val result = audioRecord.read(buffer, 0, buffer!!.size)
//                        log("[record-service] : 音频数据大小: $result")
                        if (result > 0) {
                            val tempBuffers = ByteArray(result)
                                System.arraycopy(buffer, 0, tempBuffers, 0, result)
                            CallCtrl.getInstance()
                                .pushExternalAudioFrame(buffer, timestamp)
                            // 本地缓存
                            outputStream.write(buffer, 0, result)
                            outputStream.flush()

                            // 记录最近发送时间
                            bytTotal += result.toLong()
                        } else {
                            logRecordError(result)
                        }

                    }
                    DoctorCallManager.log("[record-service] Record LOG: 录制结束")
                    audioRecord.stop()
                    audioRecord.release()
                    outputStream.close()


                    releaseAudioFocus()

                } catch (e: FileNotFoundException) {
                    Log.i(DoctorCallManager.TAG, "[record-service] Record ERROR: ${e.message}")
                    throw RuntimeException(e)
                } catch (e: IOException) {
                    Log.i(DoctorCallManager.TAG, "[record-service] Record ERROR: ${e.message}")
                    throw RuntimeException(e)
                }
            }


            private fun logRecordError(error: Int) {
                var message = ""
                when (error) {
                    AudioRecord.ERROR -> message = "generic operation failure"
                    AudioRecord.ERROR_BAD_VALUE -> message =
                        "failure due to the use of an invalid value"

                    AudioRecord.ERROR_DEAD_OBJECT -> message =
                        "object is no longer valid and needs to be recreated"

                    AudioRecord.ERROR_INVALID_OPERATION -> message =
                        "failure due to the improper use of method"
                }
                Log.i(DoctorCallManager.TAG, "Record ERROR: $message")
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        startForeground()
    }

    override fun onBind(intent: Intent): IBinder {
        Log.i(TAG,"DoctorCallService on bind")
        return object: RecordBinder() {
            override val service: DoctorCallService
                get() = this@DoctorCallService
        }
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Log.i(TAG,"DoctorCallService onUnbind")
        try {
            // 解绑服务时优先释放麦克风焦点
            releaseAudioFocus()
        }catch (ex: Exception){
            Log.i(TAG,"error on DoctorCallService onUnbind , ${ex.message}")
        }
        return super.onUnbind(intent)
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.i(TAG,"DoctorCallService on destroy")
        try {
            stopForeground(STOP_FOREGROUND_REMOVE)
            recordThread.interrupt()
            DoctorCallManager.recordService = null // 清除静态引用
        }catch (ex: Exception){
            Log.i(TAG,"error on service destroy , ${ex.message}")
        }
    }

    fun startRecording() {
        if(recordThread.state == Thread.State.NEW){
            recordThread.start()
        }
    }

    private fun startForeground() {
        val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager

        val channelId = "doctor_call"
        val channel = NotificationChannel(
            channelId, "电话医生",
            NotificationManager.IMPORTANCE_DEFAULT
        )
        //取消通知信息
        channel.setSound(null, null)
        manager.createNotificationChannel(channel)

        val notification: Notification = Notification.Builder(this).setChannelId(channelId)
            .setContentTitle("电话医生通话中")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setAutoCancel(true)
            .build()

        startForeground(1, notification)
    }

    /**
     * 上行音频焦点监听器，监听到音频焦点变化时，挂断电话
     */
    private var upstreamAudioFocusChangeListener =
        AudioManager.OnAudioFocusChangeListener { focusChange: Int ->
            DoctorCallManager.log("[record-service] focusChanged: $focusChange")
            when (focusChange) {
                AudioManager.AUDIOFOCUS_LOSS ->{
                    DoctorCallManager.log("[record-service] 永久失去上行焦点")
                    CallCtrl.getInstance().hangup()
                }
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK->{
                    DoctorCallManager.log("[record-service] 暂时失去上行焦点")
                    CallCtrl.getInstance().hangup()
                }
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                    DoctorCallManager.log("[record-service] 失去上行焦点")
                    CallCtrl.getInstance().hangup()
                    // 主线程调用
//                    Handler(Looper.getMainLooper()).post({
//                        ToastUtil.makeText(HmsApplication.appContext,"电话医生焦点",300).show()
//                    })
                }
                AudioManager.AUDIOFOCUS_GAIN ->{
                    DoctorCallManager.log("[record-service] 永久获得上行焦点")
                }
            }
        }

    private fun requestAudioFocusForUpStream(context: Context) {
//        audioManager?.requestAudioFocus()
        var builder = AudioAttributes.Builder()
        builder.setUsage(105)
        var aa = builder.build()

        audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
            .setAudioAttributes(aa)
            .setAcceptsDelayedFocusGain(true)
            .setOnAudioFocusChangeListener ( upstreamAudioFocusChangeListener)
            .build()
        // 会打印语音进程的打印。
        log("request audioFocusRequest $audioFocusRequest")
        if(audioFocusRequest!=null) {
            // 申请上行(麦克风）焦点
            val result = audioManager?.requestAudioFocus(audioFocusRequest!!)
            // 获取焦点结果处理
            if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                // 音频焦点请求成功，可以开始播放音频
                DoctorCallManager.log("[record-service] 获取上行(麦克风）焦点成功")
            } else {
                DoctorCallManager.log("[record-service] 获取上行(麦克风）焦点失败，退出")
//                DoctorCallManager.hangup()
                if (Looper.myLooper() == Looper.getMainLooper()) {
                    log("[record-service] requestAudioFocusForUpStream invoke hangup from main thread")
//                    ToastUtil.makeText(HmsApplication.appContext,"获取上行(麦克风）焦点失败",300).show()
                    DoctorCallManager.hangup()
                } else {
                    mainHandler.post {
                        log("[record-service] requestAudioFocusForUpStream invoke hangup from other thread")
                        DoctorCallManager.hangup()
                    }
                }
            }
        }else{
            // 构建焦点请求失败
            DoctorCallManager.log("[record-service] 初始化上行(麦克风）焦点失败")
        }
    }

    private fun releaseAudioFocus() {
        try {
            if (audioFocusRequest != null) {
                log("release audioFocusRequest $audioFocusRequest")
                val result = audioManager?.abandonAudioFocusRequest(audioFocusRequest!!)
                if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                    // 成功释放音频焦点
                    DoctorCallManager.log("[record-service] 释放上行(麦克风)焦点成功")
                } else {
                    // 释放焦点失败
                    DoctorCallManager.log("[record-service] 释放上行(麦克风)焦点失败")
                }
            }
        }catch(ex:Exception){
            Log.i(TAG,"error on DoctorCallService releaseAudioFocus , ${ex.message}")
        }
    }

}

abstract class RecordBinder : Binder() {
    abstract val service: DoctorCallService
}