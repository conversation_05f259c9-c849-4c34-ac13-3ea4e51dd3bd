package com.healthlink.hms.business.doctorcall

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.os.Build
import androidx.core.app.ActivityCompat

/**
 * 通过尝试录音的方式判断麦克风是否可用
 */
internal fun isMicrophoneAvailable(context: Context):<PERSON>olean{
    var available = true
    if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.P) {
        var audioRecord: AudioRecord? = null

        try {
            val sampleRate = 44100 // 大部分设备支持的采样率
            val audioSource = MediaRecorder.AudioSource.MIC
            val channelConfig = AudioFormat.CHANNEL_IN_MONO
            val audioFormat = AudioFormat.ENCODING_PCM_16BIT
            val minBufferSize =
                AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat)

            if (minBufferSize == AudioRecord.ERROR_BAD_VALUE) {
                return false
            }

            if (ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.RECORD_AUDIO
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                return false
            }
            audioRecord = AudioRecord(
                audioSource,
                sampleRate,
                channelConfig,
                audioFormat,
                minBufferSize
            )

            if (audioRecord.state != AudioRecord.STATE_INITIALIZED) {
                available = false
            } else {
                audioRecord.startRecording()
                if (audioRecord.recordingState != AudioRecord.RECORDSTATE_RECORDING) {
                    available = false
                }
                audioRecord.stop()
            }
        } catch (e: Exception) {
            available = false
        } finally {
            audioRecord?.release()
        }
    }

    return available
}