package com.healthlink.hms.business.doctorcall

import androidx.lifecycle.MutableLiveData
import com.kunminx.architecture.domain.message.MutableResult

/**
 * 健康医生网络电话功能中所用到的数据模型
 * 1. 保证界面的数据和逻辑分离，方便界面的销毁重建
 * 2. 需要保证 `DoctorCallView` 重建需要的所有数据都放在这里
 * 3. 每次的通话都会生成一个新的实例以防止数据混乱
 */
class DoctorCallModel {

    /**
     * 是否静音，控制网络电话悬浮窗中的静音按钮的 UI 状态
     */
    val isMuted: MutableLiveData<Boolean> by lazy {
        MutableLiveData(false)
    }

    /**
     * 是否显示键盘，控制网络电话悬浮窗中的键盘的 UI 状态
     */
    val showKeyboard: MutableLiveData<Boolean> by lazy {
        MutableLiveData(false).apply {
            observeForever {
                input.postValue("")
            }
        }
    }

    /**
     * 通话开始时间，网络电话悬浮窗中显示通话时长依赖这个计算
     */
    val startTime: MutableLiveData<Long> by lazy {
        MutableLiveData(0L)
    }

    /**
     * 输入的历史，网络电话悬浮窗中的拨号盘输入内容的记录
     */
    val input: MutableLiveData<String> by lazy {
        MutableLiveData("")
    }

    /**
     * 界面状态，控制网络电话悬浮窗中的 UI 状态
     * @see DoctorCallViewState
     */
    val viewState: MutableLiveData<DoctorCallViewState> by lazy {
        MutableLiveData(DoctorCallViewState.Calling).apply {
            observeForever {
                when (it) {
                    DoctorCallViewState.Talking -> {
                        startTime.postValue(System.currentTimeMillis())
                    }

                    else -> {
                        startTime.postValue(0L)
                        showKeyboard.postValue(false)
                    }
                }
            }
        }
    }

    /**
     * 网络电话一次通话周期内的状态
     * @see DoctorCallState
     */
    val doctorCallState: MutableResult<DoctorCallState> by lazy {
        MutableResult(DoctorCallState.Idle).apply {
            observeForever {
                when (it) {
                    DoctorCallState.Calling -> {
                        viewState.postValue(DoctorCallViewState.Calling)
                    }

                    DoctorCallState.Talking -> {
                        viewState.postValue(DoctorCallViewState.Talking)
                    }

                    DoctorCallState.Hanging -> {
                        viewState.postValue(DoctorCallViewState.Hanging)
                    }

                    DoctorCallState.Idle -> {
                    }

                    DoctorCallState.Error -> {

                    }

                    null -> {

                    }
                }
            }
        }
    }

    /**
     * 界面状态，控制网络电话悬浮窗中的 UI 状态
     */
    enum class DoctorCallViewState {
        Calling,    // 呼叫中
        Talking,    // 通话中
        Hanging     // 挂断中
    }

    /**
     * 网络电话一次通话周期内的状态
     */
    enum class DoctorCallState {
        Idle,       // 空闲
        Calling,    // 呼叫中
        Talking,    // 通话中
        Hanging,    // 挂断中
        Error       // 出错
    }
}