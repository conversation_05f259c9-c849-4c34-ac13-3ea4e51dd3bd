package com.healthlink.hms.viewmodels

/**
 * Created by imaginedays on 2024/6/25
 * MainViewModel 全局共享数据
 */
import android.os.Build.VERSION_CODES
import android.os.Looper
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.BaseResponseCallback
import com.healthlink.hms.mvvm.repository.MainRepository
import com.healthlink.hms.core.model.dto.BloodPressureResponseDTO
import com.healthlink.hms.core.model.dto.ChartDataDTO
import com.healthlink.hms.core.model.dto.HealthBloodpressureSummaryDTO
import com.healthlink.hms.core.model.dto.HealthHistoryDataStatusDTO
import com.healthlink.hms.core.model.dto.HealthReportDTO
import com.healthlink.hms.core.model.dto.HealthTipsDTO
import com.healthlink.hms.core.model.dto.SleepDayResponseDTO
import com.healthlink.hms.core.model.dto.charts.HealthSleepSummeryDTO
import com.healthlink.hms.core.model.dto.SleepMonthResponseDTO
import com.healthlink.hms.core.model.dto.SleepWeekResponseDTO
import com.healthlink.hms.core.model.dto.SleepYearResponseDTO
import com.healthlink.hms.core.model.dto.SpO2ItemResponseDTO
import com.healthlink.hms.core.model.dto.charts.HealthSummeryDTO
import com.healthlink.hms.core.model.dto.charts.heartrate.HeartRateStatDTO
import com.healthlink.hms.core.model.dto.charts.pressure.PressureDetailRespDTO
import com.healthlink.hms.core.model.dto.charts.pressure.PressureSummaryDTO
import com.healthlink.hms.core.model.dto.HealthSpO2SummaryDTO
import com.healthlink.hms.core.model.dto.HealthSummarizeDTO
import com.healthlink.hms.core.model.dto.HealthTempSummaryDTO
import com.healthlink.hms.core.model.dto.LiveHealthStatusDTO
import com.healthlink.hms.core.model.dto.TempItemDTO
import com.healthlink.hms.core.model.dto.TempItemResponseDTO
import com.healthlink.hms.core.model.dto.UpgradeVersionDTO
import com.healthlink.hms.core.model.dto.UserInfoDTO
import com.healthlink.hms.core.model.dto.VehicleServiceModeDTO
import com.healthlink.hms.core.model.dto.init.CallDoctorPhoneDTO
import com.healthlink.hms.core.model.dto.init.DoctorServiceDTO
import com.healthlink.hms.core.model.dto.init.VehicleCapacityDTO
import com.healthlink.hms.core.model.dto.init.VehicleCapacityModeFunsDTO
import com.healthlink.hms.core.network.model.HealthInfoDTO
import com.healthlink.hms.core.network.model.HealthInfoRequestParam
import com.kunminx.architecture.domain.message.MutableResult

class MainViewModel : ViewModel() {
    // 最新健康数据
    val homeNewHealthInfo = MutableLiveData<BaseResponse<HealthInfoDTO>>()

    // 图表数据
    val chartDetailInfos = MutableLiveData<BaseResponse<ChartDataDTO>>()

    // 首页健康卡片
    private val liveHealthStatus: MutableResult<BaseResponse<LiveHealthStatusDTO>> by lazy {
        MutableResult<BaseResponse<LiveHealthStatusDTO>>()
    }

    // 获取健康tips
    val healthTipsInfo = MutableLiveData<BaseResponse<HealthTipsDTO>>()

    // 获取健康tips 分页数据
    private val healthTipsInfoData: MutableResult<BaseResponse<HealthTipsDTO>> by lazy {
        MutableResult<BaseResponse<HealthTipsDTO>>()
    }

    // 车机功能服务列表
    private val vehicleServiceList: MutableLiveData<BaseResponse<VehicleCapacityDTO>> by lazy {
        MutableLiveData<BaseResponse<VehicleCapacityDTO>>()
    }

    // 电话医生、紧急救援服务电话号码
    private val doctorService: MutableLiveData<BaseResponse<DoctorServiceDTO>> by lazy {
        MutableLiveData<BaseResponse<DoctorServiceDTO>>()
    }

    // 电话医生 - 车辆备案信息查询
    private val doctorServicePhoneData: MutableLiveData<BaseResponse<CallDoctorPhoneDTO>> by lazy {
        MutableLiveData<BaseResponse<CallDoctorPhoneDTO>>()
    }

    // 电话医生 - 车辆备案更新手机号
    private val doctorServiceUpdateData: MutableLiveData<BaseResponse<Boolean>> by lazy {
        MutableLiveData<BaseResponse<Boolean>>()
    }

    // 通知MainActivity刷新UI
    val notifyRefreshMainUI: MutableLiveData<Boolean> by lazy {
        MutableLiveData<Boolean>()
    }

    /**
     * 记录刷新状态userRefreshFlag true 为刷新 false 为不刷新
     */
    val userRefreshFlag = MutableLiveData<Boolean>()
    val isAutoRefresh = MutableLiveData<Boolean>()

    // 统计异常数据
    val exceptionCountData = MutableLiveData<Int>()

    // 临时使用 可以优化成为一个事件对象定义 msg 消息体 action/data/type等
    val notificationData = MutableLiveData<Boolean>()

    // 是否返回首页
    private val backToHomeData = MutableLiveData<Boolean>()

    // 卡片数据是否被授权
    private val cardAuthStatusData: MutableLiveData<Boolean> by lazy {
        MutableLiveData<Boolean>()
    }

    private val cardDetailChartData: MutableResult<BaseResponse<HeartRateStatDTO>> by lazy {
        MutableResult<BaseResponse<HeartRateStatDTO>>()
    }

    //睡眠日卡片数据
//    var sleepDetailChartData = MutableLiveData<BaseResponse<SleepDayResponseDTO>>()
    private val sleepDetailChartData: MutableResult<BaseResponse<SleepDayResponseDTO>> by lazy {
        MutableResult<BaseResponse<SleepDayResponseDTO>>()
    }

    //睡眠周卡片数据
    private val sleepWeekChartData: MutableResult<BaseResponse<SleepWeekResponseDTO>> by lazy {
        MutableResult<BaseResponse<SleepWeekResponseDTO>>()
    }

    //睡眠月卡片数据
    private val sleepMonthChartData: MutableResult<BaseResponse<SleepMonthResponseDTO>> by lazy {
        MutableResult<BaseResponse<SleepMonthResponseDTO>>()
    }

    //睡眠年卡片数据
    private val sleepYearChartData: MutableResult<BaseResponse<SleepYearResponseDTO>> by lazy {
        MutableResult<BaseResponse<SleepYearResponseDTO>>()
    }

    // 压力详情页图表数据
    private val pressureChartData: MutableResult<BaseResponse<PressureDetailRespDTO>> by lazy {
        MutableResult<BaseResponse<PressureDetailRespDTO>>()
    }

    //     压力详情页健康建议与风险总结
    private val pressureSummaryData: MutableResult<BaseResponse<PressureSummaryDTO>> by lazy {
        MutableResult<BaseResponse<PressureSummaryDTO>>()
    }

    // 健康建议与风险总结
    val healthSummeryData = MutableResult<BaseResponse<HealthSummeryDTO>>()

    // 睡眠健康建议与风险总结
    val healthSleepSummeryData = MutableResult<BaseResponse<HealthSleepSummeryDTO>>()
    val healthWeekSleepSummeryData = MutableResult<BaseResponse<HealthSleepSummeryDTO>>()
    val healthMonthSleepSummeryData = MutableResult<BaseResponse<HealthSleepSummeryDTO>>()
    val healthYearSleepSummeryData = MutableResult<BaseResponse<HealthSleepSummeryDTO>>()

    //血氧卡片
    var spo2DayChartData = MutableLiveData<BaseResponse<SpO2ItemResponseDTO>>()
    var spo2WeekChartData = MutableLiveData<BaseResponse<SpO2ItemResponseDTO>>()
    var spo2MonthChartData = MutableLiveData<BaseResponse<SpO2ItemResponseDTO>>()
    var spo2YearChartData = MutableLiveData<BaseResponse<SpO2ItemResponseDTO>>()

    //血氧建议风险
    var healthSpO2SummeryData = MutableLiveData<BaseResponse<HealthSpO2SummaryDTO>>()

    //体温卡片
    var tempDayChartData = MutableLiveData<BaseResponse<TempItemResponseDTO>>()
    var tempWeekChartData = MutableLiveData<BaseResponse<TempItemResponseDTO>>()
    var tempMonthChartData = MutableLiveData<BaseResponse<TempItemResponseDTO>>()
    var tempYearChartData = MutableLiveData<BaseResponse<TempItemResponseDTO>>()

    //体温建议风险
    var tempSpO2SummeryData = MutableLiveData<BaseResponse<HealthTempSummaryDTO>>()

    // 数据使用说明
    private val dataUsageData: MutableLiveData<BaseResponse<String>> by lazy {
        MutableLiveData<BaseResponse<String>>()
    }

    //注销
    var deleteuserResult = MutableLiveData<BaseResponse<Boolean>>()

    //关联账户解绑
    var unbindAccountResult = MutableLiveData<BaseResponse<Boolean>>()

    //获取用户信息
    var userInfoData = MutableLiveData<BaseResponse<UserInfoDTO>>()

    //保存用户信息
    var saveUserInfoResult = MutableLiveData<BaseResponse<Boolean>>()

    //健康报告
    var reportDayChartData = MutableLiveData<BaseResponse<HealthReportDTO>>()
    var reportWeekChartData = MutableLiveData<BaseResponse<HealthReportDTO>>()
    var reportMonthChartData = MutableLiveData<BaseResponse<HealthReportDTO>>()
    var reportYearChartData = MutableLiveData<BaseResponse<HealthReportDTO>>()

    //健康总结
    var healthSummarizeData = MutableLiveData<BaseResponse<HealthSummarizeDTO>>()

    //血压
    var bloodPressureDayChartData = MutableResult<BaseResponse<BloodPressureResponseDTO>>()
    var bloodPressureWeekChartData = MutableResult<BaseResponse<BloodPressureResponseDTO>>()
    var bloodPressureMonthChartData = MutableResult<BaseResponse<BloodPressureResponseDTO>>()
    var bloodPressureYearChartData = MutableResult<BaseResponse<BloodPressureResponseDTO>>()

    //血压建议
    var healthBloodPressureSummary = MutableResult<BaseResponse<HealthBloodpressureSummaryDTO>>()

    val healthHistoryData = MutableResult<BaseResponse<HealthHistoryDataStatusDTO>>()

    var upgradeVersion = MutableResult<BaseResponse<UpgradeVersionDTO>>()
    //region 网络请求
    /**
     * 首页健康卡片
     */
    fun sendLiveHealthStatusReq(parameters: Map<*, *>) {
        MainRepository().getLiveHealthStatus(
            parameters,
            object : BaseResponseCallback<LiveHealthStatusDTO> {
                override fun onSuccess(response: BaseResponse<LiveHealthStatusDTO>) {
                    liveHealthStatus.value = response
                }

                override fun onFailed(response: BaseResponse<LiveHealthStatusDTO>) {
                    liveHealthStatus.value = response
                }
            })
    }

    fun getLiveHealthStatusLiveData(): MutableResult<BaseResponse<LiveHealthStatusDTO>> {
        return liveHealthStatus
    }


    /**
     * 获取健康tips
     */
    @Deprecated(
        message = "使用带分页的getHealthTipsWithVin方法",
        replaceWith = ReplaceWith("getHealthTipsWithVin(vin:String)"),
        level = DeprecationLevel.WARNING
    )
    fun getHealthTips() {
        MainRepository().getHealthTips(object : BaseResponseCallback<HealthTipsDTO> {
            override fun onSuccess(response: BaseResponse<HealthTipsDTO>) {
                healthTipsInfo.value = response
            }

            override fun onFailed(response: BaseResponse<HealthTipsDTO>) {
                healthTipsInfo.value = response
            }
        })
    }

    /**
     * 获取健康tips 分页数据
     */
    fun getHealthTipsWithVin(vin: String) {
        MainRepository().getHealthTipsWithVin(vin, object : BaseResponseCallback<HealthTipsDTO> {
            override fun onSuccess(response: BaseResponse<HealthTipsDTO>) {
                healthTipsInfoData.value = response
            }

            override fun onFailed(response: BaseResponse<HealthTipsDTO>) {
                healthTipsInfoData.value = response
            }
        })
    }

    /**
     * 获取健康tips 分页数据 livedata
     */
    fun getHealthTipsInfoLiveData(): MutableResult<BaseResponse<HealthTipsDTO>> {
        return healthTipsInfoData
    }


    //region 首页车机功能服务列表
    /**
     * 车机功能服务请求
     */
    fun sendVehicleServiceListReq(map: Map<*, *>) {
        MainRepository().getVehicleServiceList(
            map,
            object : BaseResponseCallback<VehicleCapacityDTO> {
                override fun onSuccess(response: BaseResponse<VehicleCapacityDTO>) {
                    vehicleServiceList.value = response
                }

                override fun onFailed(response: BaseResponse<VehicleCapacityDTO>) {
                    vehicleServiceList.value = response
                }
            })
    }

    /**
     * 获取livedata
     */
    fun getVehicleServiceListLiveData(): LiveData<BaseResponse<VehicleCapacityDTO>> {
        return vehicleServiceList
    }
    //endregion

    //region 首页电话医生、紧急救援服务

    fun sendDoctorServiceReq() {
        MainRepository().getDoctorService(object : BaseResponseCallback<DoctorServiceDTO> {
            override fun onSuccess(response: BaseResponse<DoctorServiceDTO>) {
                doctorService.value = response
            }

            override fun onFailed(response: BaseResponse<DoctorServiceDTO>) {
                doctorService.value = response
            }
        })
    }

    /**
     * 获取livedata
     */
    fun getDoctorServiceLiveData(): LiveData<BaseResponse<DoctorServiceDTO>> {
        return doctorService
    }
    //endregion

    /**
     * 获取图表数据
     * @param requestParam
     */
    fun getChartDataInfos(requestParam: HealthInfoRequestParam) {
        MainRepository().getNewHealthInfoDetail(
            requestParam,
            object : BaseResponseCallback<ChartDataDTO> {
                override fun onSuccess(response: BaseResponse<ChartDataDTO>) {
                    chartDetailInfos.value = response
                }

                override fun onFailed(response: BaseResponse<ChartDataDTO>) {
                    chartDetailInfos.value = response
                }
            })
    }

    /**
     * 获取首页最新数据
     * @param requestParam
     */
    fun getHomeNewHealthInfo(requestParam: HealthInfoRequestParam) {
        MainRepository().getHomeNewHealthInfo(
            requestParam,
            object : BaseResponseCallback<HealthInfoDTO> {
                override fun onSuccess(response: BaseResponse<HealthInfoDTO>) {
                    homeNewHealthInfo.value = response
                }

                override fun onFailed(response: BaseResponse<HealthInfoDTO>) {
                    homeNewHealthInfo.value = response
                }
            })
    }

    /**
     * 获取卡片详情页数据
     * @param requestParam
     */
    fun getHeartRateDetailData(requestParam: Map<*, *>) {
        MainRepository().getHeartrateDetail(
            requestParam,
            object : BaseResponseCallback<HeartRateStatDTO> {
                override fun onSuccess(response: BaseResponse<HeartRateStatDTO>) {
                    cardDetailChartData.value = response
                }

                override fun onFailed(response: BaseResponse<HeartRateStatDTO>) {
                    cardDetailChartData.value = response
                }
            })
    }

    fun getCardDetailChartLiveData(): MutableResult<BaseResponse<HeartRateStatDTO>> {
        return cardDetailChartData
    }

    fun getSleepDayDetailData(requestParam: Map<*, *>) {
        MainRepository().getSleepDayDetail(
            requestParam,
            object : BaseResponseCallback<SleepDayResponseDTO> {
                override fun onSuccess(response: BaseResponse<SleepDayResponseDTO>) {
                    sleepDetailChartData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<SleepDayResponseDTO>) {
                    sleepDetailChartData.postValue(response)
                }
            })
    }

    fun getSleepDetailChartLiveData(): MutableResult<BaseResponse<SleepDayResponseDTO>> {
        return sleepDetailChartData
    }

    fun getSleepWeekDetailData(requestParam: Map<*, *>) {
        MainRepository().getSleepWeekDetail(
            requestParam,
            object : BaseResponseCallback<SleepWeekResponseDTO> {
                override fun onSuccess(response: BaseResponse<SleepWeekResponseDTO>) {
                    sleepWeekChartData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<SleepWeekResponseDTO>) {
                    sleepWeekChartData.postValue(response)
                }
            })
    }

    fun getSleepWeekDetailLiveData(): MutableResult<BaseResponse<SleepWeekResponseDTO>> {
        return sleepWeekChartData
    }

    fun getSleepMonthDetailData(requestParam: Map<*, *>) {
        MainRepository().getSleepMonthDetail(
            requestParam,
            object : BaseResponseCallback<SleepMonthResponseDTO> {
                override fun onSuccess(response: BaseResponse<SleepMonthResponseDTO>) {
                    sleepMonthChartData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<SleepMonthResponseDTO>) {
                    sleepMonthChartData.postValue(response)
                }
            })
    }

    fun getSleepMonthDetailLiveData(): MutableResult<BaseResponse<SleepMonthResponseDTO>> {
        return sleepMonthChartData
    }

    fun getSleepYearDetailData(requestParam: Map<*, *>) {
        MainRepository().getSleepYearDetail(
            requestParam,
            object : BaseResponseCallback<SleepYearResponseDTO> {
                override fun onSuccess(response: BaseResponse<SleepYearResponseDTO>) {
                    sleepYearChartData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<SleepYearResponseDTO>) {
                    sleepYearChartData.postValue(response)
                }
            })
    }

    fun getSleepYearDetailLiveData(): MutableResult<BaseResponse<SleepYearResponseDTO>> {
        return sleepYearChartData
    }

    fun getSpO2DetailData(requestParam: Map<*, *>) {
        MainRepository().getSpO2DayDetail(requestParam,
            object : BaseResponseCallback<SpO2ItemResponseDTO> {
                override fun onSuccess(response: BaseResponse<SpO2ItemResponseDTO>) {
                    when (requestParam.getOrDefault("unit", "0")) {
                        "day" -> spo2DayChartData.postValue(response)
                        "week" -> spo2WeekChartData.postValue(response)
                        "month" -> spo2MonthChartData.postValue(response)
                        "year" -> spo2YearChartData.postValue(response)
                    }
                }

                override fun onFailed(response: BaseResponse<SpO2ItemResponseDTO>) {
                    when (requestParam.getOrDefault("unit", "0")) {
                        "day" -> spo2DayChartData.postValue(response)
                        "week" -> spo2WeekChartData.postValue(response)
                        "month" -> spo2MonthChartData.postValue(response)
                        "year" -> spo2YearChartData.postValue(response)
                    }
                }

            })
    }

    fun getTempDetailData(requestParam: Map<*, *>) {
        MainRepository().getTempDetail(requestParam,
            object : BaseResponseCallback<TempItemResponseDTO> {
                override fun onSuccess(response: BaseResponse<TempItemResponseDTO>) {
                    when (requestParam.getOrDefault("unit", "0")) {
                        "day" -> tempDayChartData.postValue(response)
                        "week" -> tempWeekChartData.postValue(response)
                        "month" -> tempMonthChartData.postValue(response)
                        "year" -> tempYearChartData.postValue(response)
                    }
                }

                override fun onFailed(response: BaseResponse<TempItemResponseDTO>) {
                    when (requestParam.getOrDefault("unit", "0")) {
                        "day" -> tempDayChartData.postValue(response)
                        "week" -> tempWeekChartData.postValue(response)
                        "month" -> tempMonthChartData.postValue(response)
                        "year" -> tempYearChartData.postValue(response)
                    }
                }

            })
    }
    //region 压力详情页
    /**
     * 压力详情页数据
     */
    fun sendPressureDetailDataReq(requestParam: Map<*, *>) {
//        pressureChartData = MainRepository().getPressureDetail(requestParam)
        MainRepository().getPressureDetail(
            requestParam,
            object : BaseResponseCallback<PressureDetailRespDTO> {

                override fun onSuccess(response: BaseResponse<PressureDetailRespDTO>) {
                    pressureChartData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<PressureDetailRespDTO>) {
                    pressureChartData.postValue(response)
                }
            })
    }

    fun sendPressureSummaryDataReq(requestParam: Map<*, *>) {
        MainRepository().getPressureSummary(
            requestParam,
            object : BaseResponseCallback<PressureSummaryDTO> {

                override fun onSuccess(response: BaseResponse<PressureSummaryDTO>) {
                    pressureSummaryData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<PressureSummaryDTO>) {
                    pressureSummaryData.postValue(response)
                }

            })
    }

    fun getPressureChartLiveData(): MutableResult<BaseResponse<PressureDetailRespDTO>> {
        return pressureChartData
    }

    fun getPressureSummaryLiveData(): MutableResult<BaseResponse<PressureSummaryDTO>> {
        return pressureSummaryData
    }

    //endregion

    /**
     * 获取健康建议与风险总结
     */
    fun getHealthSummery(requestParam: Map<*, *>) {
        MainRepository().getHealthSummery(
            requestParam,
            object : BaseResponseCallback<HealthSummeryDTO> {
                override fun onSuccess(response: BaseResponse<HealthSummeryDTO>) {
                    healthSummeryData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<HealthSummeryDTO>) {
                    healthSummeryData.postValue(response)
                }
            })
    }

    /**
     * 获取睡眠健康建议与风险总结
     */
    fun getHealthSleepSummery(requestParam: Map<*, *>) {
        MainRepository().getHealthSleepSummery(
            requestParam,
            object : BaseResponseCallback<HealthSleepSummeryDTO> {
                override fun onSuccess(response: BaseResponse<HealthSleepSummeryDTO>) {
                    healthSleepSummeryData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<HealthSleepSummeryDTO>) {
                    healthSleepSummeryData.postValue(response)
                }
            })
    }

    fun getHealthSpO2Summery(requestParam: Map<*, *>) {
        MainRepository().getSpO2Summary(
            requestParam,
            object : BaseResponseCallback<HealthSpO2SummaryDTO> {
                override fun onSuccess(response: BaseResponse<HealthSpO2SummaryDTO>) {
                    if (Looper.getMainLooper().isCurrentThread)
                        healthSpO2SummeryData.value = response
                    else
                        healthSpO2SummeryData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<HealthSpO2SummaryDTO>) {
                    if (Looper.getMainLooper().isCurrentThread)
                        healthSpO2SummeryData.value = response
                    else
                        healthSpO2SummeryData.postValue(response)
                }
            })
    }

    fun getHealthTempSummery(requestParam: Map<*, *>) {
        MainRepository().getTempSummary(
            requestParam,
            object : BaseResponseCallback<HealthTempSummaryDTO> {
                override fun onSuccess(response: BaseResponse<HealthTempSummaryDTO>) {
                    tempSpO2SummeryData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<HealthTempSummaryDTO>) {
                    tempSpO2SummeryData.postValue(response)
                }
            })
    }

    fun getHealthBloodPressureSummery(requestParam: Map<*, *>) {
        MainRepository().getBloodpressureSummary(
            requestParam,
            object : BaseResponseCallback<HealthBloodpressureSummaryDTO> {
                override fun onSuccess(response: BaseResponse<HealthBloodpressureSummaryDTO>) {
                    healthBloodPressureSummary.postValue(response)
                }

                override fun onFailed(response: BaseResponse<HealthBloodpressureSummaryDTO>) {
                    healthBloodPressureSummary.postValue(response)
                }
            })
    }


    fun getHealthWeekSleepSummery(requestParam: Map<*, *>) {
        MainRepository().getHealthSleepSummery(
            requestParam,
            object : BaseResponseCallback<HealthSleepSummeryDTO> {
                override fun onSuccess(response: BaseResponse<HealthSleepSummeryDTO>) {
                    healthWeekSleepSummeryData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<HealthSleepSummeryDTO>) {
                    healthWeekSleepSummeryData.postValue(response)
                }
            })
    }

    fun getHealthMonthSleepSummery(requestParam: Map<*, *>) {
        MainRepository().getHealthSleepSummery(
            requestParam,
            object : BaseResponseCallback<HealthSleepSummeryDTO> {
                override fun onSuccess(response: BaseResponse<HealthSleepSummeryDTO>) {
                    healthMonthSleepSummeryData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<HealthSleepSummeryDTO>) {
                    healthMonthSleepSummeryData.postValue(response)
                }
            })
    }

    fun getHealthYearSleepSummery(requestParam: Map<*, *>) {
        MainRepository().getHealthSleepSummery(
            requestParam,
            object : BaseResponseCallback<HealthSleepSummeryDTO> {
                override fun onSuccess(response: BaseResponse<HealthSleepSummeryDTO>) {
                    healthYearSleepSummeryData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<HealthSleepSummeryDTO>) {
                    healthYearSleepSummeryData.postValue(response)
                }
            })
    }
    //endregion

    //region getter&setter
    fun getIsAutoRefresh(): LiveData<Boolean> {
        return isAutoRefresh
    }

    fun setAutoRefresh() {
        isAutoRefresh.value = true
    }

    fun getUserRefreshFlag(): LiveData<Boolean> {
        return userRefreshFlag
    }

    fun setUserRefreshFlag(refreshFlag: Boolean) {
        userRefreshFlag.value = refreshFlag
    }

    fun getExceptionCountData(): LiveData<Int> {
        return exceptionCountData
    }

    fun setExceptionCount(exceptionCount: Int) {
        exceptionCountData.value = exceptionCount
    }

    fun getBackToHomeData(): MutableLiveData<Boolean> {
        return backToHomeData
    }

    fun setBackToHomeData(backToHome: Boolean) {
        backToHomeData.value = backToHome
    }

    /**
     * 心率卡片授权状态
     */
    fun getCardAuthStatusLiveData(): MutableLiveData<Boolean> {
        return cardAuthStatusData
    }

    fun setCardAuthStatusLiveData(cardAuthStatus: Boolean) {
        cardAuthStatusData.value = cardAuthStatus
    }

    /**
     * 注销
     */
    fun getDeleteUserData(userId: String) {
        MainRepository().getDeleteUser(userId, object : BaseResponseCallback<Boolean> {
            override fun onSuccess(response: BaseResponse<Boolean>) {
                Log.d("MainViewModel", "getDeleteUser onSuccess")
                deleteuserResult.value = response
            }

            override fun onFailed(response: BaseResponse<Boolean>) {
                Log.d("MainViewModel", "getDeleteUser onFailed")
                deleteuserResult.value = response
            }

        })
    }

    /**
     * 关联账户解绑
     */
    fun getUnbindUserData(userId: String) {
        MainRepository().getUnbindAccount(userId, object : BaseResponseCallback<Boolean> {
            override fun onSuccess(response: BaseResponse<Boolean>) {
                unbindAccountResult.postValue(response)
            }

            override fun onFailed(response: BaseResponse<Boolean>) {
                unbindAccountResult.postValue(response)
            }

        })
    }

    /**
     * 获取用户数据
     */
    fun getUserInfoData(userId: String) {
        MainRepository().getUserInfo(userId, object : BaseResponseCallback<UserInfoDTO> {
            override fun onSuccess(response: BaseResponse<UserInfoDTO>) {
                userInfoData.postValue(response)
            }

            override fun onFailed(response: BaseResponse<UserInfoDTO>) {
                userInfoData.postValue(response)
            }

        })
    }

    /**
     * 保存用户数据
     */
    fun saveUserData(requestParam: Map<*, *>) {
        MainRepository().saveUserInfoData(requestParam, object : BaseResponseCallback<Boolean> {
            override fun onSuccess(response: BaseResponse<Boolean>) {
                saveUserInfoResult.postValue(response)
            }

            override fun onFailed(response: BaseResponse<Boolean>) {
                saveUserInfoResult.postValue(response)
            }

        })
    }

    /**
     * 网络请求数据使用说明文字
     */
    fun sendDataUsageReq() {
        MainRepository().sendDataUsage(object : BaseResponseCallback<String> {
            override fun onSuccess(response: BaseResponse<String>) {
                dataUsageData.postValue(response)
            }

            override fun onFailed(response: BaseResponse<String>) {
                dataUsageData.postValue(response)
            }
        })
    }

    /**
     * 数据使用说明liveData
     */
    fun getDataUsageLiveData(): LiveData<BaseResponse<String>> {
        return dataUsageData
    }


    fun getHealthReportData(requestParam: Map<*, *>) {
        MainRepository().getHealthRerortInfoData(requestParam,
            object : BaseResponseCallback<HealthReportDTO> {
                override fun onSuccess(response: BaseResponse<HealthReportDTO>) {
                    when (requestParam.getOrDefault("unit", "0")) {
                        "day" -> reportDayChartData.postValue(response)
                        "week" -> reportWeekChartData.postValue(response)
                        "month" -> reportMonthChartData.postValue(response)
                        "year" -> reportYearChartData.postValue(response)
                    }
                }

                override fun onFailed(response: BaseResponse<HealthReportDTO>) {
                    when (requestParam.getOrDefault("unit", "0")) {
                        "day" -> reportDayChartData.postValue(response)
                        "week" -> reportWeekChartData.postValue(response)
                        "month" -> reportMonthChartData.postValue(response)
                        "year" -> reportYearChartData.postValue(response)
                    }
                }

            })
    }

    /**
     * 血压
     */
    fun getBloodPressureData(requestParam: Map<*, *>) {
        MainRepository().getBloodPressureDetail(requestParam,
            object : BaseResponseCallback<BloodPressureResponseDTO> {
                override fun onSuccess(response: BaseResponse<BloodPressureResponseDTO>) {
                    when (requestParam.getOrDefault("unit", "0")) {
                        "day" -> bloodPressureDayChartData.postValue(response)
                        "week" -> bloodPressureWeekChartData.postValue(response)
                        "month" -> bloodPressureMonthChartData.postValue(response)
                        "year" -> bloodPressureYearChartData.postValue(response)
                    }
                }

                override fun onFailed(response: BaseResponse<BloodPressureResponseDTO>) {
                    when (requestParam.getOrDefault("unit", "0")) {
                        "day" -> bloodPressureDayChartData.postValue(response)
                        "week" -> bloodPressureWeekChartData.postValue(response)
                        "month" -> bloodPressureMonthChartData.postValue(response)
                        "year" -> bloodPressureYearChartData.postValue(response)
                    }
                }

            })
    }


    fun getHealthSummarizeData(requestParam: Map<*, *>) {
        MainRepository().getHealthSummarizeInfoData(requestParam,
            object : BaseResponseCallback<HealthSummarizeDTO> {
                override fun onSuccess(response: BaseResponse<HealthSummarizeDTO>) {
                    healthSummarizeData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<HealthSummarizeDTO>) {
                    healthSummarizeData.postValue(response)
                }

            })
    }

    fun getHistoryStatusData(userId: String) {
        MainRepository().getHistoryStatus(
            userId,
            object : BaseResponseCallback<HealthHistoryDataStatusDTO> {
                override fun onSuccess(response: BaseResponse<HealthHistoryDataStatusDTO>) {
                    healthHistoryData.postValue(response)
                }

                override fun onFailed(response: BaseResponse<HealthHistoryDataStatusDTO>) {
                    healthHistoryData.postValue(response)
                }
            })
    }


    fun getDoctorServicePhoneLiveData(): MutableLiveData<BaseResponse<CallDoctorPhoneDTO>> {
        return doctorServicePhoneData
    }

    fun sendQueryDoctorServicePhoneReq(params: Map<*, *>) {
        MainRepository().sendQueryDoctorServicePhoneReq(
            params,
            object : BaseResponseCallback<CallDoctorPhoneDTO> {
                override fun onSuccess(doctorServicePhoneDTO: BaseResponse<CallDoctorPhoneDTO>) {
                    doctorServicePhoneData.value = doctorServicePhoneDTO
                }

                override fun onFailed(doctorServicePhoneDTO: BaseResponse<CallDoctorPhoneDTO>) {
                    doctorServicePhoneData.value = doctorServicePhoneDTO
                }
            })
    }

    fun getDoctorServiceUpdateLiveData(): MutableLiveData<BaseResponse<Boolean>> {
        return doctorServiceUpdateData
    }

    fun sendDoctorServiceUpdateDataReq(phones: Map<*, *>) {
        MainRepository().sendUpdateDoctorServicePhoneReq(
            phones,
            object : BaseResponseCallback<Boolean> {
                override fun onSuccess(doctorServicePhoneDTO: BaseResponse<Boolean>) {
                    doctorServiceUpdateData.value = doctorServicePhoneDTO
                }

                override fun onFailed(doctorServicePhoneDTO: BaseResponse<Boolean>) {
                    doctorServiceUpdateData.value = doctorServicePhoneDTO
                }
            })
    }

    //endregion

    /**
     * 网络请求是否结束（不管是失败或成功）
     */
    val networkLoadingFinished = MutableLiveData<Boolean>()

    /**
     * 网络请求的结束方式：手动或自动
     * 1 - 手动
     * 0 - 自动或其他（默认）
     */
    val networkLoadingFinishedType = MutableLiveData<String>()


    /**
     *
     */
    fun getVersionData(versionCode: String) {
        MainRepository().getVersionInfo(
            versionCode,
            object : BaseResponseCallback<UpgradeVersionDTO> {
                override fun onSuccess(response: BaseResponse<UpgradeVersionDTO>) {
                    upgradeVersion.postValue(response)
                }
                override fun onFailed(response: BaseResponse<UpgradeVersionDTO>) {
                    upgradeVersion.postValue(response)
                }
            })
    }

}


