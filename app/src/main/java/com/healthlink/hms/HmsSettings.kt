package com.healthlink.hms

object HmsSettings {

    /**
     * 场景引擎运行间隔，默认值：4 * 60 * 1000L
     */
    const val WORKER_TASK_SCENE_ENGINE_RUN_INTERVAL = 4 * 60 * 1000L  // 4分钟刷新间隔

    /**
     * 长途驾驶关怀2的累计驾驶时长阈值，默认值 2 * 60 * 60 * 1000
     */
    const val SCENE_LONG_DRIVE_2_DRIVING_TIME = 2 * 60 * 60 * 1000

    /**
     * 长途驾驶关怀2的累计休息时长阈值，默认值：5 * 60 * 1000
     */
    const val SCENE_LONG_DRIVE_2_TOTAL_REST_TIME = 5 * 60 * 1000

    /**
     * 电话医生是否打开，默认值：true
     */
    const val DOCTOR_CALL_OPEN_FLAG = true

    /**
     * 操作防抖时间设置：毫秒
     */
    const val ACTION_DELAY_TIME_MS: Int = 500

    /**
     * 拨打电话医生后的多少时间（ms）内，如果触发干预类场景，则取消该场景的后续触发
     */
    const val INTERVAL_FOR_CANCEL_SCENE_AFTER_DOCTOR_CALL = 5 * 60 * 1000

}