package com.healthlink.hms.fragment


import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Html
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.activity.HealthReportActivity
import com.healthlink.hms.activity.card.HMSCardFragmentInteractWithAcInterface
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.core.common.utils.DateUtil
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.databinding.FragmentDayHealthReportBinding
import com.healthlink.hms.fragment.viewmodel.HealthReportFragmentDataModel
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.AnnengInfluenceFactor
import com.healthlink.hms.core.model.dto.HealthReportDTO
import com.healthlink.hms.core.model.dto.ReportHealthPartDTO
import com.healthlink.hms.core.model.dto.ReportTripPort
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.viewmodels.MainViewModel
import com.healthlink.hms.views.CalHiddenTextView
import com.healthlink.hms.views.MiddleEllipsesTextView
import com.healthlink.hms.views.StatisticsItem
import com.healthlink.hms.views.StatisticsView
import java.lang.ref.WeakReference
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Calendar
import kotlin.math.max


/**
 *@Author: 付仁秀
 *@Description：
 **/
class ReportDayFragment : BaseCardFragment<FragmentDayHealthReportBinding, MainViewModel>(
    MainViewModel::class.java,
    R.layout.fragment_day_health_report
), MiddleEllipsesTextView.UpdateSeeMore, CalHiddenTextView.HiddenInterface {

    private var score = "0"
    private var reqMap = mapOf<String, String>()
    private var isAnimationPlayed = false
    private lateinit var reportHealthInfo: ReportHealthPartDTO
    private lateinit var reportTripInfo: ReportTripPort
    private var dateShow = ""
    private var healthEstimateStr = "" //健康说明
    private var healthAdviceStr = "" //健康建议
    private var healthStatusCode = "normal"
    private var healthSummaryC1 = "" //第一张卡片的健康总结
    private var healthContrastStr = ""//同期对比长文本
    private var contrastStr = "" //同期对比左侧文本
    private var popClickEnable=false

    /**
     * 行程次数
     */
    private var tripCount = 0

    /**
     * 累计驾驶时长（小时）
     */
    private var tripHours = 0

    /**
     * 累计驾驶时长（分钟）
     */
    private var tripMins = 0

    /**
     * 异常行程次数
     */
    private var unNormalCount = 0

    /**
     * 累计驾驶里程
     */
    private var totalDistance = 0f

    /**
     * 异常驾驶占比
     */
    private var unNormalTripProp = 0f

    /**
     * 正常驾驶占比
     */
    private var normalTripProp = 0f

    /**
     * 异常指标时长
     */
    private var unNormalTripTake = 0

    private lateinit var chartView: StatisticsView

    private var normalTextColor = 0
    private var middleTextColor = 0
    private var highTextColor = 0

    private var chartDataList: MutableList<StatisticsItem> = arrayListOf()


    var heartRateItems: MutableList<PopItem> = mutableListOf()
    var sleepItems: MutableList<PopItem> = mutableListOf()
    var spo2Items: MutableList<PopItem> = mutableListOf()
    var pressureItems: MutableList<PopItem> = mutableListOf()
    var tempItems: MutableList<PopItem> = mutableListOf()
    var bloodPressureItems: MutableList<PopItem> = mutableListOf()

    var fragmentDataModel = HealthReportFragmentDataModel()

    private var isProvacyMode = false

    companion object {
        private const val TAG = ""
        private const val ARG_PARAM_TYPE = "ARG_PARAM_TYPE"
        private val fragmentInteractWithAC
            get() = _fragmentInteractWithAC?.get()
        private var _fragmentInteractWithAC: WeakReference<HMSCardFragmentInteractWithAcInterface>? =
            null
        private lateinit var mUserId: String

        fun newInstance(
            cartTimeType: TimeCode,
            userId: String,
            interact: HMSCardFragmentInteractWithAcInterface
        ): ReportDayFragment {
            val fragment = ReportDayFragment()
            val args = Bundle()
            args.putString(ARG_PARAM_TYPE, cartTimeType.timeCode)
            fragment.arguments = args
            _fragmentInteractWithAC = WeakReference(interact)
            mUserId = userId
            return fragment
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            cardTimeType = it.getString(ARG_PARAM_TYPE)
        }
        isAnimationPlayed = false
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()
        initData()
        // 恢复数据
        if(savedInstanceState!=null){
            restoreDataIfPossible(savedInstanceState)
        }
    }

    private fun restoreDataIfPossible(savedInstanceState: Bundle) {
        try {
            var fragmentSavedData = savedInstanceState.getString(KEY_SAVED_DATA_SUMMARY)
            if (fragmentSavedData != null) {
                var fragmentDataModel =
                    gson.fromJson(fragmentSavedData, HealthReportFragmentDataModel::class.java)
                if(fragmentDataModel!=null){
                    this.fragmentDataModel = fragmentDataModel
                    Log.i(TAG, "init ReportDayFragment data from saved fragment success.")
                }
            }
        }catch (ex: Exception){
            Log.i(TAG, "init ReportDayFragment data from saved fragment fail. error : ${ex.message}")
        }
    }

    /**
     * 私密模式时返回首页
     */
    override fun onPrivacyModeChange(provacyMode: Boolean) {
        if (provacyMode) {
            val activity = requireActivity() as? HealthReportActivity
            activity?.backToMain()
        }
    }

    private fun playTripAni(unNormal: Float, sum: Float) {
        if (isAnimationPlayed) return
        if (tripCount == 0) return
        isAnimationPlayed = true
        val animator = ValueAnimator.ofFloat(0f, 1f)
        animator.duration = 1000
        animator.addUpdateListener(object : ValueAnimator.AnimatorUpdateListener {
            override fun onAnimationUpdate(animation: ValueAnimator) {
                val value = animation.animatedValue as Float
                binding.tripImage.setValue(unNormal * value, sum)
            }
        })
        animator.start()
    }


    override fun sendRequest(userId: String, timeCode: String) {
        var requestTimeValue = getRequestTime(timeCode)
        reqMap = mapOf(
            "userId" to userId,
            "unit" to timeCode,
            "vin" to MMKVUtil.getVinCode()!!
        )
        dateShow = requestTimeValue.third
        if (!HmsApplication.isNetworkConn()) {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        else {
            if (isDataReady)
                showLoading()
            setScrollEnable(binding.reportScroll, false)
            if(this.fragmentDataModel!=null
                && this.fragmentDataModel.healthData!=null
                && this.fragmentDataModel.healthData!!.code == "0"){
                when(cardTimeType){
                    TimeCode.TIME_CODE_DAY.timeCode -> {
                        processHealthDataDay(fragmentDataModel.healthData!!)
                    }
                    TimeCode.TIME_CODE_WEEK.timeCode -> {
                        processHealthDataWeek(fragmentDataModel.healthData!!)
                    }

                    TimeCode.TIME_CODE_MONTH.timeCode -> {
                        processHealthDataMonth(fragmentDataModel.healthData!!)
                    }

                    TimeCode.TIME_CODE_YEAR.timeCode -> {
                        processHealthDataYear(fragmentDataModel.healthData!!)
                    }
                }

            }else {
                viewModel.getHealthReportData(reqMap)
            }
        }

    }

    override fun sendDataReadyRequest(userId: String, timeCode: String) {
        var requestTimeValue = getRequestTime(timeCode)
        reqMap = mapOf(
            "userId" to userId,
            "unit" to timeCode,
            "vin" to MMKVUtil.getVinCode()!!
        )
        dateShow = requestTimeValue.third
        if (!HmsApplication.isNetworkConn()) {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showLoading()
        }

        when (timeCode) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                DataTrackUtil.dtClick("Health_Healthreports_Daytab_Click")
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                DataTrackUtil.dtClick("Health_Healthreports_Weektab_Click")
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                DataTrackUtil.dtClick("Health_Healthreports_Mouthtab_Click")
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                DataTrackUtil.dtClick("Health_Healthreports_Yeartab_Click")
            }
        }
        viewModel.getHistoryStatusData(userId)
    }

    override fun onResume() {
        super.onResume()
        if (isDataReady)
            sendRequest(mUserId, cardTimeType!!)
        else
            handler.post(runnable)
    }

    override fun onPause() {
        super.onPause()
        popClickEnable=false
        popupWindow?.let {
            if (it.isShowing) it.dismiss()
        }
        handler.removeCallbacks(runnable)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        // 保存数据
        if(fragmentDataModel.healthData!=null
            && fragmentDataModel.healthData!!.code == "0"
        ){
            this.fragmentDataModel.scollY = binding.reportScroll.scrollY
            outState.putString(KEY_SAVED_DATA_SUMMARY, gson.toJson(fragmentDataModel));
        }

    }

    private val runnable = UpdateRunnable(this)
    private class UpdateRunnable(private val fragment: ReportDayFragment) : Runnable {
        private val weakFragment = WeakReference(fragment)

        override fun run() {
            val fragment = weakFragment.get()
            if (fragment != null && !fragment.isDetached) {
                if (!fragment.isDataReady) {
                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
                    // 15 秒后再次调用
                    if (HmsApplication.isNetworkConn())
                        fragment.handler.postDelayed(this, 15000)
                }
            }
        }
    }

//    private val runnable = object : Runnable {
//        private val weekFragment = WeakReference(this@ReportDayFragment)
//        override fun run() {
//            val fragment = weekFragment.get()
//            if (fragment != null && !fragment.isDetached) {
//                if (!fragment.isDataReady) {
//                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
//                    // 15 秒后再次调用
//                    if (HmsApplication.isNetworkConn())
//                        fragment.handler.postDelayed(this, 15000)
//                }
//            }
//        }
//    }

    fun getDataReady() {
        isDataReady = true
        handler.removeCallbacks(runnable)
        sendRequest(mUserId, cardTimeType!!)
    }


    private fun initData() {

        viewModel.healthHistoryData.observe(viewLifecycleOwner) {
            val notReadyText =
                "健康报告分析中，请稍等……"
            if (it.code == "0" && it.data != null) {
                val status = it.data?.reportStatus
                if (status != null) {
                    when (cardTimeType) {
                        TimeCode.TIME_CODE_DAY.timeCode -> {
                            if (status.dayDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_WEEK.timeCode -> {
                            if (status.weekDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_MONTH.timeCode -> {
                            if (status.monthDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_YEAR.timeCode -> {
                            if (status.yearDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }
                    }
                } else {
                    getDataReady()
                }
            } else if (it.code == "5") {
                showNoAuthView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                // 无网络或者刷新失败处理
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            }
        }

        viewModel.reportDayChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthDataDay(it)
            restoreScrollY()
        }


        viewModel.reportWeekChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthDataWeek(it)
            restoreScrollY()
        }

        viewModel.reportMonthChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthDataMonth(it)
            restoreScrollY()
        }

        viewModel.reportYearChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthDataYear(it)
            restoreScrollY()
        }
    }

    private fun restoreScrollY(){
        // 恢复滚动位置
        if(this.fragmentDataModel.scollY>0) {
            binding.reportScroll.post({
                binding.reportScroll.scrollY = this.fragmentDataModel.scollY
            })
        }
    }
    private fun processHealthDataYear(it: BaseResponse<HealthReportDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.reportScroll.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            if (it.data!!.healthReport?.score.isNullOrBlank() || it.data!!.healthReport?.score == "0") {
                //说明本时段无数据
                showNoAuthView("暂无今年健康数据")
            } else {
                it.data!!.healthReport?.let { rh ->
                    reportHealthInfo = rh
                }
                it.data!!.tripReport?.let { rt ->
                    reportTripInfo = rt
                }
                initChartData()
            }
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        hideLoading()
    }

    private fun processHealthDataMonth(it: BaseResponse<HealthReportDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.reportScroll.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            if (it.data!!.healthReport?.score.isNullOrBlank() || it.data!!.healthReport?.score == "0") {
                //说明本时段无数据
                showNoAuthView("暂无本月健康数据")
            } else {
                it.data!!.healthReport?.let { rh ->
                    reportHealthInfo = rh
                }
                it.data!!.tripReport?.let { rt ->
                    reportTripInfo = rt
                }
                initChartData()
            }
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        hideLoading()
    }

    private fun processHealthDataWeek(it: BaseResponse<HealthReportDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.reportScroll.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            if (it.data!!.healthReport?.score.isNullOrBlank() || it.data!!.healthReport?.score == "0") {
                //说明本时段无数据
                showNoAuthView("暂无本周健康数据")
            } else {
                it.data!!.healthReport?.let { rh ->
                    reportHealthInfo = rh
                }
                it.data!!.tripReport?.let { rt ->
                    reportTripInfo = rt
                }
                initChartData()
            }

        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        hideLoading()
    }

    private fun processHealthDataDay(it: BaseResponse<HealthReportDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.reportScroll.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            if (it.data!!.healthReport?.score.isNullOrBlank() || it.data!!.healthReport?.score == "0") {
                //说明本时段无数据
                showNoAuthView("暂无今日健康数据")
            } else {
                it.data!!.healthReport?.let { rh ->
                    reportHealthInfo = rh
                }
                it.data!!.tripReport?.let { rt ->
                    reportTripInfo = rt
                }
                initChartData()
            }
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        hideLoading()
    }

    private fun initUI() {
        adjustSeeMoreText()
        normalTextColor = resources.getColor(R.color.health_report_highlight)
        middleTextColor = resources.getColor(R.color.health_report_chart_text_middle)
        highTextColor = resources.getColor(R.color.health_report_chart_text_high)
        binding.tvHealthEstimateContent.setCallback(this)
        binding.tvHealthAdviceContent.setCallback(this)
        binding.tvMoreText.setCallback(this)
        binding.tvContrastContent.setCallback(this)
        binding.tvMoreText.setEndPercentage(80)
        binding.tvHiddenText.setInterface(this)
        binding.tvHealthSummarize.setCallback(this)
        setScoreBarText(cardTimeType!!)
        chartView = binding.statisticsView
        binding.statisticsView.setOnPopWindowListener(object : StatisticsView.PopWindowListener {
            override fun onHeartRateClick(
                clickable: Boolean,
                offsetX: Float,
                offsetY: Float,
                directionRight: Boolean
            ) {
                if (clickable)
                    if (heartRateItems.isNotEmpty())
                        showPopupWindow(chartView, offsetX, offsetY, heartRateItems)
            }

            override fun onSleepClick(
                clickable: Boolean,
                offsetX: Float,
                offsetY: Float,
                directionRight: Boolean
            ) {
                if (clickable)
                    if (sleepItems.isNotEmpty())
                        showPopupWindow(chartView, offsetX, offsetY, sleepItems)
            }

            override fun onSpo2Click(
                clickable: Boolean,
                offsetX: Float,
                offsetY: Float,
                directionRight: Boolean
            ) {
                if (clickable)
                    if (spo2Items.isNotEmpty())
                        showPopupWindow(chartView, offsetX, offsetY, spo2Items)
            }

            override fun onPressureClick(
                clickable: Boolean,
                offsetX: Float,
                offsetY: Float,
                directionRight: Boolean
            ) {
                if (clickable)
                    if (pressureItems.isNotEmpty())
                        showPopupWindow(chartView, offsetX, offsetY, pressureItems, false)
            }

            override fun onTempertureClick(
                clickable: Boolean,
                offsetX: Float,
                offsetY: Float,
                directionRight: Boolean
            ) {
                if (clickable)
                    if (tempItems.isNotEmpty())
                        showPopupWindow(chartView, offsetX, offsetY, tempItems, false)
            }

            override fun onBloodPressurClick(
                clickable: Boolean,
                offsetX: Float,
                offsetY: Float,
                directionRight: Boolean
            ) {
                if (clickable)
                    if (bloodPressureItems.isNotEmpty())
                        showPopupWindow(chartView, offsetX, offsetY, bloodPressureItems, false)
            }

        })
    }

    fun initChartData() {
        reportHealthInfo.riskLevel?.let {
            binding.tvHealthReportConclusion.text =
                getString(R.string.health_report_level_describe) + it
        }
        binding.tvHealthReportDate.text = dateShow

        // 综合健康状况下的健康总结
        reportHealthInfo.riskDescription?.let {
            healthSummaryC1 = it
            binding.tvHealthSummarize.text = it
        }

        reportHealthInfo.healthAdvice?.let {
            healthAdviceStr = it
            binding.tvHealthAdviceContent.setIsEllipsized(false)
            binding.tvHealthAdviceContent.text = healthAdviceStr
        }

        // 健康评估
        reportHealthInfo.scoreFactorDesc?.let {
            healthEstimateStr = it
            binding.tvHealthEstimateContent.text = healthEstimateStr
        }
        //设置同期对比分数 往期与同期都有数据时有数据才显示
        if ((reportHealthInfo.scoreLastCycle != null && reportHealthInfo.scoreLastCycle != "0") && (reportHealthInfo.score != null && reportHealthInfo.score != "0")) {
            binding.rlContrast.visibility = View.VISIBLE
            try {
                val last = reportHealthInfo.scoreLastCycle!!.toInt()
                val current = reportHealthInfo.score!!.toInt()
                setCompareScoreViewLength(current, last)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        } else {
            binding.rlContrast.visibility = View.GONE
        }

        //设置同期对比右侧长文本
        reportHealthInfo.scoreChangeReason?.let {
            healthContrastStr = it
            binding.tvHiddenText.text = it
        }

        dealAllData(
            reportHealthInfo.influenceFactor,
            reportHealthInfo.sleepLevel,
            reportHealthInfo.sleepText
        )
        popClickEnable=true
        binding.statisticsView.setValue(chartDataList as ArrayList)

        reportTripInfo.tripCount?.let {
            this.tripCount = it
        }
        reportTripInfo.unNormalCount?.let {
            this.unNormalCount = it
        }
        reportTripInfo.tripMins?.let {
            this.tripHours = it / 60
            this.tripMins = it % 60
        }
        reportTripInfo.normalTripProp?.let {
            this.normalTripProp = it
        }
        reportTripInfo.unNormalTripProp?.let {
            this.unNormalTripProp = it
        }
        reportTripInfo.totalDistance?.let {
            this.totalDistance = it
        }
        reportTripInfo.unNormalTripTake?.let {
            this.unNormalTripTake = it
        }
        reportHealthInfo.score?.let {
            this.score = it
        }
        reportHealthInfo.samePeriodCompare?.let {
            binding.tvContrastContent.text = it
        }
        if (this.score.isNotEmpty() || this.score != "") {
            if (this.score.toInt() in 90..100) {
                healthStatusCode = "normal"
            } else if (this.score.toInt() in 75..89) {
                healthStatusCode = "low"
            } else if (this.score.toInt() in 60..74) {
                healthStatusCode = "middle"
            } else if (this.score.toInt() in 45..59) {
                healthStatusCode = "high"
            }
            binding.loHealthReportImage.background = null
            binding.hsView.setHealthStatus("", healthStatusCode, score.toInt())
            binding.hsView.visibility = View.VISIBLE
            setScrollEnable(binding.reportScroll)
        } else {
            binding.loHealthReportImage.background =
                resources.getDrawable(R.drawable.img_no_data)
            binding.hsView.visibility = View.GONE
            setScrollEnable(binding.reportScroll)
        }

        binding.tvTripCard1Content.text = tripCount.toString()
        binding.tvTripCard2Content.text = unNormalCount.toString()
        binding.tvTripDistanceAll.text = totalDistance.toInt().toString()
        val normalTripPerInt = this.normalTripProp.toInt()
        binding.tvNormalTripPer.text = "状态良好$normalTripPerInt%"
        val unNormalTripPerInt = this.unNormalTripProp.toInt()
        binding.tvUnNormalTripPer.text = "状态欠佳$unNormalTripPerInt%"
        binding.tvTripCard3HourValue.text = tripHours.toString()
        binding.tvTripCard3MinValue.text = tripMins.toString()
        if (tripCount == 0) {
            binding.tripImage.drawNoDataStyle()
        }
        initRichTextView(tripHours, tripMins, unNormalTripTake)
        val scrollView = binding.reportScroll
        scrollView.scrollX = 0
        scrollView.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            val height = scrollView.getChildAt(0).height // 获取ScrollView内容的总高度
            val scrollViewHeight = scrollView.height // 获取ScrollView的可见高度
            val diff =
                (height - scrollViewHeight) * if (binding.rlContrast.visibility == View.VISIBLE) 0.92f else 0.89f //scrollview中判定的距离 动画view位置底部约为总长度的的92%
            if (scrollY >= diff) {
                playTripAni(unNormalTripProp, normalTripProp + unNormalTripProp)
                DataTrackUtil.dtScroll("Health_Healthreports_Bottom_Show")
            }
            // 取消弹窗
            if(popupWindow!=null && popupWindow?.isShowing!!){
                popupWindow?.dismiss()
            }
        }

    }

    fun initRichTextView(hour: Int, min: Int, er: Int) {
        val string = "您本周累计驾驶小时分钟，其中共分钟状态欠佳"
        binding.tvDriveTimeConclusion.text = string
        val hour = hour.toString()
        val min = min.toString()
        val er = er.toString()
        var spanBuilder = SpannableStringBuilder(string)
        spanBuilder.insert(7, hour)
        spanBuilder.insert(9 + hour.length, min)
        spanBuilder.insert(15 + hour.length + min.length, er)
        spanBuilder.setSpan(
            ForegroundColorSpan(resources.getColor(R.color.health_report_highlight)),
            7,
            7 + hour.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        spanBuilder.setSpan(
            ForegroundColorSpan(resources.getColor(R.color.health_report_highlight)),
            9 + hour.length,
            9 + min.length + hour.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        spanBuilder.setSpan(
            ForegroundColorSpan(resources.getColor(R.color.health_report_highlight)),
            15 + min.length + hour.length,
            15 + min.length + hour.length + er.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        spanBuilder.setSpan(
            RelativeSizeSpan(1.636f),
            7,
            7 + hour.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        spanBuilder.setSpan(
            RelativeSizeSpan(1.636f),
            9 + hour.length,
            9 + min.length + hour.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        spanBuilder.setSpan(
            RelativeSizeSpan(1.636f),
            15 + min.length + hour.length,
            15 + hour.length + min.length + er.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        binding.tvDriveTimeConclusion.text = spanBuilder
    }

    fun getRequestTime(timeType: String): Triple<String, String, String> {
        val calendar: Calendar = Calendar.getInstance()
        val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd hh:mm:ss")
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss")
        val now: LocalDateTime = LocalDateTime.now()
        var startTime = ""
        var showDateTime = ""
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        when (timeType) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                showDateTime = String.format("%02d", now.monthValue) + "月" + String.format(
                    "%02d",
                    now.dayOfMonth
                ) + "日"
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                showDateTime = getWeekDateRange(DateUtil.FORMAT_MM_DD)
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                calendar.set(Calendar.DAY_OF_MONTH, 1)
                showDateTime = String.format(
                    "%02d",
                    calendar.get(Calendar.YEAR)
                ) + "年" + String.format("%02d", calendar.get(Calendar.MONTH) + 1) + "月"
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                calendar.set(Calendar.MONTH, 0)
                calendar.set(Calendar.DAY_OF_MONTH, 1)
                showDateTime = String.format("%02d", calendar.get(Calendar.YEAR)) + "年"
            }
        }
        startTime = simpleDateFormat.format(calendar.time)
        val endTime = now.format(formatter)
        return Triple(startTime, endTime, showDateTime)
    }

    override fun update(isEllipsized: Boolean, viewID: Int) {
        when (viewID) {
            R.id.tv_health_estimate_content -> {
                if (isEllipsized) {
                    binding.loHealthEstimateMore.visibility = View.VISIBLE
                    binding.loHealthEstimateMore.setOnClickListener { v ->
                        HMSDialogUtils.showHMSNotiDialogNew(
                            requireContext(),
                            R.layout.hms_dialog_report_see_more,
                            "健康评估",
                            healthEstimateStr,
                            "知道了"
                        ) { isPositive ->
                        }
                    }
                } else {
                    binding.loHealthEstimateMore.visibility = View.GONE
                }
            }

            R.id.tv_health_advice_content -> {
                if (isEllipsized) {
                    binding.healthAdviceMore.visibility = View.VISIBLE
                    binding.healthAdviceMore.setOnClickListener { v ->
                        HMSDialogUtils.showHMSNotiDialogNew(
                            requireContext(),
                            R.layout.hms_dialog_report_see_more,
                            "健康建议",
                            healthAdviceStr,
                            "知道了"
                        ) { isPositive ->
                        }
                    }
                } else {
                    binding.healthAdviceMore.visibility = View.GONE
                }
            }

            R.id.tv_more_text -> {
                if (isEllipsized) {
                    binding.loContrastMore.visibility = View.VISIBLE
                    binding.loContrastMore.setOnClickListener { v ->
                        HMSDialogUtils.showHMSNotiDialog(
                            requireContext(),
                            R.layout.hms_dialog_see_more,
                            "对比分析",
                            healthContrastStr,
                            "知道了"
                        ) { isPositive ->
                        }
                    }
                } else {
                    binding.loContrastMore.visibility = View.GONE
                }
            }

            R.id.tv_health_summarize -> {
                if (isEllipsized) {
                    binding.loHealthSummarizeMore.visibility = View.VISIBLE
                    binding.loHealthSummarizeMore.setOnClickListener { v ->
                        HMSDialogUtils.showHMSNotiDialog(
                            requireContext(),
                            R.layout.hms_dialog_see_more,
                            "健康说明",
                            healthSummaryC1,
                            "知道了"
                        ) { isPositive ->
                        }
                    }
                } else {
                    binding.loHealthSummarizeMore.visibility = View.GONE
                }
            }

            R.id.tv_contrast_content -> {
                //这个查看更多没用了  变成了短文本
                if (isEllipsized) {
                    binding.loContrastLeftMore.visibility = View.VISIBLE
                    binding.loContrastLeftMore.setOnClickListener { v ->
                        HMSDialogUtils.showHMSNotiDialog(
                            requireContext(),
                            R.layout.hms_dialog_see_more,
                            "同期对比",
                            contrastStr,
                            "知道了"
                        ) { isPositive ->
                        }
                    }
                } else {
                    binding.loContrastLeftMore.visibility = View.GONE
                }
            }
        }
    }


    override fun showHiddenText(str: String?) {
        str?.let {
            if (it.isNotEmpty()) {
                binding.tvMoreText.text = str
            }
        }
    }

    //设置分数对比后条的长度
    fun setCompareScoreViewLength(scoreT: Int, scoreY: Int) {
        val todayBar = binding.scoreTodayBar
        val yesterdayBar = binding.scoreYesterdayBar
        val initialWidth = binding.loContrast.width * 0.1f
        val per = binding.loContrast.width * 0.7f / 100f  //每一点分数增加的长度

        if (scoreT >= 0) {
            val layoutParams = todayBar.layoutParams
            layoutParams.width = initialWidth.toInt() + (scoreT * per).toInt()
            todayBar.layoutParams = layoutParams
            binding.tvScoreToday.text = scoreT.toString()
        }

        if (scoreY >= 0) {
            val layoutParams = yesterdayBar.layoutParams
            layoutParams.width = initialWidth.toInt() + (scoreY * per).toInt()
            yesterdayBar.layoutParams = layoutParams
            binding.tvScoreYesterday.text = scoreY.toString()
        }
    }

    fun setScoreBarText(timeCode: String) {
        var currentText = ""
        var lastText = ""
        when (timeCode) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                currentText = "今天"
                lastText = "昨天"
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                currentText = "本周"
                lastText = "上周"
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                currentText = "本月"
                lastText = "上月"
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                currentText = "今年"
                lastText = "去年"
            }
        }
        binding.tvScoreCurrentText.text = currentText
        binding.tvScoreLastText.text = lastText
    }

    var popupWindow: PopupWindow? = null

    @SuppressLint("ClickableViewAccessibility")
    fun showPopupWindow(
        anchorView: View,
        xOffset: Float,
        yOffset: Float,
        popItems: MutableList<PopItem>,
        rightDirection: Boolean = true
    ) {
        if (!popClickEnable) return
        val popupWindowWidth = 238f.dp //内容宽度加外围
        val barWidth = 40f.dp          // 柱子宽度
        val baseHeight = 90f.dp        // 最小高度
        val popupWindowHeight =
            max(baseHeight, (80f.dp + popItems.size * 28f.dp))
        val bgPadding = 7f.dp     //
        var contentView: View
        if (popItems.size == 1) {
            //非 .9图片背景  需要重新布局
            contentView =
                LayoutInflater.from(requireContext())
                    .inflate(R.layout.hr_pop_window_layout_single, null)
        } else {
            // .9背景
            contentView =
                LayoutInflater.from(requireContext()).inflate(R.layout.hr_pop_window_layout, null)
            contentView.background = resources.getDrawable(R.drawable.bg_dialog_bubble)
        }
        val linearLayout = contentView.findViewById<LinearLayout>(R.id.lo_pop_content)
        repeat(popItems.size) {
            val view = generatePopItemView(popItems[it])
            val layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            val heightInPixels = 28f.dp.toInt()
            layoutParams.height = heightInPixels
            view.setLayoutParams(layoutParams)
            linearLayout.addView(view)
        }
        var offsetX = xOffset - bgPadding
        var offsetY = yOffset
        if (!rightDirection) {
            // .9生成两个时报错了  所以选择直接翻转
            contentView.scaleX = -1f
            linearLayout.scaleX = -1f
            offsetX = offsetX - popupWindowWidth - barWidth + bgPadding * 2
        }
        popupWindow =
            PopupWindow(contentView, popupWindowWidth.toInt(), popupWindowHeight.toInt(), false)
        popupWindow!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        val location = IntArray(2)
        anchorView.getLocationOnScreen(location)
        val xPos = location[0]
        val yPos = location[1]
        popupWindow!!.setOutsideTouchable(true)
        popupWindow!!.setTouchable(true)
        popupWindow!!.showAtLocation(
            anchorView.getRootView(),
            Gravity.NO_GRAVITY,
            xPos + offsetX.toInt(),
            yPos + offsetY.toInt() - ((popupWindowHeight - 16f.dp) / 2).toInt()
        )
        contentView.setOnTouchListener { v, event ->
            val location = IntArray(2)
            v.getLocationOnScreen(location)
            val width = v.width
            val height = v.height
            var hotRect: Rect
            var edgeRect: Rect
            if (rightDirection) {
                hotRect = Rect(
                    location[0],
                    location[1],
                    location[0] + width,
                    location[1] + height
                )
                edgeRect = Rect(
                    hotRect.left - 0,
                    hotRect.top + 10f.dp.toInt(),
                    hotRect.right - 20f.dp.toInt(),
                    hotRect.bottom - 28f.dp.toInt()
                )
            } else {
                hotRect = Rect(
                    location[0] - width,
                    location[1],
                    location[0],
                    location[1] + height
                )
                edgeRect = Rect(
                    hotRect.left + 20f.dp.toInt(),
                    hotRect.top + 10f.dp.toInt(),
                    hotRect.right,
                    hotRect.bottom - 28f.dp.toInt()
                )
            }

            if (event.action == MotionEvent.ACTION_DOWN) {
                val x = event.rawX
                val y = event.rawY
                if (hotRect.contains(x.toInt(), y.toInt()) && !edgeRect.contains(
                        x.toInt(),
                        y.toInt()
                    )
                ) {
                    if (popupWindow!!.isShowing) {
                        popupWindow!!.dismiss()
                    }
                    true
                } else {
                    false
                }
            } else {
                false
            }
        }
    }

    fun generatePopItemView(popItem: PopItem): View {
        val itemView: View =
            LayoutInflater.from(requireContext()).inflate(R.layout.pop_window_item, null)
        val leftTextView = itemView.findViewById<TextView>(R.id.tv_left)
        val rightTextView = itemView.findViewById<TextView>(R.id.tv_right)
        leftTextView.text = popItem.leftText
        rightTextView.text = popItem.rightText
        leftTextView.setTextColor(adjustAlpha(popItem.textColor, 153))
        rightTextView.setTextColor(adjustAlpha(popItem.textColor))
        return itemView
    }

    data class PopItem(
        val leftText: String,
        val rightText: String,
        val textColor: Int
    )


    fun dealAllData(
        influenceFactorList: ArrayList<AnnengInfluenceFactor>?,
        sleepLevel: String?,
        sleepText: String?
    ) {
        if (influenceFactorList.isNullOrEmpty()) {
            chartDataList.add(StatisticsItem("心率", false, 100, 0, 0))
            dealSleepData(sleepText, sleepLevel)
            chartDataList.add(StatisticsItem("血氧", false, 100, 0, 0))
            chartDataList.add(StatisticsItem("压力", false, 100, 0, 0))
            chartDataList.add(StatisticsItem("体温", false, 100, 0, 0))
            chartDataList.add(StatisticsItem("血压", false, 100, 0, 0))
        } else {
            var rList = influenceFactorList.filter { it.name == "心率" }
            if (rList.isNotEmpty()) {
                dealHeartRateData(rList[0])
            } else {
                dealHeartRateData(null)
            }
            dealSleepData(sleepText, sleepLevel)
            rList = influenceFactorList.filter { it.name == "血氧" }
            if (rList.isNotEmpty()) {
                dealSpo2Data(rList[0])
            } else {
                dealSpo2Data(null)
            }
            rList = influenceFactorList.filter { it.name == "压力" }
            if (rList.isNotEmpty()) {
                dealPressureData(rList[0])
            } else {
                dealPressureData(null)
            }
            rList = influenceFactorList.filter { it.name == "体温" }
            if (rList.isNotEmpty()) {
                dealTempData(rList[0])
            } else {
                dealTempData(null)
            }
            rList = influenceFactorList.filter { it.name == "血压" }
            if (rList.isNotEmpty()) {
                dealBloodPressureData(rList[0])
            } else {
                dealBloodPressureData(null)
            }
        }

    }

    /**
     * 心率数据筛选排序
     */
    fun dealHeartRateData(influenceFactor: AnnengInfluenceFactor?) {
        if (influenceFactor == null) {
            chartDataList.add(StatisticsItem("心率", false, 100, 0, 0))
        } else {
            val proportion = influenceFactor.proportion
            val items: MutableList<PopItem> = mutableListOf()
            if (proportion != null) {
                try {
                    var fVal = 0
                    var sVal = 0
                    var tVal = 0
                    val normal = proportion.filter { it.levelCode == "0" }
                    if (normal.isNotEmpty()) {
                        val normalVal = normal[0].levelProportion?.replace("%", "")!!.toInt()
                        if (normalVal != null && normalVal > 0) {
                            val per = "${normalVal}%"
                            items.add(PopItem("正常", per, normalTextColor))
                            fVal += normalVal
                        }
                    }
                    val lower = proportion.filter { it.levelCode == "1" }
                    if (lower.isNotEmpty()) {
                        val lowerVal = lower[0].levelProportion?.replace("%", "")!!.toInt()
                        if (lowerVal != null && lowerVal > 0) {
                            val per = "${lowerVal}%"
                            items.add(PopItem("疑似心动过缓", per, middleTextColor))
                            sVal += lowerVal
                        }
                    }
                    val higher = proportion.filter { it.levelCode == "2" }
                    if (higher.isNotEmpty()) {
                        val higherVal = higher[0].levelProportion?.replace("%", "")!!.toInt()
                        if (higherVal != null && higherVal > 0) {
                            val per = "${higherVal}%"
                            items.add(PopItem("疑似心动过速", per, middleTextColor))
                            sVal += higherVal
                        }
                    }
                    val err = proportion.filter { it.levelCode == "-1" }
                    if (err.isNotEmpty()) {
                        val errVal = err[0].levelProportion?.replace("%", "")!!.toInt()
                        if (errVal != null && errVal > 0) {
                            val per = "${errVal}%"
                            items.add(PopItem("疑似心动过速", per, highTextColor))
                            tVal += errVal
                        }
                    }
                    if (fVal == 0 && sVal == 0 && tVal == 0) {
                        chartDataList.add(StatisticsItem("心率", false, fVal, sVal, tVal))
                    } else {
                        heartRateItems = items
                        chartDataList.add(StatisticsItem("心率", true, fVal, sVal, tVal))
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    chartDataList.add(StatisticsItem("心率", false, 100, 0, 0))
                }

            }
        }
    }

    /**
     * 血压数据筛选排序
     */
    fun dealBloodPressureData(influenceFactor: AnnengInfluenceFactor?) {
        if (influenceFactor == null) {
            chartDataList.add(StatisticsItem("血压", false, 100, 0, 0))
        } else {
            val proportion = influenceFactor.proportion
            val items: MutableList<PopItem> = mutableListOf()
            if (proportion != null) {
                try {
                    var fVal = 0
                    var sVal = 0
                    var tVal = 0
                    val normal = proportion.filter { it.levelCode == "1" }
                    if (normal.isNotEmpty()) {
                        val normalVal = normal[0].levelProportion?.replace("%", "")!!.toInt()
                        if (normalVal != null && normalVal > 0) {
                            val per = "${normalVal}%"
                            items.add(PopItem("正常", per, normalTextColor))
                            fVal += normalVal
                        }
                    }

                    val lower = proportion.filter { it.levelCode == "0" }
                    if (lower.isNotEmpty()) {
                        val lowerVal = lower[0].levelProportion?.replace("%", "")!!.toInt()
                        if (lowerVal != null && lowerVal > 0) {
                            val per = "${lowerVal}%"
                            items.add(PopItem("疑似低血压", per, middleTextColor))
                            sVal += lowerVal
                        }
                    }

                    val higher = proportion.filter { it.levelCode == "2" }
                    if (higher.isNotEmpty()) {
                        val higherVal = higher[0].levelProportion?.replace("%", "")!!.toInt()
                        if (higherVal != null && higherVal > 0) {
                            val per = "${higherVal}%"
                            items.add(PopItem("疑似正常高值", per, middleTextColor))
                            sVal += higherVal
                        }
                    }

                    val err = proportion.filter { it.levelCode == "3" }
                    if (err.isNotEmpty()) {
                        val errVal = err[0].levelProportion?.replace("%", "")!!.toInt()
                        if (errVal != null && errVal > 0) {
                            val per = "${errVal}%"
                            items.add(PopItem("疑似一级高血压", per, highTextColor))
                            tVal += errVal
                        }
                    }

                    val err2 = proportion.filter { it.levelCode == "4" }
                    if (err2.isNotEmpty()) {
                        val errVal2 = err2[0].levelProportion?.replace("%", "")!!.toInt()
                        if (errVal2 != null && errVal2 > 0) {
                            val per = "${errVal2}%"
                            items.add(PopItem("疑似二级高血压", per, highTextColor))
                            tVal += errVal2
                        }
                    }

                    val err3 = proportion.filter { it.levelCode == "5" }
                    if (err3.isNotEmpty()) {
                        val errVal3 = err3[0].levelProportion?.replace("%", "")!!.toInt()
                        if (errVal3 != null && errVal3 > 0) {
                            val per = "${errVal3}%"
                            items.add(PopItem("疑似三级高血压", per, highTextColor))
                            tVal += errVal3
                        }
                    }

                    if (fVal == 0 && sVal == 0 && tVal == 0) {
                        chartDataList.add(StatisticsItem("血压", false, fVal, sVal, tVal))
                    } else {
                        bloodPressureItems = items
                        chartDataList.add(StatisticsItem("血压", true, fVal, sVal, tVal))
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    chartDataList.add(StatisticsItem("血压", false, 100, 0, 0))
                }
            }
        }
    }

    /**
     * 血氧数据筛选排序
     */
    fun dealSpo2Data(influenceFactor: AnnengInfluenceFactor?) {
        if (influenceFactor == null) {
            chartDataList.add(StatisticsItem("血氧", false, 100, 0, 0))
        } else {
            val proportion = influenceFactor.proportion
            val items: MutableList<PopItem> = mutableListOf()
            if (proportion != null) {
                try {
                    var fVal = 0
                    var sVal = 0
                    var tVal = 0
                    val normal = proportion.filter { it.levelCode == "0" }
                    if (normal.isNotEmpty()) {
                        val normalVal = normal[0].levelProportion?.replace("%", "")!!.toInt()
                        if (normalVal != null && normalVal > 0) {
                            val per = "${normalVal}%"
                            items.add(PopItem("正常", per, normalTextColor))
                            fVal += normalVal
                        }
                    }

                    val lower = proportion.filter { it.levelCode == "1" }
                    if (lower.isNotEmpty()) {
                        val lowerVal = lower[0].levelProportion?.replace("%", "")!!.toInt()
                        if (lowerVal != null && lowerVal > 0) {
                            val per = "${lowerVal}%"
                            items.add(PopItem("较低", per, middleTextColor))
                            sVal += lowerVal
                        }
                    }


                    val err = proportion.filter { it.levelCode == "2" }
                    if (err.isNotEmpty()) {
                        val errVal = err[0].levelProportion?.replace("%", "")!!.toInt()
                        if (errVal != null && errVal > 0) {
                            val per = "${errVal}%"
                            items.add(PopItem("过低", per, highTextColor))
                            tVal += errVal
                        }
                    }

                    if (fVal == 0 && sVal == 0 && tVal == 0) {
                        chartDataList.add(StatisticsItem("血氧", false, fVal, sVal, tVal))
                    } else {
                        spo2Items = items
                        chartDataList.add(StatisticsItem("血氧", true, fVal, sVal, tVal))
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    chartDataList.add(StatisticsItem("血氧", false, 100, 0, 0))
                }
            }
        }
    }

    /**
     * 体温数据筛选排序
     */
    fun dealTempData(influenceFactor: AnnengInfluenceFactor?) {
        if (influenceFactor == null) {
            chartDataList.add(StatisticsItem("体温", false, 100, 0, 0))
        } else {
            val proportion = influenceFactor.proportion
            val items: MutableList<PopItem> = mutableListOf()
            if (proportion != null) {
                try {
                    var fVal = 0
                    var sVal = 0
                    var tVal = 0
                    val normal = proportion.filter { it.levelCode == "0" }
                    if (normal.isNotEmpty()) {
                        val normalVal = normal[0].levelProportion?.replace("%", "")!!.toInt()
                        if (normalVal != null && normalVal > 0) {
                            val per = "${normalVal}%"
                            items.add(PopItem("正常", per, normalTextColor))
                            fVal += normalVal
                        }
                    }
                    val lower = proportion.filter { it.levelCode == "1" }
                    if (lower.isNotEmpty()) {
                        val lowerVal = lower[0].levelProportion?.replace("%", "")!!.toInt()
                        if (lowerVal != null && lowerVal > 0) {
                            val per = "${lowerVal}%"
                            items.add(PopItem("低热", per, middleTextColor))
                            sVal += lowerVal
                        }
                    }


                    val err = proportion.filter { it.levelCode == "2" }
                    if (err.isNotEmpty()) {
                        val errVal = err[0].levelProportion?.replace("%", "")!!.toInt()
                        if (errVal != null && errVal > 0) {
                            val per = "${errVal}%"
                            items.add(PopItem("中高热", per, highTextColor))
                            tVal += errVal
                        }
                    }

                    if (fVal == 0 && sVal == 0 && tVal == 0) {
                        chartDataList.add(StatisticsItem("体温", false, fVal, sVal, tVal))
                    } else {
                        tempItems = items
                        chartDataList.add(StatisticsItem("体温", true, fVal, sVal, tVal))
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    chartDataList.add(StatisticsItem("体温", false, 100, 0, 0))
                }
            }
        }
    }

    /**
     * 压力数据筛选排序
     */
    fun dealPressureData(influenceFactor: AnnengInfluenceFactor?) {
        if (influenceFactor == null) {
            chartDataList.add(StatisticsItem("压力", false, 100, 0, 0))
        } else {
            val proportion = influenceFactor.proportion
            val items: MutableList<PopItem> = mutableListOf()
            if (proportion != null) {
                try {
                    var fVal = 0
                    var sVal = 0
                    var tVal = 0
                    val normal = proportion.filter { it.levelCode == "0" }
                    if (normal.isNotEmpty()) {
                        val normalVal = normal[0].levelProportion?.replace("%", "")!!.toInt()
                        if (normalVal != null && normalVal > 0) {
                            val per = "${normalVal}%"
                            items.add(PopItem("正常", per, normalTextColor))
                            fVal += normalVal
                        }
                    }
                    val normal2 = proportion.filter { it.levelCode == "1" }
                    if (normal2.isNotEmpty()) {
                        val normalVal2 = normal2[0].levelProportion?.replace("%", "")!!.toInt()
                        if (normalVal2 != null && normalVal2 > 0) {
                            val per = "${normalVal2}%"
                            items.add(PopItem("放松", per, normalTextColor))
                            fVal += normalVal2
                        }
                    }

                    val lower = proportion.filter { it.levelCode == "2" }
                    if (lower.isNotEmpty()) {
                        val lowerVal = lower[0].levelProportion?.replace("%", "")!!.toInt()
                        if (lowerVal != null && lowerVal > 0) {
                            val per = "${lowerVal}%"
                            items.add(PopItem("中等", per, middleTextColor))
                            sVal += lowerVal
                        }

                    }

                    val err = proportion.filter { it.levelCode == "3" }
                    if (err.isNotEmpty()) {
                        val errVal = err[0].levelProportion?.replace("%", "")!!.toInt()
                        if (errVal != null && errVal > 0) {
                            val per = "${errVal}%"
                            items.add(PopItem("偏高", per, highTextColor))
                            tVal += errVal
                        }
                    }

                    if (fVal == 0 && sVal == 0 && tVal == 0) {
                        chartDataList.add(StatisticsItem("压力", false, fVal, sVal, tVal))
                    } else {
                        pressureItems = items
                        chartDataList.add(StatisticsItem("压力", true, fVal, sVal, tVal))
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    chartDataList.add(StatisticsItem("压力", false, 100, 0, 0))
                }
            }
        }
    }

    /**
     * 睡眠数据筛选排序
     * 睡眠数据有些特殊 只有一种取值
     */
    fun dealSleepData(sleepText: String?, sleepLevel: String?) {
        if (sleepLevel == null) {
            chartDataList.add(StatisticsItem("睡眠", false, 100, 0, 0))
        } else {
            val items: MutableList<PopItem> = mutableListOf()
            when (sleepLevel) {
                "2" -> {
                    items.add(PopItem(sleepText ?: "较好", "100%", normalTextColor))
                    chartDataList.add(StatisticsItem("睡眠", true, 100, 0, 0))
                }

                "1" -> {
                    items.add(PopItem(sleepText ?: "一般", "100%", middleTextColor))
                    chartDataList.add(StatisticsItem("睡眠", true, 0, 100, 0))
                }

                "0" -> {
                    items.add(PopItem(sleepText ?: "较差", "100%", highTextColor))
                    chartDataList.add(StatisticsItem("睡眠", true, 0, 0, 100))
                }

            }
            sleepItems = items
        }
    }


    fun adjustAlpha(color: Int, alpha: Int = 255): Int {
        return Color.argb(alpha, color.red, color.green, color.blue)
    }

    /**
     * 查看更多样式调整  增加下划线
     */
    fun adjustSeeMoreText() {
        val underlineText = Html.fromHtml("<u>查看更多</u>")
        binding.tvHealthReportSeeMore1.text = underlineText
        binding.tvHealthReportSeeMore2.text = underlineText
        binding.tvHealthReportSeeMore3.text = underlineText
        binding.tvHealthReportSeeMore4.text = underlineText
        binding.tvHealthReportSeeMore5.text = underlineText
    }

    /**
     * 获取周的数据范围，并根据格式返回。
     */
    fun getWeekDateRange(format: String): String {
        val calendar = Calendar.getInstance()

        // Set the calendar to the start of the week (Monday)
        calendar.firstDayOfWeek = Calendar.MONDAY
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
        // Get the start date of the week
        val startDate = LocalDate.of(
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH) + 1,
            calendar.get(Calendar.DAY_OF_MONTH)
        )

        // Get the end date of the current week (Sunday)
        val endDate = startDate.plusDays(6)

        // Format the dates
        val formatter = DateTimeFormatter.ofPattern(format)
        val sT = startDate.format(formatter)
        val eT = endDate.format(formatter)

        // Return the formatted date range
        return "$sT-$eT"
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.tvHealthEstimateContent.setCallback(null)
        binding.tvHealthAdviceContent.setCallback(null)
        binding.tvMoreText.setCallback(null)
        binding.tvContrastContent.setCallback(null)
        binding.tvHiddenText.setInterface(null)
        binding.tvHealthSummarize.setCallback(null)
    }

}