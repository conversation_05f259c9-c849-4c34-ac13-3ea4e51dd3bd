package com.healthlink.hms.fragment

import android.app.Dialog
import android.graphics.Rect
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.TouchDelegate
import android.view.View
import android.view.ViewTreeObserver
import android.view.ViewGroup.MarginLayoutParams
import android.widget.Button
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.ClickUtils
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.activity.card.HMSCardFragmentInteractWithAcInterface
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.databinding.FragmentCardPressureBinding
import com.healthlink.hms.fragment.viewmodel.PressureFragmentModel
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.HealthDataStatusDTO
import com.healthlink.hms.core.model.dto.charts.pressure.PressureCard1DTO
import com.healthlink.hms.core.model.dto.charts.pressure.PressureCard2DTO
import com.healthlink.hms.core.model.dto.charts.pressure.PressureCard3DTO
import com.healthlink.hms.core.model.dto.charts.pressure.PressureDetailRespDTO
import com.healthlink.hms.core.model.dto.charts.pressure.PressureStatItemDTO
import com.healthlink.hms.core.model.dto.charts.pressure.PressureSummaryDTO
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.TimeUtils.getMonthListStr
import com.healthlink.hms.utils.TimeUtils.getXDataWithStep
import com.healthlink.hms.utils.getPrivacyModeDate
import com.healthlink.hms.viewmodels.MainViewModel
import com.healthlink.hms.views.ImmersiveDialog
import com.healthlink.hms.views.MiddleEllipsesTextView
import com.healthlink.hms.views.charts.PressureBarChart
import java.lang.ref.WeakReference
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Calendar

/**
 * 压力 - 二级页面
 */
class CardPressureFragment : BaseCardFragment<FragmentCardPressureBinding, MainViewModel>(
    MainViewModel::class.java,
    R.layout.fragment_card_pressure
), MiddleEllipsesTextView.UpdateSeeMore {
    private var chartDataList = arrayListOf<PressureStatItemDTO>()
    private var summaryDTO: PressureSummaryDTO? = null
    private var wmyAllDataList = arrayListOf<PressureStatItemDTO?>()
    private var wmyAllSummaryDataList = arrayListOf<PressureSummaryDTO?>()
    private var reqMap = mapOf<String, String>()
    private var healthAdviceStr = ""
    private var introString = ""
    private var isProvacyMode = false
    private var isNoDataMode = false

    private var fragmentDataModel = PressureFragmentModel()

    companion object {
        const val ARG_PARAM_TYPE = "ARG_PARAM_TYPE"
        private val fragmentInteractWithAC
            get() = _fragmentInteractWithAC?.get()
        private var _fragmentInteractWithAC: WeakReference<HMSCardFragmentInteractWithAcInterface>? =
            null
        private lateinit var mUserId: String
        private const val TAG = "CardPressureFragment"

        fun newInstance(
            cartTimeType: TimeCode,
            userId: String,
            interact: HMSCardFragmentInteractWithAcInterface
        ): CardPressureFragment {
            val fragment = CardPressureFragment()
            val args = Bundle()
            args.putString(ARG_PARAM_TYPE, cartTimeType.timeCode)
            fragment.arguments = args
            mUserId = userId
            _fragmentInteractWithAC = WeakReference(interact)
            return fragment
        }
    }

    /**
     * 获取中间卡片默认值
     */
    fun getCenterCardTitleDefault(): String {
        return if (cardTimeType == TimeCode.TIME_CODE_DAY.timeCode) "当日压力平均值" else "压力平均值"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            cardTimeType = it.getString(ARG_PARAM_TYPE)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()
        initLiveDataObserve()
        // 确保binding被初始化
        binding.curFragment = this
        // 恢复数据
        if(savedInstanceState!=null){
            restoreDataIfPossible(savedInstanceState)
        }
    }

    private fun restoreDataIfPossible(savedInstanceState: Bundle) {
        try {
            var fragmentSavedData = savedInstanceState.getString(KEY_SAVED_DATA_SUMMARY)
            if (fragmentSavedData != null) {
                var fragmentDataModel =
                    gson.fromJson(fragmentSavedData, PressureFragmentModel::class.java)
                if(fragmentDataModel!=null){
                    this.fragmentDataModel = fragmentDataModel
                    binding.svContainer.scrollY = this.fragmentDataModel.scollY
                    Log.i(TAG, "init CardTimeFragmnet data from saved fragment success.")
                }
            }

            if(savedInstanceState.getBoolean(KEY_SHOW_DIALOG_FLAG)) {
                showExplainDialog()
            }
        }catch (ex: Exception){
            Log.i(TAG, "init CardTimeFragmnet data from saved fragment fail. error : ${ex.message}")
        }
    }

    override fun onResume() {
        super.onResume()
        if (!HmsApplication.isPrivacyModeEnabled()) {
            binding.cBarChart.visibility = View.VISIBLE
            setScrollEnable(binding.svContainer)
            setViewVisable()
        } else binding.cBarChart.visibility = View.GONE
        if (isDataReady)
            sendRequest(mUserId, cardTimeType!!)
        else
            handler.post(runnable)
    }

    override fun onPause() {
        super.onPause()
        handler.removeCallbacks(runnable)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        // 保存数据
        if(this.fragmentDataModel!=null
            && this.fragmentDataModel.healthData!=null
            && this.fragmentDataModel.healthData!!.code == "0"
            && fragmentDataModel.healthSummaryResponse!=null
            && fragmentDataModel.healthSummaryResponse!!.code == "0"
        ){
            fragmentDataModel.scollY = binding.svContainer.scrollY
            outState.putString(KEY_SAVED_DATA_SUMMARY, gson.toJson(fragmentDataModel));
        }

        outState.putBoolean(KEY_SHOW_DIALOG_FLAG, fragmentDataModel.isShowExplainDialog)
    }

    override fun sendRequest(userId: String, timeCode: String) {
        reqMap = mapOf(
            "userId" to userId,
            "unit" to timeCode
        )
        if (!HmsApplication.isPrivacyModeEnabled()) {
            if (!HmsApplication.isNetworkConn()) {
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                if (isDataReady) showLoading()

                if(this.fragmentDataModel!=null
                    && this.fragmentDataModel.healthData!=null
                    && this.fragmentDataModel.healthData!!.code == "0"){
                    processHealthData(this.fragmentDataModel.healthData!!)
                }else {
                    viewModel?.sendPressureDetailDataReq(reqMap)
                }
            }
        } else initPrivacyUI(timeCode)
    }

    override fun sendDataReadyRequest(userId: String, timeCode: String) {
        isProvacyMode = HmsApplication.isPrivacyModeEnabled()
        if (!isProvacyMode) {
            if (!HmsApplication.isNetworkConn()) {
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            }else{
                showLoading()
            }
        }
        when (timeCode) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Stressreports_Daytab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.tvIntroRange.text = "当日压力平均值"
                binding.tvIntroRangeValue.text = "--"
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Stressreports_Weektab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.cBarChart.setXData(
                    TimeUtils.getWeekListStr(),
                    TimeCode.TIME_CODE_WEEK.timeCode
                )
                binding.tvIntroRange.text = "压力平均值"
                binding.tvIntroRangeValue.text = "--"
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Stressreports_Mouthtab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.cBarChart.setXData(
                    TimeUtils.getMonthListStr(),
                    TimeCode.TIME_CODE_MONTH.timeCode
                )
                binding.tvIntroRange.text = "压力平均值"
                binding.tvIntroRangeValue.text = "--"
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Stressreports_Yeartab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.cBarChart.setXData(
                    TimeUtils.getMonthListStr(),
                    TimeCode.TIME_CODE_YEAR.timeCode
                )
                binding.tvIntroRange.text = "压力平均值"
                binding.tvIntroRangeValue.text = "--"
            }
        }
        applyPrivacyStyleChanged(isProvacyMode)
        if (!isProvacyMode) {
            setScrollEnable(binding.svContainer, false)
            viewModel.getHistoryStatusData(userId)
        } else
            initPrivacyUI(timeCode)
    }

    private val runnable = UpdateRunnable(this)
    private class UpdateRunnable(private val fragment: CardPressureFragment) : Runnable {
        private val weakFragment = WeakReference(fragment)

        override fun run() {
            val fragment = weakFragment.get()
            if (fragment != null && !fragment.isDetached) {
                if (!fragment.isDataReady) {
                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
                    // 15 秒后再次调用
                    if (HmsApplication.isNetworkConn())
                        fragment.handler.postDelayed(this, 15000)
                }
            }
        }
    }

//    private val runnable = object : Runnable {
//        private val weekFragment = WeakReference(this@CardPressureFragment)
//        override fun run() {
//            val fragment = weekFragment.get()
//            if (fragment != null && !fragment.isDetached) {
//                if (!fragment.isDataReady) {
//                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
//                    // 15 秒后再次调用
//                    if (HmsApplication.isNetworkConn())
//                        fragment.handler.postDelayed(this, 15000)
//                }
//            }
//        }
//    }

    fun getDataReady() {
        isDataReady = true
        handler.removeCallbacks(runnable)
        sendRequest(mUserId, cardTimeType!!)
    }

    fun readyDataNoAuth() {
        handler.removeCallbacks(runnable)
        showNoAuthView()
        fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
    }

    private fun initLiveDataObserve() {
        viewModel.healthHistoryData.observe(viewLifecycleOwner) {
            val notReadyText =
                "压力${requireContext().resources.getString(R.string.text_data_not_ready)}"
            if (it.code == "0" && it.data != null) {
                val statusList = it.data?.dataStatusList
                if (statusList.isNullOrEmpty()) //如果是空的 也认为是有数据的
                {
                    getDataReady()
                    return@observe
                }
                var status: HealthDataStatusDTO? = null
                if (!statusList.isNullOrEmpty()) {
                    val statusArray = statusList.filter { it.dataType == "stressRead" }
                    if (!statusArray.isNullOrEmpty()) {
                        status = statusArray[0]
                    } else {
                        getDataReady()
                        return@observe
                    }
                }
                if (status != null) {
                    when (cardTimeType) {
                        TimeCode.TIME_CODE_DAY.timeCode -> {
                            if (status.dayDataStatus == null || status.dayDataStatus == 2) {
                                //如果返回值是null  也认为是有值的
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_WEEK.timeCode -> {
                            if (status.weekDataStatus == null || status.weekDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_MONTH.timeCode -> {
                            if (status.monthDataStatus == null || status.monthDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_YEAR.timeCode -> {
                            if (status.yearDataStatus == null || status.yearDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }
                    }
                } else {
                    showNetErrorOrSettingView()
                    fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
                }
            } else if (it.code == "5") {
                showNoAuthView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                // 无网络或者刷新失败处理
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            }
        }

        viewModel.getPressureChartLiveData().observe(viewLifecycleOwner) {
            this.fragmentDataModel.healthData = it
            processHealthData(it)
        }

        viewModel.getPressureSummaryLiveData().observe(viewLifecycleOwner) {
            // 缓存
            this.fragmentDataModel.healthSummaryResponse = it
            // 处理
            processSummaryData(it)
        }
    }

    private fun processHealthData(it: BaseResponse<PressureDetailRespDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            val dto = it.data!!
            var okRes =
                it.data!!.nodeList != null && it.data!!.nodeList.isNotEmpty() && isDataListNotEmpty(
                    it.data!!.nodeList
                )
    //                okRes = false
            isNoDataMode = false
            if (okRes) {
                // 显示最后一个有值的数据
                for (element in it.data!!.nodeList.reversed()) {
                    if (element?.avg != null) {
                        bindingDataToDWMYShowCard(element)
                        break
                    }
                }
                binding.tvNoDataText.visibility = View.GONE
                chartDataList = it.data!!.nodeList
                initBarChart(cardTimeType!!)
                // 压力健康建议
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)

                if (dto.startTime?.isNotEmpty() == true) {
                    map["startTime"] = dto.startTime!!
                }
                if (dto.endTime?.isNotEmpty() == true) {
                    map["endTime"] = dto.endTime!!
                }

                // 处理建议数据
                processLoadingSummaryData(map)

            } else {
    //                    initChartNoData()
                isNoDataMode = true
                initBarChart(cardTimeType!!)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
    //                initChartNoData()
            // 无网络或者刷新失败处理
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, if (cardTimeType == TimeCode.TIME_CODE_YEAR.timeCode) 500 else 400)
    }

    /**
     * 判断是否有合理的健康建议数据。如果有，则直接使用，否则去服务器获取
     */
    private fun processLoadingSummaryData(map: MutableMap<String, String>) {
        if(fragmentDataModel==null
            || fragmentDataModel.healthSummaryResponse == null
            || fragmentDataModel.healthSummaryResponse!!.code != "0") {
            viewModel.sendPressureSummaryDataReq(map)
        }else{
            processSummaryData(fragmentDataModel.healthSummaryResponse!!)
        }
    }

    private fun processSummaryData(it: BaseResponse<PressureSummaryDTO>) {
        if (it.code == "0" && it.data != null) {
            summaryDTO = it.data
            binding.pressureSummaryVo = summaryDTO
            binding.svContainer.post {
                binding.svContainer.requestLayout()
            }
            Handler(Looper.getMainLooper()).postDelayed({
                hideLoading()
                setScrollEnable(binding.svContainer)
            }, 100)
            // 重置查看更多按钮为不可见
            binding.healthRiskAll.tvHealthAdviceContent.setIsEllipsized(false)

            it.data!!.healthAdvice?.let { item ->
                healthAdviceStr = item
            }
    //                it.data!!.nounExplain?.let { explain ->
    //                    if (explain.isNotEmpty()){
    //                        this.introString=explain
    //                        binding.ivIntroTips.visibility=View.VISIBLE
    //                    }
    //                }
            // 绑定第二张卡片
            val avgTitle =
                if (cardTimeType == TimeCode.TIME_CODE_DAY.timeCode) "当日压力平均值" else "压力平均值"
            val card2DTO = PressureCard2DTO(
                avgTitle,
                summaryDTO?.pressureAvg,
                summaryDTO?.pressureType
            )
            binding.card2DTO = card2DTO

            // 绑定第三张卡片
            // 日视图只赋值一次从接口的外层获取数据
    //                if (cardTimeType == TimeCode.TIME_CODE_DAY.timeCode) {
            val card3DTO = PressureCard3DTO(
                summaryDTO?.highSum,
                summaryDTO?.mediumSum,
                summaryDTO?.normalSum,
                summaryDTO?.relaxSum,
                summaryDTO?.highProp,
                summaryDTO?.mediumProp,
                summaryDTO?.normalProp,
                summaryDTO?.relaxProp
            )
            binding.card3DTO = card3DTO
    //                } else {
    //                    // 周月年视图赋值从接口的valueList里获取数据
    //                    if (summaryDTO?.valueList != null && summaryDTO?.valueList!!.isNotEmpty()) {
    //                        // 显示最新有数据的一条
    //                        // 倒序遍历 summaryDTO.valueList
    //                        for (element in summaryDTO?.valueList!!.reversed()) {
    //                            if (element?.highSum != null) {
    //                                bindingCard3DTO(element)
    //                                break
    //                            }
    //                        }
    //
    //                        // 周月年视图下需要遍历插值存储数据
    //                        if (cardTimeType == TimeCode.TIME_CODE_WEEK.timeCode) {
    //                            storeAllWMYSummaryData(
    //                                summaryDTO?.valueList!!,
    //                                TimeUtils.getCurrentWeekDates("yyyy年MM月dd日")
    //                            )
    //                        } else if (cardTimeType == TimeCode.TIME_CODE_MONTH.timeCode) {
    //                            storeAllWMYSummaryData(
    //                                summaryDTO?.valueList!!,
    //                                TimeUtils.getCurrentMonthDates("yyyy年MM月dd日")
    //                            )
    //                        } else {
    //                            storeAllWMYSummaryData(
    //                                summaryDTO?.valueList!!,
    //                                TimeUtils.getAllMonthsOfCurrentYear("yyyy-MM")
    //                            )
    //                        }
    //                    }
    //                }
        }

        // 恢复滚动位置
        if(this.fragmentDataModel.scollY>0) {
            binding.svContainer.post {
                binding.svContainer.scrollY = this.fragmentDataModel.scollY
            }
        }

    }

    /**
     * 将周月年的数据进行插值存储，滑动小圆点是下标时获取卡片数据进行比对
     * @param valueList 原始数据集
     * @param xInteralList X轴全集 原始数据集小于X轴全集
     */
    private fun storeAllWMYSummaryData(
        valueList: ArrayList<PressureSummaryDTO>,
        xInteralList: List<String> = ArrayList()
    ) {
        if (valueList != null && valueList.isNotEmpty()) {
            wmyAllSummaryDataList.clear()
            for ((_, value) in xInteralList.withIndex()) {
                val dto = valueList.find { it.createTime?.contains(value) == true }
                if (dto != null) {
                    wmyAllSummaryDataList.add(dto)
                } else
                    wmyAllSummaryDataList.add(null)
            }
        }
    }

    private fun setCard2defaultValue(): PressureCard2DTO {
        val avgTitle =
            if (cardTimeType == TimeCode.TIME_CODE_DAY.timeCode) "当日压力平均值" else "压力平均值"
        return PressureCard2DTO(
            avgTitle,
            "--",
            ""
        )
    }


    /**
     * 设置日周月年视图选择的监听
     * 同时改变第一张和第三种压力卡片上的数据
     */
    private fun setBarSelectListener() {

        binding.cBarChart.setOnDaySelectListener { index, item ->
            // 变更第一张卡片数据
            if (isNoDataMode || wmyAllDataList.isEmpty() || index >= wmyAllDataList.size) return@setOnDaySelectListener
            if (item != null && index <= wmyAllDataList.size - 1) {
                val lastData = wmyAllDataList[index]
                bindingDataToDWMYShowCard(lastData)
            } else {
                val lastData = wmyAllDataList[index]
                var fetchTime = ""
                if(index>=0&&index<chartDataList.size && chartDataList[index].createTime!=null){
                    fetchTime = chartDataList[index].createTime!!
                }
                bindingDataToDWMYShowCard(lastData, fetchTime)
            }

            // 变更第二、三张卡片数据
            if (cardTimeType != TimeCode.TIME_CODE_DAY.timeCode) {
                // 周月年
                if (wmyAllSummaryDataList.isEmpty()) {
                    return@setOnDaySelectListener
                }
                if (index <= wmyAllSummaryDataList.size - 1) {
                    val lastData = wmyAllSummaryDataList[index]
//                bindingCard2DTO(lastData)
//                    bindingCard3DTO(lastData)
                }
            }
        }

        when (cardTimeType) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                val currentDateStr = getDayFetchTime()
                binding.cBarChart.setOnXTextSelectListener { index, xText ->
                    if (isNoDataMode) {
                        var card1DTO = PressureCard1DTO(
                            currentDateStr + " " + xText,
                            "--",
                            ""
                        )
                        binding.card1DTO = card1DTO
                        binding.card2DTO = setCard2defaultValue()

                        val card3DTO = PressureCard3DTO(
                            summaryDTO?.highSum,
                            summaryDTO?.mediumSum,
                            summaryDTO?.normalSum,
                            summaryDTO?.relaxSum,
                            summaryDTO?.highProp,
                            summaryDTO?.mediumProp,
                            summaryDTO?.normalProp,
                            summaryDTO?.relaxProp
                        )
                        binding.card3DTO = card3DTO
                    }
                }
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                binding.cBarChart.setOnXTextSelectListener { index, xText ->
                    if (isNoDataMode || (wmyAllDataList.isNotEmpty() && index >= wmyAllDataList.size)) {
                        val card1DTO = PressureCard1DTO(
                            TimeUtils.resetWMDateTime(xText),
                            "--",
                            ""
                        )
                        binding.card1DTO = card1DTO
                        binding.card2DTO = setCard2defaultValue()
                    }
                }
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                val monthDayList = TimeUtils.getMonthListStr()
                binding.cBarChart.setOnXTextSelectListener { index, xText ->
                    if (isNoDataMode || (wmyAllDataList.isNotEmpty() && index >= wmyAllDataList.size))
                        try {
                            val card1DTO = PressureCard1DTO(
                                TimeUtils.resetWMDateTime(monthDayList[index]),
                                "--",
                                ""
                            )
                            binding.card1DTO = card1DTO
                            binding.card2DTO = setCard2defaultValue()
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                }
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                binding.cBarChart.setOnXTextSelectListener { index, xText ->
                    if (isNoDataMode || (wmyAllDataList.isNotEmpty() && index >= wmyAllDataList.size)) {
                        val card1DTO = PressureCard1DTO(
                            TimeUtils.resetYDateTime(xText),
                            "--",
                            ""
                        )
                        binding.card1DTO = card1DTO
//                        binding.card2DTO = setCard2defaultValue()
                    }
                }
            }

        }
    }

    //region 初始化压力图表下的卡片数据
    // 绑定第一张卡片日周期数据到压力卡片上
    private fun bindingDataToDWMYShowCard(lastData: PressureStatItemDTO?) {
        bindingDataToDWMYShowCard(lastData, null)
    }

    private fun bindingDataToDWMYShowCard(lastData: PressureStatItemDTO?, currentTime: String?) {
        // TODO last 为空时也需要显示
        var card1DTO = PressureCard1DTO(
            lastData?.createTime ?: if (null == currentTime) "" else currentTime,
            "--",
            ""
        )
        if (lastData?.avg != null) {
            binding.tip.visibility = View.VISIBLE
            card1DTO = PressureCard1DTO(
                lastData.createTime,
                lastData.avg.toString(),
                lastData.pressureType
            )
        }
        binding.card1DTO = card1DTO
    }

    private fun bindingCard2DTO(summaryDTO: PressureSummaryDTO?) {
        if (summaryDTO?.pressureAvg == null) {
            return
        }
        val card2DTO = PressureCard2DTO(
            "压力平均值",
            summaryDTO.pressureAvg,
            summaryDTO.pressureType
        )
        binding.card2DTO = card2DTO
    }

    // 绑定数据到第三张卡片
    private fun bindingCard3DTO(cardStatusDTO: PressureSummaryDTO?) {
        var card3DTO = PressureCard3DTO(
            0,
            0,
            0,
            0,
            "0",
            "0",
            "0",
            "0"
        )
        if (cardStatusDTO?.highSum != null) {
            card3DTO = PressureCard3DTO(
                cardStatusDTO.highSum,
                cardStatusDTO.mediumSum,
                cardStatusDTO.normalSum,
                cardStatusDTO.relaxSum,
                cardStatusDTO.highProp,
                cardStatusDTO.mediumProp,
                cardStatusDTO.normalProp,
                cardStatusDTO.relaxProp
            )
        }
        binding.card3DTO = card3DTO
    }
    //endregion

    private fun initUI() {
        initExplain()
        binding.ivIntroTips.findViewById<ImageView>(R.id.iv_intro_tips)
        ClickUtils.applySingleDebouncing(binding.ivIntroTips, 1000) {
//            showHmsDialog(R.layout.hms_dialog_tips_small, "压力说明", introString)
            showExplainDialog()
        }
        binding.healthRiskAll.tvHealthAdviceContent.setCallback(this)
        binding.healthRiskAll.tvHealthAdviceContent.setEndPercentage(80)
        binding.healthRiskAll.tvSeeMore.text = Html.fromHtml("<u>查看更多</u>")
        val scrollView = binding.svContainer
        scrollView.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            val height = scrollView.getChildAt(0).height // 获取ScrollView内容的总高度
            val scrollViewHeight = scrollView.height // 获取ScrollView的可见高度
            val diff = (height - scrollViewHeight) * 0.75f //scrollview中判定的距离 动画view位置底部约为总长度的的75%
            if (scrollY >= diff) {
                MMKVUtil.getUserId()?.let {
                    DataTrackUtil.dtScroll(
                        "Health_Stressreports_Bottom_Show",
                        DataTrackUtil.userIDMap(it)
                    )
                }
            }
        }

        // 图表移动事件回调
        setBarSelectListener()
    }

    //region 压力图表
    /**
     * 心率周、月、年图表
     */
    private fun initBarChart(cartTimeType: String) {
        binding.tvNoDataText.visibility = View.GONE
        binding.tvPrivacyText.visibility = View.GONE
        binding.cBarChart.visibility = View.VISIBLE
        // 处理私密模式无数据 -> 无数据、
        binding.tvHighPer.text = "0%"
        binding.tvMiddlePer.text = "0%"
        binding.tvNormalPer.text = "0%"
        binding.tvRelaxPer.text = "0%"
        when (cartTimeType) {
            // 日
            TimeCode.TIME_CODE_DAY.timeCode -> {
                initDayBarChart()
            }
            // 周
            TimeCode.TIME_CODE_WEEK.timeCode -> {
                initWeekBarChart()
            }
            // 月
            TimeCode.TIME_CODE_MONTH.timeCode -> {
                initMonthBarChart()
            }
            // 年
            TimeCode.TIME_CODE_YEAR.timeCode -> {
                initYearBarChart()
            }
        }
    }

    private fun initChartNoData() {
        binding.tvNoDataText.visibility = View.VISIBLE
        binding.cBarChart.visibility = View.GONE
        binding.tvPrivacyText.visibility = View.GONE
    }

    private fun loadLastDTO(chartDataList: ArrayList<PressureStatItemDTO>): PressureStatItemDTO? {
        var lastDTO: PressureStatItemDTO? = null
        for (element in chartDataList.reversed()) {
            if (element.avg != null) {
                lastDTO = element
                break
            }
        }
        return lastDTO
    }

    /**
     * 压力日图表
     */
    private fun initDayBarChart() {
        // 进行插值计算
        val values = mutableListOf<String>()
        // 1. 获取当前天，从凌晨00:00到当前时间的分钟数
        val startOfDay = LocalDate.now().atStartOfDay()
        var now = LocalDateTime.now()
        // 定义时间格式
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
        // 从凌晨时间开始，每次增加一分钟，直到当前时间
        var currentTime = startOfDay
        // 分钟下标
        var index = 0
        var showTimeIndex = 1
        // 删除之前的数据
        if (chartDataList != null && chartDataList.size > 0) {
            try {
                val lastDTO = chartDataList[chartDataList.size - 1]//loadLastDTO(chartDataList)
                val firstDTO = chartDataList[0]
                val format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                val formatDayFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                currentTime = LocalDateTime.parse(firstDTO.startTime, formatDayFormat)
                now = LocalDateTime.parse(lastDTO?.startTime, format)
            } catch (ex: Exception) {
                Log.i("CardPressureFragment", "数据解析异常，时间不正确")
            }
        }

        wmyAllDataList.clear()
        var hasReallyData = false
        while (currentTime.isBefore(now) || currentTime.isEqual(now)) {
            // 打印当前时间
            val str = currentTime.format(formatter)
            val dto = chartDataList.find { it.startTime?.contains(str) == true }
            if (dto?.createTime != null && dto?.avg != null) {
                values.add("0-${dto.avg}")
                showTimeIndex = index + 1
                wmyAllDataList.add(dto)
                hasReallyData = true
            } else {
                values.add("0-0")
                wmyAllDataList.add(null)
            }

            // 半小时刻度
            currentTime = currentTime.plusMinutes(30)
            index++
        }
        // time 计算当前时间在X轴的哪个刻度上
        binding.cBarChart.setValue(
            hasReallyData,
            values,
            showTimeIndex,
            null,
            PressureBarChart.BarChartType.DAY
        )
    }

    /**
     * 压力周图表
     */
    private fun initWeekBarChart() {
        var indicatorIndex = 1
        val values = mutableListOf<String>()
        val xDataValues = mutableListOf<String>()
        // 获取当前周的每一天
        val weekDates = TimeUtils.getCurrentWeekDates("yyyy-MM-dd")
        val today = LocalDate.now()
        val formatter = DateTimeFormatter.ofPattern("MM-dd")
        // 清除之前的数据
        wmyAllDataList.clear()
        var hasReallyData = false
        for ((index, value) in weekDates.withIndex()) {
            // values
            val dto = chartDataList.find { it.startTime?.contains(value) == true }
            if (dto?.createTime != null && dto?.avg != null) {
                values.add("0-${dto.avg}")
                indicatorIndex = index + 1
                wmyAllDataList.add(dto)
                hasReallyData = true
            } else {
                values.add("0-0")
                wmyAllDataList.add(
                    PressureStatItemDTO(
                        null,
                        null,
                        null,
                        TimeUtils.formatDateString(value, outputFormat = "M月d日"),
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                    )
                )
            }

            // xDataValues
            xDataValues.add(index, TimeUtils.formatDateString(value))
        }
        binding.cBarChart.setValue(
            hasReallyData,
            values,
            indicatorIndex,
            xDataValues,
            PressureBarChart.BarChartType.WEEK
        )
    }

    /**
     * 压力月图表
     */
    private fun initMonthBarChart() {
        var indicatorIndex = 1
        val values = mutableListOf<String>()
        val xData = mutableListOf<String>()
        val monthDates = TimeUtils.getCurrentMonthDates("yyyy-MM-dd")
        wmyAllDataList.clear()
        var hasReallyData = false
        for ((index, value) in monthDates.withIndex()) {
            // values
            val dto = chartDataList.find { it.startTime?.contains(value) == true }
            if (dto?.createTime != null && dto?.avg != null) {
                values.add("0-${dto.avg}")
                indicatorIndex = index + 1
                wmyAllDataList.add(dto)
                hasReallyData = true
            } else {
                values.add("0-0")
                wmyAllDataList.add(
                    PressureStatItemDTO(
                        null,
                        null,
                        null,
                        TimeUtils.formatDateString(value, outputFormat = "M月d日"),
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                    )
                )
            }
            xData.add(value)
        }
        binding.cBarChart.setValue(
            hasReallyData,
            values,
            indicatorIndex,
            getXDataWithStep(getMonthListStr(), 7),
            PressureBarChart.BarChartType.MONTH
        )
    }

    /**
     * 压力年图表
     */
    private fun initYearBarChart() {
        var indicatorIndex = 1
        val values = mutableListOf<String>()
        val xDataValue = mutableListOf<String>()
        val months = TimeUtils.getAllMonthsOfCurrentYear()
        wmyAllDataList.clear()
        var hasReallyData = false

        chartDataList.forEachIndexed { index, it ->
            wmyAllDataList.add(it)
            if (it?.avg != null) {
                values.add("0-${it.avg}")
                indicatorIndex = index + 1
                hasReallyData = true
            } else {
                values.add("0-0")
            }
        }
        repeat(12 - chartDataList.size) {
            values.add("0-0")
        }

        for ((index, value) in months.withIndex()) {
            // xDataValue value = 2024-01
            if (index == 0) {
                xDataValue.add(value.replace("-", "\n"))
            } else {
                xDataValue.add(value.substring(5, 7))
            }
        }
//        for ((index, value) in months.withIndex()) {
//            // xDataValue value = 2024-01
//            if (index == 0) {
//                xDataValue.add(value.replace("-", "\n"))
//            } else {
//                xDataValue.add(value.substring(5, 7))
//            }
//
//            // values
//            val dto = chartDataList.find { it.startTime?.contains(value) == true }
//            if (dto?.createTime != null && dto?.avg != null) {
//                values.add("0-${dto.avg}")
//                indicatorIndex = index + 1
//                wmyAllDataList.add(dto)
//                hasReallyData = true
//
//            } else {
//                values.add("0-0")
//                //   wmyAllDataList.add(null)
//            }
//        }
        binding.cBarChart.setValue(
            hasReallyData,
            values,
            indicatorIndex,
            xDataValue,
            PressureBarChart.BarChartType.YEAR
        )
    }
    //endregion

    //region 拼接日期格式
    // 将02-01格式拼接成02月01日
    // 如果是年 则是2024-01改成2024年1月
    private fun getDateString(
        date: String,
        timeValue1: String,
        timValue2: String,
        isDeleteZero: Boolean = false
    ): String {
        var temp = date
        if (temp.isEmpty()) {
            return temp
        }

        if (temp.contains("-")) {
            temp.split("-").let {
                if (it.size == 2) {
                    if (isDeleteZero) {
                        var res = it[1]
                        if (it[1].startsWith("0")) {
                            res = it[1].substring(1)
                        }
                        temp = it[0] + timeValue1 + res + timValue2
                    } else {
                        temp = it[0] + timeValue1 + it[1] + timValue2
                    }
                }
            }
        }
        return temp
    }
    //endregion

    private fun showExplainDialog() {
        showHmsDialog(R.layout.hms_dialog_tips_small, "压力说明", introString)
    }

    private var dialog : Dialog? = null
    /**
     * 显示自定义对话框
     */
    fun showHmsDialog(layoutId: Int, title: String, message: String?) {
        // 创建自定义视图
        val view = LayoutInflater.from(requireActivity()).inflate(layoutId, null)
        val contentView = view.findViewById<RelativeLayout>(R.id.dialog_content)
        var btnPositive = view.findViewById<Button>(R.id.positiveButton)
        var tvMessage = view.findViewById<TextView>(R.id.textView)
        var tvTitle = view.findViewById<TextView>(R.id.tv_tips_title_small)
        tvTitle.text = title
        if (message != null && !message.isNullOrBlank()) {
            tvMessage.setText(message)
        }
        // 创建并显示对话框
        dialog = ImmersiveDialog(
            requireContext(),
            R.style.MyDialogStyle
        )
        // dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog!!.setContentView(view)
        dialog!!.setOnShowListener {
            HMSDialogUtils.doDialogAnimateEnter(dialog!!)
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    fragmentDataModel.isShowExplainDialog = false
                    dialog?.dismiss()
                }
                false
            }
        }
        // 设置按钮点击事件
        btnPositive.setOnClickListener {
            fragmentDataModel.isShowExplainDialog = false
            dialog?.dismiss()
        }
        dialog!!.show()
        fragmentDataModel.isShowExplainDialog = true
    }

    override fun update(isEllipsized: Boolean, viewID: Int) {
        if (isEllipsized) {
            binding.healthRiskAll.cardAdviceMore.visibility = View.VISIBLE
            binding.healthRiskAll.cardAdviceMore.setOnClickListener { v ->
                HMSDialogUtils.showHMSNotiDialog(
                    requireContext(),
                    R.layout.hms_dialog_see_more,
                    "健康建议",
                    healthAdviceStr,
                    "知道了"
                ) { isPositive ->
                }
            }
        } else {
            binding.healthRiskAll.cardAdviceMore.visibility = View.GONE
        }
    }


    private fun initPrivacyUI(timeCode: String) {
        setScrollEnable(binding.svContainer, false)
        binding.tip.visibility = View.GONE
        binding.tvPrivacyText.visibility = View.VISIBLE
        binding.tvNoDataText.visibility = View.GONE
        binding.cBarChart.visibility = View.GONE
        binding.tvFetchTime.text = getPrivacyModeDate(timeCode)
        listOf(binding.tvIntroTimeValue, binding.tvIntroRangeValue).forEach {
            it.text = "***"
        }
        binding.tvIntroTimeValueUnit.text = ""
        binding.tvIntroRangeValueUnit.text = ""
        listOf(
            binding.tvHighPer,
            binding.tvMiddlePer,
            binding.tvNormalPer,
            binding.tvRelaxPer
        ).forEach {
            it.text = "***%"
        }
        // 健康建议在私密模式下不展示
        binding.pressureSummaryVo = null
        setViewVisable()
        binding.svContainer.visibility = View.VISIBLE
    }

    private fun applyPrivacyStyleChanged(isPrivacyMode: Boolean) {
        binding.llIntroContainer.apply {
            val params = layoutParams as MarginLayoutParams
            params.bottomMargin = if (isPrivacyMode) 30f.dp.toInt() else 0f.dp.toInt()
            layoutParams = params
        }
        listOf(binding.tvIntroTimeValue, binding.tvIntroRangeValue).forEach {
            val params = it.layoutParams as MarginLayoutParams
            params.topMargin = if (isPrivacyMode) 14f.dp.toInt() else 0
            it.layoutParams = params
        }
        listOf(
            binding.tvHighPer,
            binding.tvMiddlePer,
            binding.tvNormalPer,
            binding.tvRelaxPer
        ).forEach {
            val style = if (isPrivacyMode) Typeface.BOLD else Typeface.NORMAL
            it.setTypeface(Typeface.create(Typeface.DEFAULT, style))
            it.invalidate() // 强制重绘以确保变化生效
        }
    }

    fun isDataListNotEmpty(dataList: ArrayList<PressureStatItemDTO>): Boolean {
        dataList.forEach {
            if (it.avg == null) return@forEach
            if (it.avg != 0)
                return true
        }
        return false
    }

    override fun onPrivacyModeChange(provacyMode: Boolean) {
        if (isProvacyMode != provacyMode) {
            //当前模式与变化模式不同
            sendRequest(mUserId, cardTimeType!!)
            isProvacyMode = provacyMode
        }
    }

    fun initExplain() {
        val tips = binding.ivIntroTips
        tips.visibility = View.VISIBLE
//        tips.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener{
//            override fun onGlobalLayout() {
//                tips.viewTreeObserver.removeGlobalOnLayoutListener { this }
//                val parentView=tips.parent as View
//                val rect = Rect()
//                tips.getHitRect(rect)
//                rect.top-=24
//                rect.bottom+=100
//                rect.left-=100
//                rect.right+=24
//                parentView.touchDelegate = TouchDelegate(rect, tips)
//            }
//        })
        viewAddOnGlobalLayoutListener(tips)
        this.introString = getString(R.string.description_pressure)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        try {
            dialog?.dismiss()
            dialog = null
            viewRemoveOnGlobalLayoutListener(binding.ivIntroTips)
            binding.healthRiskAll.tvHealthAdviceContent.setCallback(null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun getDayFetchTime(): String {
        val calendar = Calendar.getInstance()
        val month = calendar[Calendar.MONTH] + 1 // 月份是从0开始的，需要加1
        val day = calendar[Calendar.DAY_OF_MONTH]
        return "${month}月${day}日"
    }

    fun setViewVisable() {
        binding.tvHighPer.visibility = View.VISIBLE
        binding.tvRelaxPer.visibility = View.VISIBLE
        binding.tvMiddlePer.visibility = View.VISIBLE
        binding.tvNormalPer.visibility = View.VISIBLE
        binding.loCard4.visibility = View.VISIBLE
    }

}