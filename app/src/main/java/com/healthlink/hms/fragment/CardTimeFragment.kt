package com.healthlink.hms.fragment

import android.app.Dialog
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.ClickUtils
import com.healthlink.hms.R
import com.healthlink.hms.activity.card.HMSCardFragmentInteractWithAcInterface
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.databinding.FragmentCardTimeBinding
import com.healthlink.hms.fragment.viewmodel.HeartRateFragmentModel
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.HealthDataStatusDTO
import com.healthlink.hms.core.model.dto.charts.HealthSummeryDTO
import com.healthlink.hms.core.model.dto.charts.heartrate.HeartRateStatDTO
import com.healthlink.hms.core.model.dto.charts.heartrate.HeartRateStatItemDTO
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.TimeUtils.getMonthListStr
import com.healthlink.hms.utils.TimeUtils.getMonthListStrChn
import com.healthlink.hms.utils.TimeUtils.getWeekListStrChn
import com.healthlink.hms.utils.TimeUtils.getXDataWithStep
import com.healthlink.hms.utils.TimeUtils.resetWMDateTime
import com.healthlink.hms.utils.TimeUtils.resetYDateTime
import com.healthlink.hms.utils.getPrivacyModeDate
import com.healthlink.hms.viewmodels.MainViewModel
import com.healthlink.hms.views.ImmersiveDialog
import com.healthlink.hms.views.MiddleEllipsesTextView
import com.healthlink.hms.views.charts.HeartBarChart
import java.lang.ref.WeakReference
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Calendar

/**
 * 心率二级页面
 */
@Suppress("CAST_NEVER_SUCCEEDS")
open class CardTimeFragment : BaseCardFragment<FragmentCardTimeBinding, MainViewModel>(
    MainViewModel::class.java,
    R.layout.fragment_card_time
), MiddleEllipsesTextView.UpdateSeeMore {
    private var chartDataList = arrayListOf<HeartRateStatItemDTO>()

    // 所有数据集合，无数据用空HeartRateStatItemDTO填充
    private var wmyAllDataList = arrayListOf<HeartRateStatItemDTO?>()
    private var yDataList = arrayListOf<HeartRateStatItemDTO?>()
    private var wmyAllSummaryDataList = arrayListOf<HealthSummeryDTO?>()
    private var reqMap = mapOf<String, String>()
    private var healthAdviceStr = ""
    private var introString = ""
    private var isNoDataMode = false
    private var isProvacyMode = false

    private var fragmentDataModel  = HeartRateFragmentModel()

    companion object {
        private const val TAG = "CardTimeFragment"
        private const val ARG_PARAM_TYPE = "ARG_PARAM_TYPE"
        private val fragmentInteractWithAC get() = _fragmentInteractWithAC?.get()
        private var _fragmentInteractWithAC: WeakReference<HMSCardFragmentInteractWithAcInterface>? =
            null
        private lateinit var mUserId: String

        fun newInstance(
            cartTimeType: TimeCode,
            userId: String,
            interact: HMSCardFragmentInteractWithAcInterface
        ): CardTimeFragment {
            val fragment = CardTimeFragment()
            val args = Bundle()
            args.putString(ARG_PARAM_TYPE, cartTimeType.timeCode)
            fragment.arguments = args
            _fragmentInteractWithAC = WeakReference(interact)
            mUserId = userId
            return fragment
        }
    }

    override fun sendRequest(userId: String, timeCode: String) {
        reqMap = mapOf(
            "userId" to userId,
            "unit" to timeCode
        )
        if (!HmsApplication.isPrivacyModeEnabled()) {
            if (!HmsApplication.isNetworkConn()) {
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                resetData()
                if (isDataReady) showLoading()
                if(fragmentDataModel!=null
                    && fragmentDataModel.healthData!=null
                    && fragmentDataModel.healthData!!.code == "0"){
                    processHealthData(fragmentDataModel.healthData!!)
                }else{
                    viewModel?.getHeartRateDetailData(reqMap)
                }
            }
        } else {
            initPrivacyUI(timeCode)
        }

    }

    private fun resetData() {
        moveToBottom(binding.tvIntroTimeValueUnit)
        moveToBottom(binding.tvIntroRangeValueUnit)
    }

    override fun sendDataReadyRequest(userId: String, timeCode: String) {
        isProvacyMode = HmsApplication.isPrivacyModeEnabled()
        when (timeCode) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Heartratereports_Daytab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Heartratereports_Weektab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Heartratereports_Mouthtab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Heartratereports_Yeartab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
            }

        }
        applyPrivacyStyleChanged(HmsApplication.isPrivacyModeEnabled())
        if (!HmsApplication.isPrivacyModeEnabled()) {
            if (!HmsApplication.isNetworkConn()) {
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
                return
            }else{
                showLoading()
            }
            resetData()
            setScrollEnable(binding.svContainer, false)
            viewModel?.getHistoryStatusData(userId)
        } else {
            initPrivacyUI(timeCode)
        }
    }

    private val runnable = UpdateRunnable(this)
    private class UpdateRunnable(private val fragment: CardTimeFragment) : Runnable {
        private val weakFragment = WeakReference(fragment)

        override fun run() {
            val fragment = weakFragment.get()
            if (fragment != null && !fragment.isDetached) {
                if (!fragment.isDataReady) {
                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
                    // 15 秒后再次调用
                    if (HmsApplication.isNetworkConn())
                        fragment.handler.postDelayed(this, 15000)
                }
            }
        }
    }

//    private val runnable = object : Runnable {
//        private val weekFragment = WeakReference(this@CardTimeFragment)
//        override fun run() {
//            val fragment = weekFragment.get()
//            if (fragment != null && !fragment.isDetached) {
//                if (!fragment.isDataReady ) {
//                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
//                    // 15 秒后再次调用
//                    if (HmsApplication.isNetworkConn())
//                        fragment.handler.postDelayed(this, 15000)
//                }
//            }
//        }
//    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        arguments?.let {
            cardTimeType = it.getString(ARG_PARAM_TYPE)
        }
    }

    private fun restoreDataIfPossible(savedInstanceState: Bundle) {
        try {
            var fragmentSavedData = savedInstanceState.getString(KEY_SAVED_DATA_SUMMARY)
            if (fragmentSavedData != null) {
                var fragmentDataModel =
                    gson.fromJson(fragmentSavedData, HeartRateFragmentModel::class.java)
                if(fragmentDataModel!=null){
                    this.fragmentDataModel = fragmentDataModel
                    binding.svContainer.scrollY = this.fragmentDataModel.scollY
                    Log.i(TAG, "init CardTimeFragmnet data from saved fragment success.")
                }
            }

            if(savedInstanceState.getBoolean(KEY_SHOW_DIALOG_FLAG)) {
                showExplainDialog()
            }
        }catch (ex: Exception){
            Log.i(TAG, "init CardTimeFragmnet data from saved fragment fail. error : ${ex.message}")
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()
        initLiveDataObserve()
        // 恢复数据
        if(savedInstanceState!=null){
            restoreDataIfPossible(savedInstanceState)
        }
    }

    override fun onResume() {
        super.onResume()
        if (!HmsApplication.isPrivacyModeEnabled()) {
            setScrollEnable(binding.svContainer)
        }
        if (isDataReady)
            sendRequest(mUserId, cardTimeType!!)
        else
            handler.post(runnable)


    }

    override fun onPause() {
        super.onPause()
        handler.removeCallbacks(runnable)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        // 保存数据
        if(fragmentDataModel.healthData!=null
            && fragmentDataModel.healthData!!.code == "0"
            && fragmentDataModel.healthSummaryResponse!=null
            && fragmentDataModel.healthSummaryResponse!!.code == "0"
            ){
            fragmentDataModel.scollY = binding.svContainer.scrollY
            outState.putString(KEY_SAVED_DATA_SUMMARY, gson.toJson(fragmentDataModel));
        }

        outState.putBoolean(KEY_SHOW_DIALOG_FLAG, fragmentDataModel.isShowExplainDialog)
    }

    fun getDataReady() {
        isDataReady = true
        handler.removeCallbacks(runnable)
        sendRequest(mUserId, cardTimeType!!)
    }

    fun readyDataNoAuth() {
        handler.removeCallbacks(runnable)
        showNoAuthView()
        fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
    }

    private fun initLiveDataObserve() {
        viewModel.healthHistoryData.observe(viewLifecycleOwner) {
            val notReadyText =
                "心率${requireContext().resources.getString(R.string.text_data_not_ready)}"
            if (it.code == "0" && it.data != null) {
                val statusList = it.data?.dataStatusList
                if (statusList.isNullOrEmpty()) //如果是空的 也认为是有数据的
                {
                    getDataReady()
                    return@observe
                }
                var status: HealthDataStatusDTO? = null
                if (!statusList.isNullOrEmpty()) {
                    val statusArray = statusList.filter { it.dataType == "heartRateRead" }
                    if (!statusArray.isNullOrEmpty()) {
                        status = statusArray[0]
                    } else {
                        getDataReady()
                        return@observe
                    }
                }
                if (status != null) {
                    when (cardTimeType) {
                        TimeCode.TIME_CODE_DAY.timeCode -> {
                            if (status.dayDataStatus == null || status.dayDataStatus == 2) {
                                //如果返回值是null  也认为是有值的
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_WEEK.timeCode -> {
                            if (status.weekDataStatus == null || status.weekDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_MONTH.timeCode -> {
                            if (status.monthDataStatus == null || status.monthDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_YEAR.timeCode -> {
                            if (status.yearDataStatus == null || status.yearDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }
                    }
                } else {
                    showNetErrorOrSettingView()
                    fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
                }
            } else if (it.code == "5") {
                showNoAuthView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                // 无网络或者刷新失败处理
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            }
        }

        viewModel.getCardDetailChartLiveData().observe(viewLifecycleOwner) { it ->
            this.fragmentDataModel.healthData = it
            processHealthData(it)
        }
        /**
         * 监听健康建议数据加载的更新
         */
        viewModel.healthSummeryData.observe(viewLifecycleOwner) {
            fragmentDataModel.healthSummaryResponse = it
            processSummaryData(it)
        }
    }

    private fun processHealthData(it: BaseResponse<HeartRateStatDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            if (it.data!!.valueList != null && it.data!!.valueList.isNotEmpty()) {
                binding.tvNoDataText.visibility = View.GONE
                chartDataList = it.data!!.valueList
                //判断周月年的数据是否为空
                if (cardTimeType != null && isDataListNotEmpty(chartDataList, cardTimeType!!)) {
                    if (cardTimeType == TimeCode.TIME_CODE_DAY.timeCode) {
                        val result = chartDataList.findLast { item -> item.last != 0f }
                        binding.tvFetchTime.text = result?.createTime
                    } else {
                        binding.tvFetchTime.text = chartDataList.last().createTime ?: ""
                    }

                    binding.heartRateStatVo = it.data
                    // 心率健康建议
                    val map = mutableMapOf<String, String>()
                    map.putAll(reqMap)
                    if (it.data!!.startTime?.isNotEmpty() == true) {
                        map["startTime"] = it.data!!.startTime!!
                    }
                    if (it.data!!.endTime?.isNotEmpty() == true) {
                        map["endTime"] = it.data!!.endTime!!
                    }
                    initChartData()
                    if (fragmentDataModel == null
                        || fragmentDataModel.healthSummaryResponse == null
                        || fragmentDataModel.healthSummaryResponse!!.code != "0"
                    ) {
                        viewModel.getHealthSummery(map)
                    } else {
                        processSummaryData(fragmentDataModel.healthSummaryResponse!!)

                    }
                } else {
                    initNoDataUI(cardTimeType!!)
                }

            } else {
                initNoDataUI(cardTimeType!!)
            }

        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            initNoDataUI(cardTimeType!!)
            // 无网络或者刷新失败处理
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            (binding.svContainer)
        }, if (cardTimeType == TimeCode.TIME_CODE_YEAR.timeCode) 500 else 400)
    }

    /**
     * 处理建议数据
     */
    private fun processSummaryData(it: BaseResponse<HealthSummeryDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.healthSummeryDTO = it.data
            binding.svContainer.post {
                binding.svContainer.requestLayout()
            }
            Handler(Looper.getMainLooper()).postDelayed({
                hideLoading()
                setScrollEnable(binding.svContainer)
            }, 100)

            // 重置查看更多按钮为不可见
            binding.healthRiskAll.tvHealthAdviceContent.setIsEllipsized(false)
            it.data!!.healthAdvice?.let { item ->
                healthAdviceStr = item
            }
    //                it.data!!.nounExplain?.let { explain ->
    //                    if (explain.isNotEmpty()) {
    //                        this.introString = explain
    //                        binding.ivIntroTips.visibility = View.VISIBLE
    //                    }
    //                }
            // 第三张卡片（状态比例）来自安能的统计接口
    //                if (cardTimeType == TimeCode.TIME_CODE_DAY.timeCode) {
            val card3DTO = HealthSummeryDTO(
                binding.healthSummeryDTO?.normalProp,
                binding.healthSummeryDTO?.normalSum,
                binding.healthSummeryDTO?.lowProp,
                binding.healthSummeryDTO?.lowSum,
                binding.healthSummeryDTO?.highProp,
                binding.healthSummeryDTO?.highSum,
                null
            )
            bindingCard3DTO(card3DTO)
    //                }

            // 第三张卡片初次显示最后一个有值的数据
            if (it.data!!.valueList != null && it.data!!.valueList!!.isNotEmpty()) {
                // 显示最后一个有值的数据
    //                    for (element in it.data!!.valueList!!.reversed()) {
    //                        if (element?.normalProp != null) {
    //                            bindingCard3DTO(element)
    //                            break
    //                        }
    //                    }

                // 存储周月年视图下滑动柱状图需要显示的数据
                if (cardTimeType == TimeCode.TIME_CODE_WEEK.timeCode) {
                    storeAllWMYSummaryData(
                        it.data!!.valueList!!,
                        TimeUtils.getCurrentWeekDates("yyyy年MM月dd日")
                    )
                } else if (cardTimeType == TimeCode.TIME_CODE_MONTH.timeCode) {
                    storeAllWMYSummaryData(
                        it.data!!.valueList!!,
                        TimeUtils.getCurrentMonthDates("yyyy年MM月dd日")
                    )
                } else {
                    storeAllWMYSummaryData(
                        it.data!!.valueList!!,
                        TimeUtils.getAllMonthsOfCurrentYear("yyyy年M月")
                    )
                }

            }
        }

        // 恢复滚动位置
        if(this.fragmentDataModel.scollY>0) {
            binding.svContainer.post({
                binding.svContainer.scrollY = this.fragmentDataModel.scollY
            })
        }
    }

    private fun initUI() {
        initExplain()
        ClickUtils.applySingleDebouncing(binding.ivIntroTips, 1000) {
//            showHmsDialog(R.layout.hms_dialog_tips_small, "心率说明", introString)
            showExplainDialog()
        }
        binding.healthRiskAll.tvHealthAdviceContent.setCallback(this)
        binding.healthRiskAll.tvHealthAdviceContent.setEndPercentage(80)
        binding.healthRiskAll.tvSeeMore.text = Html.fromHtml("<u>查看更多</u>")
        // 处理图表视图与scrollView的滑动冲突
        binding.cLineChart.setOnTouchListener() { v, event ->
            if (event.action == MotionEvent.ACTION_UP) {
                binding.svContainer.requestDisallowInterceptTouchEvent(false)
            } else {
                binding.svContainer.requestDisallowInterceptTouchEvent(true)
            }
            return@setOnTouchListener false
        }

        binding.cBarChart.setOnTouchListener() { v, event ->
            if (event.action == MotionEvent.ACTION_UP) {
                binding.svContainer.requestDisallowInterceptTouchEvent(false)
            } else {
                binding.svContainer.requestDisallowInterceptTouchEvent(true)
            }
            return@setOnTouchListener false
        }

        // 图表移动事件回调
        // 日 - 折线图
        binding.cLineChart.setOnDaySelectListener { index, heart ->
            // 整个坐标系心率为0的值，都不处理
            if (heart <= 0) {
                binding.tvIntroTimeValue.text = "--"
                if(index>=0 && index<chartDataList.size){
                    binding.tvFetchTime.text = chartDataList[index].createTime
                }

                return@setOnDaySelectListener
            }
            binding.tvIntroTimeValue.text = String.format("%.0f", heart.toFloat())
            if (index < wmyAllDataList.size) {
                val dto = wmyAllDataList[index]
                // 根据下标从原始数据里获取到的值，如果不为0，才显示
                dto?.let {
                    if (it.last > 0) {
                        //兼容先
                        binding.tvFetchTime.text = it.createTime
                    }
                }
            }
        }
        // 无数据时的展示时间展示
        val currentDateStr = getDayFetchTime()
        binding.cLineChart.setOnXTextSelectListener { index, xText ->
            if (isNoDataMode)
                binding.tvFetchTime.text = currentDateStr + " " + xText
        }

        // 周月年
        binding.cBarChart.setBarSelectListener { index, heart ->
            val values = heart.split('-')
            // 心率范围
//            if (values.size == 2) {
//                if (values[0].isNotEmpty() && values[1].isNotEmpty() && values[0].toFloat() > 0 && values[1].toFloat() > 0) {
//                    binding.tvIntroRangeValue.text =
//                        String.format("%.0f-%.0f", values[0].toFloat(), values[1].toFloat())
//                } else {
//                    binding.tvIntroRangeValue.text = "--"
//                }
//            }

            if (cardTimeType == TimeCode.TIME_CODE_MONTH.timeCode) {
                val monthDates = TimeUtils.getCurrentMonthDates()
                if (index < monthDates.size) {
                    var value = monthDates[index]
                    // values
                    val dto = chartDataList.find { it.startTime.contains(value) }
                    if (dto != null) {
                        binding.tvFetchTime.text = dto?.createTime

                        var heartRangeText = "--"
                        if (values.size == 2) {
                            if (values[0].isNotEmpty() && values[1].isNotEmpty() && values[0].toFloat() > 0 && values[1].toFloat() > 0) {
                                heartRangeText = String.format(
                                    "%.0f-%.0f",
                                    values[0].toFloat(),
                                    values[1].toFloat()
                                )
                            }
                        }
                        binding.tvIntroTimeValue.text = heartRangeText
                    } else {
                        binding.tvIntroTimeValue.text = "--"
                    }
                } else {
                    binding.tvIntroTimeValue.text = "--"
                }
            } else if (index < chartDataList.size) {
                val dto = chartDataList[index]
                if (dto != null) {
                    binding.tvFetchTime.text = dto?.createTime

                    var heartRangeText = "--"
                    if (values.size == 2) {
                        if (values[0].isNotEmpty() && values[1].isNotEmpty() && values[0].toFloat() > 0 && values[1].toFloat() > 0) {
                            heartRangeText =
                                String.format("%.0f-%.0f", values[0].toFloat(), values[1].toFloat())
                        }
                    }
                    binding.tvIntroTimeValue.text = heartRangeText
                }
            } else {
                binding.tvIntroTimeValue.text = "--"
            }
//
//            // 变动心率第三张卡片
//            if (cardTimeType != TimeCode.TIME_CODE_DAY.timeCode) {
//                if (wmyAllSummaryDataList.isEmpty()) return@setBarSelectListener
//                if (index < wmyAllSummaryDataList.size - 1) {
//                    val lastData = wmyAllSummaryDataList[index] ?: return@setBarSelectListener
//                    bindingCard3DTO(lastData)
//                }
//            }
        }
        val scrollView = binding.svContainer
        scrollView.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            val height = scrollView.getChildAt(0).height // 获取ScrollView内容的总高度
            val scrollViewHeight = scrollView.height // 获取ScrollView的可见高度
            val diff = (height - scrollViewHeight) * 0.75f //scrollview中判定的距离 动画view位置底部约为总长度的的75%
            if (scrollY >= diff) {
                MMKVUtil.getUserId()?.let {
                    DataTrackUtil.dtScroll(
                        "Health_Heartratereports_Bottom_Show",
                        DataTrackUtil.userIDMap(it)
                    )
                }
            }
        }
        val monthDayList = TimeUtils.getMonthListStr()
        when (cardTimeType) {
            TimeCode.TIME_CODE_WEEK.timeCode -> {
                binding.cBarChart.setXTextSelectListener { index, xText ->
                    if (isNoDataMode || (chartDataList.isNotEmpty() && index >= chartDataList.size))
                        binding.tvFetchTime.text = resetWMDateTime(xText)
                }
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                binding.cBarChart.setXTextSelectListener { index, xText ->
                    if (isNoDataMode || (chartDataList.isNotEmpty() && index >= chartDataList.size))
                        try {
                            binding.tvFetchTime.text = resetWMDateTime(monthDayList[index])
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                }
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                binding.cBarChart.setXTextSelectListener { index, xText ->
                    if (isNoDataMode || (chartDataList.isNotEmpty() && index >= chartDataList.size))
                        binding.tvFetchTime.text = resetYDateTime(xText)
                }

            }
        }
    }

    // 绑定数据到第三张卡片
    private fun bindingCard3DTO(cardStatusDTO: HealthSummeryDTO?) {
        if (cardStatusDTO?.normalProp == null) {
            return
        }
        val card3DTO = HealthSummeryDTO(
            cardStatusDTO.normalProp,
            cardStatusDTO.normalSum,
            cardStatusDTO.lowProp,
            cardStatusDTO.lowSum,
            cardStatusDTO.highProp,
            cardStatusDTO.highSum,
            null
        )
        binding.card3DTO = card3DTO

    }

    /**
     * 将周月年的数据进行插值存储，滑动小圆点是下标时获取卡片数据进行比对
     * @param valueList 原始数据集
     * @param xInteralList X轴全集 原始数据集小于X轴全集
     */
    private fun storeAllWMYSummaryData(
        valueList: ArrayList<HealthSummeryDTO>,
        xInteralList: List<String> = ArrayList()
    ) {
        if (valueList != null && valueList.isNotEmpty()) {
            wmyAllSummaryDataList.clear()
            for ((_, value) in xInteralList.withIndex()) {
                val dto = valueList.find { it.createTime?.contains(value) == true }
                if (dto != null) {
                    wmyAllSummaryDataList.add(dto)
                } else
                    wmyAllSummaryDataList.add(null)
            }
        }
    }

    private fun initChartData() {
        isNoDataMode = false
        if (chartDataList.isEmpty()) {
            return
        }
        setUnitVisiable(true)
        // 根据不同的cardTimeType 初始化不同的view
        if (cardTimeType == TimeCode.TIME_CODE_DAY.timeCode) {
            binding.tvPrivacyText.visibility = View.GONE
            binding.cLineChart.visibility = View.VISIBLE
            binding.cBarChart.visibility = View.GONE
            initLineChart()
        } else {
            binding.tvPrivacyText.visibility = View.GONE
            binding.cLineChart.visibility = View.GONE
            binding.cBarChart.visibility = View.VISIBLE
            initBarChart(cardTimeType!!)
        }
    }
    //region 心率图表
    /**
     * 心率日图表
     */
    private fun initLineChart() {
        // 进行插值计算
        val values = mutableListOf<Int>()
        // 1. 获取当前天，从凌晨00:00到当前时间的分钟数
        val startOfDay = LocalDate.now().atStartOfDay()
        var now = LocalDateTime.now()
        // 定义时间格式
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
        // 从凌晨时间开始，每次增加一分钟，直到当前时间
        var currentTime = startOfDay
        // || currentTime.isEqual(now)
        // 分钟下标
        var index = 0
//        var showTimeIndex = 0
        // 删除之前的数据
        wmyAllDataList.clear()
        if (chartDataList != null && chartDataList.size > 0) {
            try {
                var lastDTO = chartDataList[chartDataList.size - 1]
//                var firstDTO = chartDataList[0]
                var format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
//                var formatDayFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd")
                var formatDayFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")

//                currentTime = LocalDateTime.parse(firstDTO.startTime, formatDayFormat)
                now = LocalDateTime.parse(lastDTO.startTime, format)
                currentTime = now.toLocalDate().atStartOfDay()
            } catch (ex: Exception) {
                Log.i("CardTimeFragment", "数据解析异常，时间不正确")
            }
            chartDataList.forEach {
                values.add(it.last.toInt())
                wmyAllDataList.add(it)
            }
            binding.cLineChart.setValue(values, 67, false)
        }

//        wmyAllDataList.clear()
//        while (currentTime.isBefore(now) || currentTime.isEqual(now)) {
//            // 打印当前时间
//            val str = currentTime.format(formatter)
//            val dto = chartDataList.find { it.startTime.contains(str) }
//            if (dto != null) {
//                values.add(dto.last.toInt())
//                wmyAllDataList.add(dto)
////                showTimeIndex = index + 1
//            } else {
//                values.add(0)
//                wmyAllDataList.add(null)
//            }
//
//            // 增加一分钟
//            currentTime = currentTime.plusMinutes(1)
//            index++
//        }
//        // time 计算当前时间在X轴的哪个刻度上
//        binding.cLineChart.setValue(values, 67,false)

    }

    /**
     * 心率周、月、年图表
     */
    private fun initBarChart(cartTimeType: String) {
        // 最后有值的下标
        var lastValueIndicatorIndex = 1
        // 图表数据集
        val values = mutableListOf<String>()
        // X轴文字数据集
        val xDataValues = mutableListOf<String>()
        if (cartTimeType == TimeCode.TIME_CODE_WEEK.timeCode) {
            // 获取当前周的每一天
            val weekDates = TimeUtils.getCurrentWeekDates()
            val weekDatasForCard = getWeekListStrChn()
            // 清除之前的数据
            wmyAllDataList.clear()
            for ((index, value) in weekDates.withIndex()) {
                // values
                val dto = chartDataList.find { it.startTime.contains(value) }
                if (dto != null//心率最大值或最小值要大于0
                    && (dto.min > 0 || dto.max > 0)
                ) {
                    values.add("${dto.min}-${dto.max}")
                    lastValueIndicatorIndex = index + 1
                    // dto.createTime = weekDatasForCard[index]
                    wmyAllDataList.add(dto)
                } else {
                    values.add("0-0")
                    var dtoTemp =
                        HeartRateStatItemDTO(weekDatasForCard[index], "", "", -1F, -1F, -1F, -1F)
                    wmyAllDataList.add(dtoTemp)
                }
                // xDataValues
                xDataValues.add(index, value)
            }
            binding.cBarChart.setValue(
                values,
                xDataValues,
                lastValueIndicatorIndex,
                HeartBarChart.BarChartType.WEEK, false
            )
        } else if (cartTimeType == TimeCode.TIME_CODE_MONTH.timeCode) {
            val monthDates = TimeUtils.getCurrentMonthDates()
            val monthDatesForCard = getMonthListStrChn()
            wmyAllDataList.clear()
            for ((index, value) in monthDates.withIndex()) {
                // values
                val dto = chartDataList.find { it.startTime.contains(value) }
                if (dto != null
                    //心率最大值或最小值要大于0
                    && (dto.min > 0 || dto.max > 0)
                ) {
                    values.add("${dto.min}-${dto.max}")
                    lastValueIndicatorIndex = index + 1
                    //dto.createTime = monthDatesForCard.get(index)
                    wmyAllDataList.add(dto)
                } else {
                    values.add("0-0")
                    var dtoTemp = HeartRateStatItemDTO(
                        monthDatesForCard.get(index),
                        "",
                        "",
                        -1F,
                        -1F,
                        -1F,
                        -1F
                    )
                    wmyAllDataList.add(dtoTemp)
                }
                xDataValues.add(value)
            }
            binding.cBarChart.setValue(
                values,
                getXDataWithStep(getMonthListStr(), 7),
                lastValueIndicatorIndex,
                HeartBarChart.BarChartType.MONTH, false
            )
        } else if (cartTimeType == TimeCode.TIME_CODE_YEAR.timeCode) {
            val months = TimeUtils.getAllMonthsOfCurrentYear()
            wmyAllDataList.clear()
            for ((index, value) in months.withIndex()) {
                // xDataValue value = 2024-01
                if (index == 0) {
                    xDataValues.add(value.replace("-", "\n"))
                } else {
                    xDataValues.add(value.substring(5, 7))
                }
                var tempValue = value
                try {
                    if (values.contains("\n"))
                        tempValue = value.split("\n")[1]
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                // values
                if (index < chartDataList.size) {
                    val dto = chartDataList[index]
                    if (dto != null
                        //心率最大值或最小值要大于0
                        && (dto.min > 0 || dto.max > 0)
                    ) {
                        values.add("${dto.min}-${dto.max}")
                        lastValueIndicatorIndex = index + 1
                        wmyAllDataList.add(dto)
                    } else {
                        values.add("0-0")
                        wmyAllDataList.add(null)
                    }
                } else {
                    values.add("0-0")
                    wmyAllDataList.add(null)
                }
                //    val dto = chartDataList.find { it.startTime.contains(tempValue) }

            }
            binding.cBarChart.setValue(
                values,
                xDataValues,
                lastValueIndicatorIndex,
                HeartBarChart.BarChartType.YEAR, false
            )
        }
    }
    //endregion
    private var dialog : Dialog? = null
    private fun showExplainDialog() {
        showHmsDialog(R.layout.hms_dialog_tips_small, "心率说明", introString)
    }

    /**
     * 显示自定义对话框
     */
    fun showHmsDialog(layoutId: Int, title: String, message: String?) {
        // 创建自定义视图
        val view = LayoutInflater.from(requireActivity()).inflate(layoutId, null)
        val contentView = view.findViewById<RelativeLayout>(R.id.dialog_content)
        var btnPositive = view.findViewById<Button>(R.id.positiveButton)
        var tvMessage = view.findViewById<TextView>(R.id.textView)
        var tvTitle = view.findViewById<TextView>(R.id.tv_tips_title_small)
        tvTitle.text = title
        if (message != null && !message.isNullOrBlank()) {
            tvMessage.setText(message)
        }
        // 创建并显示对话框
        dialog = ImmersiveDialog(
            requireContext(),
            R.style.MyDialogStyle
        )
        //dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog!!.setContentView(view)
        dialog!!.setOnShowListener {
            HMSDialogUtils.doDialogAnimateEnter(dialog!!)
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    fragmentDataModel.isShowExplainDialog = false
                    dialog?.dismiss()
                }
                false
            }
        }
        // 设置按钮点击事件
        btnPositive.setOnClickListener {
            fragmentDataModel.isShowExplainDialog = false
            dialog?.dismiss()
        }
        dialog!!.show()
        fragmentDataModel.isShowExplainDialog = true
    }

    override fun update(isEllipsized: Boolean, viewID: Int) {
        if (isEllipsized) {
            binding.healthRiskAll.cardAdviceMore.visibility = View.VISIBLE
            binding.healthRiskAll.cardAdviceMore.setOnClickListener { v ->
                HMSDialogUtils.showHMSNotiDialog(
                    requireContext(),
                    R.layout.hms_dialog_see_more,
                    "健康建议",
                    healthAdviceStr,
                    "知道了"
                ) { isPositive ->
                }
            }
        } else {
            binding.healthRiskAll.cardAdviceMore.visibility = View.GONE
        }
    }

    fun initPrivacyUI(timeCode: String) {
        setScrollEnable(binding.svContainer, false)
        binding.tvPrivacyText.visibility = View.VISIBLE
        binding.cLineChart.visibility = View.GONE
        binding.cBarChart.visibility = View.GONE
        binding.tvNoDataText.visibility = View.GONE
        binding.tvFetchTime.text = getPrivacyModeDate(timeCode)
        binding.tvIntroRangeValue.text = "***"
        binding.tvIntroTimeValue.text = "***"
        binding.tvBradycardiaPer.text = "***%"
        binding.tvTachycardiaPer.text = "***%"
        binding.tvNormalPer.text = "***%"
        // 健康建议设置为不可见
        binding.healthSummeryDTO = null

        moveToTop(binding.tvIntroTimeValueUnit)
        moveToTop(binding.tvIntroRangeValueUnit)
        binding.svContainer.visibility = View.VISIBLE
    }

    private fun applyPrivacyStyleChanged(isPrivacyMode: Boolean) {
        binding.llIntroContainer.apply {
            val params = layoutParams as ViewGroup.MarginLayoutParams
            params.bottomMargin = if (isPrivacyMode) 30f.dp.toInt() else 0f.dp.toInt()
            layoutParams = params
        }
        listOf(binding.tvBradycardiaPer, binding.tvTachycardiaPer, binding.tvNormalPer).forEach {
            val style = if (isPrivacyMode) Typeface.BOLD else Typeface.NORMAL
            it.setTypeface(Typeface.create(Typeface.DEFAULT, style))
            it.invalidate() // 强制重绘以确保变化生效
        }
    }

    fun initNoDataUI(timeCode: String) {
        isNoDataMode = true
        binding.tvIntroTimeValue.text = "--"
        binding.tvIntroRangeValue.text = "--"
        binding.tvBradycardiaPer.text = "0%"
        binding.tvTachycardiaPer.text = "0%"
        binding.tvNormalPer.text = "0%"
        setUnitVisiable(false)
        when (timeCode) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                binding.cBarChart.visibility = View.GONE
                binding.cLineChart.visibility = View.VISIBLE
                binding.cLineChart.setValue(mutableListOf(), 0, true)
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                binding.cBarChart.visibility = View.VISIBLE
                binding.cLineChart.visibility = View.GONE
                binding.cBarChart.setValue(
                    null,
                    TimeUtils.getWeekListStr(),
                    0,
                    HeartBarChart.BarChartType.WEEK, true
                )
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                binding.cBarChart.visibility = View.VISIBLE
                binding.cLineChart.visibility = View.GONE
                binding.cBarChart.setValue(
                    null,
                    TimeUtils.getXDataWithStep(TimeUtils.getMonthListStr(), 7),
                    0,
                    HeartBarChart.BarChartType.MONTH, true
                )
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                binding.cBarChart.visibility = View.VISIBLE
                binding.cLineChart.visibility = View.GONE
                binding.cBarChart.setValue(
                    null,
                    mutableListOf(
                        "${TimeUtils.getCurrentYearStr()}\n01",
                        "02",
                        "03",
                        "04",
                        "05",
                        "06",
                        "07",
                        "08",
                        "09",
                        "10",
                        "11",
                        "12"
                    ),
                    0,
                    HeartBarChart.BarChartType.YEAR, true
                )
            }
        }
    }

    fun isDataListNotEmpty(dataList: ArrayList<HeartRateStatItemDTO>?, cardType: String): Boolean {
        //如果是日 直接返回true 不需要内部判断 只有周月年需要内部数据判断
        //由于数据插值 获取到的list会是一个全为0的list 需要判断是否全为0 如果全是0 则认为列表为空的
        if (cardType == TimeCode.TIME_CODE_DAY.timeCode) {
            if (dataList == null) return false
            else {
                dataList.forEach {
                    if (it.last != 0f) return true
                }
            }
        } else {
            if (dataList == null) return false
            else {
                dataList.forEach {
                    if (it.max != 0f) return true
                }
            }
        }
        return false
    }

    fun setUnitVisiable(sw: Boolean) {
//        if (sw) {
//            binding.tvIntroTimeValueUnit.visibility = View.VISIBLE
//            binding.tvIntroRangeValueUnit.visibility = View.VISIBLE
//        } else {
//            binding.tvIntroTimeValueUnit.visibility = View.GONE
//            binding.tvIntroRangeValueUnit.visibility = View.GONE
//        }
    }

    fun getDayFetchTime(): String {
        val calendar = Calendar.getInstance()
        val month = calendar[Calendar.MONTH] + 1 // 月份是从0开始的，需要加1
        val day = calendar[Calendar.DAY_OF_MONTH]
        return "${month}月${day}日"
    }

    override fun onPrivacyModeChange(provacyMode: Boolean) {
        if (isProvacyMode != provacyMode) {
            //当前模式与变化模式不同
            sendRequest(mUserId, cardTimeType!!)
            isProvacyMode = provacyMode
        }
    }

//    private val globalLayoutListener = object : ViewTreeObserver.OnGlobalLayoutListener{
//        override fun onGlobalLayout() {
//            binding.ivIntroTips.viewTreeObserver.removeOnGlobalLayoutListener { this }
//            val parentView=binding.ivIntroTips.parent as View
//            val rect = Rect()
//            binding.ivIntroTips.getHitRect(rect)
//            rect.top-=24
//            rect.bottom+=100
//            rect.left-=100
//            rect.right+=24
//            parentView.touchDelegate = TouchDelegate(rect, binding.ivIntroTips)
//        }
//    }

    private fun initExplain() {
        val tips = binding.ivIntroTips
        tips.visibility = View.VISIBLE
        viewAddOnGlobalLayoutListener(tips)
        this.introString = getString(R.string.description_heartrate)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        try {
            dialog?.dismiss()
            dialog = null
            viewRemoveOnGlobalLayoutListener(binding.ivIntroTips)
            binding.healthRiskAll.tvHealthAdviceContent.setCallback(null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}