package com.healthlink.hms.fragment

import android.app.Dialog
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver
import android.widget.Button
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.ClickUtils
import com.healthlink.hms.R
import com.healthlink.hms.activity.card.HMSCardFragmentInteractWithAcInterface
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.databinding.FragmentSpo2Time2Binding
import com.healthlink.hms.fragment.viewmodel.SpO2FragmentModel
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.AltitudeItemDTO
import com.healthlink.hms.core.model.dto.HealthDataStatusDTO
import com.healthlink.hms.core.model.dto.HealthSpO2SummaryDTO
import com.healthlink.hms.core.model.dto.SpO2Card1DTO
import com.healthlink.hms.core.model.dto.SpO2Card2DTO
import com.healthlink.hms.core.model.dto.SpO2ItemDTO
import com.healthlink.hms.core.model.dto.SpO2ItemResponseDTO
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.TimeUtils.resetWMDateTime
import com.healthlink.hms.utils.TimeUtils.resetYDateTime
import com.healthlink.hms.utils.getPrivacyModeDate
import com.healthlink.hms.viewmodels.MainViewModel
import com.healthlink.hms.views.ImmersiveDialog
import com.healthlink.hms.views.MiddleEllipsesTextView
import java.lang.ref.WeakReference
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Calendar

/**
 *@Author：付仁秀
 *@Description： 血氧 fragment
 **/
class SpO2TimeFragment2 : BaseCardFragment<FragmentSpo2Time2Binding, MainViewModel>(
    MainViewModel::class.java,
    R.layout.fragment_spo2_time2
), MiddleEllipsesTextView.UpdateSeeMore {
    private var chartWeekList = arrayListOf<SpO2ItemDTO>()
    private var chartMonthList = arrayListOf<SpO2ItemDTO>()
    private var chartYearList = arrayListOf<SpO2ItemDTO>()
    private var healthWeekData: HealthSpO2SummaryDTO? = null
    private var healthMonthData: HealthSpO2SummaryDTO? = null
    private var healthYearData: HealthSpO2SummaryDTO? = null
    private var reqMap = mapOf<String, String>()
    private var healthAdviceStr = ""
    private var introString = ""
    private var isProvacyMode = false
    private var isNoDataMode = true
    private var scrollFlags = false
    private var spO2Card2DTODefault = SpO2Card2DTO(
        "0",
        "0",
        "0",
        "0",
        "0",
        "0",
        "0",
        "0",
        "0"
    )

    private var fragmentDataModel = SpO2FragmentModel()

    companion object {
        private const val TAG = "SpO2TimeFragment2"
        private const val ARG_PARAM_TYPE = "ARG_PARAM_TYPE"
        private val fragmentInteractWithAC
            get() = _fragmentInteractWithAC?.get()
        private var _fragmentInteractWithAC: WeakReference<HMSCardFragmentInteractWithAcInterface>? =
            null
        private lateinit var mUserId: String

        fun newInstance(
            cartTimeType: TimeCode,
            userId: String,
            interact: HMSCardFragmentInteractWithAcInterface
        ): SpO2TimeFragment2 {
            val fragment = SpO2TimeFragment2()
            val args = Bundle()
            args.putString(ARG_PARAM_TYPE, cartTimeType.timeCode)
            fragment.arguments = args
            _fragmentInteractWithAC = WeakReference(interact)
            mUserId = userId
            return fragment
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            cardTimeType = it.getString(ARG_PARAM_TYPE)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()
        initData()
        // 恢复数据
        if(savedInstanceState!=null){
            restoreDataIfPossible(savedInstanceState)
        }
    }

    private fun restoreDataIfPossible(savedInstanceState: Bundle) {
        try {
            var fragmentSavedData = savedInstanceState.getString(KEY_SAVED_DATA_SUMMARY)
            if (fragmentSavedData != null) {
                var fragmentDataModel =
                    gson.fromJson(fragmentSavedData, SpO2FragmentModel::class.java)
                if(fragmentDataModel!=null){
                    this.fragmentDataModel = fragmentDataModel
                    binding.svContainer.scrollY = this.fragmentDataModel.scollY
                    if (this.fragmentDataModel.isShowExplainDialog) {
                        showExplainDialog()
                    }
                    Log.i(TAG, "init SpO2TimeFragment2 data from saved fragment success.")
                }
            }
        }catch (ex: Exception){
            Log.i(TAG, "init SpO2TimeFragment2 data from saved fragment fail. error : ${ex.message}")
        }
    }

    override fun sendRequest(userId: String, timeCode: String) {
        reqMap = mapOf(
            "userId" to userId,
            "unit" to timeCode
        )
        if (!HmsApplication.isPrivacyModeEnabled()) {
            if (!HmsApplication.isNetworkConn()) {
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                resetData()
                if (isDataReady)
                    showLoading()

                //缓存有效，直接使用缓存展示
                var isCachedValid = false
                when(cardTimeType){
                    TimeCode.TIME_CODE_WEEK.timeCode -> {
                        if(this.fragmentDataModel.spo2WeekChartData!=null
                            && this.fragmentDataModel.spo2WeekChartData!!.code == "0"){
                            isCachedValid = true
                            processHealthDataWeek(this.fragmentDataModel.spo2WeekChartData!!)
                        }
                    }
                    TimeCode.TIME_CODE_MONTH.timeCode -> {
                        if(this.fragmentDataModel.spo2MonthChartData!=null
                            && this.fragmentDataModel.spo2MonthChartData!!.code == "0"){
                            isCachedValid = true
                            processHealthDataMonth(this.fragmentDataModel.spo2MonthChartData!!)
                        }
                    }
                    TimeCode.TIME_CODE_YEAR.timeCode -> {
                        if(this.fragmentDataModel.spo2YearChartData!=null
                            && this.fragmentDataModel.spo2YearChartData!!.code == "0"){
                            isCachedValid = true
                            processHealthDataYear(this.fragmentDataModel.spo2YearChartData!!)
                        }
                    }
                }
                // 缓存无效，访问网络获取
                if(!isCachedValid) {
                    viewModel.getSpO2DetailData(reqMap)
                }
            }
        } else initPrivacyUI(timeCode)
    }

    private fun resetData() {
        binding.tvC41Per.text = "0%"
        binding.tvC42Per.text = "0%"
        binding.tvC43Per.text = "0%"

        moveToBottom(binding.tvSpo2CurMinUnit)
        moveToBottom(binding.tvSpo2CurMaxUnit)
        moveToBottom(binding.tvSpo2MinUnit)
        moveToBottom(binding.tvSpo2MaxUnit)
    }

    override fun sendDataReadyRequest(userId: String, timeCode: String) {
        when (timeCode) {
            TimeCode.TIME_CODE_WEEK.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Bloodoxygenreports_Weektab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.cSpO2Wmy.setXData(
                    TimeUtils.getWeekListStr(),
                    TimeCode.TIME_CODE_WEEK.timeCode
                )
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Bloodoxygenreports_Mouthtab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.cSpO2Wmy.setXData(
                    TimeUtils.getMonthListStr(),
                    TimeCode.TIME_CODE_MONTH.timeCode
                )
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Bloodoxygenreports_Yeartab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.cSpO2Wmy.setXData(null, TimeCode.TIME_CODE_YEAR.timeCode)
            }
        }
        applyPrivacyStyleChanged(HmsApplication.isPrivacyModeEnabled())
        if (!HmsApplication.isPrivacyModeEnabled()) {
            if (!isProvacyMode) {
                if (!HmsApplication.isNetworkConn()) {
                    showNetErrorOrSettingView()
                    fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
                }else{
                    showLoading()
                }
            }
            resetData()
            setScrollEnable(binding.svContainer, false)
            viewModel.getHistoryStatusData(userId)
        } else initPrivacyUI(timeCode)
    }

    override fun onResume() {
        super.onResume()
        isProvacyMode = HmsApplication.isPrivacyModeEnabled()
        if (!isProvacyMode) setScrollEnable(binding.svContainer)
        if (isDataReady)
            sendRequest(mUserId, cardTimeType!!)
        else
            handler.post(runnable)
    }

    override fun onPause() {
        super.onPause()
        handler.removeCallbacks(runnable)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        // 判断数据的合理性
        var isHealthDataValid = false
        when(cardTimeType){
            TimeCode.TIME_CODE_WEEK.timeCode -> {
                if(this.fragmentDataModel.spo2WeekChartData!=null
                    && this.fragmentDataModel.spo2WeekChartData!!.code == "0"){
                    isHealthDataValid = true
                }
            }
            TimeCode.TIME_CODE_MONTH.timeCode -> {
                if(this.fragmentDataModel.spo2MonthChartData!=null
                    && this.fragmentDataModel.spo2MonthChartData!!.code == "0"){
                    isHealthDataValid = true
                }
            }
            TimeCode.TIME_CODE_YEAR.timeCode -> {
                if(this.fragmentDataModel.spo2YearChartData!=null
                    && this.fragmentDataModel.spo2YearChartData!!.code == "0"){
                    isHealthDataValid = true
                }
            }
        }
        // 保存数据
        if(isHealthDataValid
            && fragmentDataModel.healthSummaryResponse!=null
            && fragmentDataModel.healthSummaryResponse!!.code == "0"
        ){
            fragmentDataModel.scollY = binding.svContainer.scrollY
            outState.putString(KEY_SAVED_DATA_SUMMARY, gson.toJson(fragmentDataModel));
        }

    }

    private val runnable = UpdateRunnable(this)
    private class UpdateRunnable(private val fragment: SpO2TimeFragment2) : Runnable {
        private val weakFragment = WeakReference(fragment)

        override fun run() {
            val fragment = weakFragment.get()
            if (fragment != null && !fragment.isDetached) {
                if (!fragment.isDataReady) {
                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
                    // 15 秒后再次调用
                    if (HmsApplication.isNetworkConn())
                        fragment.handler.postDelayed(this, 15000)
                }
            }
        }
    }

//    private val runnable = object : Runnable {
//        private val weekFragment = WeakReference(this@SpO2TimeFragment2)
//        override fun run() {
//            val fragment = weekFragment.get()
//            if (fragment != null && !fragment.isDetached) {
//                if (!fragment.isDataReady) {
//                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
//                    // 15 秒后再次调用
//                    if ( HmsApplication.isNetworkConn())
//                    fragment.handler.postDelayed(this, 15000)
//                }
//            }
//        }
//    }

    fun getDataReady() {
        isDataReady = true
        handler.removeCallbacks(runnable)
        sendRequest(mUserId, cardTimeType!!)
    }

    fun readyDataNoAuth() {
        handler.removeCallbacks(runnable)
        showNoAuthView()
        fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
    }

    private fun initData() {
        viewModel.healthHistoryData.observe(viewLifecycleOwner) {
            val notReadyText =
                "血氧${requireContext().resources.getString(R.string.text_data_not_ready)}"
            if (it.code == "0" && it.data != null) {
                val statusList = it.data?.dataStatusList
                if (statusList.isNullOrEmpty()) //如果是空的 也认为是有数据的
                {
                    getDataReady()
                    return@observe
                }
                var status: HealthDataStatusDTO? = null
                if (!statusList.isNullOrEmpty()) {
                    val statusArray = statusList.filter { it.dataType == "spo2Read" }
                    if (!statusArray.isNullOrEmpty()) {
                        status = statusArray[0]
                    } else {
                        getDataReady()
                        return@observe
                    }
                }
                if (status != null) {
                    when (cardTimeType) {

                        TimeCode.TIME_CODE_WEEK.timeCode -> {
                            if (status.weekDataStatus == null || status.weekDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_MONTH.timeCode -> {
                            if (status.monthDataStatus == null || status.monthDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_YEAR.timeCode -> {
                            if (status.yearDataStatus == null || status.yearDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }
                    }
                } else {
                    showNetErrorOrSettingView()
                    fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
                }
            } else if (it.code == "5") {
                showNoAuthView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                // 无网络或者刷新失败处理
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            }
        }

        viewModel.healthSpO2SummeryData.observe(requireActivity()) {
            this.fragmentDataModel.healthSummaryResponse = it
            processSummaryData(it)
        }

        viewModel.spo2WeekChartData.observe(requireActivity()) {
            this.fragmentDataModel.spo2WeekChartData = it
            processHealthDataWeek(it)
        }

        viewModel.spo2MonthChartData.observe(requireActivity()) {
            this.fragmentDataModel.spo2MonthChartData = it
            processHealthDataMonth(it)
        }

        viewModel.spo2YearChartData.observe(requireActivity()) {
            this.fragmentDataModel.spo2YearChartData = it
            processHealthDataYear(it)
        }
        var spO2Card2DTO: SpO2Card2DTO
        // 给周月年注册滑动监听
        when (cardTimeType) {
            // 此处修改可以参照睡眠SleepTimeFragment的逻辑
            TimeCode.TIME_CODE_WEEK.timeCode -> {
                binding.cSpO2Wmy.setOnDaySelectListener { index, item ->
                    if (chartWeekList.isEmpty()) return@setOnDaySelectListener
                    var showCardDTO = SpO2Card1DTO(
                        "",//startTime 有空值
                        "--",
                        "",
                        "--",
                    )
                    if (item != null && index < chartWeekList.size) {
                        if (!chartWeekList.isNullOrEmpty()) {
                            showCardDTO = SpO2Card1DTO(
                                getFetchTime(chartWeekList[index].createTime),//startTime 有空值
                                chartWeekList[index].avg.toString(),
                                chartWeekList[index].max.toString(),
                                chartWeekList[index].min.toString()
                            )

                        }
                    } else {
                        val weekDayList = TimeUtils.getWeekListStr()
                        if (!weekDayList.isNullOrEmpty() && index < weekDayList.size) {
                            showCardDTO = SpO2Card1DTO(
                                resetWMDateTime(weekDayList[index]),//startTime 有空值
                                "--",
                                "-",
                                "--",
                            )
                        }
//                        showCardDTO = SpO2Card1DTO(
//                            getFetchTime(chartWeekList[index].createTime),//startTime 有空值
//                            "--",
//                            "-",
//                            "--",
//                        )
                    }
                    binding.cardShowInfo = showCardDTO
                }
                binding.cSpO2Wmy.setOnXTextSelectListener { index, xText ->
                    if ((!chartWeekList.isNullOrEmpty() && index >= chartWeekList.size)) {
                        val showCardDTO = SpO2Card1DTO(
                            resetWMDateTime(xText),//startTime 有空值
                            "--",
                            "-",
                            "--",
                        )
                        binding.cardShowInfo = showCardDTO
                    } else if (isNoDataMode) {
                        val showCardDTO = SpO2Card1DTO(
                            resetWMDateTime(xText),//startTime 有空值
                            "--",
                            "-",
                            "",
                        )
                        binding.cardShowInfo = showCardDTO
                    }
                }
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                binding.cSpO2Wmy.setOnDaySelectListener { index, item ->
                    // 将 if (chartMonthList.isEmpty() || index >= chartMonthList.size) return@setOnDaySelectListener
                    // index >= chartMonthList.size 判断放开
                    if (chartMonthList.isEmpty()) return@setOnDaySelectListener
                    var showCardDTO = SpO2Card1DTO(
                        "",//startTime 有空值
                        "--",
                        "",
                        "--",
                    )
                    // 加入超出索引判断
                    if (item != null && index < chartMonthList.size) {
                        if (!chartMonthList.isNullOrEmpty()) {
                            showCardDTO = SpO2Card1DTO(
                                getFetchTime(chartMonthList[index].createTime),//startTime 有空值
                                chartMonthList[index].avg.toString(),
                                chartMonthList[index].max.toString(),
                                chartMonthList[index].min.toString(),
                            )

                        }
//                        binding.tvSpo2MinUnit.visibility = View.VISIBLE
                    } else {
                        // 从默认数据源里获取
                        val monthDayList = TimeUtils.getMonthListStr()
                        if (monthDayList.isEmpty() || index >= monthDayList.size) return@setOnDaySelectListener
                        showCardDTO = SpO2Card1DTO(
                            resetWMDateTime(monthDayList[index]),//startTime 有空值
                            "--",
                            "-",
                            "--",
                        )
//                        binding.tvSpo2MinUnit.visibility = View.GONE
                    }
//                    if (!healthMonthData?.valueList.isNullOrEmpty() && index < healthMonthData?.valueList!!.size) {
//                        val spo2H = healthMonthData!!.valueList[index]
//                        spO2Card2DTO = SpO2Card2DTO(
//                            spo2H.spo2hAvg,
//                            spo2H.spo2hLevel,
//                            spo2H.normalProp,
//                            spo2H.lowProp,
//                            spo2H.tooLowProp,
//                            spo2H.normalSum,
//                            spo2H.lowSum,
//                            spo2H.tooLowSum,
//                            spo2H.createTime
//                        )
//                    } else {
//                        spO2Card2DTO = spO2Card2DTODefault
//                    }
//                    binding.cardShowInfo2 = spO2Card2DTO
                    binding.cardShowInfo = showCardDTO
                }
                val monthDayList = TimeUtils.getMonthListStr()
                binding.cSpO2Wmy.setOnXTextSelectListener { index, xText ->
                    try {
                        if (isNoDataMode || (!healthMonthData?.valueList.isNullOrEmpty() && index >= healthMonthData!!.valueList.size)) {
                            val showCardDTO = SpO2Card1DTO(
                                resetWMDateTime(monthDayList[index]),//startTime 有空值
                                "--",
                                "-",
                                "",
                            )
                            binding.cardShowInfo = showCardDTO
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }

                }
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                binding.cSpO2Wmy.setOnDaySelectListener { index, item ->
                    if (chartYearList.isEmpty()) return@setOnDaySelectListener // || index >= chartYearList.size 放开判断
                    var showCardDTO = SpO2Card1DTO(
                        "",//startTime 有空值
                        "--",
                        "",
                        "--",
                    )
                    if (item != null && index < chartYearList.size) {
                        if (!chartYearList.isNullOrEmpty()) {
                            showCardDTO = SpO2Card1DTO(
                                getFetchTime(chartYearList[index].createTime),//startTime 有空值
                                chartYearList[index].avg.toString(),
                                chartYearList[index].max.toString(),
                                chartYearList[index].min.toString()
                            )

                        }
//                        binding.tvSpo2MinUnit.visibility = View.VISIBLE
                    } else {
                        val monthDayList = TimeUtils.getAllMonthsOfCurrentYear("yyyy年M月")
                        if (monthDayList.isEmpty() || index >= monthDayList.size) return@setOnDaySelectListener
                        showCardDTO = SpO2Card1DTO(
                            monthDayList[index],//startTime 有空值
                            "--",
                            "-",
                            "--",
                        )
//                        binding.tvSpo2MinUnit.visibility = View.GONE
                    }

//                    if (!healthYearData?.valueList.isNullOrEmpty() && index < healthYearData?.valueList!!.size) {
//                        val spo2H = healthYearData!!.valueList[index]
//                        spO2Card2DTO = SpO2Card2DTO(
//                            spo2H.spo2hAvg,
//                            spo2H.spo2hLevel,
//                            spo2H.normalProp,
//                            spo2H.lowProp,
//                            spo2H.tooLowProp,
//                            spo2H.normalSum,
//                            spo2H.lowSum,
//                            spo2H.tooLowSum,
//                            spo2H.createTime
//                        )
//                    } else {
//                        spO2Card2DTO = spO2Card2DTODefault
//                    }
//                    binding.cardShowInfo2 = spO2Card2DTO
                    binding.cardShowInfo = showCardDTO
                }
                binding.cSpO2Wmy.setOnXTextSelectListener { index, xText ->
                    if (isNoDataMode || (!healthYearData?.valueList.isNullOrEmpty() && index >= healthYearData!!.valueList.size)) {
                        val showCardDTO = SpO2Card1DTO(
                            resetYDateTime(xText),//startTime 有空值
                            "--",
                            "-",
                            "",
                        )
                        binding.cardShowInfo = showCardDTO
                    }
                }
            }

        }

    }

    private fun processHealthDataYear(it: BaseResponse<SpO2ItemResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            chartYearList = it.data!!.nodeList
            if (!chartYearList.isNullOrEmpty() && isYDataListNotEmpty(chartYearList)) {
                // 第一张卡片
                val showCardDTO = SpO2Card1DTO(
                    getFetchTime(chartYearList.last().createTime),//startTime 有空值
                    chartYearList.last().avg.toString(),
                    chartYearList.last().max.toString(),
                    chartYearList.last().min.toString()
                )
                // 第二张卡片
                val showCardDTO1 = SpO2Card1DTO(
                    "", "",
                    it.data!!.max.toString(),
                    it.data!!.min.toString()
                )

                binding.cardShowInfo = showCardDTO
                binding.cardShowInfo1 = showCardDTO1
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                map.put("startTime", it.data!!.startTime)
                map.put("endTime", it.data!!.endTime)

                initChartData()

                processLoadingSummaryData(map)
            } else {
                initChartNoData(TimeCode.TIME_CODE_YEAR.timeCode)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }

        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 500)
    }

    private fun processHealthDataMonth(it: BaseResponse<SpO2ItemResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            chartMonthList = it.data!!.nodeList
            if (!chartMonthList.isNullOrEmpty() && isYDataListNotEmpty(chartMonthList)) {
                // 第一张卡片
                val showCardDTO = SpO2Card1DTO(
                    getFetchTime(chartMonthList.last().createTime),//startTime 有空值
                    chartMonthList.last().avg.toString(),
                    chartMonthList.last().max.toString(),
                    chartMonthList.last().min.toString()
                )

                // 第二张卡片
                val showCardDTO1 = SpO2Card1DTO(
                    "", "",
                    it.data!!.max.toString(),
                    it.data!!.min.toString()
                )

                binding.cardShowInfo = showCardDTO
                binding.cardShowInfo1 = showCardDTO1
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                map.put("startTime", it.data!!.startTime)
                map.put("endTime", it.data!!.endTime)

                initChartData()

                processLoadingSummaryData(map)

            } else {
                initChartNoData(TimeCode.TIME_CODE_MONTH.timeCode)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 400)
    }

    private fun processHealthDataWeek(it: BaseResponse<SpO2ItemResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            val tempChartWeekList = it.data!!.nodeList
            if (!tempChartWeekList.isNullOrEmpty() && isYDataListNotEmpty(tempChartWeekList)) {
                val tempWeakList = tempChartWeekList.clone() as ArrayList<SpO2ItemDTO>
                val lastData =  tempWeakList.last()
                // 第一张卡片
                val showCardDTO = SpO2Card1DTO(
                    getFetchTime(lastData.createTime),//startTime 有空值
                    lastData.avg.toString(),
                    lastData.max.toString(),
                    lastData.min.toString(),
                )

                // 第二张卡片
                val showCardDTO1 = SpO2Card1DTO(
                    "", "",
                    it.data!!.max.toString(),
                    it.data!!.min.toString()
                )

                binding.cardShowInfo = showCardDTO
                binding.cardShowInfo1 = showCardDTO1
                chartWeekList = tempWeakList.toMutableList() as ArrayList<SpO2ItemDTO>
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                map.put("startTime", it.data!!.startTime)
                map.put("endTime", it.data!!.endTime)

                initChartData()

                processLoadingSummaryData(map)
            } else {
                initChartNoData(TimeCode.TIME_CODE_WEEK.timeCode)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 400)
    }

    /**
     * 判断是否有合理的健康建议数据。如果有，则直接使用，否则去服务器获取
     */
    fun processLoadingSummaryData(map: MutableMap<String,String>){
        if(fragmentDataModel==null
            || fragmentDataModel.healthSummaryResponse == null
            || fragmentDataModel.healthSummaryResponse!!.code != "0") {
            viewModel.getHealthSpO2Summery(map)
        }else{
            processSummaryData(fragmentDataModel.healthSummaryResponse!!)
        }
    }

    private fun processSummaryData(it: BaseResponse<HealthSpO2SummaryDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.healthSpO2SummeryVo = it.data
            binding.svContainer.post {
                binding.svContainer.requestLayout()
            }
            Handler(Looper.getMainLooper()).postDelayed({
                hideLoading()
                setScrollEnable(binding.svContainer)
            }, 100)
            // 重置查看更多按钮为不可见
            binding.healthRiskAll.tvHealthAdviceContent.setIsEllipsized(false)

            it.data!!.healthAdvice?.let { item ->
                healthAdviceStr = item
            }
    //                it.data!!.nounExplain?.let { explain ->
    //                    if (explain.isNotEmpty()){
    //                        this.introString=explain
    //                        binding.ivIntroTips.visibility=View.VISIBLE
    //                    }
    //                }
            when (cardTimeType) {
                TimeCode.TIME_CODE_WEEK.timeCode -> {
                    healthWeekData = it.data
                    buildCard3Info(it.data!!)
                }

                TimeCode.TIME_CODE_MONTH.timeCode -> {
                    healthMonthData = it.data
                    buildCard3Info(it.data!!)
                }

                TimeCode.TIME_CODE_YEAR.timeCode -> {
                    healthYearData = it.data
                    buildCard3Info(it.data!!)
                }
            }
        }

        // 恢复滚动位置
        if(this.fragmentDataModel.scollY>0) {
            binding.svContainer.post({
                binding.svContainer.scrollY = this.fragmentDataModel.scollY
            })
        }
    }

    private fun buildCard3Info(data: HealthSpO2SummaryDTO) {
        var spO2Card2DTO = SpO2Card2DTO(
            data.spo2hAvg,
            data.spo2hLevel,
            data.normalProp,
            data.lowProp,
            data.tooLowProp,
            data.normalSum,
            data.lowSum,
            data.tooLowSum,
            data.createTime
        )
        binding.cardShowInfo2 = spO2Card2DTO
    }

    private fun initUI() {
        initExplain()
        binding.ivIntroTips.findViewById<ImageView>(R.id.iv_intro_tips)
        ClickUtils.applySingleDebouncing(binding.ivIntroTips, 1000) {
            showExplainDialog()
        }
        binding.healthRiskAll.tvHealthAdviceContent.setCallback(this)
        binding.healthRiskAll.tvHealthAdviceContent.setEndPercentage(80)
        binding.healthRiskAll.tvSeeMore.text = Html.fromHtml("<u>查看更多</u>")
        val scrollView = binding.svContainer
        scrollView.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            val height = scrollView.getChildAt(0).height // 获取ScrollView内容的总高度
            val scrollViewHeight = scrollView.height // 获取ScrollView的可见高度
            val diff = (height - scrollViewHeight) * 0.75f //scrollview中判定的距离 动画view位置底部约为总长度的的75%
            if (scrollY >= diff) {
                MMKVUtil.getUserId()?.let {
                    DataTrackUtil.dtScroll(
                        "Health_Bloodoxygenreports_Bottom_Show",
                        DataTrackUtil.userIDMap(it)
                    )
                }
            }
        }

    }

    private fun showExplainDialog() {
        showHmsDialog(R.layout.hms_dialog_tips_small, "血氧说明", introString)
    }

    fun initChartData() {
        isNoDataMode = false
        binding.tvSpo2CurMinValue.visibility = View.VISIBLE
        binding.tvSpo2MinUnit.visibility = View.VISIBLE
        binding.sPrivacyText.visibility = View.GONE
        setUnitVisiable(true)
        // 根据不同的cardTimeType 初始化不同的view
        when (cardTimeType) {
            // 时间类型日 - 折线图

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                if (chartWeekList.isEmpty()) {
                    binding.cSpO2Wmy.visibility = View.GONE
                    binding.spo2NoDataText.visibility = View.VISIBLE
                    return
                }
                binding.cSpO2Wmy.visibility = View.VISIBLE
                binding.spo2NoDataText.visibility = View.GONE
                initWeekChart()
            }
//
            TimeCode.TIME_CODE_MONTH.timeCode -> {
                if (chartMonthList.isEmpty()) {
                    binding.cSpO2Wmy.visibility = View.GONE
                    binding.spo2NoDataText.visibility = View.VISIBLE
                    return
                }
                binding.cSpO2Wmy.visibility = View.VISIBLE
                binding.spo2NoDataText.visibility = View.GONE
                initMonthChart()
            }
//
            TimeCode.TIME_CODE_YEAR.timeCode -> {
                if (chartYearList.isEmpty()) {
                    binding.cSpO2Wmy.visibility = View.GONE
                    binding.spo2NoDataText.visibility = View.VISIBLE
                    return
                }
                binding.cSpO2Wmy.visibility = View.VISIBLE
                binding.spo2NoDataText.visibility = View.GONE
                initYearChart()
            }

        }

    }

    fun initChartNoData(timeCode: String) {
        isNoDataMode = true
        binding.cSpO2Wmy.visibility = View.VISIBLE
        binding.sPrivacyText.visibility = View.GONE
        setUnitVisiable(false)
        when (timeCode) {
            TimeCode.TIME_CODE_WEEK.timeCode -> binding.cSpO2Wmy.setValue(
                null,
                null,
                1,
                timeCode
            )

            TimeCode.TIME_CODE_MONTH.timeCode -> binding.cSpO2Wmy.setValue(
                null,
                null,
                1,
                timeCode
            )

            TimeCode.TIME_CODE_YEAR.timeCode -> binding.cSpO2Wmy.setValue(
                null,
                null,
                1,
                timeCode
            )
        }
        binding.tvSpo2CurMinValue.text = "--"
        binding.tvSpo2CurMaxValue.text = ""
        binding.tvSpo2MinValue.text = "--"
        binding.tvSpo2MaxValue.text = ""
        if (timeCode == TimeCode.TIME_CODE_YEAR.timeCode) {
            binding.tvSleepDate.text = "${Calendar.getInstance()[Calendar.YEAR]}年7月"
        }
        if (timeCode == TimeCode.TIME_CODE_MONTH.timeCode) {
            binding.tvSleepDate.text = "${Calendar.getInstance()[Calendar.MONTH] + 1}月16日"
        }
    }


    private fun initWeekChart() {
        binding.cSpO2Wmy.setValue(
            chartWeekList,
            TimeUtils.getWeekListStr(),
            getBarType(chartWeekList),
            TimeCode.TIME_CODE_WEEK.timeCode
        )

    }

    private fun initMonthChart() {
        binding.cSpO2Wmy.setValue(
            chartMonthList,
            TimeUtils.getMonthListStr(),
            getBarType(chartMonthList),
            TimeCode.TIME_CODE_MONTH.timeCode
        )

    }

    private fun initYearChart() {
        binding.cSpO2Wmy.setValue(
            chartYearList,
            null,
            getBarType(chartYearList),
            TimeCode.TIME_CODE_YEAR.timeCode
        )

    }

    private var dialog: Dialog? = null

    /**
     * 显示自定义对话框
     */
    fun showHmsDialog(layoutId: Int, title: String, message: String?) {
        // 创建自定义视图
        val view = LayoutInflater.from(requireActivity()).inflate(layoutId, null)
        val contentView = view.findViewById<RelativeLayout>(R.id.dialog_content)
        var btnPositive = view.findViewById<Button>(R.id.positiveButton)
        var tvMessage = view.findViewById<TextView>(R.id.textView)
        var tvTitle = view.findViewById<TextView>(R.id.tv_tips_title_small)
        tvTitle.text = title
        if (message != null && !message.isNullOrBlank()) {
            tvMessage.setText(message)
        }
        // 创建并显示对话框
        dialog = ImmersiveDialog(
            requireContext(),
            R.style.MyDialogStyle
        )
        // dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog!!.setContentView(view)
        dialog!!.setOnShowListener {
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    fragmentDataModel.isShowExplainDialog = false
                    dialog?.dismiss()
                }
                false
            }
        }
        // 设置按钮点击事件
        btnPositive.setOnClickListener {
            fragmentDataModel.isShowExplainDialog = false
            dialog?.dismiss()
        }
        dialog!!.show()
        fragmentDataModel.isShowExplainDialog = true
    }


    fun getWeekMonthFetchTime(ori: String): String {
        var result = ""
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val dateTime = LocalDateTime.parse(ori, formatter)
        result =
            "" + dateTime.monthValue + "月" + dateTime.dayOfMonth + "日"

        return result
    }

    fun getYearFetchTime(ori: String?): String {
        if (ori == null) return ""
        var result = ""
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val dateTime = LocalDateTime.parse(ori, formatter)
        result =
            "" + dateTime.year + "年" + dateTime.monthValue + "月"
        return result
    }

    fun getBarType(oriList: ArrayList<SpO2ItemDTO>): Int {
        var max = 0
        var min = 0
        var needInit = true
        oriList.forEachIndexed { index, item ->
            if (item.avg == 0) return@forEachIndexed
            if (needInit) {
                max = item.max
                min = item.min
                needInit = false
            } else {
                if (item.max > max)
                    max = item.max
                if (item.min < min)
                    min = item.min
            }
        }

        if (max in 90..100) {
            if (min in 90..100) return 1
            else if (min in 70 until 90) return 2
            else return 3
        } else if (max in 70 until 90) {
            if (min in 70 until 90) return 4
            else return 5
        } else
            return 6

    }


    override fun update(isEllipsized: Boolean, viewID: Int) {
        if (isEllipsized) {
            binding.healthRiskAll.cardAdviceMore.visibility = View.VISIBLE
            binding.healthRiskAll.cardAdviceMore.setOnClickListener { v ->
                HMSDialogUtils.showHMSNotiDialog(
                    requireContext(),
                    R.layout.hms_dialog_see_more,
                    "健康建议",
                    healthAdviceStr,
                    "知道了"
                ) { isPositive ->
                }
            }
        } else {
            binding.healthRiskAll.cardAdviceMore.visibility = View.GONE
        }
    }

    fun initPrivacyUI(timeCode: String) {
        setScrollEnable(binding.svContainer, false)
        binding.sPrivacyText.visibility = View.VISIBLE
        binding.spo2NoDataText.visibility = View.GONE
        binding.tvSleepDate.text = getPrivacyModeDate(timeCode)
        binding.cSpO2Wmy.visibility = View.GONE
        binding.tvSpo2CurMinValue.text = "***"
        binding.tvSpo2CurMaxValue.text = "***"
        binding.tvSpo2MinValue.text = "***"
        binding.tvSpo2MaxValue.text = "***"
        binding.tvC41Per.text = "***%"
        binding.tvC42Per.text = "***%"
        binding.tvC43Per.text = "***%"

        // 健康建议在私密模式下不展示
        binding.healthSpO2SummeryVo = null

        moveToTop(binding.tvSpo2CurMinUnit)
        moveToTop(binding.tvSpo2CurMaxUnit)
        moveToTop(binding.tvSpo2MinUnit)
        moveToTop(binding.tvSpo2MaxUnit)
        binding.svContainer.visibility = View.VISIBLE
    }

    private fun applyPrivacyStyleChanged(isPrivacyMode: Boolean) {
        binding.llIntroContainer.apply {
            val params = layoutParams as MarginLayoutParams
            params.bottomMargin = if (isPrivacyMode) 30f.dp.toInt() else 0f.dp.toInt()
            layoutParams = params
        }

        binding.tvSpo2CurMinUnit.text = if (isPrivacyMode) "%-" else "%"


        binding.tvSpo2MinUnit.text = if (isPrivacyMode) "%-" else "%"
//        binding.sleepHourUnit.apply {
//            val params = layoutParams as RelativeLayout.LayoutParams
//            params.topMargin = if (isPrivacyMode) 4f.dp.toInt() else 0f.dp.toInt()
//            params.leftMargin = if (isPrivacyMode) 2f.dp.toInt() else 0f.dp.toInt()
//            if (isPrivacyMode) {
//                params.removeRule(RelativeLayout.ALIGN_BASELINE)
//            } else {
//                params.addRule(RelativeLayout.ALIGN_BASELINE, R.id.sleep_hour_value)
//            }
//            layoutParams = params
//        }

        listOf(
            binding.tvC41Per,
            binding.tvC42Per,
            binding.tvC43Per,
        ).forEach {
            val style = if (isPrivacyMode) Typeface.BOLD else Typeface.NORMAL
            it.setTypeface(Typeface.create(Typeface.DEFAULT, style))
            it.invalidate() // 强制重绘以确保变化生效
        }
    }

    fun getAltitudeList(oriList: ArrayList<AltitudeItemDTO>): ArrayList<Int> {
        //海拔数据补齐
        //向前补齐
        var resultList = arrayListOf<Int>()
        var index = 0
        var perValue = 0
        for (i in 0..oriList.size - 1) {
            if (oriList[i].avg != null) {
                perValue = oriList[i].avg!!.toInt()
                index = i
                break
            }
        }
        if (index != 0) {
            for (i in 0..index) {
                resultList.add(perValue)
            }
        }
        //向后补齐
        for (i in index..oriList.size - 1) {
            if (oriList[i].avg != null) {
                resultList.add(oriList[i].avg!!.toInt())
                perValue = oriList[i].avg!!.toInt()
            } else {
                resultList.add(perValue)
            }
        }
        return resultList
    }

    fun getLastAdvice(advice: HealthSpO2SummaryDTO): HealthSpO2SummaryDTO? {
        for (i in advice.valueList.size - 1 downTo 0) {
            if (advice.valueList[i].spo2hAvg != null)
                return advice.valueList[i]
        }
        return null
    }

    fun getFetchTime(time: String?): String {
        return time ?: ""
    }

    fun isYDataListNotEmpty(dataList: ArrayList<SpO2ItemDTO>): Boolean {
        dataList.forEach {
            if (it.avg != 0) return true
        }
        return false
    }

    fun setUnitVisiable(sw: Boolean) {
        if (sw) {
            binding.tvSpo2MinUnit.visibility = View.VISIBLE
            binding.tvSpo2MaxUnit.visibility = View.VISIBLE
            binding.tvSpo2CurMinUnit.visibility = View.VISIBLE
            binding.tvSpo2CurMaxUnit.visibility = View.VISIBLE


        } else {
            binding.tvSpo2MinUnit.visibility = View.GONE
            binding.tvSpo2MaxUnit.visibility = View.GONE
            binding.tvSpo2CurMinUnit.visibility = View.GONE
            binding.tvSpo2CurMaxUnit.visibility = View.GONE
        }
    }

    override fun onPrivacyModeChange(provacyMode: Boolean) {
        if (isProvacyMode != provacyMode) {
            //当前模式与变化模式不同
            sendRequest(mUserId, cardTimeType!!)
            isProvacyMode = provacyMode
        }
    }

    fun initExplain() {
        val tips = binding.ivIntroTips
        tips.visibility = View.VISIBLE
//        tips.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener{
//            override fun onGlobalLayout() {
//                tips.viewTreeObserver.removeGlobalOnLayoutListener { this }
//                val parentView=tips.parent as View
//                val rect = Rect()
//                tips.getHitRect(rect)
//                rect.top-=24
//                rect.bottom+=100
//                rect.left-=100
//                rect.right+=24
//                parentView.touchDelegate = TouchDelegate(rect, tips)
//            }
//        })
        viewAddOnGlobalLayoutListener(tips)
        this.introString = getString(R.string.description_spo2)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        try {
            dialog?.dismiss()
            dialog = null
            viewRemoveOnGlobalLayoutListener(binding.ivIntroTips)
            binding.healthRiskAll.tvHealthAdviceContent.setCallback(null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}