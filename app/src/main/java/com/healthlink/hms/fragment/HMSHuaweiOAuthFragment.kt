package com.healthlink.hms.fragment

import android.app.UiModeManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.JavascriptInterface
import android.webkit.ValueCallback
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import android.widget.LinearLayout
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.google.gson.Gson
import com.healthlink.hms.BuildConfig
import com.healthlink.hms.R
import com.healthlink.hms.activity.HuaweiOAuthActivity
import com.healthlink.hms.activity.HuaweiOAuthActivity.AndroidInterface
import com.healthlink.hms.activity.MainActivity
import com.healthlink.hms.core.common.utils.Constants
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.core.common.utils.SystemPropertyUtils
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.BaseResponseCallback
import com.healthlink.hms.databinding.FragmentHuaweiOauthBinding
import com.healthlink.hms.databinding.FragmentServiceListBinding
import com.healthlink.hms.mvvm.repository.MainRepository
import com.healthlink.hms.reciever.HMSAction
import com.healthlink.hms.reciever.HMSWidgetUpdateDataReceiver
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.core.model.dto.init.InitInfoDTO
import com.healthlink.hms.utils.ToastUtil
import com.healthlink.hms.utils.isDarkModeEnabled
import com.healthlink.hms.viewmodels.MainViewModel
import com.just.agentweb.AgentWeb
import com.just.agentweb.WebChromeClient
import com.just.agentweb.WebViewClient

/**
 * Created by imaginedays on 2024/7/24
 * 华为登录oauth
 */
class HMSHuaweiOAuthFragment : Fragment() {
    private var mTag =  "HMSHuaweiOAuthFragment"
    private lateinit var binding: FragmentHuaweiOauthBinding
    private lateinit var mainViewModel: MainViewModel
    private lateinit var mAgentWeb: AgentWeb
    private val baseDomainName = BuildConfig.BASE_DOMAIN_NAME
    private val appId = 111693185

    private var huaweiOAuthURL = "https://oauth-login.cloud.huawei.com/oauth2/v3/authorize?" +
    "response_type=code&state=login&client_id=${appId}" +
    "&redirect_uri=https%3A%2F%2F${baseDomainName}%2Fapp-api%2Fgwm-cockpit%2Fhms%2Ftoken2" +
    "&access_type=offline&display=touch"

    private var huaweiOAuthURLForAuthorize= "https://oauth-login.cloud.huawei.com/oauth2/v3/authorize?" +
            "response_type=code&state=auth&client_id=${appId}" +
            "&redirect_uri=https%3A%2F%2F${baseDomainName}%2Fapp-api%2Fgwm-cockpit%2Fhms%2Ftoken2" +
            "&access_type=offline&display=touch&scope=openid" +
            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Fheightweight.read" +
            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Fsleep.read" +
            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Fheartrate.read" +
            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Fstress.read" +
            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Fbloodglucose.read" +
            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Fbodytemperature.read" +
            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Foxygensaturation.read" +
            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Freproductive.read" +
            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Fdistance.read" +
            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Fhearthealth.read" +
            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Fbloodpressure.read" +
//            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Fhistorydata.open.week" +
//            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Fhistorydata.open.month" +
            "+https%3A%2F%2Fwww.huawei.com%2Fhealthkit%2Fhistorydata.open.year"


    @RequiresApi(Build.VERSION_CODES.Q)
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huawei_oauth, container, false)
        mainViewModel = ViewModelProvider(requireActivity())[MainViewModel::class.java]
        initOAuthUI()
        return binding.root
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private fun initOAuthUI() {

        //根据白天黑夜模式，设置华为登录样式
        var huaweiOAuthURLWithTheme = huaweiOAuthURL
        if(isDarkModeEnabled(requireContext())){
            huaweiOAuthURLWithTheme = "$huaweiOAuthURL&themeName=dark"
        }

        binding.tvIntro.visibility = View.INVISIBLE

        binding.hwOauthWebviewBack.setOnClickListener{
            var webView = mAgentWeb.webCreator.webView
            if(webView!=null) {
                var url = webView.url
                if (url!=null){
                    if(isLoginPage(url)) {
                        // do noting
                    }else if(isAuthPage(url)){
                        mAgentWeb.clearWebCache()
                        webView.loadUrl(huaweiOAuthURL)
                    }else{
                        mAgentWeb.back()
                    }

                }
            }
        }

        //初始化webView
        mAgentWeb = AgentWeb
            .with(this)
            .setAgentWebParent(binding.wvHuaweiOauthParent, LinearLayout.LayoutParams(-1, -1))
            .useDefaultIndicator()
            .setWebViewClient(HuaweiOAuthWebViewClient())
            .setWebChromeClient(webChromeClient) //title重定向
            .createAgentWeb()
            .ready()
            .go(huaweiOAuthURLWithTheme)

        mAgentWeb.agentWebSettings.webSettings.let{
            it.useWideViewPort = true
            it.loadWithOverviewMode = true // 缩放至屏幕的大小
            it.domStorageEnabled = true
            it.allowContentAccess = true //是否可访问Content Provider的资源，默认值 true
            it.allowFileAccess = true // 是否可访问本地文件，默认值 true
            it.javaScriptEnabled = true
        } //将图片调整到适合webview的大小

        if(isDarkModeEnabled(requireContext())){
            mAgentWeb.agentWebSettings.webSettings.let {
                it.forceDark = WebSettings.FORCE_DARK_ON
            }
        }else{
            mAgentWeb.agentWebSettings.webSettings.let {
                it.forceDark = WebSettings.FORCE_DARK_OFF
            }
        }

        //支持缩放
        mAgentWeb.webCreator.webView.settings.let{
            it.builtInZoomControls = true
            it.setSupportZoom(true)
            //隐藏缩放图标
            it.displayZoomControls = false
            //允许webview对文件的操作
            it.setAllowFileAccessFromFileURLs(true)
            it.setAllowUniversalAccessFromFileURLs(true)
            it.userAgentString="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }

        mAgentWeb.webCreator.webView.setOnLongClickListener { true }

        //清除缓存，以实现重新登录
        mAgentWeb.clearWebCache()

//        var webView = mAgentWeb.webCreator.webView
//        webView.setBackgroundColor(resources.getColor(R.color.card_color_bg))

        // 前端调android 方法
        var bridgeInterface = AndroidInterface(mAgentWeb, requireContext())
        mAgentWeb.jsInterfaceHolder.addJavaObject(
            "hlHmsBridge",bridgeInterface

        )
    }

    override fun onResume() {
        super.onResume()
        mAgentWeb.webLifeCycle.onResume()
    }

    override fun onDestroy() {
        super.onDestroy()
        mAgentWeb.webLifeCycle.onDestroy()
    }

    override fun onPause() {
        super.onPause()
        mAgentWeb.webLifeCycle.onPause()
    }

    /**
     *  自定义 WebViewClient 辅助WebView设置处理关于页面跳转，页面请求等操作【处理tel协议和视频通讯请求url的拦截转发】
     */
    inner class HuaweiOAuthWebViewClient() : WebViewClient() {
        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {

            return true
        }

        override fun shouldInterceptRequest(
            view: WebView,
            request: WebResourceRequest
        ): WebResourceResponse? {

//            Log.d(mTag,"request url is : ${request.url.toString()}")
            if(isLoginPage(request.url.toString())){
                requireActivity().runOnUiThread {
                    binding.tvIntro.visibility = View.GONE
                }

            }
            else if(isAuthPage(request.url.toString())){
                requireActivity().runOnUiThread {
                    binding.tvIntro.visibility = View.VISIBLE
                }
            }

            request.requestHeaders["Sec-Ch-Ua-Platform"] = "Windows"
            request.requestHeaders["Sec-Ch-Ua"] =
                "\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\""
            request.requestHeaders["Accept-Language"] = "zh-CN,zh;q=0.9"
            request.requestHeaders["User-Agent"] =
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36"

            return super.shouldInterceptRequest(view, request)
        }

        override fun shouldOverrideKeyEvent(view: WebView, event: KeyEvent): Boolean {
            return super.shouldOverrideKeyEvent(view, event)
        }
    }

    /**
     * title重定向获取网页的标题
     */
    private val webChromeClient: WebChromeClient = object : WebChromeClient() {

        override fun onReceivedTitle(view: WebView, title: String) {
            // 设置接收到的 title
//            if (tv_title != null) {
//                tv_title.setText(title)
//            }
            super.onReceivedTitle(view, title)
        }

        override fun onShowFileChooser(
            webView: WebView, filePathCallback: ValueCallback<Array<Uri>>,
            fileChooserParams: FileChooserParams
        ): Boolean {
//            mUploadCallbackAboveL = filePathCallback
//            if (videoFlag) {
////                recordVideo();
//            } else {
//                //url中包含saveId=0 代表化验单模块,不包含saveId=0 代表其他,可以调起拍照与相册的方法
////                takePhoto();
//            }
            return true
        }
    }


    /**
     * H5调android方法接口
     */
    inner class AndroidInterface(mAgentWeb: AgentWeb, mContext: Context?) {
        private var agent: AgentWeb? = mAgentWeb

        @JavascriptInterface
        fun hlLoginSuccess(jsonData : String){
            Log.i(mTag,"登录成功！${jsonData}")
//            this@HMSHuaweiOAuthFragment?.doLogin(jsonData)
            //打开授权页面
            binding.tvIntro.visibility = View.VISIBLE
            var huaweiOAuthURLForAuthorizeWidthTheme = huaweiOAuthURLForAuthorize
            if(context !=null && isDarkModeEnabled(context!!)){
                huaweiOAuthURLForAuthorizeWidthTheme = "$huaweiOAuthURLForAuthorize&themeName=dark"
            }
            mAgentWeb.urlLoader.loadUrl(huaweiOAuthURLForAuthorizeWidthTheme)
        }

        @JavascriptInterface
        fun hlAuthSuccess(jsonData : String){
            Log.i("HuaweiOAuthActivity","授权成功！${jsonData}")
            this@HMSHuaweiOAuthFragment?.doLogin(jsonData)
            //打开授权页面
//            mAgentWeb.urlLoader.loadUrl(huaweiOAuthURLForAuthorize)
        }
    }

    private fun doLogin(jsonData: String) {
        var loginUserInfo = Gson().fromJson(jsonData,LoginUserInfo::class.java )
        val userId  = loginUserInfo.userId
        val userToken = loginUserInfo.token
        if (userId != null && !userId.isNullOrBlank() && userToken != null && !userToken.isNullOrBlank()) {
            // 存储用户信息
            MMKVUtil.storeUserId(userId)
            MMKVUtil.storeUserToken(userToken)
            //存储授权信息
            //weightRead,temperatureRead,hearthealthRead,bloodglucoseRead,reproductiveRead,stressRead,bloodpressureRead,historyYear,historyWeek,spo2Read,sleepRead,distanceRead,heartRateRead
            loginUserInfo.userScope?.let {
                //心率
                if (it.contains("heartRateRead")) MMKVUtil.storeHeartRateAuthority(true)
                else MMKVUtil.storeHeartRateAuthority(false)
                //睡眠
                if (it.contains("sleepRead")) MMKVUtil.storeSleepAuthority(true)
                else MMKVUtil.storeSleepAuthority(false)
                //血氧
                if (it.contains("spo2Read")) MMKVUtil.storeBloodOxygenAuthority(true)
                else MMKVUtil.storeBloodOxygenAuthority(false)
                //压力
                if (it.contains("stressRead")) MMKVUtil.storeStressAuthority(true)
                else MMKVUtil.storeStressAuthority(false)
                //体温
                if (it.contains("temperatureRead")) MMKVUtil.storeTempertureAuthority(true)
                else MMKVUtil.storeTempertureAuthority(false)
                //血压
                if (it.contains("bloodpressureRead")) MMKVUtil.storeBloodPressureAuthority(true)
                else MMKVUtil.storeBloodPressureAuthority(false)
            }
//            ToastUtil.makeText(requireContext(), "登录成功", Toast.LENGTH_LONG).show()
            // 调用初始化接口
            sendInitInfoReq()
            // 通知刷新主界面
            mainViewModel.notifyRefreshMainUI.postValue(true)
            // 发送广播 用户状态已经改变
            val intentToApp = Intent(HMSWidgetUpdateDataReceiver.ACTION_HMS_USER_STATUS_UPDATE)
            intentToApp.setPackage(requireContext().packageName)
            requireContext().sendBroadcast(intentToApp)
        } else{
            ToastUtil.makeText(requireContext(), "未获取到用户信息", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * {"userId":"hl20240530183538-807704956","userScope":"weightRead,temperatureRead,hearthealthRead,bloodglucoseRead,reproductiveRead,stressRead,historyYear,historyWeek,spo2Read,sleepRead,distanceRead,heartRateRead","token":"token1234567890","expireIn":3600}
     */
    data class LoginUserInfo(
        var userId: String? = "",
        var userScope: String? = "", // "weightRead,temperatureRead,hearthealthRead,bloodglucoseRead,reproductiveRead,stressRead,historyYear,historyWeek,spo2Read,sleepRead,distanceRead,heartRateRead"
        var token: String? = "", //
        var expireIn: Long? = 0, // 单位s 3600s
        var code : String? = null,
        var message : String? = null
    ) {

    }

    /**
     * 调用初始化接口
     */
    private fun sendInitInfoReq(){
        val vin = SystemPropertyUtils.getSystemPropertyString(Constants.KEY_VECHILE_VIN, Constants.DEFAULT_VECHILE_VIN)
        MMKVUtil.storeVinCode(vin)
        val params = mutableMapOf(
            "vin" to vin,
            "sn" to SystemPropertyUtils.getSystemPropertyString(Constants.VECHILE_SN, "")
        )
        MMKVUtil.getUserId()?.let { params["userId"] = it }

        MainRepository().getInitInfo(params,object : BaseResponseCallback<InitInfoDTO> {
            override fun onSuccess(response: BaseResponse<InitInfoDTO>) {
                response.data?.let {
                    MMKVUtil.storeUserId(it.userId)
                }
            }

            override fun onFailed(response: BaseResponse<InitInfoDTO>) {
                Log.i(mTag, "获取初始化信息失败$response")
            }
        })
    }

    /**
     * 判断当前页面是否是登录页面
     */
    private fun isLoginPage(url:String):Boolean{
        return url.contains("state=login",false)
                && url.contains("oauth2/v3/authorize")
    }
    /**
     * 判断当前页面是否是授权页面
     */
    private fun isAuthPage(url:String):Boolean{
        return url.contains("state=auth",false)
                && url.contains("oauth2/v3/authorize")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        ToastUtil.cancelToast()
    }
}