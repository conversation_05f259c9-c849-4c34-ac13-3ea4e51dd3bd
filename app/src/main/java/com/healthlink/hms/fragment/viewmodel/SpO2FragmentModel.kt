package com.healthlink.hms.fragment.viewmodel

/**
 * 血氧二级页面的DataModel
 */
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.HealthSpO2SummaryDTO
import com.healthlink.hms.core.model.dto.HealthTempSummaryDTO
import com.healthlink.hms.core.model.dto.SpO2ItemResponseDTO
import com.healthlink.hms.core.model.dto.charts.pressure.PressureSummaryDTO

class SpO2FragmentModel{

    /**
     * 页面滚动的Y值
     */
    var scollY : Int = 0

    /**
     * 健康数据
     */
    var healthData : BaseResponse<SpO2ItemResponseDTO>? = null
    /**
     * 健康数据 - 周
     */
    var spo2WeekChartData : BaseResponse<SpO2ItemResponseDTO>? = null

    /**
     * 健康数据 - 月
     */
    var spo2MonthChartData : BaseResponse<SpO2ItemResponseDTO>? = null

    /**
     * 健康数据 - 年
     */
    var spo2YearChartData : BaseResponse<SpO2ItemResponseDTO>? = null


    /**
     * 健康建议响应对象
     */
    var healthSummaryResponse : BaseResponse<HealthSpO2SummaryDTO>? = null

    var isShowExplainDialog : Boolean = false
}


