package com.healthlink.hms.fragment

import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.healthlink.hms.R
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.databinding.FragmentReportWeekBinding
import com.healthlink.hms.viewmodels.MainViewModel
import java.util.Objects

class HealthReportWeekFragment : Fragment() {
    private lateinit var mainViewModel: MainViewModel
    private lateinit var binding: FragmentReportWeekBinding
    private var cardTimeType: String? = null
    companion object {
        private const val ARG_PARAM_TYPE = "ARG_PARAM_TYPE"
        fun newInstance(cartTimeType: TimeCode): HealthReportWeekFragment {
            val fragment = HealthReportWeekFragment()
            val args = Bundle()
            args.putString(ARG_PARAM_TYPE, cartTimeType.timeCode)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            cardTimeType = it.getString(ARG_PARAM_TYPE)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // DataBindingUtil
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_report_week, container, false)
        // 初始化viewmodel
        mainViewModel = ViewModelProvider(Objects.requireNonNull(requireActivity()))[MainViewModel::class.java]
        // 初始化UI
        initUI()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
    }

    private fun initData() {

    }
    private fun initUI() {
        // 设置行程分析文字
        val journeyAnalyse = resources.getString(R.string.journey_analyse)
        binding.tvJourneyAnalyse.text = Html.fromHtml(journeyAnalyse,Html.FROM_HTML_MODE_LEGACY)
    }

}