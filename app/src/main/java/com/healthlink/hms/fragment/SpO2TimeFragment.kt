package com.healthlink.hms.fragment

import android.app.Dialog
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.ClickUtils
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.activity.card.HMSCardFragmentInteractWithAcInterface
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.core.model.dto.AltitudeItemDTO
import com.healthlink.hms.core.model.dto.HealthDataStatusDTO
import com.healthlink.hms.core.model.dto.HealthSpO2SummaryDTO
import com.healthlink.hms.core.model.dto.SpO2Card1DTO
import com.healthlink.hms.core.model.dto.SpO2Card2DTO
import com.healthlink.hms.core.model.dto.SpO2Card3DTO
import com.healthlink.hms.core.model.dto.SpO2ItemDTO
import com.healthlink.hms.core.model.dto.SpO2ItemResponseDTO
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.databinding.FragmentSpo2TimeBinding
import com.healthlink.hms.fragment.viewmodel.SpO2FragmentModel
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.getPrivacyModeDate
import com.healthlink.hms.viewmodels.MainViewModel
import com.healthlink.hms.views.ImmersiveDialog
import com.healthlink.hms.views.MiddleEllipsesTextView
import java.lang.ref.WeakReference
import java.util.Calendar

/**
 *@Author：付仁秀
 *@Description： 血氧 fragment
 **/
class SpO2TimeFragment : BaseCardFragment<FragmentSpo2TimeBinding, MainViewModel>(
    MainViewModel::class.java,
    R.layout.fragment_spo2_time
), MiddleEllipsesTextView.UpdateSeeMore {
    private var chartDayList = arrayListOf<SpO2ItemDTO>()
    private var reqMap = mapOf<String, String>()
    private var healthAdviceStr = ""
    private var introString = ""
    private var altitudeValueList: ArrayList<Int>? = arrayListOf()
    private var altitudeTimeList: ArrayList<String>? = arrayListOf()
    private var pairExt = Pair(0, 0)
    private var pairExt2 = Pair(0, 0)
    private var isNoDataMode = false
    private var isProvacyMode = false

    private var fragmentDataModel = SpO2FragmentModel()

    companion object {
        private const val TAG = "SpO2TimeFragment"
        private const val ARG_PARAM_TYPE = "ARG_PARAM_TYPE"
        private val fragmentInteractWithAC
            get() = _fragmentInteractWithAC?.get()
        private var _fragmentInteractWithAC: WeakReference<HMSCardFragmentInteractWithAcInterface>? =
            null
        private lateinit var mUserId: String

        fun newInstance(
            cartTimeType: TimeCode,
            userId: String,
            interact: HMSCardFragmentInteractWithAcInterface
        ): SpO2TimeFragment {
            val fragment = SpO2TimeFragment()
            val args = Bundle()
            args.putString(ARG_PARAM_TYPE, cartTimeType.timeCode)
            fragment.arguments = args
            _fragmentInteractWithAC = WeakReference(interact)
            mUserId = userId
            return fragment
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            cardTimeType = it.getString(ARG_PARAM_TYPE)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()
        initData()
        // 恢复数据
        if(savedInstanceState!=null){
            restoreDataIfPossible(savedInstanceState)
        }
    }

    private fun restoreDataIfPossible(savedInstanceState: Bundle) {
        try {
            var fragmentSavedData = savedInstanceState.getString(KEY_SAVED_DATA_SUMMARY)
            if (fragmentSavedData != null) {
                var fragmentDataModel =
                    gson.fromJson(fragmentSavedData, SpO2FragmentModel::class.java)
                if(fragmentDataModel!=null){
                    this.fragmentDataModel = fragmentDataModel
                    binding.svContainer.scrollY = this.fragmentDataModel.scollY
                    Log.i(TAG, "init SpO2TimeFragment data from saved fragment success.")
                }
            }

            if(savedInstanceState.getBoolean(KEY_SHOW_DIALOG_FLAG)) {
                showExplainDialog()
            }
        }catch (ex: Exception){
            Log.i(TAG, "init SpO2TimeFragment data from saved fragment fail. error : ${ex.message}")
        }
    }

    override fun sendRequest(userId: String, timeCode: String) {
        reqMap = mapOf(
            "userId" to userId,
            "unit" to timeCode
        )
        if (!HmsApplication.isPrivacyModeEnabled()) {
            if (!HmsApplication.isNetworkConn()) {
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                resetData()
                if (isDataReady)
                    showLoading()
                if(fragmentDataModel.healthData!=null
                    && fragmentDataModel.healthData!!.code == "0"){
                    processHealthData(fragmentDataModel.healthData!!)
                }else {
                    viewModel.getSpO2DetailData(reqMap)
                }
            }
        } else initPrivacyUI(timeCode)
    }

    private fun resetData() {
        binding.altitudeCurrentValue.text = "--"
        binding.tvC41Per.text = "0%"
        binding.tvC42Per.text = "0%"
        binding.tvC43Per.text = "0%"
        binding.tvSpo2RangeNoData.text = "--"

        moveToBottom(binding.sleepHourUnit,0)
        moveToBottom(binding.altitudeCurrentValue,0)
        moveToBottom(binding.sleepHourValue,0)
        moveToBottom(binding.tvC1AlUnit,0)
        moveToBottom(binding.tvSpo2MinUnit)
        moveToBottom(binding.tvSpo2MaxUnit)
        moveToBottom(binding.tvAltitudeMinUnit)
        moveToBottom(binding.tvAltitudeMaxUnit)
    }

    override fun sendDataReadyRequest(userId: String, timeCode: String) {
        when (timeCode) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Bloodoxygenreports_Daytab_Click",
                    DataTrackUtil.userIDMap(userId)

                )
            }
        }
        if (!HmsApplication.isPrivacyModeEnabled()) {
            if (!isProvacyMode) {
                if (!HmsApplication.isNetworkConn()) {
                    showNetErrorOrSettingView()
                    fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
                }else{
                    showLoading()
                }
            }
            resetData()
            setScrollEnable(binding.svContainer, false)
            viewModel.getHistoryStatusData(userId)
        } else initPrivacyUI(timeCode)
    }


    override fun onResume() {
        super.onResume()
        isProvacyMode = HmsApplication.isPrivacyModeEnabled()
        if (!isProvacyMode) setScrollEnable(binding.svContainer)
        if (isDataReady)
            sendRequest(mUserId, cardTimeType!!)
        else
            handler.post(runnable)
    }

    override fun onPause() {
        super.onPause()
        handler.removeCallbacks(runnable)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        // 保存数据
        if(fragmentDataModel.healthData!=null
            && fragmentDataModel.healthData!!.code == "0"
            && fragmentDataModel.healthSummaryResponse!=null
            && fragmentDataModel.healthSummaryResponse!!.code == "0"
        ){
            fragmentDataModel.scollY = binding.svContainer.scrollY
            outState.putString(KEY_SAVED_DATA_SUMMARY, gson.toJson(fragmentDataModel));
        }

        outState.putBoolean(KEY_SHOW_DIALOG_FLAG, fragmentDataModel.isShowExplainDialog)
    }

    private val runnable = UpdateRunnable(this)
    private class UpdateRunnable(private val fragment: SpO2TimeFragment) : Runnable {
        private val weakFragment = WeakReference(fragment)

        override fun run() {
            val fragment = weakFragment.get()
            if (fragment != null && !fragment.isDetached) {
                if (!fragment.isDataReady) {
                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
                    // 15 秒后再次调用
                    if (HmsApplication.isNetworkConn())
                        fragment.handler.postDelayed(this, 15000)
                }
            }
        }
    }

//    private val runnable = object : Runnable {
//        private val weekFragment = WeakReference(this@SpO2TimeFragment)
//        override fun run() {
//            val fragment = weekFragment.get()
//            if (fragment != null && !fragment.isDetached) {
//                if (!fragment.isDataReady) {
//                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
//                    // 15 秒后再次调用
//                    if (HmsApplication.isNetworkConn())
//                    fragment.handler.postDelayed(this, 15000)
//                }
//            }
//        }
//    }

    private fun getDataReady() {
        isDataReady = true
        handler.removeCallbacks(runnable)
        sendRequest(mUserId, cardTimeType!!)
    }

    fun readyDataNoAuth() {
        handler.removeCallbacks(runnable)
        showNoAuthView()
        fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
    }

    private fun initData() {
        viewModel.healthHistoryData.observe(viewLifecycleOwner) {
            val notReadyText =
                "血氧${requireContext().resources.getString(R.string.text_data_not_ready)}"
            if (it.code == "0" && it.data != null) {
                val statusList = it.data?.dataStatusList
                if (statusList.isNullOrEmpty()) //如果是空的 也认为是有数据的
                {
                    getDataReady()
                    return@observe
                }
                var status: HealthDataStatusDTO? = null
                if (!statusList.isNullOrEmpty()) {
                    val statusArray = statusList.filter { it.dataType == "spo2Read" }
                    if (!statusArray.isNullOrEmpty()) {
                        status = statusArray[0]
                    } else {
                        getDataReady()
                        return@observe
                    }
                }
                if (status != null) {
                    when (cardTimeType) {
                        TimeCode.TIME_CODE_DAY.timeCode -> {
                            if (status.dayDataStatus == null || status.dayDataStatus == 2) {
                                //如果返回值是null  也认为是有值的
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }
                    }
                } else {
                    showNetErrorOrSettingView()
                    fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
                }
            } else if (it.code == "5") {
                showNoAuthView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                // 无网络或者刷新失败处理
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            }
        }



        viewModel.healthSpO2SummeryData.observe(requireActivity()) {
            this.fragmentDataModel.healthSummaryResponse = it
            processSummaryData(it)
        }
        viewModel.spo2DayChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthData(it)
        }

        // 滑动监听
        when (cardTimeType) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                binding.cSpO2Day.setOnDaySelectListener { index, item ->
                    if (!isNoDataMode) {
                        var showCardDTO = SpO2Card1DTO(
                            "",//startTime 有空值
                            "--",
                            "",
                            "--",
                        )
                        if (item != null) {
                            if (!chartDayList.isNullOrEmpty()) {
                                val mm = getMaxAndMin(chartDayList)
                                showCardDTO = SpO2Card1DTO(
                                    getFetchTime(chartDayList[index].createTime),//startTime 有空值
                                    chartDayList[index].avg.toString(),
                                    mm.first.toString(),
                                    mm.second.toString(),
                                )

                            }
//                            binding.tvSpo2MinUnit.visibility = View.VISIBLE
                        } else {
                            if (!altitudeTimeList.isNullOrEmpty() && altitudeTimeList!!.size == chartDayList.size && index < chartDayList.size)
                                showCardDTO = SpO2Card1DTO(
                                    altitudeTimeList!![index],
                                    "--",
                                    "",
                                    "--",
                                )
                            else {
                                var fetchTime = ""
                                if(index>=0&&index<chartDayList.size && chartDayList[index].createTime!=null){
                                    fetchTime = chartDayList[index].createTime!!
                                }
                                showCardDTO = SpO2Card1DTO(
                                    fetchTime,//startTime 有空值
                                    "--",
                                    "",
                                    "--",
                                )
//                            binding.tvSpo2MinUnit.visibility = View.GONE
                            }
                        }
                        binding.altitudeCurrentValue.text = "--"
                        binding.cardShowInfo = showCardDTO
                        altitudeValueList?.let {
                            if (it.size <= chartDayList.size && index < it.size) {
                                binding.altitudeCurrentValue.text =
                                    altitudeValueList!![index].toString()
                            }
                        }
                    }

                }
                val currentDateStr = getDayFetchTime()
                binding.cSpO2Day.setOnxTextSelectListener { index, xText ->
                    if (isNoDataMode)
                        binding.tvSleepDate.text = currentDateStr + " " + xText
                }
            }

        }

    }

    private fun processHealthData(it: BaseResponse<SpO2ItemResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            if (!it.data!!.nodeList.isNullOrEmpty()) {
                chartDayList = it.data!!.nodeList
                var showCardDTO3 = SpO2Card3DTO(
                    "-",
                    "--"
                )
                if (it.data!!.gwmHealthAltitudeDetailVO != null) {
                    val altiDayStat = it.data!!.gwmHealthAltitudeDetailVO.altitudeDayStat
                    val altiList = it.data!!.gwmHealthAltitudeDetailVO.altitudeDetailList
                    pairExt = delAltitudeData(altiList)
                    pairExt2 = delAltitudeExt(altiList)
                    if (altiListIsNull(altiList)) {
                        altitudeValueList = null
                        showCardDTO3 = SpO2Card3DTO(
                            "-",
                            "--"
                        )
                        pairExt = Pair(100, 0)
                    } else {
                        altitudeValueList = getAltitudeList(altiList)
                        altitudeTimeList = getAltitudeTimeList(altiList)
                        if (altiDayStat != null) {
                            showCardDTO3 = SpO2Card3DTO(
                                pairExt2.second.toString(),
                                "-${pairExt2.first}"
                            )

                        } else {
                            showCardDTO3 = SpO2Card3DTO(
                                "-",
                                "--"
                            )
                        }
                    }
                } else {
                    pairExt = Pair(100, 0)
                }

                val showCardDTO = SpO2Card1DTO(
                    getFetchTime(chartDayList.last().createTime),//startTime 有空值
                    chartDayList.last().avg.toString(),
                    chartDayList.last().max.toString(),
                    chartDayList.last().min.toString(),
                )
                // 卡片1数据
                binding.cardShowInfo = showCardDTO
                // 卡片2数据
                binding.cardShowInfo1 = SpO2Card1DTO(
                    getFetchTime(chartDayList.last().createTime),//startTime 有空值
                    "--",
                    it.data!!.max.toString(),
                    it.data!!.min.toString()
                )
                if (it.data!!.min <= 0) {
                    binding.tvSpo2MinUnit.visibility = View.INVISIBLE
                }
                // 卡片1数据
                binding.cardShowInfo3 = showCardDTO3

                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                map.put("startTime", it.data!!.startTime)
                map.put("endTime", it.data!!.endTime)

                initChartData()

                processLoadingSummaryData(map)

            } else {
                initChartNoData()
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 400)
    }

    /**
     * 判断是否有合理的健康建议数据。如果有，则直接使用，否则去服务器获取
     */
    private fun processLoadingSummaryData(map: MutableMap<String,String>){
        if(fragmentDataModel==null
            || fragmentDataModel.healthSummaryResponse == null
            || fragmentDataModel.healthSummaryResponse!!.code != "0") {
            viewModel.getHealthSpO2Summery(map)
        }else{
            processSummaryData(fragmentDataModel.healthSummaryResponse!!)
        }
    }
    private fun processSummaryData(it: BaseResponse<HealthSpO2SummaryDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.healthSpO2SummeryVo = it.data
            binding.svContainer.post {
                binding.svContainer.requestLayout()
            }
            Handler(Looper.getMainLooper()).postDelayed({
                hideLoading()
                setScrollEnable(binding.svContainer)
            }, 100)
            // 重置查看更多按钮为不可见
            binding.healthRiskAll.tvHealthAdviceContent.setIsEllipsized(false)
            it.data!!.healthAdvice?.let { item ->
                healthAdviceStr = item
            }
    //                it.data!!.nounExplain?.let { explain ->
    //                    if (explain.isNotEmpty()) {
    //                        this.introString = explain
    //                        binding.ivIntroTips.visibility = View.VISIBLE
    //                    }
    //                }
            var spO2Card2DTO = SpO2Card2DTO(
                it.data!!.spo2hAvg,
                it.data!!.spo2hLevel,
                it.data!!.normalProp,
                it.data!!.lowProp,
                it.data!!.tooLowProp,
                it.data!!.normalSum,
                it.data!!.lowSum,
                it.data!!.tooLowSum,
                it.data!!.createTime
            )
            binding.cardShowInfo2 = spO2Card2DTO
        }

        // 恢复滚动位置
        if(this.fragmentDataModel.scollY>0) {
            binding.svContainer.post {
                binding.svContainer.scrollY = this.fragmentDataModel.scollY
            }
        }
    }

    private fun initUI() {
        initExplain()
        binding.ivIntroTips.findViewById<ImageView>(R.id.iv_intro_tips)
        ClickUtils.applySingleDebouncing(binding.ivIntroTips, 1000) {
            showExplainDialog()
        }
        binding.healthRiskAll.tvHealthAdviceContent.setCallback(this)
        binding.healthRiskAll.tvHealthAdviceContent.setEndPercentage(80)
        binding.healthRiskAll.tvSeeMore.text = Html.fromHtml("<u>查看更多</u>")
        val scrollView = binding.svContainer
        scrollView.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            val height = scrollView.getChildAt(0).height // 获取ScrollView内容的总高度
            val scrollViewHeight = scrollView.height // 获取ScrollView的可见高度
            val diff = (height - scrollViewHeight) * 0.75f //scrollview中判定的距离 动画view位置底部约为总长度的的75%
            if (scrollY >= diff) {
                MMKVUtil.getUserId()?.let {
                    DataTrackUtil.dtScroll(
                        "Health_Bloodoxygenreports_Bottom_Show",
                        DataTrackUtil.userIDMap(it)
                    )
                }
            }
        }
        applyPrivacyStyleChanged(HmsApplication.isPrivacyModeEnabled())
    }

    private fun showExplainDialog() {
        showHmsDialog(R.layout.hms_dialog_tips_small, "血氧说明", introString)
    }

    fun initChartData() {
        isNoDataMode = false
        setUnitVisiable(true)
        initDayChart()
    }

    fun initChartNoData() {
        isNoDataMode = true
        binding.cSpO2Day.visibility = View.VISIBLE
        binding.sPrivacyText.visibility = View.GONE
        binding.tvSpo2MinValue.text = "--"
        binding.sleepHourValue.text = "--"
        binding.sleepHourUnit.text = " 海拔"
        binding.tvAltitudeMinValue.text = "--"
        setUnitVisiable(false)
        val currentDateStr = getDayFetchTime()
        binding.tvSleepDate.text = currentDateStr + " 12:00"

    }


    private fun initDayChart() {
        binding.cSpO2Day.visibility = View.VISIBLE
        binding.sPrivacyText.visibility = View.GONE
        binding.cSpO2Day.setValue(chartDayList, altitudeValueList, pairExt, getYType(chartDayList))
    }

    private var dialog: Dialog? = null

    /**
     * 显示自定义对话框
     */
    fun showHmsDialog(layoutId: Int, title: String, message: String?) {
        // 创建自定义视图
        val view = LayoutInflater.from(requireActivity()).inflate(layoutId, null)
        val contentView = view.findViewById<RelativeLayout>(R.id.dialog_content)
        var btnPositive = view.findViewById<Button>(R.id.positiveButton)
        var tvMessage = view.findViewById<TextView>(R.id.textView)
        var tvTitle = view.findViewById<TextView>(R.id.tv_tips_title_small)
        tvTitle.text = title
        if (message != null && !message.isNullOrBlank()) {
            tvMessage.setText(message)
        }
        // 创建并显示对话框
        dialog = ImmersiveDialog(
            requireContext(),
            R.style.MyDialogStyle
        )
        //dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog!!.setContentView(view)
        dialog!!.setOnShowListener {
            HMSDialogUtils.doDialogAnimateEnter(dialog!!)
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    fragmentDataModel.isShowExplainDialog = false
                    dialog?.dismiss()
                }
                false
            }
        }
        // 设置按钮点击事件
        btnPositive.setOnClickListener {
            fragmentDataModel.isShowExplainDialog = false
            dialog?.dismiss()
        }
        dialog!!.show()
        fragmentDataModel.isShowExplainDialog = true
    }

    fun getMaxAndMin(oriList: ArrayList<SpO2ItemDTO>): Pair<Int, Int> {
        var max = 0
        var min = 0
        var needInit = true
        oriList.forEachIndexed { index, item ->
            if (item.avg == 0) return@forEachIndexed
            if (needInit) {
                max = item.max
                min = item.min
                needInit = false
            } else {
                if (item.max > max)
                    max = item.max
                if (item.min < min)
                    min = item.min
            }

        }
        return Pair(max, min)
    }


    fun getYType(oriList: ArrayList<SpO2ItemDTO>): Int {
        oriList.forEach {
            if (it.avg in 1 until 70)
                return 1
        }
        return 0
    }

    override fun update(isEllipsized: Boolean, viewID: Int) {
        if (isEllipsized) {
            binding.healthRiskAll.cardAdviceMore.visibility = View.VISIBLE
            binding.healthRiskAll.cardAdviceMore.setOnClickListener { v ->
                HMSDialogUtils.showHMSNotiDialog(
                    requireContext(),
                    R.layout.hms_dialog_see_more,
                    "健康建议",
                    healthAdviceStr,
                    "知道了"
                ) { isPositive ->
                }
            }
        } else {
            binding.healthRiskAll.cardAdviceMore.visibility = View.GONE
        }
    }

    fun delAltitudeData(oriList: ArrayList<AltitudeItemDTO>): Pair<Int, Int> {
        var max = 0
        var min = 0
        var pair = Pair(0, 0)
        if (!oriList.isNullOrEmpty()) {
            for (i in 0..(oriList.size - 1)) {
                if (oriList[i].avg != null) {
                    max = oriList[i].avg!!.toInt()
                    min = oriList[i].avg!!.toInt()
                    break
                }
            }
            oriList.forEach {
                if (it.avg == null) return@forEach
                if (it.avg!! > max)
                    max = it.avg!!.toInt()
                if (it.avg!! < min)
                    min = it.avg!!.toInt()
            }
            if (max in 10..95 && min in 10..95) {
                pair = Pair(100, 0)
            } else {
                val tempMax = (max / 10) * 10 + 20
                val tempMin = (min / 10) * 10 - 20
                pair = Pair(tempMax, tempMin)
            }
        }
        return pair
    }


    fun delAltitudeExt(oriList: ArrayList<AltitudeItemDTO>): Pair<Int, Int> {
        var max = 0
        var min = 0
        var pair = Pair(0, 0)
        if (!oriList.isNullOrEmpty()) {
            for (i in 0..(oriList.size - 1)) {
                if (oriList[i].avg != null) {
                    max = oriList[i].avg!!.toInt()
                    min = oriList[i].avg!!.toInt()
                    break
                }
            }
            oriList.forEach {
                if (it.max == null) return@forEach
                if (it.avg!!.toInt() > max)
                    max = it.avg!!.toInt()
                if (it.avg!!.toInt() < min)
                    min = it.avg!!.toInt()
            }
            pair = Pair(max, min)
        }
        return pair
    }

    fun initPrivacyUI(timeCode: String) {
        setScrollEnable(binding.svContainer, false)
        binding.sPrivacyText.visibility = View.VISIBLE
        binding.spo2NoDataText.visibility = View.GONE
        binding.cSpO2Day.visibility = View.GONE
        binding.tvSleepDate.text = getPrivacyModeDate(timeCode)
        binding.sleepHourValue.text = "***"
        binding.sleepHourUnit.text = "%海拔"
        binding.altitudeCurrentValue.text = "***"
        binding.tvSpo2MinValue.text = "***"
        binding.tvSpo2MaxValue.text = "***"
        binding.tvAltitudeMinValue.text = "***"
        binding.tvAltitudeMaxValue.text = "***"
        binding.tvC41Per.text = "***%"
        binding.tvC42Per.text = "***%"
        binding.tvC43Per.text = "***%"
        binding.tvSpo2RangeNoData.text = "***%"

        // 健康建议在私密模式下不展示
        binding.healthSpO2SummeryVo = null

        moveToTop(binding.sleepHourUnit)
        moveToTop(binding.tvAltitudeMaxUnit)
        moveToTop(binding.altitudeCurrentValue, 9)
        moveToTop(binding.tvC1AlUnit)
        moveToTop(binding.tvSpo2MinUnit)
        moveToTop(binding.tvSpo2MaxUnit)
        moveToTop(binding.tvAltitudeMinUnit)
        moveToTop(binding.tvAltitudeMaxUnit)
        binding.svContainer.visibility = View.VISIBLE
    }

    private fun applyPrivacyStyleChanged(isPrivacyMode: Boolean) {
        binding.llIntroContainer.apply {
            val params = layoutParams as ViewGroup.MarginLayoutParams
            params.bottomMargin = if (isPrivacyMode) 30f.dp.toInt() else 0f.dp.toInt()
            layoutParams = params
        }
        binding.tvC1AlUnit.text = if (isPrivacyMode) "" else "m"
        binding.tvSpo2MinUnit.text = if (isPrivacyMode) "%-" else "%"
        binding.tvAltitudeMinUnit.text = if (isPrivacyMode) "m-" else "m"

        listOf(
            binding.tvC41Per,
            binding.tvC42Per,
            binding.tvC43Per,
        ).forEach {
            val style = if (isPrivacyMode) Typeface.BOLD else Typeface.NORMAL
            it.setTypeface(Typeface.create(Typeface.DEFAULT, style))
            it.invalidate() // 强制重绘以确保变化生效
        }
    }


    fun altiListIsNull(oriList: ArrayList<AltitudeItemDTO>): Boolean {
        var isNull = true

        for (i in 0..oriList.size - 1) {
            if (oriList[i].avg != null) {
                isNull = false
                break
            }
        }
        return isNull
    }

    fun getAltitudeTimeList(oriList: ArrayList<AltitudeItemDTO>): ArrayList<String> {
        var resultList = arrayListOf<String>()
        oriList.forEachIndexed { index, item ->
            resultList.add(item.createTime ?: "")
        }
        return resultList
    }

    fun getAltitudeList(oriList: ArrayList<AltitudeItemDTO>): ArrayList<Int> {
        //海拔数据补齐
        //向前补齐
        var resultList = arrayListOf<Int>()
        var index = 0
        var perValue = 0
        for (i in 0..oriList.size - 1) {
            if (oriList[i].avg != null) {
                perValue = oriList[i].avg!!.toInt()
                index = i
                break
            }
        }
        if (index != 0) {
            for (i in 0..index) {
                resultList.add(perValue)
            }
        }
        //向后补齐
        for (i in index..oriList.size - 1) {
            if (oriList[i].avg != null) {
                resultList.add(oriList[i].avg!!.toInt())
                perValue = oriList[i].avg!!.toInt()
            } else {
                resultList.add(perValue)
            }
        }
        return resultList
    }

    fun getFetchTime(time: String?): String {
        if (time != null) return time
        else return ""
    }

    fun getDayFetchTime(): String {
        val calendar = Calendar.getInstance()
        val month = calendar[Calendar.MONTH] + 1 // 月份是从0开始的，需要加1
        val day = calendar[Calendar.DAY_OF_MONTH]
        return "${month}月${day}日"
    }

    fun setUnitVisiable(sw: Boolean) {
        if (sw) {
            binding.tvSpo2MinUnit.visibility = View.VISIBLE
            binding.tvSpo2MaxUnit.visibility = View.VISIBLE
            binding.tvC1AlUnit.visibility = View.VISIBLE
            binding.tvAltitudeMaxUnit.visibility = View.VISIBLE
            binding.tvAltitudeMinUnit.visibility = View.VISIBLE
            binding.tvAltitudeMaxValue.visibility = View.VISIBLE
            binding.sleepScoreLayout.visibility = View.VISIBLE
            binding.sleepScoreLayoutNoData.visibility = View.GONE
            binding.sleepHourUnit.text = "% 海拔"

        } else {
            binding.sleepScoreLayout.visibility = View.GONE
            binding.sleepScoreLayoutNoData.visibility = View.VISIBLE
            binding.tvAltitudeMaxValue.visibility = View.GONE
            binding.tvAltitudeMaxUnit.visibility = View.GONE
            binding.tvAltitudeMinUnit.visibility = View.GONE
            binding.tvSpo2MaxUnit.visibility = View.GONE
            binding.tvSpo2MinUnit.visibility = View.GONE
            binding.tvC1AlUnit.visibility = View.GONE
            binding.sleepHourUnit.text = "海拔"
        }
    }

    override fun onPrivacyModeChange(provacyMode: Boolean) {
        applyPrivacyStyleChanged(provacyMode)
        if (isProvacyMode != provacyMode) {
            //当前模式与变化模式不同
            if (isDataReady) {
                sendRequest(mUserId, cardTimeType!!)
            } else {
                sendDataReadyRequest(mUserId, cardTimeType!!)
            }
            isProvacyMode = provacyMode
        }
    }

    fun initExplain() {
        val tips = binding.ivIntroTips
        tips.visibility = View.VISIBLE
//        tips.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener{
//            override fun onGlobalLayout() {
//                tips.viewTreeObserver.removeGlobalOnLayoutListener { this }
//                val parentView=tips.parent as View
//                val rect = Rect()
//                tips.getHitRect(rect)
//                rect.top-=24
//                rect.bottom+=100
//                rect.left-=100
//                rect.right+=24
//                parentView.touchDelegate = TouchDelegate(rect, tips)
//            }
//        })
        viewAddOnGlobalLayoutListener(tips)
        this.introString = getString(R.string.description_spo2)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        try {
            dialog?.dismiss()
            dialog = null
            viewRemoveOnGlobalLayoutListener(binding.ivIntroTips)
            binding.healthRiskAll.tvHealthAdviceContent.setCallback(null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}