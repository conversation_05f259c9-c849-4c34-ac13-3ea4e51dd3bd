package com.healthlink.hms.fragment

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.blankj.utilcode.util.ThreadUtils.runOnUiThread
import com.healthlink.hms.Contants.VehicleServiceModeType
import com.healthlink.hms.adapter.ModeAdapter
import com.healthlink.hms.fragment.HMSServiceListFragment.Companion.COOL_OPERATOR_CHECK
import com.healthlink.hms.fragment.HMSServiceListFragment.Companion.RELAX_OPERATOR_CHECK
import com.healthlink.hms.fragment.HMSServiceListFragment.Companion.WARN_OPERATOR_CHECK
import com.healthlink.hms.fragment.HMSServiceListFragment.VehicleMsgDTO
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import java.lang.ref.WeakReference

class BackgroundThread(fragment: HMSServiceListFragment) : Thread() {
    private val TAG = "HMSServiceListFragment"
    private val fragmentRef = WeakReference(fragment)
    lateinit var handler: Handler

    override fun run() {
        Looper.prepare()
        handler = Handler(Looper.myLooper()!!) { msg ->
            val fragment = fragmentRef.get()
                ?: // Fragment 已经被回收，直接返回
                return@Handler true

            // 在这里使用 fragment 的时候，先检查是否 still alive
            if (!fragment.isAdded || fragment.isDetached) {
                return@Handler true
            }

            // 处理消息
            Log.i(TAG, "handler: $msg")
//            val weakContext = WeakReference(requireContext())
            val weakAdapter = WeakReference(fragment.binding.recyclerView.adapter as? ModeAdapter)
            when (msg.what) {
                WARN_OPERATOR_CHECK -> {
                    val dto = msg.obj as VehicleMsgDTO
                    val isSupportSteerWheel =
                        true//GwmAdapterManagerKotCoroutines.isSupportSteerWheelHeating()
                    val isSupportSeatHeat = true//GwmAdapterManagerKotCoroutines.isSupportSeatHeat()
                    val res = GwmAdapterManagerKotCoroutines.getWarnModeStatus()
                    Log.i(TAG, "WARN_OPERATOR_CHECK: $dto $res")
                    var isOperationSuccess = true
                    if (isSupportSteerWheel && isSupportSeatHeat) {
                        val okRes =
                            (res.first != null && res.first == dto.level[0]) && (res.second != null && res.second == dto.level[1])
                        isOperationSuccess = okRes
                    } else if (isSupportSteerWheel && !isSupportSeatHeat) {
                        val okRes = (res.first != null && res.first == dto.level[0])
                        isOperationSuccess = okRes
                    } else if (!isSupportSteerWheel && isSupportSeatHeat) {
                        val okRes = (res.second != null && res.second == dto.level[1])
                        isOperationSuccess = okRes
                    }

                    if (dto.operation == 0) { // 关闭
                        if (!isOperationSuccess) {
                            fragment.showOpenModeFailToast(
                                fragment.requireContext(),
                                "${VehicleServiceModeType.WARN.modeName}关闭失败，请重试")
                        }
                    } else {
                        if (!isOperationSuccess) {
                            fragment.showOpenModeFailToast(fragment.requireContext(),"${VehicleServiceModeType.WARN.modeName}开启失败，请重试")

                        }
                    }
                    runOnUiThread {
                        weakAdapter.get()?.changeModelOpenStatus(dto.position,
                            GwmAdapterManagerKotCoroutines.isWarnModeOpen())
                    }
                }
                COOL_OPERATOR_CHECK -> {
                    val dto = msg.obj as VehicleMsgDTO
                    val status = "0"//GwmAdapterManagerKotCoroutines.getCoolModeStatus()
                    Log.i(TAG, "COOL_OPERATOR_CHECK: $dto $status")
                    if (dto.operation == 0) { // 关闭
                        // 定义关闭成功
                        val okRes = (status != null && status == dto.level[0])
                        if (!okRes) {
                            fragment.showOpenModeFailToast(
                                fragment.requireContext(),
                                "${VehicleServiceModeType.COOL.modeName}关闭失败，请重试")
                        }

                    } else {
                        // 定义打开成功
                        val okRes = (status != null && status != "0" && status != "-1")
                        if (!okRes) {
                            fragment.showOpenModeFailToast(fragment.requireContext(),"${VehicleServiceModeType.COOL.modeName}开启失败，请重试")
                        }
                    }
                    runOnUiThread {
                        weakAdapter.get()?.changeModelOpenStatus(
                            dto.position,
                            GwmAdapterManagerKotCoroutines.isCoolModeOpen()
                        )
                    }
                }
                RELAX_OPERATOR_CHECK -> {
                    val dto = msg.obj as VehicleMsgDTO
                    val status = "0"//GwmAdapterManagerKotCoroutines.getRelaxModeStatus()

                    Log.i(TAG, "RELAX_OPERATOR_CHECK: $dto $status")
                    if (dto.operation == 0) { // 关闭
                        // 定义关闭成功
                        val okRes = (status != null && status == dto.level[0])
                        if (!okRes) {
                            fragment.showOpenModeFailToast(
                                fragment.requireContext(),
                                "${VehicleServiceModeType.RELAX.modeName}关闭失败，请重试")

                        }
                    } else {
                        // 定义打开成功
                        val okRes = (status != null && status != "0" && status != "-1")
                        if (!okRes) {
                            fragment.showOpenModeFailToast(fragment.requireContext(),"${VehicleServiceModeType.RELAX.modeName}开启失败，请重试")
                        }
                    }
                    runOnUiThread {
                        weakAdapter.get()?.changeModelOpenStatus(
                            dto.position,
                            GwmAdapterManagerKotCoroutines.isRelaxModeOpen()
                        )
                    }
                }
                else -> {

                }
            }

            // 您的消息处理逻辑...
            true
        }
        Looper.loop()
    }

    fun stopThread() {
        // 在 Fragment 销毁时调用，结束该线程的 Looper 循环
        handler.looper.quitSafely()
    }
}