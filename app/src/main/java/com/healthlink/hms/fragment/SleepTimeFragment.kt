package com.healthlink.hms.fragment

import android.app.Dialog
import android.graphics.Rect
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.TouchDelegate
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import com.blankj.utilcode.util.ClickUtils
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.activity.card.HMSCardFragmentInteractWithAcInterface
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.databinding.FragmentSleepTimeBinding
import com.healthlink.hms.fragment.TempTimeFragment.Companion
import com.healthlink.hms.fragment.viewmodel.HeartRateFragmentModel
import com.healthlink.hms.fragment.viewmodel.SleepFragmentModel
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.HealthDataStatusDTO
import com.healthlink.hms.core.model.dto.SleepCardShow2DTO
import com.healthlink.hms.core.model.dto.SleepCardShowDTO
import com.healthlink.hms.core.model.dto.SleepDayItemDTO
import com.healthlink.hms.core.model.dto.SleepDayResponseDTO
import com.healthlink.hms.core.model.dto.SleepMonthResponseDTO
import com.healthlink.hms.core.model.dto.SleepWMItemDTO
import com.healthlink.hms.core.model.dto.SleepWMYDataDTO
import com.healthlink.hms.core.model.dto.SleepWeekResponseDTO
import com.healthlink.hms.core.model.dto.SleepYearResponseDTO
import com.healthlink.hms.core.model.dto.charts.HealthSleepSummeryDTO
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.TimeUtils.resetWMDateTime
import com.healthlink.hms.utils.TimeUtils.resetYDateTime
import com.healthlink.hms.utils.ToastUtil
import com.healthlink.hms.utils.getPrivacyModeDate
import com.healthlink.hms.viewmodels.MainViewModel
import com.healthlink.hms.views.ImmersiveDialog
import com.healthlink.hms.views.MiddleEllipsesTextView
import com.healthlink.hms.views.charts.Sleep
import com.healthlink.hms.views.charts.SleepDataType
import com.healthlink.hms.views.charts.SleepItem
import com.healthlink.hms.views.charts.SleepTime
import java.lang.ref.WeakReference
import java.sql.Time
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Calendar

/**
 *@Author：付仁秀
 *@Description：
 **/
class SleepTimeFragment : BaseCardFragment<FragmentSleepTimeBinding, MainViewModel>(
    MainViewModel::class.java,
    R.layout.fragment_sleep_time
), MiddleEllipsesTextView.UpdateSeeMore {
    private var year = "2024"
    private var chartDataList = arrayListOf<SleepDayItemDTO>()
    private var chartWeekList = arrayListOf<SleepWMItemDTO>()
    private var chartMonthList = arrayListOf<SleepWMItemDTO>()
    private var chartTempList = arrayListOf<SleepWMItemDTO>()
    private var chartYearList = arrayListOf<SleepWMYDataDTO>()
    private var startDate: String = ""
    private var endDate: String = ""
    private var startTime: String = ""
    private var endTime: String = ""
    private var reqMap = mapOf<String, String>()
    private var healthAdviceStr = ""
    private var introString = ""
    private var isNoDataMode = false
    private var isProvacyMode = false

    private var fragmentDataModel  = SleepFragmentModel()


    companion object {
        private const val ARG_PARAM_TYPE = "ARG_PARAM_TYPE"
        private val fragmentInteractWithAC
            get() = _fragmentInteractWithAC?.get()
        private var _fragmentInteractWithAC: WeakReference<HMSCardFragmentInteractWithAcInterface>? =
            null
        private lateinit var mUserId: String
        private var TAG = "SleepTimeFragment"

        fun newInstance(
            cartTimeType: TimeCode,
            userId: String,
            interact: HMSCardFragmentInteractWithAcInterface
        ): SleepTimeFragment {
            val fragment = SleepTimeFragment()
            val args = Bundle()
            args.putString(ARG_PARAM_TYPE, cartTimeType.timeCode)
            fragment.arguments = args
            _fragmentInteractWithAC = WeakReference(interact)
            mUserId = userId
            return fragment
        }
    }

    override fun sendRequest(userId: String, timeCode: String) {
        reqMap = mapOf(
            "userId" to userId,
            "unit" to timeCode
        )
        // 前后台切换时，如果切换了隐私模式
        applyPrivacyStyleChanged(HmsApplication.isPrivacyModeEnabled() != isProvacyMode)
        if (!HmsApplication.isPrivacyModeEnabled()) {

            moveToBottom(binding.sleepAllTime)
            moveToBottom(binding.sleepHourUnit)
            moveToBottom(binding.sleepMinUnit)
            moveToBottom(binding.tvSleepScoreUnit)

            if (!HmsApplication.isNetworkConn()) {
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                if (isDataReady)
                    showLoading()
                setScrollEnable(binding.svContainer, false)

                // 判断数据的合理性
                var isHealthDataValid = false
                when(cardTimeType){
                    TimeCode.TIME_CODE_DAY.timeCode -> {
                        if(this.fragmentDataModel.healthDataDay!=null
                            && this.fragmentDataModel.healthDataDay!!.code == "0"){
                            processHealthDataDay(fragmentDataModel.healthDataDay!!)
                        }else{
                            viewModel.getSleepDayDetailData(reqMap)
                        }
                    }
                    TimeCode.TIME_CODE_WEEK.timeCode -> {
                        if(this.fragmentDataModel.healthDataWeek!=null
                            && this.fragmentDataModel.healthDataWeek!!.code == "0"){
                            processHealthDataWeek(fragmentDataModel.healthDataWeek!!)
                        }else{
                            viewModel.getSleepWeekDetailData(reqMap)
                        }
                    }
                    TimeCode.TIME_CODE_MONTH.timeCode -> {
                        if(this.fragmentDataModel.healthDataMonth!=null
                            && this.fragmentDataModel.healthDataMonth!!.code == "0"){
                            processHealthDataMonth(fragmentDataModel.healthDataMonth!!)
                        }else{
                            viewModel.getSleepMonthDetailData(reqMap)
                        }
                    }
                    TimeCode.TIME_CODE_YEAR.timeCode -> {
                        if(this.fragmentDataModel.healthDataYear!=null
                            && this.fragmentDataModel.healthDataYear!!.code == "0"){
                            processHealthDataYear(fragmentDataModel.healthDataYear!!)
                        }else{
                            viewModel.getSleepYearDetailData(reqMap)
                        }
                    }
                }

//                when (timeCode) {
//
//
//                    TimeCode.TIME_CODE_WEEK.timeCode -> viewModel.(reqMap)
//
//                    TimeCode.TIME_CODE_MONTH.timeCode -> viewModel.(reqMap)
//
//                    TimeCode.TIME_CODE_YEAR.timeCode -> viewModel.(reqMap)
//                }
            }
        } else {
            initPrivacyUI(timeCode)
        }
    }

    override fun sendDataReadyRequest(userId: String, timeCode: String) {
        reqMap = mapOf(
            "userId" to userId,
            "unit" to timeCode
        )

        applyPrivacyStyleChanged(HmsApplication.isPrivacyModeEnabled())
        when (timeCode) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Sleepreports_Daytab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Sleepreports_Weektab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Sleepreports_Mouthtab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Sleepreports_Yeartab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
            }
        }
        initCardTextShow()
        if (!HmsApplication.isPrivacyModeEnabled()) {
            if (!HmsApplication.isNetworkConn()) {
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            }else{
                showLoading()
            }
            moveToBottom(binding.sleepAllTime)
            moveToBottom(binding.sleepHourUnit)
            moveToBottom(binding.sleepMinUnit)
            moveToBottom(binding.tvSleepScoreUnit)
            viewModel.getHistoryStatusData(userId)
        } else {
            initPrivacyUI(timeCode)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            cardTimeType = it.getString(ARG_PARAM_TYPE)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()
        initLiveDataObserve()
        // 恢复数据
        if(savedInstanceState!=null){
            restoreDataIfPossible(savedInstanceState)
        }
    }

    private fun restoreDataIfPossible(savedInstanceState: Bundle) {
        try {
            var fragmentSavedData = savedInstanceState.getString(KEY_SAVED_DATA_SUMMARY)
            if (fragmentSavedData != null) {
                var fragmentDataModel =
                    gson.fromJson(fragmentSavedData, SleepFragmentModel::class.java)
                if(fragmentDataModel!=null){
                    this.fragmentDataModel = fragmentDataModel
                    binding.svContainer.scrollY = this.fragmentDataModel.scollY
                    Log.i(TAG, "init CardTimeFragmnet data from saved fragment success.")
                }
            }

            if(savedInstanceState.getBoolean(KEY_SHOW_DIALOG_FLAG)) {
                showExplainDialog()
            }
        }catch (ex: Exception){
            Log.i(TAG, "init CardTimeFragmnet data from saved fragment fail. error : ${ex.message}")
        }
    }

    override fun onResume() {
        super.onResume()
        if (!HmsApplication.isPrivacyModeEnabled()) setScrollEnable(binding.svContainer)
        else initCardTextShow()
        if (isDataReady)
            sendRequest(mUserId, cardTimeType!!)
        else
            handler.post(runnable)
    }

    override fun onPause() {
        super.onPause()
        handler.removeCallbacks(runnable)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        // 判断数据的合理性
        var isHealthDataValid = false
        when(cardTimeType){
            TimeCode.TIME_CODE_DAY.timeCode -> {
                if(this.fragmentDataModel.healthDataDay!=null
                    && this.fragmentDataModel.healthDataDay!!.code == "0"){
                    isHealthDataValid = true
                }
            }
            TimeCode.TIME_CODE_WEEK.timeCode -> {
                if(this.fragmentDataModel.healthDataWeek!=null
                    && this.fragmentDataModel.healthDataWeek!!.code == "0"){
                    isHealthDataValid = true
                }
            }
            TimeCode.TIME_CODE_MONTH.timeCode -> {
                if(this.fragmentDataModel.healthDataMonth!=null
                    && this.fragmentDataModel.healthDataMonth!!.code == "0"){
                    isHealthDataValid = true
                }
            }
            TimeCode.TIME_CODE_YEAR.timeCode -> {
                if(this.fragmentDataModel.healthDataYear!=null
                    && this.fragmentDataModel.healthDataYear!!.code == "0"){
                    isHealthDataValid = true
                }
            }
        }
        // 保存数据
        if(isHealthDataValid
            && fragmentDataModel.healthSummaryResponse!=null
            && fragmentDataModel.healthSummaryResponse!!.code == "0"
        ){
            fragmentDataModel.scollY = binding.svContainer.scrollY
            outState.putString(KEY_SAVED_DATA_SUMMARY, gson.toJson(fragmentDataModel));
        }

        outState.putBoolean(KEY_SHOW_DIALOG_FLAG, fragmentDataModel.isShowExplainDialog)

    }

    private val runnable = UpdateRunnable(this)
    private class UpdateRunnable(private val fragment: SleepTimeFragment) : Runnable {
        private val weakFragment = WeakReference(fragment)

        override fun run() {
            val fragment = weakFragment.get()
            if (fragment != null && !fragment.isDetached) {
                if (!fragment.isDataReady) {
                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
                    // 15 秒后再次调用
                    if (HmsApplication.isNetworkConn())
                        fragment.handler.postDelayed(this, 15000)
                }
            }
        }
    }


    fun getDataReady() {
        isDataReady = true
        handler.removeCallbacks(runnable)
        sendRequest(mUserId, cardTimeType!!)
    }

    fun readyDataNoAuth() {
        handler.removeCallbacks(runnable)
        showNoAuthView()
        fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
    }

    private fun initLiveDataObserve() {
        viewModel.healthHistoryData.observe(viewLifecycleOwner) {
            val notReadyText =
                "睡眠${requireContext().resources.getString(R.string.text_data_not_ready)}"
            if (it.code == "0" && it.data != null) {
                val statusList = it.data?.dataStatusList
                if (statusList.isNullOrEmpty()) //如果是空的 也认为是有数据的
                {
                    getDataReady()
                    return@observe
                }
                var status: HealthDataStatusDTO? = null
                if (!statusList.isNullOrEmpty()) {
                    val statusArray = statusList.filter { it.dataType == "sleepRead" }
                    if (!statusArray.isNullOrEmpty()) {
                        status = statusArray[0]
                    } else {
                        getDataReady()
                        return@observe

                    }
                }
                if (status != null) {
                    when (cardTimeType) {
                        TimeCode.TIME_CODE_DAY.timeCode -> {
                            if (status.dayDataStatus == null || status.dayDataStatus == 2) {
                                //如果返回值是null  也认为是有值的
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_WEEK.timeCode -> {
                            if (status.weekDataStatus == null || status.weekDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_MONTH.timeCode -> {
                            if (status.monthDataStatus == null || status.monthDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_YEAR.timeCode -> {
                            if (status.yearDataStatus == null || status.yearDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }
                    }
                } else {
                    showNetErrorOrSettingView()
                    fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
                }
            } else if (it.code == "5") {
                showNoAuthView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                // 无网络或者刷新失败处理
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            }
        }


        viewModel.getSleepDetailChartLiveData().observe(viewLifecycleOwner) {
            this.fragmentDataModel.healthDataDay = it
            processHealthDataDay(it)
        }
        viewModel.getSleepWeekDetailLiveData().observe(viewLifecycleOwner) {
            this.fragmentDataModel.healthDataWeek = it
            processHealthDataWeek(it)
        }
        viewModel.getSleepMonthDetailLiveData().observe(viewLifecycleOwner) {
            this.fragmentDataModel.healthDataMonth = it
            processHealthDataMonth(it)
        }
        viewModel.getSleepYearDetailLiveData().observe(viewLifecycleOwner) {
            this.fragmentDataModel.healthDataYear = it
            processHealthDataYear(it)
        }

        // 监听风险与建议
        viewModel.healthSleepSummeryData.observe(viewLifecycleOwner) {
            processSummaryData(it)
        }
        viewModel.healthWeekSleepSummeryData.observe(viewLifecycleOwner) {
            processSummaryData(it)
        }
        viewModel.healthMonthSleepSummeryData.observe(viewLifecycleOwner) {
            processSummaryData(it)
        }
        viewModel.healthYearSleepSummeryData.observe(viewLifecycleOwner) {
            processSummaryData(it)
        }

        // 给周月年注册滑动监听
        when (cardTimeType) {
            TimeCode.TIME_CODE_WEEK.timeCode -> {
                binding.cSleepWmy.setOnSelectListener { index, item ->
                    if (chartWeekList.isEmpty() || index >= chartWeekList.size) return@setOnSelectListener
                    var showCardDTO = SleepCardShowDTO(
                        null,
                        "--",
                        "当周睡眠得分",
                        "平均睡眠",
                        "--",
                        "--",
                        null,
                        null
                    )
//                    var showCardDTO2 = deafultCardShow2DTO
                    if (item != null) {
                        if (chartWeekList.isNotEmpty()) {
                            val dayDetail = chartWeekList[index]
                            var score = "0"
                            dayDetail.sleep?.let {
                                score = it.toString()
                            }
                            if (dayDetail.manualSleepTime != 0)
                                score = "--"

//                            var isAllZero =
//                                dayDetail.allSleepTimeHour == 0 && dayDetail.allSleepTimeMin == 0 && score == "0"

                            if (index < chartWeekList.size) {
                                showCardDTO = SleepCardShowDTO(
                                    getFetchTime(dayDetail.createTime, ""),
                                    score,
                                    "当周睡眠得分",
                                    "平均睡眠",
                                    dayDetail.allSleepTimeHour.toString(),
                                    dayDetail.allSleepTimeMin.toString(),
                                    dayDetail.nightSleepTimeHour,
                                    dayDetail.nightSleepTimeMin
                                )
                                modifySleepCardShowDTOByAllSleepTime(dayDetail.allSleepTime, showCardDTO)
//                                showCardDTO2 = SleepCardShow2DTO(
//                                    "",
//                                    "",
//                                    dayDetail.allSleepTime.toString(),
//                                    dayDetail.awakeTime.toString(),
//                                    dayDetail.deepSleepTime.toString(),
//                                    dayDetail.lightSleepTime.toString(),
//                                    dayDetail.dreamTime.toString(),
//                                    dayDetail.sporadicSleepTime.toString(),
//                                    "",
//                                    "",
//                                    "",
//                                    "",
//                                    ""
//                                )
                            } else {
//                                showCardDTO2 = deafultCardShow2DTO
                            }

                        }

                    } else {
                        showCardDTO = SleepCardShowDTO(
                            getFetchTime(chartWeekList[index].createTime, ""),
                            "--",
                            "当周睡眠得分",
                            "平均睡眠",
                            "--",
                            "--",
                            null,
                            null
                        )
//                        showCardDTO2 = deafultCardShow2DTO
                    }
                    binding.cardShowInfo = showCardDTO
//                    binding.cardShowInfo2 = showCardDTO2
                    Log.d(TAG, "DataObserveWeek: ->" + showCardDTO.allSleepTimeMin)
                }
                binding.cSleepWmy.setOnXTextSelectListener { index, xText ->
//                    if (isNoDataMode || (chartWeekList.isNotEmpty() && index >= chartWeekList.size)) {
                    // 仅处理有数据时滑动到未来日期无数据的情况
                    if ((chartWeekList.isNotEmpty() && index >= chartWeekList.size)) {
                        val showCardDTO = SleepCardShowDTO(
                            resetWMDateTime(xText),
                            "--",
                            "当周睡眠得分",
                            "平均睡眠",
                            "--",
                            "--",
                            null,
                            null
                        )
                        binding.cardShowInfo = showCardDTO

//                        resetData()
                    } else if (isNoDataMode) { // 仅处理无数据的情况
                        val showCardDTO = SleepCardShowDTO(
                            resetWMDateTime(xText),
                            "--",
                            "当周睡眠得分",
                            "平均睡眠",
                            "--",
                            "",
                            null,
                            null
                        )
                        binding.cardShowInfo = showCardDTO
                    }
                }
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                binding.cSleepWmy.setOnSelectListener { index, item ->
                    if (chartMonthList.isEmpty() || index >= chartMonthList.size) return@setOnSelectListener
                    var showCardDTO = SleepCardShowDTO(
                        null,
                        "--",
                        "当月睡眠得分",
                        "平均睡眠",
                        "--",
                        "--",
                        null,
                        null
                    )
//                    var showCardDTO2 = deafultCardShow2DTO
                    if (item != null) {
                        if (chartMonthList.isNotEmpty()) {
                            val dayDetail = chartMonthList[index]
                            var score = "0"
                            dayDetail.sleep?.let {
                                score = it.toString()
                            }
                            if (dayDetail.manualSleepTime != 0)
                                score = "--"

//                            var isAllZero =
//                                dayDetail.allSleepTimeHour == 0 && dayDetail.allSleepTimeMin == 0 && score == "0"
                            showCardDTO = SleepCardShowDTO(
                                getFetchTime(dayDetail.createTime, ""),
                                score,
                                "当月睡眠得分",
                                "平均睡眠",
                                dayDetail.allSleepTimeHour.toString(),
                                dayDetail.allSleepTimeMin.toString(),
                                dayDetail.nightSleepTimeHour,
                                dayDetail.nightSleepTimeMin
                            )

                            modifySleepCardShowDTOByAllSleepTime(dayDetail.allSleepTime, showCardDTO)


//                            showCardDTO2 = SleepCardShow2DTO(
//                                "",
//                                "",
//                                dayDetail.allSleepTime.toString(),
//                                dayDetail.awakeTime.toString(),
//                                dayDetail.deepSleepTime.toString(),
//                                dayDetail.lightSleepTime.toString(),
//                                dayDetail.dreamTime.toString(),
//                                dayDetail.sporadicSleepTime.toString(),
//                                "",
//                                "",
//                                "",
//                                "",
//                                ""
//                            )
                        } else {
//                            showCardDTO2 = deafultCardShow2DTO
                        }
                    } else {
                        showCardDTO = SleepCardShowDTO(
                            getFetchTime(chartMonthList[index].createTime, ""),
                            "--",
                            "当月睡眠得分",
                            "平均睡眠",
                            "--",
                            "--",
                            null,
                            null
                        )
//                        showCardDTO2 = deafultCardShow2DTO
                    }
                    binding.cardShowInfo = showCardDTO
//                    binding.cardShowInfo2 = showCardDTO2
                }
                val monthDayList = TimeUtils.getMonthListStr()
                binding.cSleepWmy.setOnXTextSelectListener { index, xText ->
                    // 处理有数据时滑动动未来日期无数据的情况
                    if ((chartMonthList.isNotEmpty() && index >= chartMonthList.size)) {
                        try {
                            val showCardDTO = SleepCardShowDTO(
                                resetWMDateTime(monthDayList[index]),
                                "--",
                                "当月睡眠得分",
                                "平均睡眠",
                                "--",
                                "--",
                                null,
                                null
                            )
                            binding.cardShowInfo = showCardDTO
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    } else if (isNoDataMode) { // 仅处理无数据模式
                        val showCardDTO = SleepCardShowDTO(
                            resetWMDateTime(monthDayList[index]),
                            "--",
                            "当月睡眠得分",
                            "平均睡眠",
                            "--",
                            "",
                            null,
                            null
                        )
                        binding.cardShowInfo = showCardDTO
                    }

                }
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                binding.cSleepWmy.setOnSelectListener { index, item ->
                    if (chartYearList.isEmpty() || index >= chartYearList.size) return@setOnSelectListener
                    var showCardDTO = SleepCardShowDTO(
                        null,
                        "--",
                        "当年睡眠得分",
                        "平均睡眠",
                        "--",
                        "--",
                        null,
                        null
                    )
//                    var showCardDTO2 = deafultCardShow2DTO
                    if (item != null) {
                        if (chartYearList.isNotEmpty()) {
                            val dayDetail = chartYearList[index]

                            var score = "0"
                            dayDetail.avgSleepScore?.let {
                                score = it.toString()
                            }

//                            var isAllZero =
//                                dayDetail.avgSleepTimeHour == 0 && dayDetail.avgSleepTimeMin == 0 && score == "0"

                            showCardDTO = SleepCardShowDTO(
                                getFetchTime(dayDetail.createTime, ""),
                                dayDetail.avgSleepScore.toString(),
                                "当年睡眠得分",
                                "平均睡眠",
                                dayDetail.avgSleepTimeHour.toString(),
                                dayDetail.avgSleepTimeMin.toString(),
                                dayDetail.avgNightSleepTimeHour,
                                dayDetail.avgNightSleepTimeMin,
                            )

                            modifySleepCardShowDTOByAllSleepTime(dayDetail.avgSleepTime, showCardDTO)


//                            showCardDTO2 = SleepCardShow2DTO(
//                                "",
//                                "",
//                                dayDetail.avgSleepTime.toString(),
//                                dayDetail.avgAwakeTime.toString(),
//                                dayDetail.avgDeepSleepTime.toString(),
//                                dayDetail.avgLightSleepTime.toString(),
//                                dayDetail.avgDreamTime.toString(),
//                                dayDetail.avgSporadicSleepTime.toString(),
//                                "",
//                                "",
//                                "",
//                                "",
//                                ""
//                            )
                        } else {
//                            showCardDTO2 = deafultCardShow2DTO
                        }
                    } else {
                        showCardDTO = SleepCardShowDTO(
                            getFetchTime(chartYearList[index].createTime, ""),
                            "--",
                            "当年睡眠得分",
                            "平均睡眠",
                            "--",
                            "--",
                            null,
                            null
                        )
//                        showCardDTO2 = deafultCardShow2DTO
                    }
                    binding.cardShowInfo = showCardDTO
//                    binding.cardShowInfo2 = showCardDTO2
                }
                binding.cSleepWmy.setOnXTextSelectListener { index, xText ->
                    if (chartYearList.isNotEmpty() && index >= chartYearList.size) {
                        val showCardDTO = SleepCardShowDTO(
                            resetYDateTime(xText),
                            "--",
                            "当年睡眠得分",
                            "平均睡眠",
                            "--",
                            "--",
                            null,
                            null
                        )
                        binding.cardShowInfo = showCardDTO
                    } else if (isNoDataMode) {
                        val showCardDTO = SleepCardShowDTO(
                            resetYDateTime(xText),
                            "--",
                            "当年睡眠得分",
                            "平均睡眠",
                            "--",
                            "",
                            null,
                            null
                        )
                        binding.cardShowInfo = showCardDTO
                    }
                }
            }
        }
    }

    private fun processHealthDataYear(it: BaseResponse<SleepYearResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            if (!it.data!!.sleepYearVO?.monthDataList.isNullOrEmpty()) {
                var tempYearList = it.data!!.sleepYearVO!!.monthDataList
                if (isYDataListNotEmpty(tempYearList)) {
                    tempYearList = tempYearList.clone() as ArrayList<SleepWMYDataDTO>
                    year = it.data!!.sleepYearVO!!.year
                    val lastData = tempYearList.last()
                    val showCardDTO = SleepCardShowDTO(
                        getFetchTime(lastData.createTime, ""),
                        lastData.avgSleepScore.toString(),
                        "当年睡眠得分",
                        "平均睡眠",
                        lastData.avgSleepTimeHour.toString(),
                        lastData.avgSleepTimeMin.toString(),
                        lastData.avgNightSleepTimeHour,
                        lastData.avgNightSleepTimeMin,
                    )

                    modifySleepCardShowDTOByAllSleepTime(lastData.avgSleepTime, showCardDTO)

                    val showCardDTO1 = SleepCardShowDTO(
                        "",
                        it.data!!.sleepYearVO.avgSleepScore.toString(),
                        "当年睡眠得分",
                        "",
                        "",
                        "",
                        0,
                        0,
                    )

                    // 构建第三张卡片
                    val showCardDTO2 = SleepCardShow2DTO(
                        it.data!!.sleepYearVO.avgDeepSleepTime.toString(), // 深睡时间
                        it.data!!.sleepYearVO.avgLightSleepTime.toString(), // 浅睡时间
                        it.data!!.sleepYearVO.avgDreamTime.toString(), // 快速眼动
                        it.data!!.sleepYearVO.avgAwakeTime.toString(), // 清醒时间
                        it.data!!.sleepYearVO.avgSporadicSleepTime.toString() // 零星小睡
                    )

                    binding.cardShowInfo = showCardDTO
                    binding.cardShowInfo1 = showCardDTO1
                    binding.cardShowInfo2 = showCardDTO2
                    chartYearList = tempYearList.toMutableList() as ArrayList<SleepWMYDataDTO>
                    // 风险与建议请求
                    val map = mutableMapOf<String, String>()
                    map.putAll(reqMap)
                    val dto = it.data!!
                    if (dto.startTime.isNotEmpty()) {
                        map["startTime"] = dto.startTime
                    }
                    if (dto.endTime.isNotEmpty()) {
                        map["endTime"] = dto.endTime
                    }

                    initChartData()
                    // 处理建议数据
                    processLoadingSummaryData(map)
                } else {
                    initChartNoData(TimeCode.TIME_CODE_YEAR.timeCode)
                }
            } else {
                // 无数据
                initChartNoData(TimeCode.TIME_CODE_YEAR.timeCode)
            }

        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 500)
    }

    private fun processHealthDataMonth(it: BaseResponse<SleepMonthResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            if (!it.data!!.sleepStatVO?.nodeList.isNullOrEmpty() && it.data!!.sleepStatVO?.avgSleepTime != 0) {
                chartTempList.clear()
                val tempList = it.data!!.sleepStatVO!!.nodeList
                if (isWMDataListNotEmpty(tempList)) {
                    chartTempList = tempList.clone() as  ArrayList<SleepWMItemDTO>
                    val lastData = chartTempList.last()
                    var score = lastData.sleep.toString()
                    if (lastData.manualSleepTime != 0)
                        score = "--"
                    val showCardDTO = SleepCardShowDTO(
                        getFetchTime(lastData.createTime, ""),
                        score,
                        "当月睡眠得分",
                        "总睡眠",
                        lastData.allSleepTimeHour.toString(),
                        lastData.allSleepTimeMin.toString(),
                        lastData.nightSleepTimeHour,
                        lastData.nightSleepTimeMin,
                    )

                    modifySleepCardShowDTOByAllSleepTime(lastData.allSleepTime, showCardDTO)

                    // 构建第二张卡片
                    val showCardDTO1 = SleepCardShowDTO(
                        "",
                        it.data!!.sleepStatVO.avgSleepScore.toString(),
                        "当月睡眠得分",
                        "",
                        "",
                        "",
                        0,
                        0,
                    )

                    // 构建第三张卡片
                    val showCardDTO2 = SleepCardShow2DTO(
                        it.data!!.sleepStatVO.avgDeepSleepTime.toString(), // 深睡时间
                        it.data!!.sleepStatVO.avgLightSleepTime.toString(), // 浅睡时间
                        it.data!!.sleepStatVO.avgDreamTime.toString(), // 快速眼动
                        it.data!!.sleepStatVO.avgAwakeTime.toString(), // 清醒时间
                        it.data!!.sleepStatVO.avgSporadicSleepTime.toString() // 零星小睡
                    )

                    binding.cardShowInfo = showCardDTO
                    binding.cardShowInfo1 = showCardDTO1
                    binding.cardShowInfo2 = showCardDTO2

                    chartMonthList = chartTempList.toMutableList() as ArrayList<SleepWMItemDTO>
                    // 风险与建议请求
                    val map = mutableMapOf<String, String>()
                    map.putAll(reqMap)
                    val dto = it.data!!
                    if (dto.startTime.isNotEmpty()) {
                        map["startTime"] = dto.startTime
                    }
                    if (dto.endTime.isNotEmpty()) {
                        map["endTime"] = dto.endTime
                    }
                    initChartData()

                    // 处理建议数据
                    processLoadingSummaryData(map)
                } else {
                    initChartNoData(TimeCode.TIME_CODE_MONTH.timeCode)
                }
            } else {
                // 无数据
                initChartNoData(TimeCode.TIME_CODE_MONTH.timeCode)
            }

        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            initChartNoData(TimeCode.TIME_CODE_MONTH.timeCode)
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 400)
    }

    private fun processHealthDataWeek(it: BaseResponse<SleepWeekResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            if (!it.data!!.sleepStatVO?.nodeList.isNullOrEmpty()) {
                chartTempList.clear()
                val tempList = it.data!!.sleepStatVO!!.nodeList
                if (isWMDataListNotEmpty(tempList)) {
                    chartTempList = tempList.clone() as  ArrayList<SleepWMItemDTO>
                    val lastData = chartTempList.last()
                    var sleepScore = lastData.sleep.toString()
                    if (lastData.manualSleepTime != 0)
                        sleepScore = "--"
                    val showCardDTO = SleepCardShowDTO(
                        getFetchTime(lastData.createTime, ""),
                        sleepScore,
                        "当周睡眠得分",
                        "总睡眠",
                        lastData.allSleepTimeHour.toString(),
                        lastData.allSleepTimeMin.toString(),
                        lastData.nightSleepTimeHour,
                        lastData.nightSleepTimeMin,
                    )
                    modifySleepCardShowDTOByAllSleepTime(lastData.allSleepTime, showCardDTO)

                    // 构建第二张卡片
                    val showCardDTO1 = SleepCardShowDTO(
                        "",
                        it.data!!.sleepStatVO.avgSleepScore.toString(),
                        "当周睡眠得分",
                        "",
                        "",
                        "",
                        0,
                        0,
                    )

                    // 构建第三张卡片
                    val showCardDTO2 = SleepCardShow2DTO(
                        it.data!!.sleepStatVO.avgDeepSleepTime.toString(), // 深睡时间
                        it.data!!.sleepStatVO.avgLightSleepTime.toString(), // 浅睡时间
                        it.data!!.sleepStatVO.avgDreamTime.toString(), // 快速眼动
                        it.data!!.sleepStatVO.avgAwakeTime.toString(), // 清醒时间
                        it.data!!.sleepStatVO.avgSporadicSleepTime.toString() // 零星小睡
                    )

                    binding.cardShowInfo = showCardDTO
                    binding.cardShowInfo1 = showCardDTO1
                    binding.cardShowInfo2 = showCardDTO2
                    chartWeekList = chartTempList.toMutableList() as ArrayList<SleepWMItemDTO>
                    // 风险与建议请求
                    val map = mutableMapOf<String, String>()
                    map.putAll(reqMap)
                    val dto = it.data!!
                    if (dto.startTime.isNotEmpty()) {
                        map["startTime"] = dto.startTime
                    }
                    if (dto.endTime.isNotEmpty()) {
                        map["endTime"] = dto.endTime
                    }

                    initChartData()
                    // 处理建议数据
                    processLoadingSummaryData(map)

                } else {
                    initChartNoData(TimeCode.TIME_CODE_WEEK.timeCode)
                }
            } else {
                // 无数据
                initChartNoData(TimeCode.TIME_CODE_WEEK.timeCode)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 400)
    }

    private fun processHealthDataDay(it: BaseResponse<SleepDayResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            if (!it.data!!.sleepDayVO?.fragmentList.isNullOrEmpty()) {
                it.data?.sleepDayVO?.let { dayDTO ->
                    val showCardDTO = SleepCardShowDTO(
                        getFetchTime(dayDTO.createTime, ""),
                        dayDTO.sleep,
                        "当日睡眠得分",
                        "总睡眠",
                        dayDTO.nightSleepTimeHour.toString(),
                        dayDTO.nightSleepTimeMin.toString(),
                        dayDTO.nightSleepTimeHour,
                        dayDTO.nightSleepTimeMin,
                    )
                    // 使用 SleepCardShowDTO 对象做中间转换，可以把日、周、月、年数据转换成XML界面使用的数据
                    binding.cardShowInfo = showCardDTO
                }
                val sleepDayVO = it.data!!.sleepDayVO!!
                chartDataList = sleepDayVO.fragmentList
                startTime = getTime(sleepDayVO.fallSleepTime)
                endTime = getTime(sleepDayVO.wakeupTime)
                startDate = getDate(sleepDayVO.fallSleepTime)
                endDate = getDate(sleepDayVO.wakeupTime)
                // 风险与建议请求
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                val dto = it.data!!
                if (dto.startTime.isNotEmpty()) {
                    map["startTime"] = dto.startTime
                }
                if (dto.endTime.isNotEmpty()) {
                    map["endTime"] = dto.endTime
                }

                // 构建第二张卡片
                val showCardDTO1 = SleepCardShowDTO(
                    "",
                    it.data!!.sleepDayVO.sleep.toString(),
                    "当日睡眠得分",
                    "",
                    "",
                    "",
                    0,
                    0,
                )

                // 日视图：构建第三张卡片
                val showCardDTO2 = SleepCardShow2DTO(
                    sleepDayVO.deepSleepTime.toString(), // 深睡时间
                    sleepDayVO.lightSleepTime.toString(), // 浅睡时间
                    sleepDayVO.dreamTime.toString(), // 快速眼动
                    sleepDayVO.awakeTime.toString(), // 清醒时间
                    ""
                )
                binding.cardShowInfo1 = showCardDTO1
                // 第三张卡片
                binding.cardShowInfo2 = showCardDTO2
                initChartData()

                // 处理建议数据
                processLoadingSummaryData(map)

            } else {
                // 无数据
                initChartNoData(TimeCode.TIME_CODE_DAY.timeCode)
            }

        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }

        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 400)
    }

    /**
     * 判断是否有合理的健康建议数据。如果有，则直接使用，否则去服务器获取
     */
    private fun processLoadingSummaryData(map: MutableMap<String, String>) {
        if(fragmentDataModel==null
            || fragmentDataModel.healthSummaryResponse == null
            || fragmentDataModel.healthSummaryResponse!!.code != "0") {
            viewModel.getHealthSleepSummery(map)
        }else{
            processSummaryData(fragmentDataModel.healthSummaryResponse!!)
        }
    }

    /**
     * 处理睡眠健康建议数据
     */
    private fun processSummaryData(it: BaseResponse<HealthSleepSummeryDTO>) {
        fragmentDataModel.healthSummaryResponse = it
        if (it.code == "0" && it.data != null) {
            binding.healthSleepSummeryVo = it.data
            binding.svContainer.post {
                binding.svContainer.requestLayout()
            }
            Handler(Looper.getMainLooper()).postDelayed({
                hideLoading()
                setScrollEnable(binding.svContainer)
            }, 100)
            // 重置查看更多按钮为不可见
            binding.healthRiskAll.tvHealthAdviceContent.setIsEllipsized(false)
            it.data!!.healthAdvice?.let { item ->
                healthAdviceStr = item
            }
        } else {
            ToastUtil.makeText(requireContext(), "获取数据失败$it", Toast.LENGTH_SHORT).show()
        }

        // 恢复滚动位置
        if(this.fragmentDataModel.scollY>0) {
            binding.svContainer.post {
                binding.svContainer.scrollY = this.fragmentDataModel.scollY
            }
        }
    }

    /**
     * 重置睡眠卡片一的数据,解决当天无数据时，显示平均睡眠0小时0分钟的问题
     * 判断接口返回数据为空是判断所有数据的 allSleepTime != null && allSleepTime == 0
     * 其中有一个数据满足以上条件都认为是非空
     */
    private fun modifySleepCardShowDTOByAllSleepTime(allSleepTime: Int?, showCardDTO: SleepCardShowDTO) {
        if (allSleepTime == null || allSleepTime == 0) {
            showCardDTO.allSleepTimeHour = "--"
            showCardDTO.allSleepTimeMin = "--"
        }
    }

    private fun resetData() {
        val showCardDTO = SleepCardShowDTO(
            binding.tvSleepDate.text.toString(),
            "--",
            binding.tvSleepIntroRange.toString(),
            "平均睡眠",
            "--",
            "",
            null,
            null
        )
        // 使用 SleepCardShowDTO 对象做中间转换，可以把日、周、月、年数据转换成XML界面使用的数据
        binding.cardShowInfo = showCardDTO

        binding.cardShowInfo2 = SleepCardShow2DTO(
            null, null, null, null, // 深睡时间
            null
        )

        binding.tvSleepScoreValue.text = "--"

    }

    private fun initUI() {
        initExplain()
        ClickUtils.applySingleDebouncing(binding.ivIntroTips, 1000) {
//            showHmsDialog(R.layout.hms_dialog_tips_small, "睡眠说明", introString)
            showExplainDialog()
        }
        ClickUtils.applySingleDebouncing(binding.ivIntroTips2, 1000) {
//            showHmsDialog(R.layout.hms_dialog_tips_small, "睡眠说明", introString)
            showExplainDialog()
        }
        binding.healthRiskAll.tvHealthAdviceContent.setCallback(this)
        binding.healthRiskAll.tvHealthAdviceContent.setEndPercentage(80)
        binding.healthRiskAll.tvSeeMore.text = Html.fromHtml("<u>查看更多</u>")
        // 处理图表视图与scrollView的滑动冲突

        val scrollView = binding.svContainer
        scrollView.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            val height = scrollView.getChildAt(0).height // 获取ScrollView内容的总高度
            val scrollViewHeight = scrollView.height // 获取ScrollView的可见高度
            val diff = (height - scrollViewHeight) * 0.75f //scrollview中判定的距离 动画view位置底部约为总长度的的75%
            if (scrollY >= diff) {
                MMKVUtil.getUserId()?.let {
                    DataTrackUtil.dtScroll(
                        "Health_Sleepreports_Bottom_Show",
                        DataTrackUtil.userIDMap(it)
                    )
                }
            }
        }

    }

    fun initChartData() {
        isNoDataMode = false
        binding.sPrivacyText.visibility = View.GONE
        setUnitVisiable(true)
        // 根据不同的cardTimeType 初始化不同的view
        when (cardTimeType) {
            // 时间类型日 - 折线图
            TimeCode.TIME_CODE_DAY.timeCode -> {
                binding.cSleepDay.visibility = View.VISIBLE
                binding.cSleepWmy.visibility = View.GONE
                initDayChart()
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                binding.cSleepDay.visibility = View.GONE
                binding.cSleepWmy.visibility = View.VISIBLE
                initWeekChart()
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                binding.cSleepDay.visibility = View.GONE
                binding.cSleepWmy.visibility = View.VISIBLE
                initMonthChart()
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                binding.cSleepDay.visibility = View.GONE
                binding.cSleepWmy.visibility = View.VISIBLE
                initYearChart()
            }

        }

    }

    fun initChartNoData(timeCode: String) {
        isNoDataMode = true
        setUnitVisiable(false)
        binding.sleepMinValue.text = ""
        binding.tvSleepDate.text = getDayFetchTime()
        when (timeCode) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                binding.cSleepDay.visibility = View.VISIBLE
                binding.cSleepWmy.visibility = View.GONE
                binding.tvSleepIntroRange.text = "当日睡眠得分"
                binding.cSleepDay.setValue(
                    null, getYesterday(), getCurrentDay(), "22:00", "08:00"
                )

                resetData()

            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                binding.cSleepDay.visibility = View.GONE
                binding.cSleepWmy.visibility = View.VISIBLE
                binding.tvSleepIntroRange.text = "当周睡眠得分"
                binding.cSleepWmy.setValue(null, TimeUtils.getWeekListStr(), 0)
                resetData()
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                binding.cSleepDay.visibility = View.GONE
                binding.cSleepWmy.visibility = View.VISIBLE
                binding.tvSleepIntroRange.text = "当月睡眠得分"
                binding.cSleepWmy.setValue(null, TimeUtils.getMonthListStr(), 1)
                resetData()
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                binding.cSleepDay.visibility = View.GONE
                binding.cSleepWmy.visibility = View.VISIBLE
                binding.tvSleepIntroRange.text = "当年睡眠得分"
                binding.cSleepWmy.setValue(null, null, 2)
                resetData()
            }

        }
        if (cardTimeType == TimeCode.TIME_CODE_YEAR.timeCode) {
            binding.tvSleepDate.text = "${Calendar.getInstance()[Calendar.YEAR]}年7月"
        }
        if (cardTimeType == TimeCode.TIME_CODE_MONTH.timeCode) {
            binding.tvSleepDate.text = "${Calendar.getInstance()[Calendar.MONTH] + 1}月16日"
        }

    }


    private fun initDayChart() {
        var sleepList: MutableList<SleepItem> = ArrayList<SleepItem>()
        var tempList: MutableList<SleepDayItemDTO> = ArrayList<SleepDayItemDTO>()

        var addtemp = 0
        var sleepItem: SleepItem
        for (index in chartDataList.indices) {
            if (index == 0) {
                tempList.add(chartDataList[0])
            } else if (index == chartDataList.size - 1) {
                if (tempList.size < 9) {
                    sleepItem =
                        SleepItem(
                            status = changeDayData(chartDataList[index].sleepStateCode),
                            duration = 9,
                            startTime = tempList[0].startTime,
                            endTime = tempList.last().endTime,
                            tempList.size

                        )
                    addtemp = addtemp + 9 - tempList.size
                } else {
                    sleepItem =
                        SleepItem(
                            status = changeDayData(chartDataList[index].sleepStateCode),
                            duration = tempList.size,
                            startTime = tempList[0].startTime,
                            endTime = tempList.last().endTime,
                            tempList.size
                        )
                }
                sleepList.add(sleepItem)
            } else {
                if (chartDataList[index].sleepStateCode == chartDataList[index - 1].sleepStateCode) {
                    tempList.add(chartDataList[index])
                } else {
                    if (tempList.size < 9) {
                        sleepItem =
                            SleepItem(
                                status = changeDayData(chartDataList[index - 1].sleepStateCode),
                                duration = 9,
                                startTime = tempList[0].startTime,
                                endTime = tempList.last().endTime,
                                tempList.size
                            )
                        addtemp = addtemp + 9 - tempList.size
                    } else {
                        sleepItem =
                            SleepItem(
                                status = changeDayData(chartDataList[index - 1].sleepStateCode),
                                duration = tempList.size,
                                startTime = tempList[0].startTime,
                                endTime = tempList.last().endTime,
                                tempList.size
                            )

                    }
                    sleepList.add(sleepItem)
                    tempList.clear()
                    tempList.add(chartDataList[index])
                }
            }

        }

        Log.d(TAG, "initDayChart:" + sleepList.size)
        binding.cSleepDay.setValue(
            Sleep(
                total = chartDataList.size + addtemp, items = sleepList as ArrayList<SleepItem>
            ), startDate, endDate, startTime, endTime
        )

    }


    private fun initWeekChart() {
        var sleepList: MutableList<SleepTime> = ArrayList<SleepTime>()
        chartWeekList.forEachIndexed { index, item ->
            var sleepTime = SleepTime(
                item.deepSleepTime,
                item.lightSleepTime,
                item.dreamTime,
                item.awakeTime,
                item.allSleepTime,
                item.sporadicSleepTime,
                item.manualSleepTime,
                ""
            )
            sleepList.add(sleepTime)
        }
        //sleepList.reverse()

        binding.cSleepWmy.setValue(SleepDataType(sleepList), TimeUtils.getWeekListStr(), 0)

    }

    private fun initMonthChart() {
        var sleepList: MutableList<SleepTime> = ArrayList<SleepTime>()
        chartMonthList.forEachIndexed { index, item ->
            var sleepTime = SleepTime(
                item.deepSleepTime,
                item.lightSleepTime,
                item.dreamTime,
                item.awakeTime,
                item.allSleepTime,
                item.sporadicSleepTime,
                item.manualSleepTime,
                ""
            )
            sleepList.add(sleepTime)
        }
        // sleepList.reverse()
        binding.cSleepWmy.setValue(SleepDataType(sleepList), TimeUtils.getMonthListStr(), 1)
    }

    private fun initYearChart() {
        var sleepList: MutableList<SleepTime> = ArrayList<SleepTime>()
        chartYearList.forEachIndexed { index, item ->
            //手动输入和自动记录都有时  只显示自动记录的
            //只有手动输入的时显示手动输入的
            var sleepTime: SleepTime
            if (item.avgDeepSleepTime == 0 && item.avgLightSleepTime == 0 && item.avgDreamTime == 0 && item.avgAwakeTime == 0 && item.avgSporadicSleepTime == 0 && item.avgManualSleepTime > 0) {
                sleepTime = SleepTime(
                    0,
                    0,
                    0,
                    0,
                    item.avgSleepTimeHour * 60 + item.avgSleepTimeMin,
                    0,
                    item.avgManualSleepTime,
                    item.month
                )
            } else {
                sleepTime = SleepTime(
                    item.avgDeepSleepTime,
                    item.avgLightSleepTime,
                    item.avgDreamTime,
                    item.avgAwakeTime,
                    item.avgSleepTimeHour * 60 + item.avgSleepTimeMin,
                    item.avgSporadicSleepTime,
                    0,
                    item.month
                )
            }
            sleepList.add(sleepTime)
        }
        binding.cSleepWmy.setValue(SleepDataType(sleepList), null, 2)
    }


//endregion

//region 计算日期工具方法


    private fun showExplainDialog() {
        showHmsDialog(R.layout.hms_dialog_tips_small, "睡眠说明", introString)
    }

    private var dialog: Dialog? = null
    /**
     * 显示自定义对话框
     */
    fun showHmsDialog(layoutId: Int, title: String, message: String?) {
        // 创建自定义视图
        val view = LayoutInflater.from(requireActivity()).inflate(layoutId, null)
        val contentView = view.findViewById<RelativeLayout>(R.id.dialog_content)
        var btnPositive = view.findViewById<Button>(R.id.positiveButton)
        var tvMessage = view.findViewById<TextView>(R.id.textView)
        var tvTitle = view.findViewById<TextView>(R.id.tv_tips_title_small)
        tvTitle.text = title
        if (message != null && !message.isNullOrBlank()) {
            tvMessage.setText(message)
        }
        // 创建并显示对话框
        dialog = ImmersiveDialog(
            requireContext(),
            R.style.MyDialogStyle
        )
        //dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog!!.setContentView(view)
        dialog!!.setOnShowListener {
            HMSDialogUtils.doDialogAnimateEnter(dialog!!)
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    fragmentDataModel.isShowExplainDialog = false
                    dialog?.dismiss()
                }
                false
            }
        }
        // 设置按钮点击事件
        btnPositive.setOnClickListener {
            fragmentDataModel.isShowExplainDialog = false
            dialog?.dismiss()
        }
        dialog!!.show()
        fragmentDataModel.isShowExplainDialog = true
    }

    fun getDate(ori: String): String {

        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val dateTime = LocalDateTime.parse(ori, formatter)
        return String.format("%02d", dateTime.monthValue) + "/" + String.format(
            "%02d",
            dateTime.dayOfMonth
        )
    }

    fun getTime(ori: String): String {

        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val dateTime = LocalDateTime.parse(ori, formatter)
        return String.format("%02d", dateTime.hour) + ":" + String.format("%02d", dateTime.minute)
    }


    fun changeDayData(ori: Int): Int {
        var result = 1
        when (ori) {
            1 -> result = 1
            2 -> result = 2
            3 -> result = 3
            4 -> result = 4

        }
        return result
    }


    fun initPrivacyUI(timeCode: String) {
        setScrollEnable(binding.svContainer, false)
        setUnitVisiable(true)
        binding.cSleepDay.visibility = View.GONE
        binding.cSleepWmy.visibility = View.GONE
        binding.sPrivacyText.visibility = View.VISIBLE
        binding.tvSleepDate.text = getPrivacyModeDate(timeCode)
        binding.tvSleepIntroRange.text = when (timeCode) {
            TimeCode.TIME_CODE_DAY.timeCode -> "当日睡眠得分"
            TimeCode.TIME_CODE_WEEK.timeCode -> "当周睡眠得分"
            TimeCode.TIME_CODE_MONTH.timeCode -> "当月睡眠得分"
            TimeCode.TIME_CODE_YEAR.timeCode -> "当年睡眠得分"
            else -> "当日睡眠得分"
        }
        binding.sleepHourValue.text = "***"
        binding.sleepMinValue.text = "***"
        binding.tvSleepScoreValue.text = "***"
        binding.clDeepSleepTime.text = "***小时***分钟"
        binding.clLightSleepTime.text = "***小时***分钟"
        binding.clRemTime.text = "***小时***分钟"
        binding.clAwakeTime.text = "***小时***分钟"
        binding.clSporadicTime.text = "***小时***分钟"
        binding.tvSleepDayC4Deep.text = "***小时***分钟"
        binding.tvSleepDayC4Light.text = "***小时***分钟"
        binding.tvSleepDayC4Dream.text = "***小时***分钟"
        binding.tvSleepDayC4Awake.text = "***小时***分钟"
        // 健康建议在私密模式下不展示
        binding.healthSleepSummeryVo = null

        moveToTop(binding.sleepAllTime)
        moveToTop(binding.sleepHourUnit)
        moveToTop(binding.sleepMinUnit)
        moveToTop(binding.tvSleepScoreUnit)
        binding.svContainer.visibility = View.VISIBLE
    }

    private fun applyPrivacyStyleChanged(isPrivacyMode: Boolean) {
        binding.llIntroContainer.apply {
            val params = layoutParams as ViewGroup.MarginLayoutParams
            params.bottomMargin = if (isPrivacyMode) 30f.dp.toInt() else 0f.dp.toInt()
            layoutParams = params
        }
        listOf(
            binding.clDeepSleepTime,
            binding.clLightSleepTime,
            binding.clRemTime,
            binding.clAwakeTime,
            binding.clSporadicTime,
            binding.tvSleepDayC4Deep,
            binding.tvSleepDayC4Light,
            binding.tvSleepDayC4Dream,
            binding.tvSleepDayC4Awake
        ).forEach {
            // 创建新的 Typeface 实例，避免与现有样式冲突
            val style = if (isPrivacyMode) Typeface.BOLD else Typeface.NORMAL
            it.setTypeface(Typeface.create(Typeface.DEFAULT, style))
            it.invalidate() // 强制重绘以确保变化生效
        }
    }

    override fun update(isEllipsized: Boolean, viewID: Int) {
        if (isEllipsized) {
            binding.healthRiskAll.cardAdviceMore.visibility = View.VISIBLE
            binding.healthRiskAll.cardAdviceMore.setOnClickListener { v ->
                HMSDialogUtils.showHMSNotiDialog(
                    requireContext(),
                    R.layout.hms_dialog_see_more,
                    "健康建议",
                    healthAdviceStr,
                    "知道了"
                ) { isPositive ->
                }
            }
        } else {
            binding.healthRiskAll.cardAdviceMore.visibility = View.GONE
        }
    }


    fun getFetchTime(time: String?, default: String): String {
        if (time != null)
            return time
        else
            return default
    }

    fun isWMDataListNotEmpty(dataList: ArrayList<SleepWMItemDTO>): Boolean {
        dataList.forEach {
            if (it.allSleepTime != null && it.allSleepTime != 0)
                return true
        }
        return false
    }

    fun isYDataListNotEmpty(dataList: ArrayList<SleepWMYDataDTO>): Boolean {
        dataList.forEach {
            if (it.avgSleepTime != null)
                return true
        }
        return false
    }

    fun getCurrentDay(): String {
        val calendar: Calendar = Calendar.getInstance()
        val month: Int = calendar.get(Calendar.MONTH) + 1 // 月份是从0开始的，需要加1
        val day: Int = calendar.get(Calendar.DAY_OF_MONTH)
        return "${month}/${day}"
    }

    fun getYesterday(): String {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_MONTH, -1) // 将日期减1天
        return "${calendar.get(Calendar.MONTH) + 1}/${calendar.get(Calendar.DAY_OF_MONTH)}"
    }

    fun setUnitVisiable(sw: Boolean) {
        if (sw) {
            binding.sleepHourUnit.visibility = View.VISIBLE
            binding.sleepMinUnit.visibility = View.VISIBLE
            binding.sleepAllTime.visibility = View.VISIBLE
            binding.tvSleepScoreUnit.visibility = View.VISIBLE
        } else {
            binding.sleepHourUnit.visibility = View.GONE
            binding.sleepMinUnit.visibility = View.GONE
            binding.sleepAllTime.visibility = View.GONE
            binding.tvSleepScoreUnit.visibility = View.GONE
        }
    }

    fun getDayFetchTime(): String {
        val calendar = Calendar.getInstance()
        val month = calendar[Calendar.MONTH] + 1 // 月份是从0开始的，需要加1
        val day = calendar[Calendar.DAY_OF_MONTH]
        return "${month}月${day}日"
    }

    override fun onPrivacyModeChange(provacyMode: Boolean) {
        if (isProvacyMode != provacyMode) {
            //当前模式与变化模式不同
            sendRequest(mUserId, cardTimeType!!)
            isProvacyMode = provacyMode
        }
    }

    fun initExplain() {
        val tips = binding.ivIntroTips
        tips.visibility = View.VISIBLE
        viewAddOnGlobalLayoutListener(tips)

        val tips2 = binding.ivIntroTips2
        tips2.visibility = View.VISIBLE
        viewAddOnGlobalLayoutListener(tips2)
        this.introString = getString(R.string.description_sleep)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        try {
            dialog?.dismiss()
            dialog = null
            viewRemoveOnGlobalLayoutListener(binding.ivIntroTips)
            viewRemoveOnGlobalLayoutListener(binding.ivIntroTips2)
            ToastUtil.cancelToast()
            binding.healthRiskAll.tvHealthAdviceContent.setCallback(null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        handler.removeCallbacksAndMessages(null)
        _fragmentInteractWithAC = null
    }

    fun initCardTextShow(){
        //初始化卡片文字可见
        when (cardTimeType) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                binding.sleepAllTime.text = "总睡眠"
                binding.legendSleepOfSleepLittle.visibility = View.GONE
                binding.legendSleep.visibility = View.GONE
                binding.sleepCount2.visibility = View.VISIBLE
                binding.sleepCount.visibility = View.GONE
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                binding.cSleepWmy.setXData(TimeUtils.getWeekListStr(), 0)
                binding.sleepAllTime.text = "平均睡眠"
                binding.legendSleepOfSleepLittle.visibility = View.VISIBLE
                binding.legendSleep.visibility = View.VISIBLE
                binding.sleepCount2.visibility = View.GONE
                binding.sleepCount.visibility = View.VISIBLE
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                binding.cSleepWmy.setXData(TimeUtils.getMonthListStr(), 1)
                binding.sleepAllTime.text = "平均睡眠"
                binding.legendSleepOfSleepLittle.visibility = View.VISIBLE
                binding.legendSleep.visibility = View.VISIBLE
                binding.sleepCount2.visibility = View.GONE
                binding.sleepCount.visibility = View.VISIBLE
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                binding.cSleepWmy.setXData(null, 2)
                binding.sleepAllTime.text = "平均睡眠"
                binding.legendSleepOfSleepLittle.visibility = View.VISIBLE
                binding.legendSleep.visibility = View.VISIBLE
                binding.sleepCount2.visibility = View.GONE
                binding.sleepCount.visibility = View.VISIBLE
            }
        }
    }
}