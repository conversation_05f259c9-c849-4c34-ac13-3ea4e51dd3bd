package com.healthlink.hms.thirdLibUse;

import android.content.Context;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.gwm.tts.service.client.ConnectStateListener;
import com.gwm.tts.service.client.GwmTTSManager;
import com.gwm.tts.service.client.TTSListener;
import com.gwm.tts.service.request.Priority;
import com.gwm.tts.service.request.StreamChannel;
import com.gwm.tts.service.request.TTSRequest;

import java.util.Map;

/**
 * Created by imaginedays on 2024/5/28
 */
public class HMSTTSProvider {
    private static final String TAG = "HMSTTSProvider";
    private HandlerThread ttsHandlerThread = null;
    private static final String sThreadName = "HMSTTSProvider-tts-ability";
    private static final int ERRO_FOCUS_REQUEST_FAIL = 101;//未申请到焦点
    private static final int ERRO_FOCUS_LOSS = 102;//焦点被丢失
    private static final int ERRO_BREAKED = 103;//被打断
    private static final int ERRO_CANCEL = 104;//被取消
    private static final int ERRO_EXPIRED = 105;//过期
    private static final int ERRO_UNKNOW = 106;//未知
    private Context context;
    private volatile boolean mBusy = false;
    private volatile boolean isConnect = false;
    private ConnectStateListener connectStateListener;
    public TTSListener ttsListener;

    private Handler ttsHandler;
    private volatile boolean mInit = false;

    public void init(Context context) throws RemoteException {
        ttsHandlerThread = new HandlerThread(sThreadName);
        ttsHandlerThread.start();

        this.context = context;
        this.ttsHandler = new Handler(ttsHandlerThread.getLooper());
        Log.w(TAG, "new Handler()=" + ttsHandler);
        doRealInit();
    }

    private void doRealInit() {
        Log.w(TAG, "doRealInit()");
        getTtsHandler().post(new Runnable() {
            @Override
            public void run() {
                Log.w(TAG, "init()");
                try {
                    GwmTTSManager.getInstance().init(context, getConnectStateListener());
                    GwmTTSManager.getInstance().setTTSListener(getTTSListener());
                    mInit = true;
                } catch (RemoteException e) {
                    Log.w(TAG, "init() e=" + e.getMessage());
                }
            }
        });
    }

    public void playTTS(String ttsMsg, boolean stopCurrent) throws RemoteException {
        if (!isPlaying() && stopCurrent) {
            getTtsHandler().removeCallbacksAndMessages(null);
            stopPlayTTS();
        }
        getTtsHandler().post(new Runnable() {
            @Override
            public void run() {
                if (TextUtils.isEmpty(ttsMsg)) {
                    Log.i(TAG, "playTTS ttsMsg empty");
                    return;
                }
                if (!isConnect) {
                    Log.i(TAG, "playTTS isConnect=" + isConnect + " mInit=" + mInit);
                    doRealInit();
                }
                Log.d(TAG, "playTTS ttsMsg:" + ttsMsg);
                String ttsId = TTSRequest.createRequestId();
                TTSRequest ttsRequest = new TTSRequest(ttsMsg, ttsId, -1, Priority.T1, StreamChannel.INTERACTIVE);
                GwmTTSManager.getInstance().playTTS(ttsRequest);
            }
        });
    }

    public boolean isPlaying() throws RemoteException {
        return mBusy;
    }

    public void stopPlayTTS() throws RemoteException {
        //GwmTTSManager MessageQueen 导致delay
        Log.d(TAG, "stopPlayTTS()");
        GwmTTSManager.getInstance().stopAllTTS(StreamChannel.INTERACTIVE);
    }

    private Handler getTtsHandler() {
        return ttsHandler;
    }

    public TTSListener getTTSListener() {
        if (null == ttsListener) {
            ttsListener = new TTSListener() {
                @Override
                public void onStart(String ttsId) {
                    //开始播放
                    Log.d(TAG, "onStart() ttsId:" + ttsId);
                    mBusy = true;
                }

                @Override
                public void onCancel(String ttsId, int cause) {
                    Log.d(TAG, "onCancel(),ttsId:" + ttsId + ",cause:" + cause);
                    mBusy = false;
                }

                @Override
                public void onFinish(String ttsId) {
                    //结束播放
                    Log.d(TAG, "onFinish() ttsId:" + ttsId);
                    mBusy = false;
                }
            };
        }
        return ttsListener;
    }

    public ConnectStateListener getConnectStateListener() throws RemoteException {
        if (null == connectStateListener) {
            connectStateListener = new ConnectStateListener() {
                @Override
                public void onConnectState(boolean connect) {
                    Log.w(TAG, "onConnectState(),connect:" + connect);
                    try {
                        isConnect = connect;
                        notifyInitAbilityCallback(isConnect);
                    } catch (RemoteException e) {
                        Log.i(TAG, "onConnectState(),connect error:" + e.getMessage());
                    }
                }
            };
        }
        return connectStateListener;
    }

    //初始化回调
    private void notifyInitAbilityCallback(boolean result) throws RemoteException {
//        for (Map.Entry<String, InitAbilityCallback> entry : initAbilityCallbacks.entrySet()) {
//            InitAbilityCallback callBack = entry.getValue();
//            if (callBack.asBinder().isBinderAlive()) {
//                try {
//                    callBack.onInitResult(result);
//                } catch (RemoteException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
    }

}
