package com.healthlink.hms.mvvm.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.healthlink.hms.core.data.repository.InitRepository
import com.healthlink.hms.core.model.dto.init.InitInfoDTO
import com.healthlink.hms.core.network.di.MainDispatcher
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class InitViewModel @Inject constructor(
    private val initRepository: InitRepository,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher
) : ViewModel() {

    // 定义UI状态
    sealed class InitUiState {
        object Loading : InitUiState()
        data class Success(val data: InitInfoDTO?) : InitUiState()
        data class Error(val message: String) : InitUiState()
    }

    private val _initState = MutableStateFlow<InitUiState>(InitUiState.Loading)
    val initState = _initState.asStateFlow()

    fun fetchInitInfo(param: Map<String, String>?) {
        viewModelScope.launch(mainDispatcher) {
            _initState.value = InitUiState.Loading

            initRepository.getInitInfo(param)
                .catch { e ->
                    _initState.value = InitUiState.Error(e.message ?: "Unknown error")
                }
                .collect { result ->
                    result.fold(
                        onSuccess = { response ->
                            _initState.value = InitUiState.Success(response.data)
                        },
                        onFailure = { e ->
                            _initState.value = InitUiState.Error(e.message ?: "Unknown error")
                        }
                    )
                }
        }
    }
}