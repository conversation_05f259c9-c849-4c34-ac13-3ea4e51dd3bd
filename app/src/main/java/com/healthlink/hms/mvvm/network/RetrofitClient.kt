package com.healthlink.hms.mvvm.network

import com.healthlink.hms.BuildConfig
import com.healthlink.hms.core.network.NetworkApi
import com.healthlink.hms.core.network.api.ApiServiceKot

/**
 * Created by imaginedays on 2024/7/19
 *
 *
 */
object RetrofitClient {
    private const val BASE_URL = BuildConfig.BASE_URL
    // 创建一个 OkHttpClient.Builder
    //        okHttpClientBuilder.addInterceptor(RequestInterceptor(NetworkRequiredInfo(HmsApplication.instance))
    var apiService: ApiServiceKot = NetworkApi.createService(ApiServiceKot::class.java)
//    private val retrofit: Retrofit = Retrofit.Builder()
//        .baseUrl(BASE_URL)
//        .client(okHttpClient)
//        .addConverterFactory(GsonConverterFactory.create())
//        .addCallAdapterFactory(CoroutineCallAdapterFactory())
//        .build()



//    val apiService: ApiServiceKot = retrofit.create(ApiServiceKot::class.java)
}