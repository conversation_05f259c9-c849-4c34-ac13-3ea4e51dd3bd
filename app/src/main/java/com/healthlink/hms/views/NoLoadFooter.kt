package com.healthlink.hms.views

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import android.widget.LinearLayout
import android.widget.Scroller
import com.scwang.smart.refresh.layout.api.RefreshFooter
import com.scwang.smart.refresh.layout.api.RefreshKernel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.constant.RefreshState
import com.scwang.smart.refresh.layout.constant.SpinnerStyle

 class NoLoadFooter(context: Context) : LinearLayout(context), RefreshFooter {
     private val scroller = Scroller(context)
    init {
        // 初始化自定义 Footer 布局（可以为空）
    }


    @SuppressLint("RestrictedApi")
    override fun onFinish(layout: RefreshLayout, success: Boolean): Int {
        return 0 // 不显示加载完成提示
    }

     @SuppressLint("RestrictedApi")
     override fun onHorizontalDrag(p0: Float, p1: Int, p2: Int) {

     }

     override fun isSupportHorizontalDrag(): Boolean {
       return  false
     }

     override fun autoOpen(p0: Int, p1: Float, p2: Boolean): Boolean {
         return  false
     }

     @SuppressLint("RestrictedApi")
     override fun setNoMoreData(noMoreData: Boolean): Boolean {
        return true // 保持默认行为
    }

     @SuppressLint("RestrictedApi")
     override fun onStateChanged(p0: RefreshLayout, p1: RefreshState, p2: RefreshState) {

     }

     override fun getView(): View {
        return this
    }

    @SuppressLint("RestrictedApi")
    override fun onInitialized(kernel: RefreshKernel, height: Int, extendHeight: Int) {
        // 可选：初始化时的额外处理
        kernel.refreshLayout.setEnableAutoLoadMore(false)
    }

     @SuppressLint("RestrictedApi")
     override fun onMoving(p0: Boolean, p1: Float, p2: Int, p3: Int, p4: Int) {

     }

     @SuppressLint("RestrictedApi")
     override fun onReleased(layout: RefreshLayout, height: Int, extendHeight: Int) {
        // 可选：当释放手势时的处理
         layout.finishLoadMoreWithNoMoreData()
    }

    override fun getSpinnerStyle(): SpinnerStyle {
        return SpinnerStyle.Translate // 选择 Footer 的样式
    }

     @SuppressLint("RestrictedApi")
     override fun setPrimaryColors(vararg p0: Int) {
     }

     @SuppressLint("RestrictedApi")
     override fun onStartAnimator(layout: RefreshLayout, height: Int, extendHeight: Int) {

    }


}

