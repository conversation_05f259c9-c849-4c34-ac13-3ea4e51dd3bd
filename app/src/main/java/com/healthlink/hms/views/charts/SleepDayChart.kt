package com.healthlink.hms.views.charts

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Point
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.TypedValue
import android.view.MotionEvent
import android.view.View
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import com.healthlink.hms.R
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import com.healthlink.hms.utils.ChartTouchEventDelegate
import com.healthlink.hms.utils.ViewDrawUtils
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

import kotlin.math.abs

class SleepDayChart(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    //屏幕宽高
    private var perDp = 0f //1dp所占的值
    private var scrWidth = 0f
    private var scrHeight = 0f
    private var xData: Array<String> = arrayOf("20:00", "02:00", "08:00", "14:00", "20:00")
    private var sleepsData: Sleep? = null
    private lateinit var paintLine: Paint
    private lateinit var paintGradientLine: Paint
    private lateinit var paintXText: Paint
    private lateinit var midStrPainText: Paint
    private lateinit var paintNoDataText: Paint
    private lateinit var paintSleep: Paint
    private lateinit var paintPillar: Paint
    private lateinit var paintRound: Paint
    private lateinit var paintBessel: Paint
    private var w=0
    private var h=0
    private var oldw=0
    private var oldh=0

    private var xSlider = 0f //滑块的x轴位置

    private var mPath: Path
    private val curveCircleRadius = 22f.dp

    // the coordinates of the first curve
    private val mFirstCurveStartPoint = Point()
    private val mFirstCurveEndPoint = Point()
    private val mFirstCurveControlPoint1 = Point()
    private val mFirstCurveControlPoint2 = Point()

    //the coordinates of the second curve
    private var mSecondCurveStartPoint = Point()
    private val mSecondCurveEndPoint = Point()
    private val mSecondCurveControlPoint1 = Point()
    private val mSecondCurveControlPoint2 = Point()


    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        mPath = Path()
        initPaint()
    }

    /**
     * 初始化画笔
     */
    private fun initPaint() {

        paintLine = Paint()
        paintLine.style = Paint.Style.STROKE
        paintLine.strokeWidth = 1f
        paintLine.color =
            resources.getColor(R.color.c_e6e6e6_2e2e2e)//context.colorCompat(R.color.e6e6e6_2e2e2e)

        paintGradientLine = Paint()
        paintGradientLine.style = Paint.Style.STROKE
        paintGradientLine.strokeWidth = 1f

        paintXText = Paint()
        paintXText.isAntiAlias = true
        paintXText.strokeWidth = 1f
        paintXText.textSize = 22f.sp
        paintXText.textAlign = Paint.Align.CENTER
        paintXText.color =
            resources.getColor(R.color.s_color_on_surface);//context.colorCompat(R.color.color_on_surface)

        paintNoDataText = Paint()
        paintNoDataText.isAntiAlias = true
        paintNoDataText.textSize = 26f.sp;//.sp
        paintNoDataText.textAlign = Paint.Align.CENTER
        paintNoDataText.color =
            resources.getColor(R.color.text_color_fc_60);//context.colorCompat(R.color.color_on_surface)


        paintSleep = Paint()
        paintSleep.style = Paint.Style.FILL
        paintSleep.isAntiAlias = true
        paintSleep.color =
            resources.getColor(R.color.c_blue_7fbeff);//context.colorCompat(R.color.blue_7fbeff)

        paintPillar = Paint()
        paintPillar.style = Paint.Style.FILL
        paintPillar.isAntiAlias = true
        paintPillar.color =
            resources.getColor(R.color.c_blue_7fbeff);//context.colorCompat(R.color.blue_7fbeff)

        paintRound = Paint()
        paintRound.style = Paint.Style.FILL
        paintRound.isAntiAlias = true
        paintRound.color =
            resources.getColor(R.color.slider_round);//context.colorCompat(R.color.ffffff_6e6e6e)

        paintBessel = Paint()
        paintBessel.style = Paint.Style.FILL
        paintBessel.isAntiAlias = true
        paintBessel.color =
            resources.getColor(R.color.slider_round_bessel_bg)
        // 日图 顶部中间文字
        midStrPainText = Paint()
        midStrPainText.isAntiAlias = true
        midStrPainText.strokeWidth = 1f
        midStrPainText.textSize = 22f.sp
        midStrPainText.textAlign = Paint.Align.CENTER
        midStrPainText.color =
            resources.getColor(R.color.text_color_fc_40)
    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        this.w=w
        this.h=h
        this.oldh=oldh
        this.oldw=oldw
        scrWidth = width.toFloat()
        scrHeight = height.toFloat()
        ySpacing = scrHeight / 9 //y轴分9份
        perDp = scrHeight / 450
        //底部圆滑块可以滑动的范围
        xWithStart = margin + paintXText.measureText("00/00") / 2
        xWithEnd = scrWidth - xWithStart
        xSlider = scrWidth / 2

        if (sleepsData == null) {
            xSpacing = (xWithEnd - xWithStart) / (xData.size - 1)
        } else {
            sleepsData?.let {
                xSpacing = (xWithEnd - xWithStart) / (it.total + 1) //时间段分割成分钟
            }
        }
    }

    private val touchEventDelegate by lazy {
        ChartTouchEventDelegate(
            setSliderX = {
                xSlider = it
                invalidate()
            }
        )
    }
    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return touchEventDelegate.onTouchEvent(
            event,
            this,
            xSlider,
            xSpacing,
            ySpacing,
            xWithStart,
            xWithEnd,
            sleepsData?.items?.size ?: 0,
            actionUp = { x, y ->
                when{
                    touchEventDelegate.isSlider -> false
                    Math.abs(x - touchEventDelegate.mDownX) < curveCircleRadius -> {
                        xSlider= x
                        invalidate()
                        true
                    }
                    else -> false
                }
            },
            bottomExtraSpace =40f,
        )
    }

    private val margin = 50f.dp//20f.dp //左右两边距离
    private var xWithStart = 0f //x轴的起始点
    private var xWithEnd = 0f  //x轴结束点
    private var ySpacing = 0f //高度分割份数后间距
    private var xSpacing = 0f //x轴分割份数后间距

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawGradientBackgroundUnderXText(canvas)
        //画柱子
        drawPillar(canvas)

        drawGradientLine(canvas)
        //底部
        drawBessel(canvas)
        //画x轴方向文字
        drawX(canvas)
    }

    private fun drawGradientBackgroundUnderXText(canvas: Canvas) {
        // 画渐变颜色
        ViewDrawUtils.drawGradientBackgroundUnderXText(canvas,context,scrWidth,ySpacing,8)
    }

    private fun drawX(canvas: Canvas) {
        paintXText.textSize = 22f.sp
        val s1 =
            "${context.getString(R.string.bed_time)} " + startTime
        val dis1 = abs(xWithStart + paintXText.measureText(s1) / 2 - xSlider)
        var y1 = ySpacing * 8 - 10f - 0.6 * ySpacing
        if (dis1 < curveCircleRadius * 3) {
            paintXText.typeface = Typeface.DEFAULT_BOLD
            var temp = 1 - dis1 / curveCircleRadius * 2
            if (temp < 0f || temp > 1f) {
                temp = 1f
            }
            //y1 -= 60f * temp
            paintXText.color = resources.getColor(R.color.text_color_fc_100)
        } else {
            paintXText.typeface = Typeface.DEFAULT
            paintXText.color = resources.getColor(R.color.text_color_fc_40)
        }

        canvas.drawText(s1, xWithStart + paintXText.measureText(s1) / 2, y1.toFloat(), paintXText)
        canvas.drawText(
            startDay,
            xWithStart + paintXText.measureText(startDay) / 2,
            (y1 - 30f.dp).toFloat(),
            paintXText
        )
        val s2 =
            "${context.getString(R.string.rise_time)}" + endTime
        val dis2 = abs(xWithEnd - paintXText.measureText(s2) / 2 - xSlider)
        var y2 = ySpacing * 8 - 10f - 0.6 * ySpacing
        if (dis2 < curveCircleRadius * 3) {
            paintXText.typeface = Typeface.DEFAULT_BOLD
            //y2 -= 60f * (1 - dis2 / (xSlider - curveCircleRadius * 3))
            paintXText.color = resources.getColor(R.color.text_color_fc_100)
        } else {
            paintXText.typeface = Typeface.DEFAULT
            paintXText.color = resources.getColor(R.color.text_color_fc_40)
        }

        canvas.drawText(
            s2,
            scrWidth - paintXText.measureText(s2) / 2 - margin,
            y2.toFloat(),
            paintXText
        )
        canvas.drawText(
            endDay,
            scrWidth - paintXText.measureText(endDay) / 2 - margin,
            (y2 - 30f.dp).toFloat(),
            paintXText
        )


    }

    private fun drawPillar(canvas: Canvas) {
        var top = 0f
        var bottom = 0f
        var preDuration = 0 //前一状态时长
        var duration = 0 //时间累加
        var tempTop = 1f
        var tempBottom: Float
        var startColor = 0
        var endColor = 0
        val colors = intArrayOf(startColor, endColor)
        var nextTop = 0f
        var preTop = 0f
        if (sleepsData == null) return
        sleepsData?.let {
            it.items.forEachIndexed { index, item ->
                var sleepTypeStr = ""
                // 计算当前图形的高、画笔颜色、阴影颜色、
                var rectShader = arrayOf(0)
                when (item.status) {
                    4 -> { //清醒
                        var color = Color.parseColor("#CCFF9933")
                        var bColor = Color.parseColor("#CCFF8033")
                        rectShader = arrayOf(color, color, bColor)
                        endColor = color
                        paintSleep.color = color
                        paintPillar.color = color
                        top = 1.4f
                        bottom = 2.2f
                        sleepTypeStr = "清醒"
                    }

                    3 -> { //深睡
                        var color = Color.parseColor("#CC6A3FE7")
                        var tColor = Color.parseColor("#CC9B36E2")
                        rectShader = arrayOf(tColor, color, color)
                        endColor = color
                        paintSleep.color = color
                        paintPillar.color = color
                        top = 5.3f
                        bottom = 6.1f
                        sleepTypeStr = "深睡"
                    }

                    2 -> { //快速眼动
                        var color = Color.parseColor("#CCFF6633")
                        var tColor = Color.parseColor("#CCFF8033")
                        var bColor = Color.parseColor("#CCE64988")
                        rectShader = arrayOf(tColor, color, bColor)
                        endColor = color
                        paintSleep.color = color
                        paintPillar.color = color
                        top = 2.7f
                        bottom = 3.5f
                        sleepTypeStr = "快速眼动"
                    }

                    1 -> { //浅睡
                        var color = Color.parseColor("#CCCC2CDD")
                        var tColor = Color.parseColor("#CCE64988")
                        var bColor = Color.parseColor("#CC9B36E2")
                        rectShader = arrayOf(tColor, color, bColor)
                        endColor = color
                        paintSleep.color = color
                        paintPillar.color = color
                        top = 4.0f
                        bottom = 4.8f
                        sleepTypeStr = "浅睡"
                    }
                }

                //计算下一个图形的高度，如果是当前为最后一组，则下一组图形的高度为0.
                if (index < it.items.size - 1) {
                    var nextItem = it.items[index + 1]
                    when (nextItem.status) {
                        4 -> { //清醒
                            nextTop = 1.5f
                        }

                        3 -> { // 深睡
                            nextTop = 5.4f
                        }

                        2 -> { //快速眼动
                            nextTop = 2.8f
                        }

                        1 -> { //浅睡
                            nextTop = 4.1f
                        }
                    }
                } else {
                    nextTop = 0f
                }


                //计算当前图形的矩形部分的左上位置和右下位置
                var x0 = xWithStart + xSpacing * duration - 1f
                var y0 = ySpacing * top
                var x1 = xWithStart + xSpacing * (duration + item.duration) + 1f
                var y1 = ySpacing * bottom
                var h = 60F
                var r = 15F


                val tLinearGradient = LinearGradient(
                    x1 - (x1 - x0) / 2,
                    y0 - r,
                    x1 - (x1 - x0) / 2,
                    y1 + r, rectShader.toIntArray(), null, Shader.TileMode.MIRROR
                )
                paintSleep.shader = tLinearGradient

                //画出当前图形的画笔路径（左侧只与前一个图形是否存在及高度有关；右侧只与下一个图形是否存在及高度有关）
                var path = Path()

                var shaderPath = Path()
                //记录阴影右上角Y值
                var shaderStartY = 0f

                //四分之一圆弧（左上角）
                //前置图形不存在，或者前置图形高度高于当前高度（在当前图形的下方）,则左上角为内圆弧，否则为外圆弧
                if (preTop == 0F || preTop > top) {
                    path.arcTo(x0, y0, x0 + 2 * r, y0 + 2 * r, 180F, 90F, false)
                } else {
                    path.arcTo(x0, y0 - 2 * r, x0 + 2 * r, y0, -180F, -90F, false)
                }
                //直线
                path.lineTo(x1 - r, y0)
                //四分之一圆弧（右上角）
                //下一个图形不存在，或者下一个图形高度高于当前高度（在当前图形的下方）,则右上角为内圆弧，否则为外圆弧
                if (nextTop == 0F || nextTop > top) {
                    path.arcTo(x1 - 2 * r, y0, x1, y0 + 2 * r, 270F, 90F, false)
                } else {
                    path.arcTo(x1 - 2 * r, y0 - 2 * r, x1, y0, 90F, -90F, false)
                }
                //直线
                path.lineTo(x1, y1 + r)
                shaderPath.moveTo(x0 + r, y1)
                shaderPath.lineTo(x1 - r, y1)
                //四分之一圆弧（右下角）!!!!
                //下一个图形不存在，或者下一个图形高度小于当前高度（在当前图形的上方）,则右下角为内圆弧，否则为外圆弧
                if (nextTop == 0F || nextTop < top) {
                    path.arcTo(x1 - 2 * r, y1 - 2 * r, x1, y1, 0F, 90F, false)
                    shaderStartY = y1 - r
                    shaderPath.moveTo(x1, shaderStartY)
                    shaderPath.arcTo(x1 - 2 * r, y1 - 2 * r, x1, y1, 0F, 90F, false)
                } else {
                    path.arcTo(x1 - 2 * r, y1, x1, y1 + 2 * r, 0F, -90F, false)
                    shaderStartY = y1 + r
                    shaderPath.moveTo(x1, shaderStartY)
                    shaderPath.arcTo(x1 - 2 * r, y1, x1, y1 + 2 * r, 0F, -90F, false)
                }
                //直线
                path.lineTo(x0 + r, y1)
                shaderPath.lineTo(x0 + r, y1)

                //四分之一弧（左下角）
                //前一个图形不存在，或者前一个图形高度小于当前高度（在当前图形的上方）,则左下角为内圆弧，否则为外圆弧
                if (preTop == 0F || preTop < top) {
                    path.arcTo(x0, y1 - 2 * r, x0 + 2 * r, y1, 90F, 90F, false)
                    shaderPath.arcTo(x0, y1 - 2 * r, x0 + 2 * r, y1, 90F, 90F, false)
                } else {
                    path.arcTo(x0, y1, x0 + 2 * r, y1 + 2 * r, -90F, -90F, false)
                    shaderPath.arcTo(x0, y1, x0 + 2 * r, y1 + 2 * r, -90F, -90F, false)
                }
                //直线
                path.lineTo(x0, y0 + r)
                path.close()

                shaderPath.lineTo(x0, ySpacing * 7)
                shaderPath.lineTo(x1, ySpacing * 7)
                shaderPath.lineTo(x1, shaderStartY)
                shaderPath.close()
                canvas.drawPath(path, paintSleep)


                // 画阴影
                if (xSlider < xWithStart + xSpacing * (duration + item.duration) && xSlider > xWithStart + xSpacing * duration) {
                    onDaySelectListener?.invoke(index, item)

                    val mLinearGradient = LinearGradient(
                        xSlider,
                        ySpacing * top + ySpacing / 2,
                        xSlider,
                        ySpacing * 7,
                        intArrayOf(
                            adjustAlpha(paintPillar.color, 120),
                            adjustAlpha(paintPillar.color, 0)
                        ), null, Shader.TileMode.MIRROR
                    )
                    paintPillar.shader = mLinearGradient

                    canvas.drawPath(shaderPath, paintPillar)
//                    canvas.drawRect(
//                        RectF(
//                            xWithStart + xSpacing * duration,
//                            ySpacing * top + ySpacing / 2,
//                            xWithStart + xSpacing * (duration + item.duration),
//                            ySpacing * 8
//                        ), paintPillar
//                    )
                    paintXText.textSize = 18f.sp
                    val midStr =
                        sleepTypeStr + " " + item.timeDuration + "分钟    " + getTimeStr(item.startTime) + " - " + getTimeStr(
                            item.endTime
                        )
                    canvas.drawText(midStr, ((scrWidth / 2)), ySpacing/2, midStrPainText)
                }


                if (index > 0 && index < it.items.size) {
                    if (tempTop < top) {
                        tempTop += 0.9f
                        tempBottom = bottom - 0.9f
                        colors[0] = startColor
                        colors[1] = endColor
                    } else {
                        tempBottom = tempTop + 0.1f
                        tempTop = bottom - 0.1f
                        colors[0] = endColor
                        colors[1] = startColor
                    }

                    val mLinearGradient = LinearGradient(
                        xWithStart + xSpacing * duration,
                        ySpacing * tempTop,
                        xWithStart + xSpacing * duration,
                        ySpacing * tempBottom, colors, null, Shader.TileMode.MIRROR
                    )
                    paintGradientLine.shader = mLinearGradient

                    canvas.drawLine(
                        xWithStart + xSpacing * duration,
                        ySpacing * tempTop,
                        xWithStart + xSpacing * duration,
                        ySpacing * tempBottom,
                        paintGradientLine
                    )
                }
                tempTop = top
                tempBottom = bottom
                preDuration = item.duration
                duration += item.duration
                startColor = endColor
                preTop = top
            }
        }
    }

    private fun drawBessel(canvas: Canvas) {
        // 第一条曲线开始点
        mFirstCurveStartPoint[(xSlider - curveCircleRadius * 3).toInt()] = (ySpacing * 8).toInt()
        // 第一条曲线结束点
        mFirstCurveEndPoint[xSlider.toInt()] =
            (ySpacing * 8 - curveCircleRadius - curveCircleRadius / 4).toInt()
        // 第二条开始点
        mSecondCurveStartPoint = mFirstCurveEndPoint
        mSecondCurveEndPoint[(xSlider + curveCircleRadius * 3).toInt()] = (ySpacing * 8).toInt()

        // 第一条控制点
        mFirstCurveControlPoint1[(mFirstCurveStartPoint.x + curveCircleRadius + curveCircleRadius / 4).toInt()] =
            mFirstCurveStartPoint.y
        mFirstCurveControlPoint2[(mFirstCurveEndPoint.x - curveCircleRadius * 2 + curveCircleRadius).toInt()] =
            mFirstCurveEndPoint.y
        // 第二条控制点
        mSecondCurveControlPoint1[(mSecondCurveStartPoint.x + curveCircleRadius * 2 - curveCircleRadius).toInt()] =
            mSecondCurveStartPoint.y
        mSecondCurveControlPoint2[(mSecondCurveEndPoint.x - curveCircleRadius - curveCircleRadius / 4).toInt()] =
            mSecondCurveEndPoint.y
        mPath.reset()
        mPath.moveTo(0f, ySpacing * 8)
        mPath.lineTo(mFirstCurveStartPoint.x.toFloat(), mFirstCurveStartPoint.y.toFloat())
        mPath.cubicTo(
            mFirstCurveControlPoint1.x.toFloat(), mFirstCurveControlPoint1.y.toFloat(),
            mFirstCurveControlPoint2.x.toFloat(), mFirstCurveControlPoint2.y.toFloat(),
            mFirstCurveEndPoint.x.toFloat(), mFirstCurveEndPoint.y.toFloat()
        )
        mPath.cubicTo(
            mSecondCurveControlPoint1.x.toFloat(), mSecondCurveControlPoint1.y.toFloat(),
            mSecondCurveControlPoint2.x.toFloat(), mSecondCurveControlPoint2.y.toFloat(),
            mSecondCurveEndPoint.x.toFloat(), mSecondCurveEndPoint.y.toFloat()
        )
        mPath.lineTo(scrWidth, ySpacing * 8)
        mPath.lineTo(scrWidth, scrHeight)
        mPath.lineTo(0f, scrHeight)
        mPath.close()

        //底部灰色
        canvas.drawPath(mPath, paintBessel)
        //底部滑块
        canvas.drawCircle(xSlider, ySpacing * 8 + 5f, curveCircleRadius, paintRound)
    }

    private var startDay = ""
    private var endDay = ""
    private var startTime = ""
    private var endTime = ""

    fun setValue(
        value: Sleep?,
        startDay: String,
        endDay: String,
        startTime: String,
        endTime: String
    ): SleepDayChart {
        this.startDay = startDay
        this.endDay = endDay
        this.startTime = startTime
        this.endTime = endTime
        this.sleepsData = value
        onSizeChanged(w, h, oldw, oldh)
        postInvalidate()
        return this
    }

    private fun drawGradientLine(canvas: Canvas) {
        if (sleepsData == null) {
            canvas.drawText(
                context.getString(R.string.no_sleep_data),
                scrWidth / 2f,
                ySpacing*9f/8f*3f,
                paintNoDataText
            )
        }
    }

    private var onDaySelectListener: ((index: Int, item: SleepItem) -> Unit)? = null

    fun setOnDaySelectListener(l: ((index: Int, item: SleepItem) -> Unit)): SleepDayChart {
        this.onDaySelectListener = l
        return this
    }

    fun adjustAlpha(color: Int, alpha: Int): Int {
        return Color.argb(alpha, color.red, color.green, color.blue)
    }

    fun getTimeStr(ori: String): String {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val dateTime = LocalDateTime.parse(ori, formatter)
        return String.format("%02d", dateTime.hour) + ":" + String.format("%02d", dateTime.minute)
    }
}