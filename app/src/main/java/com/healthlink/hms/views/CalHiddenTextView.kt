package com.healthlink.hms.views

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import kotlin.math.min

/**
 * @Author: 付仁秀
 * @Description：
 */
class CalHiddenTextView : AppCompatTextView {
    var hiddenInterface: HiddenInterface? = null

    constructor(context: Context?) : super(context!!)

    constructor(context: Context?, attrs: AttributeSet?) : super(
        context!!, attrs
    )

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context!!, attrs, defStyleAttr
    )

    override fun onDraw(canvas: Canvas) {
        val layout = layout
        if (layout != null) {
            val maxLines = maxLines // 获取最大显示行数
            val linesDisplayed = min(layout.lineCount.toDouble(), maxLines.toDouble())
                .toInt() // 实际显示的行数
            val endCharOfVisibleText = layout.getLineEnd(linesDisplayed - 1) // 最后一行的结束字符位置
            val fullText = text.toString() // 获取完整的文本内容
            if (endCharOfVisibleText < fullText.length) {
                val res = fullText.substring(endCharOfVisibleText)
                hiddenInterface!!.showHiddenText(res)
            }
        }
        super.onDraw(canvas)
    }

     fun setInterface(hInterface: HiddenInterface?) {
        hiddenInterface = hInterface
    }

    interface HiddenInterface {
        fun showHiddenText(str: String?)
    }
}