package com.healthlink.hms.views

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import com.healthlink.hms.R

/**
 *@Author: 付仁秀
 *@Description：
 **/
class TripAniView(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    private var width = 0
    private var height = 0
    private var sideLength = 0
    private var radius = 0f
    private var strokeL = 0f
    private var strokeS = 0f
    private var colorL = 0
    private var colorS = 0
    private var colorNoData = 0
    private var l = 0f
    private var s = 360f
    private var hasData = true
    private lateinit var paintL: Paint
    private lateinit var paintS: Paint
    private lateinit var paintN: Paint


    private fun initStyleData() {
        strokeL = sideLength * 0.08f
        strokeS = strokeL * 0.7f
        radius = (sideLength - strokeS) / 2
        colorS = resources.getColor(R.color.health_report_trip_normal)
        colorL = resources.getColor(R.color.health_report_trip_abnormal)
        colorNoData =resources.getColor(R.color.health_report_trip_no_data)
    }

    private fun initPaint() {
        paintL = Paint()
        paintL.style = Paint.Style.STROKE
        paintL.strokeWidth = strokeL
        paintL.color = colorL
        paintL.isAntiAlias = true
        paintL.strokeCap = Paint.Cap.ROUND
        paintS = Paint()
        paintS.style = Paint.Style.STROKE
        paintS.strokeWidth = strokeS
        paintS.color = colorS
        paintS.isAntiAlias = true
        paintS.strokeCap = Paint.Cap.ROUND
        paintN = Paint()
        paintN.style = Paint.Style.STROKE
        paintN.strokeWidth = strokeS
        paintN.color = colorNoData
        paintN.isAntiAlias = true
        paintN.strokeCap = Paint.Cap.ROUND
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        width = measuredWidth
        height = measuredHeight
        sideLength = width.coerceAtMost(height)
        initStyleData()
        initPaint()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        var left = (width - sideLength) / 2f + strokeL
        var top = (height - sideLength) / 2f + strokeL
        var right = left + sideLength - strokeL * 2f
        var bottom = top + sideLength - strokeL * 2f
        var rectF = RectF(left, top, right, bottom)
        if (hasData) {
            canvas.drawArc(rectF, -85f, l, false, paintL)
            canvas.drawArc(rectF, -95f, -s, false, paintS)
        }else{
            canvas.drawArc(rectF, 0f, 360f, false, paintN)
        }
    }

    fun setValue(value: Float, sum: Float) {
        hasData=true
        if (sum == 0f) {
            l = 0f
            s = 360f
            return
        }
        var temp = value / sum * 360f
        if (temp == 0f) {
            l = 0f
        } else if (temp <= 10f) {
            l = 1f
        } else if (temp >= 339f && temp < 360) {
            l = 339f
        } else if (temp == 360f) {
            l = 360f
        } else {
            l = temp - 10f
        }
        if (l == 0f) {
            s = 360f
        } else if (l == 339f) {
            s = 1f
        } else if (l == 360f) {
            s = 0f
        } else {
            s = 360f - l - 20f
        }
        postInvalidate()
    }

    fun drawNoDataStyle() {
        hasData=false
        postInvalidate()
    }

}