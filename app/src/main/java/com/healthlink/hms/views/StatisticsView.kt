package com.healthlink.hms.views

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Rect
import android.graphics.RectF
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import androidx.core.graphics.toRect
import com.healthlink.hms.R
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import kotlin.random.Random


/**
 *@Author: 付仁秀
 *@Description：
 **/
class StatisticsView(context: Context, attrs: AttributeSet?) : View(context, attrs) {

    private var vWidth = 0
    private var vHeight = 0
    private var upTextColor = 0
    private var downTextColor = 0
    private var baseLineColor = 0
    private var dataNormalColor = 0
    private var dataWarningColor = 0
    private var dataDangerColor = 0
    private var effectTextColor = 0
    private var invalidTextColor = 0
    private var baseTextPaint: Paint
    private var baseLinePaint: Paint
    private var columnPaint: Paint
    private var barWidth = 0f
    private var lineWidth = 0f
    private val xTexts = arrayOf("心率", "睡眠", "血氧", "压力", "体温", "血压")
    private var datas: ArrayList<StatisticsItem> = arrayListOf()
    private var popWindowListener: PopWindowListener? = null
    private var clickMap: MutableMap<String, ClickRectItem> = mutableMapOf()
    private val TAG = "STATISTICSVIEW"


    init {
        upTextColor = resources.getColor(R.color.health_report_chart_base_text_up)
        downTextColor = resources.getColor(R.color.health_report_chart_base_text_down)
        baseLineColor = resources.getColor(R.color.health_report_chart_base_line)
        dataNormalColor = resources.getColor(R.color.health_report_chart_base_text_up)
        dataWarningColor = resources.getColor(R.color.health_report_chart_warning)
        dataDangerColor = resources.getColor(R.color.health_report_chart_danger)
        effectTextColor = resources.getColor(R.color.health_report_chart_effective)
        invalidTextColor = resources.getColor(R.color.health_report_chart_invalid)

        barWidth = 40f.dp
        lineWidth = 1f.dp
        baseLinePaint = Paint()
        baseLinePaint.isAntiAlias = true
        baseLinePaint.strokeWidth = lineWidth
        baseLinePaint.color = baseLineColor
        baseLinePaint.style=Paint.Style.STROKE
        baseLinePaint.strokeCap=Paint.Cap.ROUND

        baseTextPaint = Paint()
        baseTextPaint.isAntiAlias = true
        baseTextPaint.textSize = 22f.sp
        baseTextPaint.color = upTextColor

        columnPaint = Paint()
        columnPaint.isAntiAlias = true
        columnPaint.style = Paint.Style.FILL
        columnPaint.strokeWidth = 0f

    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        vWidth = MeasureSpec.getSize(widthMeasureSpec)
        vHeight = MeasureSpec.getSize(heightMeasureSpec)
    }

    override fun onDraw(canvas: Canvas) {
        drawColumn(canvas)
        drawBase(canvas)
    }

    fun drawBase(canvas: Canvas) {
        //画线
        val lineY = (vHeight / 2).toFloat()
        canvas.drawLine(lineWidth/2f, lineY, vWidth.toFloat()- lineWidth/2f, lineY, baseLinePaint)
        //画文字
        baseTextPaint.textSize = 22f.sp
        baseTextPaint.color = upTextColor
        var text = "正常"
        val bounds = Rect()
        baseTextPaint.getTextBounds(text, 0, text.length, bounds)
        val textHeight = bounds.height()
        val baseLine = getTextBaseLine(text, baseTextPaint)
        var textY = lineY - 17f.dp - (textHeight - baseLine)
        canvas.drawText(text, 0f, textY, baseTextPaint)
        text = "异常"
        textY = lineY + 17f.dp + baseLine
        baseTextPaint.color = downTextColor
        canvas.drawText(text, 0f, textY, baseTextPaint)
    }

    fun drawColumn(canvas: Canvas) {
        val xStart = 78f.dp
        val divider = 76f.dp
        val middleY = vHeight / 2f
        var x0 = xStart
        var x1 = xStart + barWidth
        var y0 = 0f
        var y1 = middleY
        val perL = 1.5f.dp
        val radius = 8f.dp
        val topRadius = floatArrayOf(
            radius, radius,
            radius, radius,
            0f, 0f,
            0f, 0f,
        )
        val bottomRadius = floatArrayOf(
            0f, 0f,
            0f, 0f,
            radius, radius,
            radius, radius
        )
        val noRadius = floatArrayOf(
            0f, 0f,
            0f, 0f,
            0f, 0f,
            0f, 0f
        )
        if (datas.isNullOrEmpty()) return
        repeat(6) { index ->
            val normalLength = datas[index].normal
            val warningLength = datas[index].warning
            val dangerlength = datas[index].danger
            val isEffective = datas[index].isEffective
            val name = datas[index].name
            x0 = xStart + (divider + barWidth) * index
            x1 = x0 + barWidth
            val initialY0 = middleY - normalLength * perL
            y0 = initialY0
            y1 = middleY - 0.5f.dp
            columnPaint.color = adjustAlpha(dataNormalColor, isEffective)
            drawRoundRect(columnPaint, x0, y0, x1, y1, topRadius, canvas)
            //画下半部分
            if (dangerlength == 0 && warningLength > 0) {
                //仅警告
                columnPaint.color = adjustAlpha(dataWarningColor, isEffective)
                y0 = middleY + 0.5f.dp
                y1 = y0 + warningLength * perL
                drawRoundRect(columnPaint, x0, y0, x1, y1, bottomRadius, canvas)
            } else if (dangerlength > 0 && warningLength == 0) {
                columnPaint.color = adjustAlpha(dataDangerColor, isEffective)
                y0 = middleY + 1f.dp
                y1 = y0 + dangerlength * perL
                drawRoundRect(columnPaint, x0, y0, x1, y1, bottomRadius, canvas)
            } else if (dangerlength > 0 && warningLength > 0) {
                columnPaint.color = adjustAlpha(dataWarningColor, isEffective)
                y0 = middleY + 1f.dp
                y1 = y0 + warningLength * perL
                drawRoundRect(columnPaint, x0, y0, x1, y1, noRadius, canvas)
                columnPaint.color = adjustAlpha(dataDangerColor, isEffective)
                y0 = y1
                y1 = y0 + dangerlength * perL
                drawRoundRect(columnPaint, x0, y0, x1, y1, bottomRadius, canvas)
            }
            //画文字
            baseTextPaint.textSize = 16f.sp
            baseTextPaint.color = barTextColor(isEffective)
            val baseline = getTextBaseLine(xTexts[index], baseLinePaint)
            val textX = x0 + 4f.dp
            val textY = y1 + 12f.dp + baseline
            canvas.drawText(xTexts[index], textX, textY, baseTextPaint)
            val hotValue = 20f  //热区放大值
            val rect = RectF(x0 - hotValue, initialY0 - hotValue, x1 + hotValue, y1 + hotValue)
            val offsetY = initialY0 + (y1 - initialY0) / 2f
            val clickRectItem = ClickRectItem(isEffective, rect.toRect(), x1, offsetY)
            clickMap[xTexts[index]] = clickRectItem
        }
    }


    private fun getTextBaseLine(text: String, paint: Paint): Float {
        val fontMetrics = paint.getFontMetrics()
        val bounds = Rect()
        paint.getTextBounds(text, 0, text.length, bounds)
        val textHeight = bounds.height()
        val baseline = (textHeight - fontMetrics.bottom - fontMetrics.top) / 2
        return baseline
    }


    fun drawRoundRect(
        paint: Paint,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float,
        radii: FloatArray,
        canvas: Canvas
    ) {
        val rect = RectF(
            left,
            top,
            right,
            bottom
        )
        val path = Path().apply {
            addRoundRect(rect, radii, android.graphics.Path.Direction.CW)
        }
        canvas.drawPath(path, paint)
    }


    fun initDatas(): ArrayList<StatisticsItem> {
        val result: ArrayList<StatisticsItem> = arrayListOf()
        val names = arrayOf("心率", "血压", "血氧", "压力", "体温", "血压")
        repeat(6) {
            val num1 = Random.nextInt(0, 101)
            val num2 = Random.nextInt(0, 101 - num1)
            val num3 = 100 - num1 - num2
            result.add(StatisticsItem(names[it], true, num1, num2, num3))
        }
        return result
    }

    fun optimizeDataList(oriList: ArrayList<StatisticsItem>): ArrayList<StatisticsItem> {
        //优化最小值 使的极小值看起来不突兀  1 2 3 -> 4
        val result: ArrayList<StatisticsItem> = arrayListOf()
        repeat(6) {
            val a = oriList[it].normal
            val b = oriList[it].warning
            val c = oriList[it].danger
            //计算0的个数
            val temp = oriList[it]
            if (a == 100 || b == 100 || c == 100) {
                //两个0
                result.add(temp)
            } else if (a + b == 100) {
                if (a < 4) {
                    temp.normal = 4
                    temp.warning = 96
                } else if (b < 4) {
                    temp.normal = 96
                    temp.warning = 4
                }
                result.add(temp)
            } else if (b + c == 100) {
                if (b < 4) {
                    temp.warning = 4
                    temp.danger = 96
                } else if (c < 4) {
                    temp.warning = 96
                    temp.danger = 4
                }
                result.add(temp)
            } else if (a + c == 100) {
                if (a < 4) {
                    temp.normal = 4
                    temp.danger = 96
                } else if (c < 4) {
                    temp.normal = 96
                    temp.danger = 4
                }
                result.add(temp)
            } else {
                if (a < 4 && c < 4) {
                    temp.normal = 4
                    temp.warning = 92
                    temp.danger = 4
                } else if (a < 4) {
                    temp.normal = 4
                    if (b > c) {
                        temp.warning -= (4 - a)
                    } else if (b < c) {
                        temp.danger -= (4 - a)
                    } else {
                        temp.warning = 48
                        temp.danger = 48
                    }
                } else if (c < 4) {
                    temp.danger = 4
                    if (a > b) {
                        temp.normal -= (4 - c)
                    } else if (a < b) {
                        temp.warning -= (4 - c)
                    } else {
                        temp.normal = 48
                        temp.danger = 48
                    }
                }
                result.add(temp)
            }
        }
        return result
    }

    fun adjustAlpha(color: Int, isEffective: Boolean): Int {
        var alpha = 255
        if (!isEffective) alpha = 77 //30%
        return Color.argb(alpha, color.red, color.green, color.blue)
    }

    fun barTextColor(isEffective: Boolean): Int {
        return if (isEffective) effectTextColor
        else invalidTextColor
    }

    open fun setOnPopWindowListener(listener: PopWindowListener?) {
        this.popWindowListener = listener
    }

    open interface PopWindowListener {
        fun onHeartRateClick(
            clickable: Boolean,
            offsetX: Float,
            offsetY: Float,
            directionRight: Boolean
        )

        fun onSleepClick(
            clickable: Boolean,
            offsetX: Float,
            offsetY: Float,
            directionRight: Boolean
        )

        fun onSpo2Click(clickable: Boolean, offsetX: Float, offsetY: Float, directionRight: Boolean)
        fun onPressureClick(
            clickable: Boolean,
            offsetX: Float,
            offsetY: Float,
            directionRight: Boolean
        )

        fun onTempertureClick(
            clickable: Boolean,
            offsetX: Float,
            offsetY: Float,
            directionRight: Boolean
        )

        fun onBloodPressurClick(
            clickable: Boolean,
            offsetX: Float,
            offsetY: Float,
            directionRight: Boolean
        )
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event!!.action === MotionEvent.ACTION_DOWN) {

            clickMap["心率"]?.clickRect?.let { rect ->
                if (rect.contains(event!!.x.toInt(), event!!.y.toInt())) {
                    val clickItem = clickMap["心率"]!!
                    popWindowListener?.onHeartRateClick(
                        clickItem.clickable,
                        clickItem.offsetX,
                        clickItem.offsetY,
                        true
                    )
                    Log.d(TAG, "onTouchEvent: 心率")
                    return true
                }
            }

            clickMap["睡眠"]?.clickRect?.let { rect ->
                if (rect.contains(event!!.x.toInt(), event!!.y.toInt())) {
                    val clickItem = clickMap["睡眠"]!!
                    popWindowListener?.onSleepClick(
                        clickItem.clickable,
                        clickItem.offsetX,
                        clickItem.offsetY,
                        true
                    )
                    Log.d(TAG, "onTouchEvent: 睡眠")
                    return true
                }
            }

            clickMap["血氧"]?.clickRect?.let { rect ->
                if (rect.contains(event!!.x.toInt(), event!!.y.toInt())) {
                    val clickItem = clickMap["血氧"]!!
                    popWindowListener?.onSpo2Click(
                        clickItem.clickable,
                        clickItem.offsetX,
                        clickItem.offsetY,
                        true
                    )
                    Log.d(TAG, "onTouchEvent: 血氧")
                    return true
                }
            }

            clickMap["压力"]?.clickRect?.let { rect ->
                if (rect.contains(event!!.x.toInt(), event!!.y.toInt())) {
                    val clickItem = clickMap["压力"]!!
                    popWindowListener?.onPressureClick(
                        clickItem.clickable,
                        clickItem.offsetX,
                        clickItem.offsetY,
                        false
                    )
                    Log.d(TAG, "onTouchEvent: 压力")
                    return true
                }
            }

            clickMap["体温"]?.clickRect?.let { rect ->
                if (rect.contains(event!!.x.toInt(), event!!.y.toInt())) {
                    val clickItem = clickMap["体温"]!!
                    popWindowListener?.onTempertureClick(
                        clickItem.clickable,
                        clickItem.offsetX,
                        clickItem.offsetY,
                        false
                    )
                    Log.d(TAG, "onTouchEvent: 体温")
                    return true
                }
            }

            clickMap["血压"]?.clickRect?.let { rect ->
                if (rect.contains(event!!.x.toInt(), event!!.y.toInt())) {
                    val clickItem = clickMap["血压"]!!
                    popWindowListener?.onBloodPressurClick(
                        clickItem.clickable,
                        clickItem.offsetX,
                        clickItem.offsetY,
                        false
                    )
                    Log.d(TAG, "onTouchEvent: 血压")
                    return true
                }
            }
        }
        return super.onTouchEvent(event)
    }

    fun setValue(items: ArrayList<StatisticsItem>) {
        datas = optimizeDataList(items)
        invalidate()
    }

    data class ClickRectItem(
        val clickable: Boolean,
        val clickRect: Rect,
        val offsetX: Float,
        val offsetY: Float  // 柱子中间值  并非pop的Y  这个时候还不知道pop多高呢
    )


}