package com.healthlink.hms.views

import android.animation.Animator
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.SweepGradient
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import com.healthlink.hms.R
import com.healthlink.hms.ktExt.dp
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin


/**
 * 健康状态的动效实现View
 */
class HealthStatusGradientRingView : View {

    val TAG = "HealthStatusGradientRingView"

    var isDrawableAssist = false

    //颜色值
    private val colorsMap: MutableMap<String, Array<String>> = mutableMapOf(
        "normal" to arrayOf("#1FCB5A","#74FF93","#DCFFCE","#21CC5B","#C2FF91"),
        "low" to arrayOf("#67CCFF","#6DFF9C","#B9FFDC","#00C6E2","#BFF4FF"),
        "middle" to arrayOf("#EEAB0A","#FFD793","#FFF98E","#D59C09","#FAF167"),
        "high" to arrayOf("#FF1010","#FFB86D","#FFD281","#FB4321" ,"#FFD051" )
    )

    //分数值圆点颜色值
    private val scorePointShadowColorMap: MutableMap<String, Int> = mutableMapOf(
        "normal" to Color.parseColor("#80000000"),
        "low" to Color.parseColor("#80000000"),
        "middle" to Color.parseColor("#4D21273D"),
        "high" to Color.parseColor("#4D21273D")
    )

    //角度
    private val ringAngles : MutableMap<String, Float> = mutableMapOf<String,Float>(
        "normal" to 0F,
        "low" to 270F,
        "middle" to 180F,
        "high" to 90F
    )
    //分数段
    private var scoresMap: MutableMap<String,Array<Int>> = mutableMapOf(
        "normal" to arrayOf(100,90),
        "low" to arrayOf(90,75),
        "middle" to arrayOf(75,60),
        "high" to arrayOf(60,45)// TOOD 45-60
    )
    // 建议图标
    private var iconMap: MutableMap<String,Int> = mutableMapOf(
        "normal" to R.drawable.health_status_icon_normal,
        "low" to R.drawable.health_status_icon_low,
        "middle" to R.drawable.health_status_icon_middle,
        "high" to R.drawable.health_status_icon_high
    )

    val originalWidth = 336F
    val originalHeight = 300F
    var targetWidth = (width - 16.dp).toInt()
    var targetHeight = (height - 16.dp).toInt()
    var targetScale = targetWidth / originalWidth


    // 分数值大小 TODO 适配计算
    private var scoreFontSize = 60F
    private var scoreFontColor = R.color.text_color_fc_100

    //初始等级
    private var nextHealtStatusCode = "normal"
    private var currentHealtStatusCode = "normal"

    private var oAnimator: ObjectAnimator = ObjectAnimator.ofFloat(this, "animationTime", 2500F)

    private var paintCurrentCircle: Paint? = null
    // 圆环的宽度 TODO 适配计算
    private val strokeWidth = (10F*2)
    // 圆环的角度
    private var sweepAngle = 360f
    // 动画时间/进度
    private var animationTime = 0F

//    private var currentStatusColors:Array<String> = arrayOf()
//    private var nextStatusColors:Array<String> = arrayOf()

    //圆环旋转的角度（默认450）
    private var ringRotateTotalAngle = 450F
    // 加载游标（四分之一圆环）：开始旋转的角度
    private var ringStartAngle = 90F
    // 加载游标（四分之一圆环）的旋过程中的角度
    private var currentRingAngle = 0f
    //游标的目标角度（根据Level值决定）
    private var ringLevelAngle = 0f
    // 加载游标（四分之一圆环）的颜色渐变进度值
    private var ringColorTransformProgress = 1F
    // 加载游标的开始颜色
    private var currentRingStartColor = Color.parseColor("#21CC5B")
    // 加载游标的结束颜色
    private var currentRingEndColor = Color.parseColor("#C2FF91")
    // 加载圆环的渐变色进度值
    private var currentCircleTransformProgress = 1F
    // 加载圆环的渐变色数组（默认良好）
    private var currentCircleGradientColors = intArrayOf(
        Color.parseColor("#1FCB5A"),
        Color.parseColor("#74FF93"),
        Color.parseColor("#DCFFCE"),
        Color.parseColor("#1FCB5A"),
    )

    // 下一健康状态游标的渐变色数组
    private var nextCircleGradientColors = intArrayOf(
        Color.parseColor("#B9FFDC"),
        Color.parseColor("#6DFF9C"),
        Color.parseColor("#67CCFF"),
        Color.parseColor("#B9FFDC")
    )

    // 下一健康状态游标的开始颜色
    private var nextHealthStatusRingStartColor = Color.parseColor("#BFF4FF")
    // 下一健康状态游标的结束颜色
    private var nextHealthStatusRingEndColor = Color.parseColor("#00C6E2")
    // 健康前评分
    private var nextScore = 100F
    // 健康评分
    private var currentScore = 100F
    // 健康评分（展示用）
    private var showingScore = 100F
    // 得分占分数阶段的百分（分阶段的进度，每个得分阶段不一样）
    private var nextHealthStatusPointPercent = 1F
    // 得分的进度
    private var nextHealthStatusPointProgress = 1F
    // 健康评分的圆点的初始角度
    private var pointCircleStartAngle = 0F

    // 健康状态图标
    private lateinit var currentIconBitmap: Bitmap
    private lateinit var nextIconBitmap: Bitmap
    private lateinit var iconBitmap: Bitmap
    private var iconBitmapAlpha = 1F

    // 健康状态晃动的X
    private var nextHealthStatusIconShakingDeltaX = 0F
    // 健康状态晃动的X
    private var icomBitmapRotationAngle = 0F

    constructor(context: Context?) : super(context) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init()
    }

    private fun init() {
        paintCurrentCircle = Paint()
        paintCurrentCircle!!.isAntiAlias = true
        paintCurrentCircle!!.style = Paint.Style.STROKE
        paintCurrentCircle!!.strokeWidth = strokeWidth

        // 加载图片
        setIconBitmap(R.drawable.health_status_icon_normal)
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        this.targetWidth = (width - 8.dp).toInt()
        this.targetHeight = (height - 8.dp).toInt()
        this.targetScale = targetWidth / originalWidth

        //画加载圆环
        drawCircle(canvas)
        // 画加载游标
        drawRing(canvas)
        //画评分点
        drawPointCircle(canvas)
        //画分数
        drawScoreText(canvas)
        // 画状态图标
        drawHealtStatusIcon(canvas);

        drawAssisLine(canvas)
    }

    private fun drawAssisLine(canvas:Canvas) {
        if(isDrawableAssist) {
            var alPaint = Paint()
            alPaint.color = Color.RED
            canvas.drawLine(0F,height/2F,width/1F,height/2F,alPaint)
            canvas.drawLine(width/2F,0F,width/2F,height/1F,alPaint)
        }
    }

    /**
     * 画健康状态提示的图标
     */
    private fun drawHealtStatusIcon(canvas: Canvas) {
        if(nextHealtStatusCode == "middle" || nextHealtStatusCode == "high"){
            doShakingDraw(canvas)
        }else {
            doRotatingDraw(canvas)
        }
    }

    fun doShakingDraw(canvas:Canvas){
        val src = Rect(0, 0, iconBitmap.width, iconBitmap.height)
        var bitmapWidth = 64F * targetScale
        var bitmapHeight = 64F * targetScale
        val dst = Rect(
            targetWidth/2 - bitmapWidth.toInt()/2 + this.nextHealthStatusIconShakingDeltaX.toInt(),
            (height+8.dp).toInt()/3 - bitmapHeight.toInt()/2,
            width/2 + bitmapWidth.toInt()/2 +  this.nextHealthStatusIconShakingDeltaX.toInt(),
            (height+8.dp).toInt()/3 +  bitmapHeight.toInt()/2)

        var p = Paint()
        p.alpha = (iconBitmapAlpha * 255).toInt()
        canvas.drawBitmap(this.iconBitmap, src, dst,p)
    }

    fun doRotatingDraw(canvas:Canvas){
        // Calculate pivot point for rotation
        canvas.save()
        val pivotX = width / 2 - 18F.dp
        val pivotY = height / 4 + 37F.dp

        var bitmapWidth = 64F * targetScale
        var bitmapHeight = 64F * targetScale

        canvas.rotate(icomBitmapRotationAngle , pivotX, pivotY)
        val src = Rect(0, 0, iconBitmap.width, iconBitmap.height)
        val dst = Rect(
            width / 2 -  bitmapWidth.toInt()/2,
            (height+8.dp).toInt() / 3 - bitmapHeight.toInt()/2,
            width / 2 +  bitmapWidth.toInt()/2,
            (height+8.dp).toInt() / 3 + bitmapHeight.toInt()/2
        )

        var p = Paint()
        p.alpha = (iconBitmapAlpha * 255).toInt()
        canvas.drawBitmap(this.iconBitmap, src, dst, p)
        canvas.restore()
    }

    /**
     * 画分数
     */
    private fun drawScoreText(canvas: Canvas) {
        //分值画笔
        var paint = Paint()
        paint.color = resources.getColor(scoreFontColor)
        paint.textSize = scoreFontSize * targetScale
        paint.isAntiAlias = true
        paint.setTypeface(Typeface.create(Typeface.DEFAULT_BOLD,700,false))
        //单位画笔
        var paintUnit = Paint(paint)
        paintUnit.textSize = scoreFontSize * targetScale * 0.8F
        //计算宽度
        var scoreText = "${this.showingScore.toInt()}"
        var scoreTextWidth = paint.measureText(scoreText)
        var scoreUnitWidth = paintUnit.measureText("分")
        //画分数
        var x = (width/2)+8.dp/2 - (scoreTextWidth+scoreUnitWidth)/2
        var y = ((height+8.dp)*3/4 - paint.textSize/2)
        canvas.drawText(scoreText, x, y, paint)
        //画单位
        x += scoreTextWidth
        canvas.drawText("分",x, y , paintUnit)
    }

    /**
     * 画评分点
     */
    private fun drawPointCircle(canvas: Canvas){
        var scale = width / originalWidth
        var strokeWidthUse = strokeWidth * scale

        var paintRing = Paint()
        paintRing.style = Paint.Style.STROKE
        paintRing.strokeWidth = strokeWidthUse * 1.2F
        paintRing.isAntiAlias = true
        paintRing.strokeCap = Paint.Cap.ROUND
        paintRing.color = Color.WHITE

        val ring = RectF(
            strokeWidthUse + 8.dp,
            strokeWidthUse + 8.dp,
            width - 8.dp- strokeWidthUse,
            height - 8.dp - strokeWidthUse
        )

        paintCurrentCircle!!.strokeCap = Paint.Cap.ROUND

        calcNextHealthStatusPointPercent()

        // 因画圆角引入的角度差
        var deltaAngle = 360F * (strokeWidthUse * 2)/(Math.PI*(width-16.dp))
        // 应分配给分数段的角度(总角度 - 减去2个圆角占据的角度）
        var totalPointAngle = 90f - deltaAngle.toFloat()
        // 分数指示圆点的开始位置（分数段内满分位置）
        var pointCircleInitAngle = this.pointCircleStartAngle + 90f  - deltaAngle.toFloat()/2
        // 分数指示圆点的偏移量
        var pointCircleDeltaAngle = totalPointAngle * (1 - nextHealthStatusPointPercent)
        // 分数指示圆点的实际角度
        var pointCircleAngle = pointCircleInitAngle - pointCircleDeltaAngle * nextHealthStatusPointProgress

        var centerX = ring.centerX()
        var centerY = ring.centerY()
        var ringR = ring.width() / 2
        var pointCenterX = centerX + ringR * cos(pointCircleAngle * PI /180)
        var pointCenterY = centerY + ringR * sin(pointCircleAngle * PI /180)

        // 纯黑30%透明度，Y轴2px，模糊4px
        var shadowPaint = Paint()
        var shadowColor = scorePointShadowColorMap[this.nextHealtStatusCode]
        if(shadowColor!=null) {
            shadowPaint.color = Color.parseColor("#00000000")
            shadowPaint!!.setShadowLayer(8F,0F,0F,shadowColor)
        }
        shadowPaint.strokeWidth = paintRing.strokeWidth
        shadowPaint!!.isAntiAlias = true
        shadowPaint!!.style = Paint.Style.STROKE
        canvas.drawCircle(pointCenterX.toFloat(),pointCenterY.toFloat(),strokeWidth*0.3F / 2,shadowPaint)

        canvas.drawArc(ring, pointCircleAngle ,0.1F, false, paintRing!!)

//        Log.v(TAG, "piont circle angle : $pointCircleAngle")
    }

    private fun calcNextHealthStatusPointPercent() {
        var scores: Array<Int>? = scoresMap.get(nextHealtStatusCode)
        var deltaPoint: Int = 0
        if (scores != null) {
            deltaPoint = scores[0] - scores[1];
            this.nextHealthStatusPointPercent = ((nextScore - scores[1]) / deltaPoint)
        } else {
            this.nextHealthStatusPointPercent = 0F
        }
    }

    /**
     * 画加载的基础圆环
     */
    private fun drawCircle(canvas: Canvas){
        // 获取视图的宽高
        val width = width - 8.dp
        val height = height - 8.dp
        var scale = width / originalWidth
        var strokeWidthUse = strokeWidth * scale
        paintCurrentCircle?.strokeWidth = strokeWidthUse
        val radius = Math.min(width, height) / 2 - strokeWidthUse

        val positions = floatArrayOf(
            0f,
            0.23f,
            0.79f,
            1f
        )

        //设置渐变色
        var currentCircleGradientColorsTemp = currentCircleGradientColors.clone()
        for(i in currentCircleGradientColorsTemp.indices){
            currentCircleGradientColorsTemp[i] = interpolateColor(currentCircleGradientColors[i],nextCircleGradientColors[i],1-currentCircleTransformProgress )
        }

        // 创建渐变颜色
        val gradient = SweepGradient(width / 2f, height / 2f, currentCircleGradientColorsTemp, positions)
        val matrix =Matrix ()
        matrix.setRotate(90f, width / 2f, height / 2f) //后两个参数是圆心
        gradient!!.setLocalMatrix(matrix)
        paintCurrentCircle!!.shader = gradient

        val oval = RectF(
            strokeWidthUse +8.dp,
            strokeWidthUse +8.dp,
            width - strokeWidthUse,
            height - strokeWidthUse
        )

        // 纯黑30%透明度，Y轴2px，模糊4px
        var shadowPaint = Paint()
        shadowPaint.color = Color.argb(77,0,0,0)
        shadowPaint.strokeWidth = strokeWidthUse
        shadowPaint!!.isAntiAlias = true
        shadowPaint!!.style = Paint.Style.STROKE
        //shadowPaint!!.maskFilter = BlurMaskFilter(2F, BlurMaskFilter.Blur.NORMAL)
        shadowPaint!!.setShadowLayer(5F,0F,3F,Color.argb(77,0,0,0))
        canvas.drawArc(oval, 90f , sweepAngle, false, shadowPaint!!)

        //绘制圆环
        canvas.drawArc(oval, 90f , sweepAngle, false, paintCurrentCircle!!)

        if(isDrawableAssist) {
            var alPaint = Paint()
            alPaint.color = Color.RED
            canvas.drawLine(0F, height / 2F, width / 1F, height / 2F, alPaint)
            canvas.drawLine(width / 2F, 0F, width / 2F, height / 1F, alPaint)
        }
    }

    /**
     * 画加载游标：旋转的四分之一圆环
     */
    private fun drawRing(canvas: Canvas){
        var scale = width / originalWidth
        var strokeWidthUse = strokeWidth * scale * 2F

        var paintRing = Paint()
        paintRing.style = Paint.Style.STROKE
        paintRing.strokeWidth = strokeWidthUse
        paintRing.isAntiAlias = true
        paintRing.strokeCap = Paint.Cap.ROUND

        var positions = floatArrayOf(0f,0.26f,0.26f,0.51f,0.51f,0.76f,0.76f,1f,0f)


        var startColor = getAlphaColor(this.currentRingStartColor,this.ringColorTransformProgress)
        var endColor = getAlphaColor(this.currentRingEndColor,this.ringColorTransformProgress)

        startColor = interpolateColor(this.currentRingStartColor,this.nextHealthStatusRingStartColor,1-ringColorTransformProgress)
        endColor = interpolateColor(this.currentRingEndColor,this.nextHealthStatusRingEndColor,1-ringColorTransformProgress)
        var gradientColorsRing = intArrayOf(startColor, endColor,-1,-1,-1,-1,-1,-1,-1) // 渐变色数组

        canvas.save()
        canvas.rotate(currentRingAngle , (width) / 2f, (height) / 2f)
        val gradientRing = SweepGradient((width) / 2f, (height) / 2f, gradientColorsRing, positions)
        paintRing!!.shader = gradientRing

        val ring = RectF(
            strokeWidthUse/2 + 8.dp,
            strokeWidthUse/2 + 8.dp,
            width - 8.dp - strokeWidthUse/2,
            height - 8.dp - strokeWidthUse/2
        )
        var deltaAngle = 360F * (strokeWidthUse)/(Math.PI*(width-16.dp))

        // 纯黑30%透明度，Y轴2px，模糊4px
        var shadowPaint = Paint()
        shadowPaint.color = Color.argb(77,0,0,0)
        shadowPaint.strokeWidth = strokeWidthUse
        shadowPaint!!.isAntiAlias = true
        shadowPaint!!.style = Paint.Style.STROKE
        shadowPaint!!.setShadowLayer(0F,0F,0F,Color.argb(77,0,0,0))

        canvas.drawArc(ring
            , 0F +  0.5F * deltaAngle.toFloat() + 1
            ,90f - 2F * 0.5F * deltaAngle.toFloat() - 1
            , false, shadowPaint!!)

        canvas.drawArc(ring
            , 0F + 0.5F * deltaAngle.toFloat()+ 1
            ,90f - 2F * 0.5F * deltaAngle.toFloat()- 1
            , false, paintRing!!)
        canvas.restore()

//        Log.v(TAG, "ring start angle : $currentRingAngle")
    }

    // 设置渐变色
    fun setGradientColors(colors: IntArray) {
        currentCircleGradientColors = colors
        invalidate() // 刷新视图
    }

    // 设置圆环的角度
    fun setSweepAngle(angle: Float) {
        sweepAngle = angle
        invalidate() // 刷新视图
    }

    fun resetNextHealthStatusRingAngle(healthStatus:String){
        this.ringStartAngle = ringAngles.get(healthStatus)!! //this.currentRingAngle % 360

        //颜色拷贝
        this.currentCircleGradientColors = this.nextCircleGradientColors.clone()
        this.currentRingStartColor = this.nextHealthStatusRingStartColor
        this.currentRingEndColor = this.nextHealthStatusRingEndColor
        //分数值
        this.currentScore = nextScore

    }

    fun setAnimationTime(t: Float){

        var scale = width / originalWidth
        // 前后圆角占据的角度
        var deltaAngle = 360F * (strokeWidth * 2F * scale)/(Math.PI*(width-16.dp)) / 2

        this.animationTime = t
        if(t < 1500){
            this.currentCircleTransformProgress = 1F

            this.ringColorTransformProgress = 1F
            this.currentRingAngle = (ringStartAngle +t/1500 * (this.ringRotateTotalAngle + deltaAngle*1F).toFloat())%360


            this.nextHealthStatusPointPercent = 0F
            this.nextHealthStatusPointProgress = 0F
            this.pointCircleStartAngle = this.currentRingAngle

            this.nextHealthStatusIconShakingDeltaX = 0F
        }
        else if(t > 1500 && t< 1700){
            //TODO 图标消失：缩放 50%、透明度 100 -> 0
            var progress = (t-1500)/200F
            this.iconBitmapAlpha = progress
            this.currentRingAngle = ringStartAngle
        }
        else if(t > 1700){
            var progress = (t - 1700 )/800F
            var fadeInAlpha = (t - 1700 )/800F
            var fadeOutAlpha = 1 - fadeInAlpha

            // 图标动画时间段
            setIconBitmap(iconMap.get(this.nextHealtStatusCode)!!)
            if(t < 2250){
                this.iconBitmapAlpha = 1F
            }else{
                this.iconBitmapAlpha = 1F
            }

            this.currentCircleTransformProgress = fadeOutAlpha

            this.ringColorTransformProgress = fadeOutAlpha
            this.currentRingAngle = ringStartAngle


            this.pointCircleStartAngle = this.currentRingAngle

            this.nextHealthStatusPointProgress = progress
            this.showingScore = this.currentScore + (this.nextScore - this.currentScore)*progress

//            var r = DecelerateInterpolator()

            this.nextHealthStatusIconShakingDeltaX = (progress * 80) % 15* (1-progress) - 5 * (1-progress)

            this.icomBitmapRotationAngle = (-10F * Math.abs(Math.cos(progress.toDouble()*2*Math.PI))).toFloat() + (progress*2)/2*10

        }else{
            nextHealthStatusPointProgress = 1F
        }
//        Log.d(TAG,"ringStartAngle=$ringStartAngle,currentRingAngle=$currentRingAngle,pointCircleStartAngle=$pointCircleStartAngle")
        invalidate()
    }

    /**
     * 根据颜色获取进程中的渐变色
     */
    private fun interpolateColor(color1: Int, color2: Int, ratio: Float): Int {
        val r1 = Color.red(color1)
        val g1 = Color.green(color1)
        val b1 = Color.blue(color1)

        val r2 = Color.red(color2)
        val g2 = Color.green(color2)
        val b2 = Color.blue(color2)

        val r = (r1 * (1 - ratio) + r2 * ratio).toInt()
        val g = (g1 * (1 - ratio) + g2 * ratio).toInt()
        val b = (b1 * (1 - ratio) + b2 * ratio).toInt()

        return Color.rgb(r, g, b)
    }

    /**
     * 获取指定颜色的指定透明度的颜色值
     */
    private fun getAlphaColor(color: Int , newAlpha:Float): Int{
        var red = Color.red(color).toFloat()
        var green = Color.green(color).toFloat()
        var blue = Color.blue(color).toFloat()

        return Color.argb(newAlpha, red/255, green/255, blue/255);
    }

    private fun setIconBitmap(drawableId: Int){
        val bitmap = BitmapFactory.decodeResource(resources, drawableId)
        this.iconBitmap = bitmap
    }

    fun setCurrentStatusColors(colors: Array<String>){
//        this.currentStatusColors = colors

        this.currentCircleGradientColors[0] = Color.parseColor(colors[0])
        this.currentCircleGradientColors[1] = Color.parseColor(colors[1])
        this.currentCircleGradientColors[2] = Color.parseColor(colors[2])
        this.currentCircleGradientColors[3] = Color.parseColor(colors[0])
        this.currentRingStartColor = Color.parseColor(colors[3])
        this.currentRingEndColor = Color.parseColor(colors[4])
    }

    private fun setNextStatusColors(colors: Array<String>){
//        this.nextStatusColors = colors
        this.nextCircleGradientColors[0] = Color.parseColor(colors[0])
        this.nextCircleGradientColors[1] = Color.parseColor(colors[1])
        this.nextCircleGradientColors[2] = Color.parseColor(colors[2])
        this.nextCircleGradientColors[3] = Color.parseColor(colors[0])
        this.nextHealthStatusRingStartColor = Color.parseColor(colors[3])
        this.nextHealthStatusRingEndColor = Color.parseColor(colors[4])
    }

    private fun setRingLevelAngle(ringLevelAngle:Float){
        this.ringLevelAngle = ringLevelAngle

        if(this.ringStartAngle <= ringLevelAngle ){
            this.ringRotateTotalAngle = 360F + ringLevelAngle - ringStartAngle
        }else{
            this.ringRotateTotalAngle = 360F + (360F - (ringStartAngle - ringLevelAngle))
        }
    }

    fun setHealthStatus(fromStatus:String,toStatus:String,score: Int){

        Log.d("HealthStatusGradientRingView", "setHealthStatus is invoked." )

        this.nextHealtStatusCode = toStatus
        this.ringStartAngle = ringAngles.get(toStatus)!!

        synchronized(oAnimator) {
            this.nextScore = score.toFloat()
            setNextStatusColors(colorsMap.get(toStatus)!!)
            setRingLevelAngle(ringAngles.get(toStatus)!!)
//            setIconBitmap(iconMap.get(toStatus)!!)

            oAnimator.duration = 2500
            oAnimator.repeatCount = 0
            oAnimator.interpolator = AccelerateDecelerateInterpolator()

            oAnimator.addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {
                    resetNextHealthStatusRingAngle(toStatus)

                }

                override fun onAnimationCancel(animation: Animator) {}

                override fun onAnimationRepeat(animation: Animator) {}

            })

            oAnimator.start()
        }
    }
}
