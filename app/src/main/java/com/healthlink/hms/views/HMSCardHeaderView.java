package com.healthlink.hms.views;

import android.content.Context;
import android.graphics.drawable.Animatable2;
import android.graphics.drawable.AnimatedVectorDrawable;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.github.penfeizhou.animation.apng.APNGDrawable;
import com.github.penfeizhou.animation.loader.ResourceStreamLoader;
import com.healthlink.hms.R;
import com.scwang.smart.refresh.layout.api.RefreshHeader;
import com.scwang.smart.refresh.layout.simple.SimpleComponent;

/**
 * Created by imaginedays on 2024/5/30
 */
public class HMSCardHeaderView extends SimpleComponent implements RefreshHeader {
    private AnimatedVectorDrawable drawable;
    private Animatable2.AnimationCallback animationCallback;

    public HMSCardHeaderView(@NonNull Context context) {
        this(context, null);
    }

    public HMSCardHeaderView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public HMSCardHeaderView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        View view = LayoutInflater.from(context).inflate(R.layout.hms_card_refresh_head_view, this);
        // 使用APNG动画
        ImageView imageView = view.findViewById(R.id.iv_loading_amin);
        imageView.setImageResource(R.drawable.loading_80x80);
        drawable = (AnimatedVectorDrawable) imageView.getDrawable();

        // 定义并注册动画回调
        animationCallback = new Animatable2.AnimationCallback() {
            @Override
            public void onAnimationEnd(Drawable drawable) {
                super.onAnimationEnd(drawable);
                ((AnimatedVectorDrawable) drawable).start();  // 循环播放动画
            }
        };

        if (drawable != null) {
            drawable.registerAnimationCallback(animationCallback);
            drawable.start();
        }
    }
        @Override
        protected void onDetachedFromWindow() {
            super.onDetachedFromWindow();
            // 停止动画并移除回调，防止内存泄漏
            if (drawable != null && animationCallback != null) {
                drawable.unregisterAnimationCallback(animationCallback);
                drawable.stop();
            }
        }
}
