package com.healthlink.hms.views.charts

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Point
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.Typeface
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import com.healthlink.hms.utils.ChartTouchEventDelegate
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.ViewDrawUtils

import kotlin.math.abs


/**
 *@Author：付仁秀
 *@Description：
 **/
class SleepBarChart(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    private val TAG = "SleepBarChart"

    //屏幕宽高
    private var perDp = 0f //1dp所占的值
    private var scrWidth = 0f
    private var scrHeight = 0f
    private var xData = ArrayList<String>()
    private var type: Int = 0
    private var sleepData: SleepDataType? = null
    private var xCount = 30
    private var barWidth: Float = 20f.dp
    private var yP: Float = 0f
    private var w = 0
    private var h = 0
    private var oldw = 0
    private var oldh = 0

    private var deepC: Int = ContextCompat.getColor(context, R.color.sleep_deep_color)
    private var lightC: Int = ContextCompat.getColor(context, R.color.sleep_light_color)
    private var dreamC: Int = ContextCompat.getColor(context, R.color.sleep_dream_color)
    private var oddmentsC: Int = ContextCompat.getColor(context, R.color.sleep_oddments_color)
    private var sleepC: Int = ContextCompat.getColor(context, R.color.sleep_sleep_color)


    private lateinit var paintLine: Paint
    private lateinit var paintGradientLine: Paint
    private lateinit var paintXText: Paint
    private lateinit var paintSleep: Paint
    private lateinit var paintPillar: Paint
    private lateinit var paintRound: Paint
    private lateinit var paintBessel: Paint
    private lateinit var paintYText: Paint // y轴坐标
    private lateinit var paintNoDataText: Paint


    private lateinit var paintBar: Paint

    private var xSlider = 0f //滑块的x轴位置

    private var mPath: Path
    private val curveCircleRadius = 22f.dp

    // the coordinates of the first curve
    private val mFirstCurveStartPoint = Point()
    private val mFirstCurveEndPoint = Point()
    private val mFirstCurveControlPoint1 = Point()
    private val mFirstCurveControlPoint2 = Point()

    //the coordinates of the second curve
    private var mSecondCurveStartPoint = Point()
    private val mSecondCurveEndPoint = Point()
    private val mSecondCurveControlPoint1 = Point()
    private val mSecondCurveControlPoint2 = Point()


    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        mPath = Path()
        initPaint()

    }

    /**
     * 初始化画笔
     */
    private fun initPaint() {

        paintLine = Paint()
        paintLine.style = Paint.Style.STROKE
        paintLine.strokeWidth = 1f
        paintLine.color =
            resources.getColor(R.color.c_e6e6e6_2e2e2e)//context.colorCompat(R.color.e6e6e6_2e2e2e)

        paintGradientLine = Paint()
        paintGradientLine.style = Paint.Style.STROKE
        paintGradientLine.strokeWidth = 1f

        paintXText = Paint()
        paintXText.isAntiAlias = true
        paintXText.strokeWidth = 1f
        paintXText.textSize = 22f.sp
        paintXText.textAlign = Paint.Align.CENTER
        paintXText.color =
            resources.getColor(R.color.c_color_on_surface);//context.colorCompat(R.color.color_on_surface)

        // Y轴上文字
        paintYText = Paint()
        paintYText.isAntiAlias = true
        paintYText.textSize = 22f.sp
        paintYText.strokeWidth = 1f
        paintYText.textAlign = Paint.Align.RIGHT
        paintYText.color =
            resources.getColor(R.color.text_color_fc_40)//resources.getColor(R.color.c_666666_808080);//context.colorCompat(R.color.secondary_666666_808080)
        paintSleep = Paint()
        paintSleep.style = Paint.Style.FILL
        paintSleep.isAntiAlias = true
        paintSleep.color =
            resources.getColor(R.color.c_blue_7fbeff);//context.colorCompat(R.color.blue_7fbeff)

        paintPillar = Paint()
        paintPillar.style = Paint.Style.FILL
        paintPillar.isAntiAlias = true
        paintPillar.color =
            resources.getColor(R.color.c_blue_7fbeff);//context.colorCompat(R.color.blue_7fbeff)

        paintRound = Paint()
        paintRound.style = Paint.Style.FILL
        paintRound.isAntiAlias = true
        paintRound.color =
            resources.getColor(R.color.slider_round);//context.colorCompat(R.color.ffffff_6e6e6e)

        paintBessel = Paint()
        paintBessel.style = Paint.Style.FILL
        paintBessel.isAntiAlias = true
        paintBessel.color =
            resources.getColor(R.color.slider_round_bessel_bg)

        paintBar = Paint()
        paintBar.style = Paint.Style.FILL
        paintBar.isAntiAlias = true

        paintNoDataText = Paint()
        paintNoDataText.isAntiAlias = true
        paintNoDataText.textSize = 26f.sp;//.sp
        paintNoDataText.textAlign = Paint.Align.CENTER
        paintNoDataText.color =
            resources.getColor(R.color.text_color_fc_60);//context.colorCompat(R.color.color_on_surface)
    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        this.w = w
        this.h = h
        this.oldh = oldh
        this.oldw = oldw
        scrWidth = width.toFloat()
        scrHeight = height.toFloat()
        ySpacing = scrHeight / 8f //y轴分8份
        perDp = scrHeight / 450
        //底部圆滑块可以滑动的范围

        if (type == 1)
            xWithStart = margin + paintXText.measureText("00-00") / 2
        else
            xWithStart = margin + paintXText.measureText(xData[0]) / 2
        xWithEnd = scrWidth - xWithStart
        xSpacing = (xWithEnd - xWithStart) / xCount
        xTextSpacing = (xWithEnd - xWithStart) / xCount
        if (!sleepData?.items.isNullOrEmpty()) {
            var lastIndex = 0
            for (i in sleepData!!.items.size - 1 downTo 0) {
                if (sleepData!!.items[i].sleepTime != 0) {
                    lastIndex = i
                    break
                }

            }
            if (lastIndex > 0 || sleepData!!.items[0].sleepTime != 0) {
                xSlider = xWithStart + xSpacing * lastIndex
            } else {
                xSlider = (scrWidth / 2)- 1
            }
        } else {
            xSlider = (scrWidth / 2)- 1
        }
        Log.d(TAG, "xWidth: -> " + scrWidth.toString())
        Log.d(TAG, "xSlider: -> " + xSlider.toString())
        yP = ySpacing * 4 / getMaxSleepTimeAndText()
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        parent.requestDisallowInterceptTouchEvent(true)
        return super.dispatchTouchEvent(ev)
    }

    private val touchEventDelegate by lazy {
        ChartTouchEventDelegate(
            setSliderX = {
                xSlider = it
                invalidate()
            }
        )
    }
    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return touchEventDelegate.onTouchEvent(
            event,
            this,
            xSlider,
            xSpacing,
            ySpacing,
            xWithStart,
            xWithEnd,
            sleepData?.items?.size?:0,
        )
    }

    private val margin = 50f.dp//20f.dp //左右两边距离
    private var xWithStart = 0f //x轴的起始点
    private var xWithEnd = 0f  //x轴结束点
    private var ySpacing = 10f //高度分割份数后间距
    private var xSpacing = 0f //x轴分割份数后间距
    private var xTextSpacing = 0f //x轴文字分割份数后间距

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawGradientBackgroundUnderXText(canvas)
        //画柱子
        drawPillar(canvas)
        //垂直渐变线
        drawGradientLine(canvas)
        //底部
        drawBessel(canvas)
        //画x轴方向文字
        drawX(canvas)

    }


    private fun drawGradientBackgroundUnderXText(canvas: Canvas) {
        // 画渐变颜色
        ViewDrawUtils.drawGradientBackgroundUnderXText(canvas,context,scrWidth,ySpacing,7)
    }

    private fun drawX(canvas: Canvas) {
        xData.forEachIndexed { index, s ->
            val x = xWithStart + xTextSpacing * index
            val dis = abs(x - xSlider)
            var y = ySpacing * 7 - 25f.dp
            if (dis < xTextSpacing / 2) {
                paintXText.typeface = Typeface.DEFAULT_BOLD
                y -= 20f.dp * (1 - dis / xTextSpacing)
                paintXText.color = resources.getColor(R.color.text_color_fc_100)
                onXTextSelectListener?.invoke(index, s)
            } else {
                paintXText.typeface = Typeface.DEFAULT
                paintXText.color = resources.getColor(R.color.text_color_fc_40)

            }
            if (type == 2) {
                if (index == 0) {
                    val tp = TextPaint()
                    tp.color = paintXText.color
                    tp.style = paintXText.style
                    tp.textSize = paintXText.textSize
                    tp.typeface = paintXText.typeface
                    tp.isAntiAlias = true
                    tp.strokeWidth = 1f
                    val point = Point((x.toInt() + 24.dp).toInt(), (y.toInt() - 20.dp).toInt())
                    textCenter(
                        s,
                        tp,
                        canvas,
                        point,
                        100,
                        Layout.Alignment.ALIGN_NORMAL,
                        0.8f,
                        0f,
                        false
                    )
                } else {
                    canvas.drawText(s, x, y, paintXText)
                }
            } else {
                canvas.drawText(s, x, y, paintXText)
            }

        }

    }
    private fun textCenter(
        string: String, textPaint: TextPaint, canvas: Canvas, point: Point, width: Int,
        align: Layout.Alignment, spacingmult: Float, spacingadd: Float, includepad: Boolean
    ) {
        val staticLayout =
            StaticLayout(string, textPaint, width, align, spacingmult, spacingadd, includepad)
        canvas.save()
        canvas.translate(
            (-staticLayout.width / 2 + point.x).toFloat(),
            (-staticLayout.height / 2 + point.y).toFloat()
        )
        staticLayout.draw(canvas)
        canvas.restore()
    }
    private fun drawPillar(canvas: Canvas) {
        var startColor = 0
        var endColor = 0
        if (sleepData == null) return
        if (sleepData!!.items.isNullOrEmpty()) return
        for (index in 0..xCount) {
            val x = xWithStart + xTextSpacing * index
            val dis = Math.abs(x - xSlider)
            val isSelected=dis < xTextSpacing / 2
            var tempX = xWithStart + xSpacing * index
            if (index < sleepData!!.items.size) {
                var item = sleepData!!.items[index]
                paintBar.strokeWidth = barWidth
                var tempY = ySpacing * 5.3F
                var allsleep = 0f
                if (isSelected) {
                    onSelectListener?.invoke(index, item)
                    paintBar.color = deepC
                    canvas.drawLine(
                        tempX,
                        tempY,
                        tempX,
                        (tempY - item.deepSleep * yP),
                        paintBar
                    )
                    tempY = tempY - item.deepSleep * yP
                    paintBar.color = lightC
                    canvas.drawLine(
                        tempX,
                        tempY,
                        tempX,
                        (tempY - item.lightSleep * yP),
                        paintBar
                    )
                    tempY = tempY - item.lightSleep * yP
                    paintBar.color = dreamC
                    canvas.drawLine(
                        tempX,
                        tempY,
                        tempX,
                        (tempY - item.dreamSleep * yP),
                        paintBar
                    )
                    tempY = tempY - item.dreamSleep * yP
                    paintBar.color = oddmentsC
                    canvas.drawLine(
                        tempX,
                        tempY,
                        tempX,
                        (tempY - item.sporadicSleep * yP),
                        paintBar
                    )
                    if (item.manualSleepTime != 0) {
                        tempY = ySpacing * 5.3F - item.manualSleepTime * yP
                        paintBar.color = sleepC
                        canvas.drawLine(
                            tempX,
                            tempY,
                            tempX,
                            ySpacing * 5.3F,
                            paintBar
                        )
                    }
                } else {
                    paintBar.color = adjustAlpha(deepC, 168)
                    canvas.drawLine(
                        tempX,
                        tempY,
                        tempX,
                        (tempY - item.deepSleep * yP),
                        paintBar
                    )
                    tempY = tempY - item.deepSleep * yP
                    paintBar.color = adjustAlpha(lightC, 168)
                    canvas.drawLine(
                        tempX,
                        tempY,
                        tempX,
                        (tempY - item.lightSleep * yP),
                        paintBar
                    )
                    tempY = tempY - item.lightSleep * yP
                    paintBar.color = adjustAlpha(dreamC, 168)
                    canvas.drawLine(
                        tempX,
                        tempY,
                        tempX,
                        (tempY - item.dreamSleep * yP),
                        paintBar
                    )
                    tempY = tempY - item.dreamSleep * yP
                    paintBar.color = adjustAlpha(oddmentsC, 168)
                    canvas.drawLine(
                        tempX,
                        tempY,
                        tempX,
                        (tempY - item.sporadicSleep * yP),
                        paintBar
                    )

                    if (item.manualSleepTime != 0) {
                        tempY = ySpacing * 5.3F - item.manualSleepTime * yP
                        paintBar.color = adjustAlpha(sleepC, 168)
                        canvas.drawLine(
                            tempX,
                            tempY,
                            tempX,
                            ySpacing * 5.3F,
                            paintBar
                        )
                    }
                }
            } else {
                if (isSelected) {
                    onSelectListener?.invoke(index, null)
                }
            }

        }
    }

    private fun drawBessel(canvas: Canvas) {
        // 第一条曲线开始点
        mFirstCurveStartPoint[(xSlider - curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()
        // 第一条曲线结束点
        mFirstCurveEndPoint[xSlider.toInt()] =
            (ySpacing * 7 - curveCircleRadius - curveCircleRadius / 4).toInt()
        // 第二条开始点
        mSecondCurveStartPoint = mFirstCurveEndPoint
        mSecondCurveEndPoint[(xSlider + curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()

        // 第一条控制点
        mFirstCurveControlPoint1[(mFirstCurveStartPoint.x + curveCircleRadius + curveCircleRadius / 4).toInt()] =
            mFirstCurveStartPoint.y
        mFirstCurveControlPoint2[(mFirstCurveEndPoint.x - curveCircleRadius * 2 + curveCircleRadius).toInt()] =
            mFirstCurveEndPoint.y
        // 第二条控制点
        mSecondCurveControlPoint1[(mSecondCurveStartPoint.x + curveCircleRadius * 2 - curveCircleRadius).toInt()] =
            mSecondCurveStartPoint.y
        mSecondCurveControlPoint2[(mSecondCurveEndPoint.x - curveCircleRadius - curveCircleRadius / 4).toInt()] =
            mSecondCurveEndPoint.y
        mPath.reset()
        mPath.moveTo(0f, ySpacing * 7)
        mPath.lineTo(mFirstCurveStartPoint.x.toFloat(), mFirstCurveStartPoint.y.toFloat())
        mPath.cubicTo(
            mFirstCurveControlPoint1.x.toFloat(), mFirstCurveControlPoint1.y.toFloat(),
            mFirstCurveControlPoint2.x.toFloat(), mFirstCurveControlPoint2.y.toFloat(),
            mFirstCurveEndPoint.x.toFloat(), mFirstCurveEndPoint.y.toFloat()
        )
        mPath.cubicTo(
            mSecondCurveControlPoint1.x.toFloat(), mSecondCurveControlPoint1.y.toFloat(),
            mSecondCurveControlPoint2.x.toFloat(), mSecondCurveControlPoint2.y.toFloat(),
            mSecondCurveEndPoint.x.toFloat(), mSecondCurveEndPoint.y.toFloat()
        )
        mPath.lineTo(scrWidth, ySpacing * 7)
        mPath.lineTo(scrWidth, scrHeight)
        mPath.lineTo(0f, scrHeight)
        mPath.close()

        //底部灰色
        canvas.drawPath(mPath, paintBessel)
        //底部滑块
        canvas.drawCircle(xSlider, ySpacing * 7 + 5f, curveCircleRadius, paintRound)
    }

    private var startDay = ""
    private var endDay = ""

    fun setXData(dateList: ArrayList<String>?, type: Int) {
        this.type = type
        when (type) {
            0 -> {
                dateList?.let {
                    xCount = it.size - 1
                    xData = it
                }
            }

            1 -> {
                dateList?.let {
                    xCount = it.size - 1
                    xData = TimeUtils.getXDataWithStep(it, 7)
                }
            }

            2 -> {
                xCount = 11
                xData = arrayListOf(
                    "${TimeUtils.getCurrentYearStr()}\n01",
                    "02",
                    "03",
                    "04",
                    "05",
                    "06",
                    "07",
                    "08",
                    "09",
                    "10",
                    "11",
                    "12"
                )
            }
        }
        onSizeChanged(this.w, this.h, this.oldw, this.oldh)
        postInvalidate()
    }


    fun setValue(value: SleepDataType?, dateList: ArrayList<String>?, type: Int): SleepBarChart {
        this.type = type
        when (type) {
            0 -> {  //周
                dateList?.let {
                    xCount = it.size - 1
                    this.barWidth = 20f.dp
                    xData = it
                }

            }

            1 -> {  //月
                dateList?.let {
                    xCount = it.size - 1
                    this.barWidth = 20f.dp
                    xData = TimeUtils.getXDataWithStep(it, 7)
                }

            }

            2 -> {  //年
                xCount = 11
                this.barWidth = 20f.dp
                xData = arrayListOf(
                    "${TimeUtils.getCurrentYearStr()}\n01",
                    "02",
                    "03",
                    "04",
                    "05",
                    "06",
                    "07",
                    "08",
                    "09",
                    "10",
                    "11",
                    "12"
                )
            }
        }
        this.sleepData = value
        onSizeChanged(this.w, this.h, this.oldw, this.oldh)
        postInvalidate()
        return this
    }

    private fun drawGradientLine(canvas: Canvas) {
        if (sleepData == null) {
            canvas.drawText(
                context.getString(R.string.no_sleep_data),
                scrWidth / 2f,
                ySpacing * 3f,
                paintNoDataText
            )
        } else {
            val mLinearGradient = LinearGradient(
                xSlider, ySpacing, xSlider, ySpacing * 6,
                intArrayOf(
                    resources.getColor(R.color.indicator_line_start),
                    resources.getColor(R.color.indicator_line_center),
                    resources.getColor(R.color.indicator_line_start)
                ), null, Shader.TileMode.MIRROR
            )
            paintGradientLine.shader = mLinearGradient

            if (ySpacing > 0) {
                canvas.drawLine(xSlider, ySpacing, xSlider, ySpacing * 6, paintGradientLine)
            }
        }
    }

    private var onSelectListener: ((index: Int, item: SleepTime?) -> Unit)? = null

    fun setOnSelectListener(l: ((index: Int, item: SleepTime?) -> Unit)): SleepBarChart {
        this.onSelectListener = l
        return this
    }

    private var onXTextSelectListener: ((index: Int, xText: String) -> Unit)? = null

    fun setOnXTextSelectListener(l: ((index: Int, xText: String) -> Unit)): SleepBarChart {
        this.onXTextSelectListener = l
        return this
    }

    fun adjustAlpha(color: Int, alpha: Int): Int {
        return Color.argb(alpha, color.red, color.green, color.blue)
    }


    fun getMaxSleepTimeAndText(): Int {  //获取最大值  并且 获取柱状图日期
        var max: Int = 0
        sleepData?.let {
            it.items.forEachIndexed { index, item ->
                if (item.sleepTime > max)
                    max = item.sleepTime
            }
        }
        return max
    }
}