package com.healthlink.hms.views

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView

class MiddleEllipsesTextView : AppCompatTextView {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet, defStyle: Int) : super(
        context,
        attrs,
        defStyle
    )
    private var isEllipsized=false
    private var updateSeeMore:UpdateSeeMore?=null
    private var endPercentage=0.75f
    override fun onDraw(canvas: Canvas) {
        val layout = layout
        if (layout != null) {
            val lineCount = layout.lineCount
            if (lineCount - maxLines > 0) {
                isEllipsized=true
                val ellipsisStart = layout.getLineStart(maxLines - 1)
                val ellipsisEnd = layout.getLineEnd(maxLines - 1)
                val lastLineText = text.subSequence(ellipsisStart, ellipsisEnd)
                val mid = (lastLineText.length * endPercentage).toInt()
                val firstPart = lastLineText.subSequence(0, mid).toString()
                val ellipsizedText = "$firstPart..."
                val text = text.subSequence(0, ellipsisStart).toString() + ellipsizedText
                setText(text)

            }
        }

        updateSeeMore?.update(isEllipsized,this.id)
        super.onDraw(canvas)
    }
    open fun setCallback(callback: UpdateSeeMore?) {
        this.updateSeeMore = callback
    }
    //给查看更多是否显示标记位
    open interface UpdateSeeMore{
        fun update(isEllipsized:Boolean,viewID: Int);

    }
    open fun setEndPercentage(per:Int){
        this.endPercentage= per.toFloat()/100f
    }

    fun setIsEllipsized(isEllipsized: Boolean){
        this.isEllipsized = isEllipsized
    }

}
