package com.healthlink.hms.views.charts

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Point
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import com.blankj.utilcode.util.LogUtils
import com.healthlink.hms.R
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import com.healthlink.hms.utils.ChartTouchEventDelegate
import com.healthlink.hms.utils.ViewDrawUtils
import kotlin.math.abs

/**
 * Created by imaginedays on 2024/6/3
 * 心率 日月年
 */
class HeartMonthChart(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    //屏幕宽高
    private var scrWidth = 0f
    private var scrHeight = 0f
    private var perDp = 0f //1dp所占的值
    private var xData = mutableListOf("5-13")
    private var yScaleStart = 40f  //初始最低刻度值
    private var yScaleStep = 45 //刻度间隔值

    private var heartData = mutableListOf<String>()
    private lateinit var paintLine: Paint
    private lateinit var paintGradientLine: Paint
    private lateinit var paintPolyline: Paint //心率折线
    private lateinit var paintXText: Paint
    private lateinit var paintYText: Paint
    private lateinit var paintPillar: Paint
    private lateinit var paintRound: Paint
    private lateinit var paintBessel: Paint

    private var animDuration = 500L
    private var anim: ValueAnimator? = null
    private var mPercent = 0f //动画进度
    private var xSlider = 0f //滑块的x轴位置

    private var mPath: Path
    private val curveCircleRadius = 22f.dp

    // the coordinates of the first curve
    private val mFirstCurveStartPoint = Point()
    private val mFirstCurveEndPoint = Point()
    private val mFirstCurveControlPoint1 = Point()
    private val mFirstCurveControlPoint2 = Point()

    //the coordinates of the second curve
    private var mSecondCurveStartPoint = Point()
    private val mSecondCurveEndPoint = Point()
    private val mSecondCurveControlPoint1 = Point()
    private val mSecondCurveControlPoint2 = Point()

    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        mPath = Path()
        initPaint()
    }

    /**
     * 初始化画笔
     */
    private fun initPaint() {
        // Y轴刻度线
        paintLine = Paint()
        paintLine.style = Paint.Style.STROKE
        paintLine.strokeWidth = 1f
        paintLine.color = resources.getColor(R.color.color_y_line)

        // 指示渐变线
        paintGradientLine = Paint()
        paintGradientLine.style = Paint.Style.STROKE
        paintGradientLine.strokeWidth = 2f

        // X轴上文字
        paintXText = Paint()
        paintXText.isAntiAlias = true
        paintXText.strokeWidth = 1f
        paintXText.textSize = 22f.sp
        paintXText.textAlign = Paint.Align.CENTER
        paintXText.color = resources.getColor(R.color.text_color_fc_40)

        // Y轴上文字
        paintYText = Paint()
        paintYText.isAntiAlias = true
        paintYText.textSize = 22f.sp
        paintYText.strokeWidth = 1f
        paintYText.textAlign = Paint.Align.RIGHT
        paintYText.color = resources.getColor(R.color.text_color_fc_40)

        paintPolyline = Paint()
        paintPolyline.style = Paint.Style.FILL
        paintPolyline.strokeWidth = 4f
        paintPolyline.isAntiAlias = true
        paintPolyline.color = resources.getColor(R.color.indicator_inner)

        // 柱状图
        paintPillar = Paint()
        paintPillar.style = Paint.Style.FILL
        paintPillar.isAntiAlias = true
        paintPillar.color = resources.getColor(R.color.heart_month_poly_line)

        // 指示滑块圆
        paintRound = Paint()
        paintRound.style = Paint.Style.FILL
        paintRound.isAntiAlias = true
        paintRound.color = resources.getColor(R.color.slider_round)

        // 滑块底部 通过shader设置渐变色
        paintBessel = Paint()
        paintBessel.style = Paint.Style.FILL
        paintBessel.isAntiAlias = true
        paintBessel.color =
            resources.getColor(R.color.slider_round_bessel_bg)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        scrWidth = width.toFloat()
        scrHeight = height.toFloat()
        ySpacing = scrHeight / 8f //y轴分8份
        perDp = scrHeight / 450
        //底部圆滑块可以滑动的范围
        xWithStart = margin + paintXText.measureText(xData[0]) / 2
        xWithEnd = scrWidth - xWithStart  - paintYText.measureText("000")
        xSpacing = (xWithEnd - xWithStart) / (heartData.size - 1)

        xTextSpacing = xSpacing

        xSlider = xSpacing * (day - 1) + xWithStart
    }

    private val touchEventDelegate by lazy {
        ChartTouchEventDelegate(
            setSliderX = {
                xSlider = it
                invalidate()
            }
        )
    }
    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return touchEventDelegate.onTouchEvent(
            event,
            this,
            xSlider,
            xSpacing,
            ySpacing,
            xWithStart,
            xWithEnd,
            heartData.size,
            actionUp = { x, y ->
                when{
                    touchEventDelegate.isSlider -> false
                    Math.abs(x - touchEventDelegate.mDownX) > xSpacing -> {
                        onMonthMoveListener?.invoke(event.x > touchEventDelegate.mDownX)
                        true
                    }
                    else -> {
                        heartData.forEachIndexed { index, _ ->
                            val x = xWithStart + xSpacing * index
                            val dis = abs(x - event.x)
                            if (dis < xSpacing / 2) {
                                xSlider = x
                                invalidate()
                                return@forEachIndexed
                            }
                        }
                        true
                    }
                }
            },
            onDataSelected = {index ->
                if (index < heartData.size) {
                    onMonthSelectListener?.invoke(index, heartData[index])
                }
            }
        )
    }

    private val margin = 50f.dp//20f.dp //左右两边距离
    private var xWithStart = 0f //x轴的起始点
    private var xWithEnd = 0f  //x轴结束点
    private var ySpacing = 0f //高度分割份数后间距
    private var xSpacing = 0f //x轴柱子分割份数后间距
    private var xTextSpacing = 0f //x轴文字分割份数后间距

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // X轴背景渐变
        drawGradientBackgroundUnderXText(canvas)
        //画y轴方向横线与文字
        drawY(canvas)
        //垂直渐变线
        drawGradientLine(canvas)
        //画柱子
        drawPillar(canvas)
        //底部
        drawBessel(canvas)
        //画x轴方向文字
        drawX(canvas)
    }

    private fun drawGradientBackgroundUnderXText(canvas: Canvas) {
        // 画渐变颜色
        ViewDrawUtils.drawGradientBackgroundUnderXText(canvas,context,scrWidth,ySpacing,7)
    }

    private fun drawX(canvas: Canvas) {
        xData.forEachIndexed { index, s ->
            if (index % 3 == 0) {
                val x = xWithStart + xTextSpacing * index
                val dis = abs(x - xSlider)
                var y = ySpacing * 7 - 40f
                if (dis < xTextSpacing / 2) {
                    paintXText.typeface = Typeface.DEFAULT_BOLD
                    y -= 40f * (1 - dis / xTextSpacing)
                    paintXText.color = resources.getColor(R.color.text_color_fc_100)
                } else {
                    paintXText.typeface = Typeface.DEFAULT
                    paintXText.color = resources.getColor(R.color.text_color_fc_40)
                }
                canvas.drawText(s, x, y, paintXText)
            }

        }
    }

    private fun drawPillar(canvas: Canvas) {
        //画柱子
        heartData.forEachIndexed { index, i ->
            if (xSlider < xWithStart + xSpacing * index + xSpacing / 2 && xSlider > xWithStart + xSpacing * index - xSpacing / 2) {
                paintPillar.color = resources.getColor(R.color.heart_month_poly_line)//context.colorCompat(R.color.fd3a5e_fe3b5f)
                onMonthSelectListener?.invoke(index, i)
            } else {
                paintPillar.color = resources.getColor(R.color.heart_month_poly_line_default)//context.colorCompat(R.color.ff7693_ff7894)
            }
            if (i.length > 3) { // 0-0 这种length = 3

                val data = i.split("-")
                if (data[1].toFloat() - data[0].toFloat() < 5) {
                    canvas.drawCircle(
                        xWithStart + xSpacing * index,
                        ySpacing * 5f - ySpacing * ((data[1].toFloat() / 2 + data[0].toFloat() / 2 - yScaleStart) / yScaleStep) * mPercent,
                        10f.dp, paintPillar
                    )
                } else {
                    val radius = 10f.dp // 圆角半径
                    canvas.drawRoundRect(
                        RectF(
                            xWithStart + xSpacing * index - 10f.dp,
                            ySpacing * 5f - ySpacing * ((data[1].toFloat() - yScaleStart) / yScaleStep) * mPercent,
                            xWithStart + xSpacing * index + 10f.dp,
                            ySpacing * 5f - ySpacing * ((data[0].toFloat() - yScaleStart) / yScaleStep) * mPercent
                        ), radius, radius, paintPillar
                    )
                }
            }
        }
    }

    private fun drawY(canvas: Canvas) {
        for (i in 0..4) {
            canvas.drawLine(
                margin, ySpacing * (i + 1), scrWidth - margin,
                ySpacing * (i + 1), paintLine
            )

            canvas.drawText(
                (yScaleStart + (4 - i) * yScaleStep).toInt().toString(),
                scrWidth - margin,
                ySpacing * (i + 1) - 10f,
                paintYText
            )
        }
    }

    private fun drawBessel(canvas: Canvas) {
        // 第一条曲线开始点
        mFirstCurveStartPoint[(xSlider - curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()
        // 第一条曲线结束点
        mFirstCurveEndPoint[xSlider.toInt()] =
            (ySpacing * 7 - curveCircleRadius - curveCircleRadius / 4).toInt()
        // 第二条开始点
        mSecondCurveStartPoint = mFirstCurveEndPoint
        mSecondCurveEndPoint[(xSlider + curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()

        // 第一条控制点
        mFirstCurveControlPoint1[(mFirstCurveStartPoint.x + curveCircleRadius + curveCircleRadius / 4).toInt()] =
            mFirstCurveStartPoint.y
        mFirstCurveControlPoint2[(mFirstCurveEndPoint.x - curveCircleRadius * 2 + curveCircleRadius).toInt()] =
            mFirstCurveEndPoint.y
        // 第二条控制点
        mSecondCurveControlPoint1[(mSecondCurveStartPoint.x + curveCircleRadius * 2 - curveCircleRadius).toInt()] =
            mSecondCurveStartPoint.y
        mSecondCurveControlPoint2[(mSecondCurveEndPoint.x - curveCircleRadius - curveCircleRadius / 4).toInt()] =
            mSecondCurveEndPoint.y
        mPath.reset()
        mPath.moveTo(0f, ySpacing * 7)
        mPath.lineTo(mFirstCurveStartPoint.x.toFloat(), mFirstCurveStartPoint.y.toFloat())
        mPath.cubicTo(
            mFirstCurveControlPoint1.x.toFloat(), mFirstCurveControlPoint1.y.toFloat(),
            mFirstCurveControlPoint2.x.toFloat(), mFirstCurveControlPoint2.y.toFloat(),
            mFirstCurveEndPoint.x.toFloat(), mFirstCurveEndPoint.y.toFloat()
        )
        mPath.cubicTo(
            mSecondCurveControlPoint1.x.toFloat(), mSecondCurveControlPoint1.y.toFloat(),
            mSecondCurveControlPoint2.x.toFloat(), mSecondCurveControlPoint2.y.toFloat(),
            mSecondCurveEndPoint.x.toFloat(), mSecondCurveEndPoint.y.toFloat()
        )
        mPath.lineTo(scrWidth, ySpacing * 7)
        mPath.lineTo(scrWidth, scrHeight)
        mPath.lineTo(0f, scrHeight)
        mPath.close()

        //底部灰色
        canvas.drawPath(mPath, paintBessel)
        //底部滑块
        canvas.drawCircle(xSlider, ySpacing * 7 + 5f, curveCircleRadius, paintRound)
    }

    private var yMarkMax = 1 //Y轴刻度最大值

    // 指示指针展现在哪一天的位置
    private var day = 1

    @Suppress("IMPLICIT_CAST_TO_ANY")
    fun setValue(
        heart: MutableList<String>,
        mXData: MutableList<String>,day: Int
    ): HeartMonthChart {
        this.day = day
        heartData.clear()
        heartData.addAll(heart)
        xData.clear()
        xData = mXData
        startAnimation()
        return this
    }

    private fun startAnimation() {
        anim = ValueAnimator.ofObject(AngleEvaluator(), 0f, 1f)
        anim?.interpolator = AccelerateDecelerateInterpolator()
        anim?.addUpdateListener { animation ->
            mPercent = animation.animatedValue as Float
            postInvalidate()
        }
        anim?.duration = animDuration
        anim?.start()
    }

    private fun drawGradientLine(canvas: Canvas) {
        val mLinearGradient = LinearGradient(
            xSlider, ySpacing, xSlider, ySpacing * 6,
            intArrayOf(
                resources.getColor(R.color.indicator_line_start),
                resources.getColor(R.color.indicator_line_center),
                resources.getColor(R.color.indicator_line_start)
            ), null, Shader.TileMode.MIRROR
        )
        paintGradientLine.shader = mLinearGradient

        if (ySpacing > 0) {
            canvas.drawLine(xSlider, ySpacing, xSlider, ySpacing * 6, paintGradientLine)
        }
    }

    private var onMonthSelectListener: ((index: Int, heart: String) -> Unit)? = null

    fun setOnMonthSelectListener(l: ((index: Int, heart: String) -> Unit)): HeartMonthChart {
        this.onMonthSelectListener = l
        return this
    }

    private var onMonthMoveListener: ((isPre: Boolean) -> Unit)? = null

    fun setOnMonthMoveListener(l: ((index: Boolean) -> Unit)): HeartMonthChart {
        this.onMonthMoveListener = l
        return this
    }
}