package com.healthlink.hms.views

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.MotionEvent
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import com.healthlink.hms.R
import kotlin.math.abs

class CustomBarStretchScrollView(context: Context?, attrs: AttributeSet?) : StretchScrollView(
    context!!, attrs
) {

    private val scrollBarRect = RectF() // 使用 RectF 支持圆角滚动条
    private val scrollBarThickness = 4 // 滚动条宽度
    private val scrollBarHeightFixed = 92f // 固定滚动条高度
    private val scrollBarCornerRadius = 4f // 圆角半径
    private val scrollBarColor = context!!.getColor(R.color.scrollbar_thumb_color) // 滚动条颜色
    @SuppressLint("ResourceAsColor")
    private val scrollBarPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = scrollBarColor
        alpha = 0 // 初始透明度为 0
    }

    private val handler = Handler(Looper.getMainLooper())
    private var isScrollBarVisible = false // 滚动条是否可见
    private var fadeAnimator: ValueAnimator? = null // 透明度动画
    private var lastY = 0f // 记录上一次Y轴的触摸位置
    private var lastX = 0f // 记录上一次X轴的触摸位置
    private val threshold = 10 // 定义安全滑动阈值，防止小范围滑动触发滚动条

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawCustomScrollBar(canvas)
    }

    private fun drawCustomScrollBar(canvas: Canvas) {
        val viewHeight = height // 可见区域高度
        val childView = getChildAt(0) // 获取子视图

        if (childView != null) {
            val contentHeight = childView.height // 内容总高度
            val scrollY = scrollY // 当前滚动位置

            if (contentHeight <= viewHeight) {
                // 如果内容高度不超过视图高度，则不显示滚动条
                return
            }

            // 计算可滚动的总高度
            val scrollableHeight = contentHeight - viewHeight

            // 计算滚动条的顶部位置，使用正确的比例
            val scrollBarTop = (scrollY.toFloat() / scrollableHeight.toFloat()) * (contentHeight - scrollBarHeightFixed)

            // 计算滚动条底部位置
            val scrollBarBottom = scrollBarTop + scrollBarHeightFixed

            // 设置滚动条的矩形区域 (使用 RectF 支持圆角)
            scrollBarRect.set(width - scrollBarThickness.toFloat(), scrollBarTop, width.toFloat(), scrollBarBottom)

            // 绘制滚动条 (使用圆角矩形)
            canvas.drawRoundRect(scrollBarRect, scrollBarCornerRadius, scrollBarCornerRadius, scrollBarPaint)
        }
    }

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        when (ev.action) {
            MotionEvent.ACTION_DOWN -> {
                // 记录初始触摸点
                lastY = ev.y
                lastX = ev.x
            }
            MotionEvent.ACTION_MOVE -> {
                val currentY = ev.y
                val currentX = ev.x
                val distanceY = abs(currentY - lastY)
                val distanceX = abs(currentX - lastX)

                // 只有当垂直滑动距离大于横向滑动距离，并且垂直滑动距离超过阈值时才显示滚动条
                if (distanceY > threshold && distanceY > distanceX) {
                    showScrollBar()
                }
            }
            MotionEvent.ACTION_UP -> {
                // 停止滑动，隐藏滚动条
                handler.removeCallbacks(hideRunnable)
                handler.postDelayed(hideRunnable, 1000)
            }
        }
        return super.onTouchEvent(ev)
    }

    private fun showScrollBar() {
        if (!isScrollBarVisible) {
            isScrollBarVisible = true
            startFadeAnimation(255) // 设置为完全可见
            invalidate() // 重新绘制
        }
        // 每次滑动时重置隐藏延迟
        handler.removeCallbacks(hideRunnable)
        handler.postDelayed(hideRunnable, 1000) // 1秒后隐藏滚动条
    }

    private fun hideScrollBar() {
        // 只有在滚动条可见时才进行隐藏
        if (isScrollBarVisible) {
            isScrollBarVisible = false
            startFadeAnimation(0) // 设置为完全透明
        }
    }

    private fun startFadeAnimation(targetAlpha: Int) {
        // 取消之前的动画
        fadeAnimator?.cancel()

        // 创建透明度动画
        fadeAnimator = ValueAnimator.ofInt(scrollBarPaint.alpha, targetAlpha).apply {
            duration = 300 // 动画持续时间
            addUpdateListener { animator ->
                scrollBarPaint.alpha = ((animator.animatedValue as Int) * 0.4).toInt()
                invalidate() // 重新绘制
            }
            start()
        }
    }

    // 隐藏滚动条的Runnable
    private val hideRunnable = Runnable {
        hideScrollBar()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 清理资源，防止内存泄漏
        handler.removeCallbacks(hideRunnable) // 移除隐藏回调
        fadeAnimator?.cancel() // 取消当前的动画
    }

}
