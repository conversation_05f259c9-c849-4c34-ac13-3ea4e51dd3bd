package com.healthlink.hms.views

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.content.ContextCompat
import com.healthlink.hms.R

/**
 * Created by imaginedays on 2024/7/30
 *
 *
 */
class ClearableEditText(context: Context, attrs: AttributeSet) : AppCompatEditText(context, attrs) {

    private var clearButtonImage: Drawable? = null

    init {
        // 初始化清除按钮图像
        clearButtonImage = ContextCompat.getDrawable(context, R.drawable.btn_search_clean)

        // 添加文本监听器
        addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                showClearButton()
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        // 设置点击监听器
        setOnTouchListener { _, event ->
            if (compoundDrawablesRelative[2] != null) {
                val clearButtonStart: Float
                val clearButtonEnd: Float
                var isClearButtonClicked = false
                if (layoutDirection == LAYOUT_DIRECTION_RTL) {
                    clearButtonEnd = (clearButtonImage!!.intrinsicWidth + paddingStart).toFloat()
                    if (event.x < clearButtonEnd) {
                        isClearButtonClicked = true
                    }
                } else {
                    clearButtonStart = (width - paddingEnd - clearButtonImage!!.intrinsicWidth).toFloat()
                    if (event.x > clearButtonStart) {
                        isClearButtonClicked = true
                    }
                }
                if (isClearButtonClicked) {
                    if (event.action == android.view.MotionEvent.ACTION_UP) {
                        text?.clear()
                    }
                    return@setOnTouchListener true
                }
            }
            return@setOnTouchListener false
        }
    }

    private fun showClearButton() {
        if (text?.isNotEmpty() == true) {
            setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, clearButtonImage, null)
        } else {
            setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
        }
    }
}