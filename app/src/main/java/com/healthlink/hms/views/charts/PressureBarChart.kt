package com.healthlink.hms.views.charts

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Point
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.Typeface
import android.text.AutoText
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import com.healthlink.hms.utils.ChartTouchEventDelegate
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.ViewDrawUtils
import kotlin.math.abs

/**
 * Created by imaginedays on 2024/6/28
 * 压力 日周月年
 */
class PressureBarChart(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    // 声明变量名为barChartType的整形枚举
    enum class BarChartType(val type: Int) {
        DAY(0),
        WEEK(1),
        MONTH(2),
        YEAR(3)
    }

    //屏幕宽高
    private var scrWidth = 0f
    private var scrHeight = 0f
    private var perDp = 0f //1dp所占的值
    private var xData = mutableListOf<String>("   ")
    private var xDataDay = mutableListOf("00:00", "06:00", "12:00", "18:00", "24:00")
    private val yData = mutableListOf(99, 79, 59, 29, 0)
    private var yScaleStart = 0f  //初始最低刻度值
    private var yScaleStep = 30 //刻度间隔值
    private var intervalOneUnit = 0f // 一个单位所对应的px ySpacing / yScaleStep
    private var totalIntervalUnit = 0f // 相差多少个单位（与8 * yScaleStep对比相差多少个单位）
    private var barChartType = BarChartType.DAY
    private var xCount = 30

    private var pressureData = mutableListOf<String>()
    private lateinit var paintLine: Paint
    private lateinit var paintGradientLine: Paint
    private lateinit var paintPolyline: Paint //心率折线
    private lateinit var paintXText: Paint
    private lateinit var paintYText: Paint
    private lateinit var paintPillar: Paint
    private lateinit var paintRound: Paint
    private lateinit var paintBessel: Paint

    private var animDuration = 500L
    private var anim: ValueAnimator? = null
    private var mPercent = 0f //动画进度
    private var xSlider = 0f //滑块的x轴位置
    private var w = 0
    private var h = 0
    private var oldw = 0
    private var oldh = 0
    private var isNoDataMode=false

    private var mPath: Path
    private val curveCircleRadius = 22f.dp

    // the coordinates of the first curve
    private val mFirstCurveStartPoint = Point()
    private val mFirstCurveEndPoint = Point()
    private val mFirstCurveControlPoint1 = Point()
    private val mFirstCurveControlPoint2 = Point()

    //the coordinates of the second curve
    private var mSecondCurveStartPoint = Point()
    private val mSecondCurveEndPoint = Point()
    private val mSecondCurveControlPoint1 = Point()
    private val mSecondCurveControlPoint2 = Point()

    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        mPath = Path()
        initPaint()
    }

    /**
     * 初始化画笔
     */
    private fun initPaint() {
        // Y轴刻度线
        paintLine = Paint()
        paintLine.style = Paint.Style.STROKE
        paintLine.strokeWidth = 1f
        paintLine.color = resources.getColor(R.color.color_y_line)

        // 指示渐变线
        paintGradientLine = Paint()
        paintGradientLine.style = Paint.Style.STROKE
        paintGradientLine.strokeWidth = 2f

        // X轴上文字
        paintXText = Paint()
        paintXText.isAntiAlias = true
        paintXText.strokeWidth = 1f
        paintXText.textSize = 22f.sp
        paintXText.textAlign = Paint.Align.CENTER
        paintXText.color = resources.getColor(R.color.text_color_fc_40)

        // Y轴上文字
        paintYText = Paint()
        paintYText.isAntiAlias = true
        paintYText.textSize = 22f.sp
        paintYText.strokeWidth = 1f
        paintYText.textAlign = Paint.Align.RIGHT
        paintYText.color = resources.getColor(R.color.text_color_fc_40)

        paintPolyline = Paint()
        paintPolyline.style = Paint.Style.FILL
        paintPolyline.strokeWidth = 4f
        paintPolyline.isAntiAlias = true
        paintPolyline.color = resources.getColor(R.color.indicator_inner)

        // 柱状图
        paintPillar = Paint()
        paintPillar.style = Paint.Style.FILL
        paintPillar.isAntiAlias = true
        paintPillar.color = resources.getColor(R.color.heart_month_poly_line)

        // 指示滑块圆
        paintRound = Paint()
        paintRound.style = Paint.Style.FILL
        paintRound.isAntiAlias = true
        paintRound.color = resources.getColor(R.color.slider_round)

        // 滑块底部 通过shader设置渐变色
        paintBessel = Paint()
        paintBessel.style = Paint.Style.FILL
        paintBessel.isAntiAlias = true
        paintBessel.color =
            resources.getColor(R.color.slider_round_bessel_bg)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        this.w = w
        this.h = h
        this.oldh = oldh
        this.oldw = oldw
        scrWidth = width.toFloat()
        scrHeight = height.toFloat()
        ySpacing = scrHeight / 8f //y轴分8份
        intervalOneUnit = ySpacing / yScaleStep
        perDp = scrHeight / 450
        initBaseUnit()
    }

    private fun initBaseUnit() {
        // 底部圆滑块可以滑动的范围
        xWithStart = margin + paintXText.measureText(xData[0]) / 2
        xWithEnd = scrWidth - 2 * margin - paintYText.measureText(yMarkMax.toString()) * 2f

        when (barChartType) {
            BarChartType.DAY -> {
                xCount = 2 * 24
                xSpacing = (xWithEnd - xWithStart) / (2 * 24)
                xTextSpacing = (xWithEnd - xWithStart) / (xData.size - 1)
            }

            else -> {
                xCount = xData.size - 1
                xSpacing = (xWithEnd - xWithStart) / (xData.size - 1)
                xTextSpacing = xSpacing
            }
        }
        if (pressureData.isNullOrEmpty()) {
            xSlider =
                margin + (scrWidth - 2 * margin - paintYText.measureText(yMarkMax.toString()) * 2f) / 2
        } else
            xSlider = xSpacing * (lastValueIndex - 1) + xWithStart
    }

    private val touchEventDelegate by lazy {
        ChartTouchEventDelegate(
            setSliderX = {
                xSlider = it
                invalidate()
            }
        )
    }
    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return touchEventDelegate.onTouchEvent(
            event,
            this,
            xSlider,
            xSpacing,
            ySpacing,
            xWithStart,
            xWithEnd,
            pressureData.size,
            actionUp = { x, y ->
                when{
                    touchEventDelegate.isSlider -> false
                    Math.abs(x - touchEventDelegate.mDownX) > xSpacing -> {
                        onDayMoveListener?.invoke(x > touchEventDelegate.mDownX)
                        true
                    }
                    else -> false
                }
            }
        )
    }

    private val margin = 50f.dp//20f.dp //左右两边距离
    private var xWithStart = 0f //x轴的起始点
    private var xWithEnd = 0f  //x轴结束点
    private var ySpacing = 0f //高度分割份数后间距
    private var xSpacing = 0f //x轴柱子分割份数后间距
    private var xTextSpacing = 0f //x轴文字分割份数后间距

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // X轴背景渐变
        drawGradientBackgroundUnderXText(canvas)
        //画y轴方向横线与文字
        drawY(canvas)
        //画柱子
        drawPillar(canvas)
        //垂直渐变线
        drawGradientLine(canvas)
        //底部
        drawBessel(canvas)
        //画x轴方向文字
        drawX(canvas)
    }

    private fun drawGradientBackgroundUnderXText(canvas: Canvas) {
        // 画渐变颜色
        ViewDrawUtils.drawGradientBackgroundUnderXText(canvas,context,scrWidth,ySpacing,7)
    }

    private fun drawX(canvas: Canvas) {
        xData.forEachIndexed { index, s ->
            val x = xWithStart + xTextSpacing * index
            val dis = abs(x - xSlider)
            var y = ySpacing * 7 - 40f
            if (dis < xTextSpacing / 2) {
                paintXText.typeface = Typeface.DEFAULT_BOLD
//                y -= 40f * (1 - dis / xTextSpacing)
                if (barChartType == BarChartType.YEAR && index == 0) {
                    y -= ((30f.dp - 22.sp) * (1 - dis / xTextSpacing))
                } else {
                    y -= 30f.dp * (1 - dis / xTextSpacing)
                }
                paintXText.color = resources.getColor(R.color.text_color_fc_100)
                onXTextSelectListener?.invoke(index,s)
            } else {
                paintXText.typeface = Typeface.DEFAULT
                paintXText.color = resources.getColor(R.color.text_color_fc_40)
            }

            if (barChartType == BarChartType.YEAR) {
                if (index == 0) {
                    val tp = TextPaint()
                    tp.color = paintXText.color
                    tp.style = paintXText.style
                    tp.textSize = paintXText.textSize
                    tp.typeface = paintXText.typeface
                    tp.isAntiAlias = true
                    tp.strokeWidth = 1f
                    val point = Point((x.toInt() + 14.dp).toInt(), (y.toInt() - 20.dp).toInt())
                    textCenter(
                        s,
                        tp,
                        canvas,
                        point,
                        100,
                        Layout.Alignment.ALIGN_NORMAL,
                        0.8f,
                        0f,
                        false
                    )
                } else {
                    canvas.drawText(s, x, y, paintXText)
                }
            } else {
                canvas.drawText(s, x, y, paintXText)
            }
        }
    }

    private fun textCenter(
        string: String, textPaint: TextPaint, canvas: Canvas, point: Point, width: Int,
        align: Layout.Alignment, spacingmult: Float, spacingadd: Float, includepad: Boolean
    ) {
        val staticLayout =
            StaticLayout(string, textPaint, width, align, spacingmult, spacingadd, includepad)
        canvas.save()
        canvas.translate(
            (-staticLayout.width / 2 + point.x).toFloat(),
            (-staticLayout.height / 2 + point.y).toFloat()
        )
        staticLayout.draw(canvas)
        canvas.restore()
    }

    private fun drawPillar(canvas: Canvas) {
        //画柱子

        for (index in 0..xCount) {
            val isSelected =
                xSlider < xWithStart + xSpacing * index + xSpacing / 2 && xSlider > xWithStart + xSpacing * index - xSpacing / 2
            if (index < pressureData.size && !pressureData[index].equals("0-0")) {
                val i = pressureData[index]
                if (i.length > 3) { // 0-0 这种length = 3
                    val data = i.split("-")
                    // 是否选中
                    if (isSelected) {
                        paintPillar.color = getBarColorByValue(data[1].toInt())
                        onDaySelectListener?.invoke(index, i)
                    } else {
                        paintPillar.color = adjustAlpha(getBarColorByValue(data[1].toInt()))
                    }

                    if (data[1].toFloat() - data[0].toFloat() < 5) {
                        canvas.drawCircle(
                            xWithStart + xSpacing * index,
                            ySpacing * 5f - ySpacing * ((data[1].toFloat() / 2 + data[0].toFloat() / 2 - yScaleStart) / yScaleStep) * mPercent,
                            10f, paintPillar
                        )
                    } else {
                        val rect = RectF(
                            xWithStart + xSpacing * index - 10f.dp,
                            ySpacing * 5f - ySpacing * ((data[1].toFloat() - yScaleStart) / yScaleStep) * mPercent,
                            xWithStart + xSpacing * index + 10f.dp,
                            ySpacing * 5f - ySpacing * ((data[0].toFloat() - yScaleStart) / yScaleStep) * mPercent
                        )
                        val radius = 10f.dp // 圆角半径
                        val radii = floatArrayOf(
                            radius, radius, // 左上角
                            radius, radius, // 右上角
                            0f, 0f,         // 右下角
                            0f, 0f          // 左下角
                        )
                        val path = Path().apply {
                            addRoundRect(rect, radii, android.graphics.Path.Direction.CW)
                        }
                        canvas.drawPath(path, paintPillar)
                    }
                }
            } else {
                if (isSelected) {
                    onDaySelectListener?.invoke(index, null)
                }
            }
        }
    }

    // 计算最大值与最小值的差值，分配不同的颜色
    private fun getBarColorByValue(pressure: Int): Int {
        when (pressure) {
            in 1..29 -> {
                return Color.parseColor(context.getString(R.string.card_press_bar_bg_color_relax))
            }

            in 30..59 -> {
                return Color.parseColor(context.getString(R.string.card_press_bar_bg_color_normal))
            }

            in 60..79 -> {
                return Color.parseColor(context.getString(R.string.card_press_bar_bg_color_middle))
            }

            in 80..100 -> {
                return Color.parseColor(context.getString(R.string.card_press_bar_bg_color_high))
            }
        }
        return Color.parseColor(context.getString(R.string.card_press_bar_bg_color_relax))
    }

    /**
     * alpha 0.5 = 127 0.6 = 153
     * 白天模式 放松 0.6 正常 0.5 中等 0.6 偏高 0.6
     * 黑夜模式 放松 0.6 正常 0.6 中等 0.6 偏高 0.6
     */
    private fun adjustAlpha(color: Int, alpha: Int = 153): Int {
        return Color.argb(alpha, color.red, color.green, color.blue)
    }

    private fun drawY(canvas: Canvas) {
        intervalOneUnit = ySpacing / yScaleStep
        var y = ySpacing + (21 * intervalOneUnit) // 向下移动多少个相差间隔
        for (i in 0..<yData.size) {
            var diffPX = 0f
            var diffInterval = 0;
            if (i != 0 && i != yData.size) {
                diffInterval = (yData[i - 1] - yData[i])
                totalIntervalUnit += diffInterval
                diffPX = (diffInterval * intervalOneUnit)
                y += diffPX
            }
            canvas.drawLine(
                margin, y, scrWidth - margin,
                y, paintLine
            )
            canvas.drawText(
                yData[i].toString(),
                scrWidth - margin,
                y - 10f,
                paintYText
            )

//            canvas.drawLine(
//                margin, ySpacing * (i + 1) , scrWidth - margin,
//                ySpacing * (i + 1), paintLine
//            )
//
//            canvas.drawText(
//                (yScaleStart + (4 - i) * yScaleStep).toInt().toString(),
//                scrWidth - margin,
//                ySpacing * (i + 1) - 10f,
//                paintYText
//            )
        }
    }

    private fun drawBessel(canvas: Canvas) {
        // 第一条曲线开始点
        mFirstCurveStartPoint[(xSlider - curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()
        // 第一条曲线结束点
        mFirstCurveEndPoint[xSlider.toInt()] =
            (ySpacing * 7 - curveCircleRadius - curveCircleRadius / 4).toInt()
        // 第二条开始点
        mSecondCurveStartPoint = mFirstCurveEndPoint
        mSecondCurveEndPoint[(xSlider + curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()

        // 第一条控制点
        mFirstCurveControlPoint1[(mFirstCurveStartPoint.x + curveCircleRadius + curveCircleRadius / 4).toInt()] =
            mFirstCurveStartPoint.y
        mFirstCurveControlPoint2[(mFirstCurveEndPoint.x - curveCircleRadius * 2 + curveCircleRadius).toInt()] =
            mFirstCurveEndPoint.y
        // 第二条控制点
        mSecondCurveControlPoint1[(mSecondCurveStartPoint.x + curveCircleRadius * 2 - curveCircleRadius).toInt()] =
            mSecondCurveStartPoint.y
        mSecondCurveControlPoint2[(mSecondCurveEndPoint.x - curveCircleRadius - curveCircleRadius / 4).toInt()] =
            mSecondCurveEndPoint.y
        mPath.reset()
        mPath.moveTo(0f, ySpacing * 7)
        mPath.lineTo(mFirstCurveStartPoint.x.toFloat(), mFirstCurveStartPoint.y.toFloat())
        mPath.cubicTo(
            mFirstCurveControlPoint1.x.toFloat(), mFirstCurveControlPoint1.y.toFloat(),
            mFirstCurveControlPoint2.x.toFloat(), mFirstCurveControlPoint2.y.toFloat(),
            mFirstCurveEndPoint.x.toFloat(), mFirstCurveEndPoint.y.toFloat()
        )
        mPath.cubicTo(
            mSecondCurveControlPoint1.x.toFloat(), mSecondCurveControlPoint1.y.toFloat(),
            mSecondCurveControlPoint2.x.toFloat(), mSecondCurveControlPoint2.y.toFloat(),
            mSecondCurveEndPoint.x.toFloat(), mSecondCurveEndPoint.y.toFloat()
        )
        mPath.lineTo(scrWidth, ySpacing * 7)
        mPath.lineTo(scrWidth, scrHeight)
        mPath.lineTo(0f, scrHeight)
        mPath.close()

        //底部灰色
        canvas.drawPath(mPath, paintBessel)
        //底部滑块
        canvas.drawCircle(xSlider, ySpacing * 7 + 5f, curveCircleRadius, paintRound)
    }

    private var yMarkMax = 1 //Y轴刻度最大值

    // 指针指示在X轴的哪一个刻度
    private var lastValueIndex = 1

    private var chartHasReallyData = false
    fun setXData(dateList: ArrayList<String>?, wMYType: String) {
        when (wMYType) {
            TimeCode.TIME_CODE_WEEK.timeCode -> {
                barChartType = BarChartType.WEEK
                dateList?.let {
                    xData = it
                }
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                barChartType = BarChartType.MONTH
                dateList?.let {
                    xData = it
                }
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                barChartType = BarChartType.YEAR
                xData = arrayListOf(
                    "01",
                    "02",
                    "03",
                    "04",
                    "05",
                    "06",
                    "07",
                    "08",
                    "09",
                    "10",
                    "11",
                    "12"
                )
            }
        }
        onSizeChanged(this.w, this.h, this.oldw, this.oldh)
        postInvalidate()
    }

    @Suppress("IMPLICIT_CAST_TO_ANY")
    fun setValue(
        hasReallyData: Boolean,
        pressure: MutableList<String>,
        lastValueIndex: Int,
        xTextData: MutableList<String>? = xDataDay,
        outterBarChartType: BarChartType
    ): PressureBarChart {
        chartHasReallyData = hasReallyData
        barChartType = outterBarChartType
        this.lastValueIndex = lastValueIndex
        if (!hasReallyData) {
            if (outterBarChartType == BarChartType.DAY) {
                this.lastValueIndex = 25
            } else if (outterBarChartType == BarChartType.WEEK) {
                this.lastValueIndex = 4
            } else if (outterBarChartType == BarChartType.MONTH) {
                this.lastValueIndex = 16
            } else if (outterBarChartType == BarChartType.YEAR) {
                this.lastValueIndex = 7
            }
        }
        pressureData.clear()
        pressureData.addAll(pressure)
        xData.clear()
        xData.addAll(xTextData ?: xDataDay)
        initBaseUnit()
        startAnimation()
        return this
    }

    private fun startAnimation() {
        anim = ValueAnimator.ofObject(AngleEvaluator(), 0f, 1f)
        anim?.interpolator = AccelerateDecelerateInterpolator()
        anim?.addUpdateListener { animation ->
            mPercent = animation.animatedValue as Float
            postInvalidate()
        }
        anim?.duration = animDuration
        anim?.start()
    }

    private fun drawGradientLine(canvas: Canvas) {
        intervalOneUnit = ySpacing / yScaleStep
        val startY = ySpacing + 21 * intervalOneUnit
        val mLinearGradient = LinearGradient(
            xSlider, startY, xSlider, ySpacing * 6,
            intArrayOf(
                resources.getColor(R.color.indicator_line_start),
                resources.getColor(R.color.indicator_line_center),
                resources.getColor(R.color.indicator_line_start)
            ), null, Shader.TileMode.MIRROR
        )
        val noDataLinearGradient = LinearGradient(
            xSlider, startY, xSlider, ySpacing * 6,
            intArrayOf(
                resources.getColor(R.color.indicator_grey_line_start),
                resources.getColor(R.color.indicator_grey_line_center),
                resources.getColor(R.color.indicator_grey_line_start)
            ), null, Shader.TileMode.MIRROR
        )

        paintGradientLine.shader = if (chartHasReallyData) mLinearGradient else noDataLinearGradient

        if (ySpacing > 0) {
            canvas.drawLine(xSlider, startY, xSlider, ySpacing * 6, paintGradientLine)
        }
    }

    private var onDaySelectListener: ((index: Int, heart: String?) -> Unit)? = null

    fun setOnDaySelectListener(l: ((index: Int, heart: String?) -> Unit)): PressureBarChart {
        this.onDaySelectListener = l
        return this
    }

    private var onXTextSelectListener: ((index: Int, xText: String) -> Unit)? = null

    fun setOnXTextSelectListener(l: ((index: Int, xText: String) -> Unit)): PressureBarChart {
        this.onXTextSelectListener = l
        return this
    }


    private var onDayMoveListener: ((isPre: Boolean) -> Unit)? = null

    fun setOnDayMoveListener(l: ((index: Boolean) -> Unit)): PressureBarChart {
        this.onDayMoveListener = l
        return this
    }
}