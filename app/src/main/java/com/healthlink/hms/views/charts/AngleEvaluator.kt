package com.healthlink.hms.views.charts

import android.animation.TypeEvaluator
import kotlin.math.abs

/**
 * Created by imaginedays on 2024/6/3
 */
class AngleEvaluator : TypeEvaluator<Float> {
    override fun evaluate(fraction: Float, startValue: Float, endValue: Float): Float {
        // Normalize the start and end values
        val start = startValue % 360
        val end = endValue % 360

        // Calculate the shortest path difference
        val diff = end - start

        val shortestDiff = when {
            abs(diff) <= 180 -> diff
            diff > 180 -> diff - 360
            else -> diff + 360
        }

        return start + shortestDiff * fraction
    }
}