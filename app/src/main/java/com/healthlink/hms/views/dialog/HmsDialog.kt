package com.healthlink.hms.views.dialog

import android.app.Activity
import android.app.Dialog
import android.app.UiModeManager
import android.content.Context
import android.content.DialogInterface
import android.graphics.Color
import android.util.Log
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.webkit.WebSettings
import androidx.core.view.WindowCompat
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.ktExt.setUpSystemBar
import com.healthlink.hms.ktExt.translucentStatusBar
import com.healthlink.hms.utils.isDarkModeEnabled

/**
 * 对话框基类，实现打开、关闭对话框的跟踪。
 */
open class HmsDialog: Dialog{

    // 无参数构造函数
    constructor(context: Context) : super(context) {
        init()
    }

    // 接受 Context 的构造函数
    constructor(context: Context, themeResId: Int) : super(context, themeResId) {
        init()
    }

    // 接受 Context 和 Bundle 的构造函数
    constructor(context: Context, cancelable: Boolean, onCancelListener: DialogInterface.OnCancelListener?) : super(context, cancelable, onCancelListener) {
        init()
    }

    private fun init() {
        setUpSystemBar()
    }

    fun setUpSystemBar(){
//        val window = window
        // Build.VERSION.SDK_INT >= 30 windowInsetsControllerCompat 才有值否则为空
        val windowInsetsControllerCompat = WindowCompat.getInsetsController(window!!,window!!.decorView)// ViewCompat.getWindowInsetsController(window.decorView)
        val resources = context.resources
        val lightStatusBar = resources.getBoolean(R.bool.lightStatusBar)
        val lightNavigationBar = resources.getBoolean(R.bool.lightNavigationBar)

        //设置状态栏图标深浅色
        windowInsetsControllerCompat?.isAppearanceLightStatusBars = lightStatusBar

        //设置Dock栏图标深浅色
        windowInsetsControllerCompat?.isAppearanceLightNavigationBars = lightNavigationBar

        //设置状态栏背景色
        window?.statusBarColor = resources.getColor(R.color.statusBarColor, null)

        //设置Dock栏背景色
        window?.navigationBarColor = resources.getColor(R.color.navigationBarColor, null)
        translucentStatusBar()
    }

    fun translucentStatusBar() {
        val window = window
        window?.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        window?.decorView?.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE)
        window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window?.statusBarColor = Color.TRANSPARENT

//    val uiModeManager  = activity.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
//    //设置状态栏文字颜色
//    setStatusBarTextColor(window, uiModeManager.nightMode == UiModeManager.MODE_NIGHT_YES)
    }

    override fun show() {
        super.show()
        setStatusBarColor(false)
        HmsApplication.isDialogShow = true
        Log.d("HmsDialog", "isDialogShow is = ${HmsApplication.isDialogShow}")
    }

    override fun dismiss() {
        super.dismiss()
        setStatusBarColor(true)
        HmsApplication.isDialogShow = false
        Log.d("HmsDialog", "isDialogShow is = ${HmsApplication.isDialogShow}")
    }

    /**
     * 黑夜模式 statusBar 白色 对话框弹出时 statusBar 白色
     * 白天模式 statusBar 黑色 对话框弹出时 statusBar 白色
     */
    private fun setStatusBarColor(isLight : Boolean) {
        val windowInsetsControllerCompat = WindowCompat.getInsetsController(window!!,window!!.decorView)// ViewCompat.getWindowInsetsController(window.decorView)
        if (!isDarkModeEnabled(context)) {
            windowInsetsControllerCompat.isAppearanceLightStatusBars = isLight
        }
    }
}