package com.healthlink.hms.views

import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.util.AttributeSet
import android.util.Log
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ProgressBar
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.ktExt.dp
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.asin
import kotlin.math.cos
import kotlin.math.sin

class CustomProgressBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : ProgressBar(context, attrs, defStyle) {

    private val mTag = "CustomProgressBar"

    private val circlePaint = Paint().apply {
        isAntiAlias = true
        color = context.getColor(R.color.progress_bar_ring)
        style = Paint.Style.FILL
    }

    init {

    }

    private var progressColor = Color.BLUE
    private var backgroundColor = Color.GRAY
    private var outSiteCircleRadius = 22f
    private var innerSiteCircleRadius = 15f
    private var progressStartX = 0F

    public var startColor: Int = Color.parseColor(HmsApplication.appContext.getString(R.string.card_status_normal_bg_color))
    public var middleColor: Int = Color.parseColor(HmsApplication.appContext.getString(R.string.card_status_warning_bg_color))
    public var endColor: Int = Color.parseColor(HmsApplication.appContext.getString(R.string.card_status_danger_bg_color))

    /**
     * 自定义Float类型进度条，精确控制到小数点。
     * 整数的progress会导致进度少时，卡顿现象。
     */
    private var customProgress: Float = 0.0F
    fun setCustomProgress(cp: Float){
        this.customProgress = cp
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {

//        Log.d(mTag, "onDraw customProgress = $customProgress ")

        // 清除之前的路径
        path.reset()
        // 获取视图的宽度和高度
        val width = width
        val height = height
        innerSiteCircleRadius = 0.45F*height
        outSiteCircleRadius = 0.75F*height
        // 内圆半径 = 高度 / 2
        var radius = height / 2F
        val progressBgStartX = radius
        this.progressStartX = progressBgStartX + outSiteCircleRadius
        // 进度条总实际长度 = 总长度 - 2个外圆的半径
        var actualWidth = width - 2 * outSiteCircleRadius - 2 * radius
        // 进度长度 = 进度条总实际长度 * 进度值
        var progressWidth = actualWidth * customProgress/100F

        // 画前半部分

        val progressBgStartY = radius
        drawStart(canvas, progressBgStartX,  progressWidth + outSiteCircleRadius  , progressBgStartY, height.toFloat())
        // 绘制进度圆（圆心位置 = 进度宽读 + 2个外圆半径）
        val circleX = this.progressStartX + progressWidth
        val circleY = radius
        canvas.drawCircle(circleX, circleY, innerSiteCircleRadius , circlePaint)
        // 画后半部分
        var remainProgressWidth = actualWidth - progressWidth
        val remainProgressStartX = circleX
        val remainProgressStartY = radius
        drawEnd(canvas, remainProgressStartX ,  remainProgressWidth, remainProgressStartY , height.toFloat())
    }

    // 可选：设置进度圆的颜色
    fun setProgressColor(color: Int) {
        progressColor = color
        invalidate()
    }

    // 可选：设置背景圆的颜色
    override fun setBackgroundColor(color: Int) {
        backgroundColor = color
        invalidate()
    }

    fun setProgressAnimation(toProgress: Int){
        // 边界值
        var progress = toProgress*1.0F
        if(progress<0){
            progress = 0F
        }else if(progress>100){
            progress = 100F
        }
        // 执行动画
        var oAnimator: ObjectAnimator = ObjectAnimator.ofFloat(this, "customProgress", 0F, progress)
        oAnimator.duration = 1000
        oAnimator.repeatCount = 0
        oAnimator.interpolator = AccelerateDecelerateInterpolator()
        oAnimator.start()
    }

    private val path = Path()

    /**
     * 绘制进度条前半部分
     */
    private fun drawStart(canvas:Canvas, startX: Float, width: Float, startY: Float, height: Float){
        // 设置半径
        val radius = height / 2F
        // 结束点
        val endX = startX + width * 1F
        val endY = height / 2f

        var totalAngle = (2F*asin(height/(2*outSiteCircleRadius))*180/PI).toFloat()
        var startAngle = 270F - (180F-totalAngle)/2F

        // 绘制凸半圆（起始端）
        path.addArc(
            startX - radius, startY - radius,
            startX + radius, startY + radius,
            90f, 180f
        )
        // 连线到凹圆
        var detalX = abs(outSiteCircleRadius * cos(startAngle/180*PI)).toFloat()
        path.lineTo(endX - detalX, endY - radius)
        // 绘制凹圆（1/4的外圆弧度）


        path.addArc(
            endX - outSiteCircleRadius, endY - outSiteCircleRadius,
            endX + outSiteCircleRadius, endY + outSiteCircleRadius,
            startAngle, -totalAngle
        )
        // 连线至凸圆
        path.lineTo(startX, startY + radius)
        // 划线
        var paint = Paint()
        paint.isAntiAlias = true
        paint.style = Paint.Style.FILL
        paint.shader = LinearGradient(0F,0F, endX,endY,
            intArrayOf(startColor,middleColor,endColor),null, Shader.TileMode.REPEAT)
//        canvas.drawPath(path,paint)
    }

    private fun drawEnd(canvas:Canvas, startX: Float, width: Float, y0: Float, height: Float){
        // 设置半径
        val radius = height / 2f
        // 计算中心点
        val startY = height / 2f
        val endX = startX + width + outSiteCircleRadius
        val endY = height / 2f

//        path.reset()
        // 绘制凸半圆（起始端）
        // 画弧
        var totalAngle = (2F*asin(height/(2*outSiteCircleRadius))*180/PI).toFloat()
        var startAngle = (totalAngle)/2F
        path.addArc(
            startX - outSiteCircleRadius, startY - outSiteCircleRadius,
            startX + outSiteCircleRadius, startY + outSiteCircleRadius,
            startAngle, -totalAngle
        )
        // 连线
        path.lineTo(endX , endY - radius)

        path.addArc(
            endX - radius, endY - radius,
            endX + radius, endY + radius,
            -90F, 180f
        )
        // 连线
        var deltaX = outSiteCircleRadius * abs(cos(startAngle/180*PI))
        path.lineTo(startX + deltaX.toFloat() , startY+radius)

        var paint = Paint()
        paint.isAntiAlias = true
        paint.shader = LinearGradient(0F,0F, endX + radius,endY+radius,
            intArrayOf(startColor,middleColor,endColor),null, Shader.TileMode.REPEAT)
        canvas.drawPath(path,paint)
    }
}
