
package com.healthlink.hms.views.SegmentedBarView;

public class Segment {
    private String customText;                           //自定义显示文本
    private String descriptionText;                      //描述信息
    private String topDescriptionText;                   //分段控件上部文字描述
    private int color;                                   //分段控件颜色
    private float minValue = -1;                         //分段控件的起始值（start）
    private float maxValue = -1;                         //分段控件的结束值(end)

    private int gradientStartColor;
    private int gradientEndColor;

    public Segment(float minValue, float maxValue, String descriptionText, int color, int gradientStartColor, int gradientEndColor) {
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.descriptionText = descriptionText;
        this.color = color;
        this.gradientStartColor = gradientStartColor;
        this.gradientEndColor = gradientEndColor;
    }

    public Segment(String customText, String descriptionText, int color, int gradientStartColor, int gradientEndColor) {
        this.customText = customText;
        this.descriptionText = descriptionText;
        this.color = color;
        this.gradientStartColor = gradientStartColor;
        this.gradientEndColor = gradientEndColor;
    }

    public Segment(float minValue, float maxValue, int color, int gradientStartColor, int gradientEndColor) {
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.color = color;
        this.gradientStartColor = gradientStartColor;
        this.gradientEndColor = gradientEndColor;
    }

    public Segment(float minValue, float maxValue, String customText, String descriptionText, int color, int gradientStartColor, int gradientEndColor) {
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.customText = customText;
        this.descriptionText = descriptionText;
        this.color = color;
        this.gradientStartColor = gradientStartColor;
        this.gradientEndColor = gradientEndColor;
    }

    public String getDescriptionText() {
        return descriptionText;
    }

    public int getColor() {
        return color;
    }

    public int getGradientStartColor() {
        return gradientStartColor;
    }

    public int getGradientEndColor() {
        return gradientEndColor;
    }

    public float getMinValue() {
        return minValue;
    }

    public float getMaxValue() {
        return maxValue;
    }

    public String getCustomText() {
        return customText;
    }

    public void setCustomText(String customText) {
        this.customText = customText;
    }

    public Segment setDescriptionText(String descriptionText) {
        this.descriptionText = descriptionText;
        return this;
    }

    public void setColor(int color) {
        this.color = color;
    }

    public void setMinValue(float minValue) {
        this.minValue = minValue;
    }

    public void setMaxValue(float maxValue) {
        this.maxValue = maxValue;
    }

    public String getTopDescriptionText() {
        return topDescriptionText;
    }

    public Segment setTopDescriptionText(String topDescriptionText) {
        this.topDescriptionText = topDescriptionText;
        return this;
    }

    @Override
    public String toString() {
        return "Segment{" +
                "descriptionText='" + descriptionText + '\'' +
                ", color=" + color +
                ", minValue=" + minValue +
                ", maxValue=" + maxValue +
                '}';
    }
}
