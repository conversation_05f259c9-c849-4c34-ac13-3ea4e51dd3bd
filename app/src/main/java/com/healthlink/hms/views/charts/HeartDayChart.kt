package com.healthlink.hms.views.charts

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Point
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import androidx.core.graphics.alpha
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import com.healthlink.hms.utils.ChartTouchEventDelegate
import com.healthlink.hms.utils.ViewDrawUtils
import java.lang.Math.abs
import kotlin.math.roundToInt

/**
 * Created by imaginedays on 2024/6/3
 * 日 - 心率图表
 *
 */
class HeartDayChart(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    private val mTag = "HeartDayChart"

    //屏幕宽高
    private var scrWidth = 0f
    private var scrHeight = 0f
    private var perDp = 0f //1dp所占的值
    private var xData: Array<String> = arrayOf("00:00", "06:00", "12:00", "18:00", "24:00")
    private var yData: Array<Int> = arrayOf(220, 175, 130, 85, 40)
    private var heartData = mutableListOf<Int>()
    private var heartDataMirror = mutableListOf<Int>()
    private var heartDataSplit = mutableListOf<Pair<Int, Int>>() //心率折线图分段索引集合
    private lateinit var paintLine: Paint // y轴线
    private lateinit var paintGradientLine: Paint //指示渐变线
    private lateinit var paintXText: Paint // x轴坐标
    private lateinit var paintYText: Paint // y轴坐标
    private lateinit var paintPolyline: Paint // 心率折线
    private lateinit var paintPolyShadow: Paint // 心率折线阴影
    private lateinit var paintRound: Paint  // 指示滑块圆
    private lateinit var paintBessel: Paint  // 滑块底部
    private val heartRateGapForZero = 4 //空值间隔

    private var w = 0
    private var h = 0
    private var oldw = 0
    private var oldh = 0
    
    private var xSlider = 0f //滑块的x轴位置
    private var ySlider = 80f //滑块的y轴位置
    private var indexSlider = 0 //滑块的y轴位置
    private var mLinePath: Path  //折线路径
    private val curveCircleRadius = 22f.dp
    private var mPath: Path  //滑块贝塞尔

    //第一条曲线的坐标
    private val mFirstCurveStartPoint = Point()
    private val mFirstCurveEndPoint = Point()
    private val mFirstCurveControlPoint1 = Point()
    private val mFirstCurveControlPoint2 = Point()

    //第二条曲线的坐标
    private var mSecondCurveStartPoint = Point()
    private val mSecondCurveEndPoint = Point()
    private val mSecondCurveControlPoint1 = Point()
    private val mSecondCurveControlPoint2 = Point()

    private var isRest = false //是否静息
    private var restHeart = 0 //静息值
    private var isNoData = false

    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        mLinePath = Path()
        mPath = Path()
        initPaint()
    }

    /**
     * 初始化画笔
     */
    private fun initPaint() {
        // Y轴刻度线
        paintLine = Paint()
        paintLine.style = Paint.Style.STROKE
        paintLine.strokeWidth = 1f
        paintLine.color = resources.getColor(R.color.color_y_line)

        // 指示渐变线
        paintGradientLine = Paint()
        paintGradientLine.style = Paint.Style.STROKE
        paintGradientLine.strokeWidth = 2f

        // 折现阴影
        paintPolyShadow = Paint()
        paintPolyShadow.style = Paint.Style.FILL

        // X轴上文字
        paintXText = Paint()
        paintXText.isAntiAlias = true
        paintXText.strokeWidth = 1f
        paintXText.textSize = 22f.sp
        paintXText.textAlign = Paint.Align.CENTER
        paintXText.color =
            resources.getColor(R.color.text_color_fc_40);//context.colorCompat(R.color.color_on_surface)

        // Y轴上文字
        paintYText = Paint()
        paintYText.isAntiAlias = true
        paintYText.textSize = 22f.sp
        paintYText.strokeWidth = 1f
        paintYText.textAlign = Paint.Align.RIGHT
        paintYText.color =
            resources.getColor(R.color.text_color_fc_40)//resources.getColor(R.color.c_666666_808080);//context.colorCompat(R.color.secondary_666666_808080)

        // 心率折线
        paintPolyline = Paint()
        paintPolyline.style = Paint.Style.STROKE
        paintPolyline.strokeWidth = 4f
        paintPolyline.isAntiAlias = true
        paintPolyline.strokeJoin = Paint.Join.ROUND
        paintPolyline.color =
            resources.getColor(R.color.heart_poly_line);//context.colorCompat(R.color.fc355c_fc3159)

        // 指示滑块圆
        paintRound = Paint()
        paintRound.style = Paint.Style.FILL
        paintRound.isAntiAlias = true
        paintRound.color =
            resources.getColor(R.color.slider_round);//context.colorCompat(R.color.ffffff_6e6e6e)

        // 滑块底部 通过shader设置渐变色
        paintBessel = Paint()
        paintBessel.style = Paint.Style.FILL
        paintBessel.isAntiAlias = true
        paintBessel.color =
            resources.getColor(R.color.slider_round_bessel_bg)
    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        this.w = w
        this.h = h
        this.oldh = oldh
        this.oldw = oldw
        scrWidth = width.toFloat()
        scrHeight = height.toFloat()
        ySpacing = scrHeight / 8f //y轴分8份
        perDp = scrHeight / 450
        // 底部圆滑块可以滑动的范围
        xWithStart = margin + paintXText.measureText("0000") / 2
        xWithEnd = scrWidth - xWithStart - paintYText.measureText("0000")
        xTextSpacing = (xWithEnd - xWithStart) / (xData.size - 1)
        xSpacing = xTextSpacing / 72    //一个小时6个值
        if (heartData.isEmpty())
            xSlider = xWithEnd - (xWithEnd - xWithStart) / 2
        else
            xSlider = xSpacing * time + xWithStart
    }

    /**
     * 触控代理处理对象（访问时初始化）
     */
    private val touchEventDelegate by lazy {
        ChartTouchEventDelegate(
            setSliderX = {
                xSlider = it
                invalidate()
            }
        )
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return touchEventDelegate.onTouchEvent(
            event,
            this,
            xSlider,
            xSpacing,
            ySpacing,
            xWithStart,
            xWithEnd,
            heartData.size,
            actionUp = { x, y ->
                when {
                    // 滑动中，不透传事件
                    touchEventDelegate.isSlider -> false
                    // x的变化量超过一个x数据间隔，触发onDayMoveListener，透传事件
                    abs(x - touchEventDelegate.mDownX) > xSpacing -> {
                        onDayMoveListener?.invoke(x > touchEventDelegate.mDownX)
                        true
                    }
                    // x的变化量未超过一个x数据间隔，判断是否新的数据选择，不透传事件
                    else ->
                    {
                        heartData.forEachIndexed { index, hr ->
                            val x = xWithStart + xSpacing * index
                            val dis = abs(x - event.x)
                            if (dis < xSpacing / 2) {
                                xSlider = x
                                //indexSlider = index

                                if (hr > 0) {
                                    var tempHR = hr
                                    onDaySelectListener?.invoke(index, tempHR)
                                    invalidate()
                                }
                                // 如果心率无效，且不属于画线部分的心率，则画 --
                                else if(index< heartData.size && heartData[index]<=0){
                                    var isDrawLine = isDrawLineOfIndex(indexSlider)
                                    if(!isDrawLine){
                                        var tempHR = -1
                                        onDaySelectListener?.invoke(index, tempHR)
                                    }
                                }
                                //  超出数据范围
                                else if(index > heartData.size - 1){
                                    var tempHR = -1
                                    onDaySelectListener?.invoke(index, tempHR)
                                }
                                // 如果不是有效数据，不更新
                                else {
                                    invalidate()
                                    // heartDataMirror[index]
                                }

                                return@forEachIndexed
                            }
                        }
                        // 处理左侧越界问题,需要修正坐标，下发选择事件
                        if(event.x < xWithStart){
                            xSlider = xWithStart
                            doDataSelected(0)
                            invalidate()
                        }

                        // 处理右侧越界问题，需要需要修正坐标，下发选择事件
                        else if(event.x > xWithEnd){
                            xSlider = xWithEnd
                            doDataSelected(heartData.size-1)
                            invalidate()
                        }

                        false
                    }
                }
            },
            onDataSelected = { index ->
                doDataSelected(index)
            }
        )
    }

    private fun doDataSelected(index: Int) {
        if (heartData.isNotEmpty() && index >= 0 && index <= heartData.size - 1) {
            // 选中的下标有数据
            if (heartData[index] > 0) {
                onDaySelectListener?.invoke(index, heartData[index])
                Log.d(mTag, "heartRate is ${heartData[index]}")
            }
            // 有补充数据无实际数据，不更新
            else if (heartDataMirror[index] > 0) {
                Log.d(mTag, "heartDataMirror is ${heartData[index]}")
            }
            // 否则更新为 --
            else {
                Log.d(mTag, "no mirror data,index is  ${index}")
                onDaySelectListener?.invoke(index, -1)
            }
        } else {
            Log.d(mTag, "out of data,index is  ${index}")
            onDaySelectListener?.invoke(index, -1)
        }
    }

    private val margin = 50f.dp//20f.dp //左右两边距离
    private var xWithStart = 0f //x轴的起始点
    private var xWithEnd = 0f  //x轴结束点
    private var ySpacing = 0f //高度分割份数后间距
    private var xSpacing = 0f //x轴柱子分割份数后间距
    private var xTextSpacing = 0f //x轴文字分割份数后间距

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // X轴背景渐变
        drawGradientBackgroundUnderXText(canvas)
        //画y轴方向横线与文字
        drawY(canvas)
        //垂直渐变线
        drawGradientLine(canvas)
        //折线
        drawPolyline(canvas)
        //底部
        drawBessel(canvas)
        //画x轴方向文字
        drawX(canvas)
    }

    private fun drawGradientBackgroundUnderXText(canvas: Canvas) {
        // 画渐变颜色
        ViewDrawUtils.drawGradientBackgroundUnderXText(canvas,context,scrWidth,ySpacing,7)
    }

    /**
     * 绘制X轴
     */
    private fun drawX(canvas: Canvas) {
        xData.forEachIndexed { index, s ->
            val x = xWithStart + xTextSpacing * index
            val dis = abs(x - xSlider)
            var y = ySpacing * 7 - 45f
            if (dis < xTextSpacing / 2) {
                paintXText.typeface = Typeface.DEFAULT_BOLD
                y -= 40f * (1 - dis / xTextSpacing)
                paintXText.color = resources.getColor(R.color.text_color_fc_100)
                onXTextSelectListener?.invoke(index, s)
            } else {
                paintXText.typeface = Typeface.DEFAULT
                paintXText.color = resources.getColor(R.color.text_color_fc_40)

            }
            canvas.drawText(s, x, y, paintXText)
        }
    }

    /**
     * 根据心率分段数据，逐段绘制曲线
     */
    private fun drawPolyline(canvas: Canvas) {
        var x0: Float
        var x1: Float
        var y0: Float
        var y1: Float
        if (isNoData) return
        paintPolyline.color =
            if (isRest) resources.getColor(R.color.c_cccccc_656565) else resources.getColor(R.color.indicator_inner)
        //画折线
        x0 = 0F
        y0 = 0F
        var path = Path()
        var shadowPath = Path()
        // 根据分段数据逐段绘制
        heartDataSplit.forEachIndexed { index, pair ->
            path.reset()
            shadowPath.reset()
            var xE = 0f
            x0 = xWithStart + xSpacing * pair.first
            y0 = ySpacing * 5f - ySpacing * ((heartDataMirror[pair.first] - 40) / 45f)
            path.moveTo(x0, y0)
            if (pair.first == pair.second-1) {
                //心率只有一个点的时候 左侧 或者 只有一个点
                x1 = x0+3f.dp
                y1 = y0
                path.lineTo(x1, y1)
                xE = x1
            } else if(pair.first == pair.second){
                //心率只有一个点的时候 左侧有数据 右侧有一个点的时候
                x1 = x0+3f.dp
                y1 = y0
                path.lineTo(x1, y1)
                xE = x1
            }else {
                for (i in pair.first..pair.second) {
                    x1 = xWithStart + xSpacing * i
                    y1 = 0f
                    if (heartData[i] != 0) {
                        y1 = ySpacing * 5f - ySpacing * ((heartData[i] - 40) / 45f)
                        path.lineTo(x1, y1)
                        xE = x1
                    }

                }
            }
            canvas.drawPath(path, paintPolyline)
            // 绘制心率曲线的阴影
            shadowPath = path
            shadowPath.lineTo(xE, ySpacing * 6)
            shadowPath.lineTo(x0, ySpacing * 6)
            shadowPath.lineTo(x0, y0)
            drawPolyShadow(shadowPath, canvas)
        }
        // 绘制指示圆点 - 如果超过范围则指定为边界点
        indexSlider = ((xSlider - xWithStart) / xSpacing).roundToInt()
        if (indexSlider < 0) {
            indexSlider = 0
        } else if (indexSlider >= heartData.size) {
            indexSlider = heartData.size - 1
        }
        // 判断是否要指示线画圆点
        if (indexSlider < heartDataMirror.size && heartDataMirror[indexSlider] > 0) {
            //计算y坐标：y坐标 = y坐标总高度  - 心率转换坐标 - 40
            ySlider = ySpacing * 5f - ySpacing * ((heartDataMirror[indexSlider] - 40) / 45f)
            val style = paintPolyline.style
            paintPolyline.style = Paint.Style.FILL
            paintPolyline.color =
                resources.getColor(R.color.indicatior_outer)
            canvas.drawCircle(xSlider, ySlider, 10f.dp, paintPolyline)
            paintPolyline.color =
                adjustAlpha(resources.getColor(R.color.indicator_inner))
            canvas.drawCircle(xSlider, ySlider, 6f.dp, paintPolyline)
            paintPolyline.style = style
        }
        //绘制指示线
        if (isRest && restHeart > 0) {
            paintPolyline.color =
                resources.getColor(R.color.indicator_inner);//context.colorCompat(R.color.fc355c_fc3159)
            canvas.drawLine(
                margin,
                ySpacing * 5f - ySpacing * ((restHeart - 40) / 45f),
                scrWidth - margin,
                ySpacing * 5f - ySpacing * ((restHeart - 40) / 45f),
                paintPolyline
            )
        }
    }

    /**
     * 绘制Y坐标轴
     */
    private fun drawY(canvas: Canvas) {
        val y = ySpacing * 5f - ySpacing * ((restHeart - 40) / 45f)
        for (i in 0..4) {
            canvas.drawLine(
                margin, ySpacing * (i + 1), scrWidth - margin,
                ySpacing * (i + 1), paintLine
            )
            paintYText.color =
                resources.getColor(R.color.c_666666_808080);//context.colorCompat(R.color.secondary_666666_808080)
            if (!isRest || abs(y - (ySpacing * (i + 1))) > 25) { //静息值重叠
                canvas.drawText(
                    yData[i].toString(), scrWidth - margin, ySpacing * (i + 1) - 10f, paintYText
                )
            }
        }

        if (isRest && restHeart > 0) {
            paintYText.color =
                resources.getColor(R.color.indicator_inner);//context.colorCompat(R.color.fc355c_fc3159)
            canvas.drawText(restHeart.toString(), scrWidth - margin, y - 10f, paintYText)
        }
    }

    /**
     *  绘制滑块底部
     */
    private fun drawBessel(canvas: Canvas) {
        // 第一条曲线开始点
        mFirstCurveStartPoint[(xSlider - curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()
        // 第一条曲线结束点
        mFirstCurveEndPoint[xSlider.toInt()] =
            (ySpacing * 7 - curveCircleRadius - curveCircleRadius / 4).toInt()
        // 第二条开始点
        mSecondCurveStartPoint = mFirstCurveEndPoint
        mSecondCurveEndPoint[(xSlider + curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()

        // 第一条控制点
        mFirstCurveControlPoint1[(mFirstCurveStartPoint.x + curveCircleRadius + curveCircleRadius / 4).toInt()] =
            mFirstCurveStartPoint.y
        mFirstCurveControlPoint2[(mFirstCurveEndPoint.x - curveCircleRadius * 2 + curveCircleRadius).toInt()] =
            mFirstCurveEndPoint.y
        // 第二条控制点
        mSecondCurveControlPoint1[(mSecondCurveStartPoint.x + curveCircleRadius * 2 - curveCircleRadius).toInt()] =
            mSecondCurveStartPoint.y
        mSecondCurveControlPoint2[(mSecondCurveEndPoint.x - curveCircleRadius - curveCircleRadius / 4).toInt()] =
            mSecondCurveEndPoint.y
        mPath.reset()
        mPath.moveTo(0f, ySpacing * 7)
        mPath.lineTo(mFirstCurveStartPoint.x.toFloat(), mFirstCurveStartPoint.y.toFloat())
        mPath.cubicTo(
            mFirstCurveControlPoint1.x.toFloat(), mFirstCurveControlPoint1.y.toFloat(),
            mFirstCurveControlPoint2.x.toFloat(), mFirstCurveControlPoint2.y.toFloat(),
            mFirstCurveEndPoint.x.toFloat(), mFirstCurveEndPoint.y.toFloat()
        )
        mPath.cubicTo(
            mSecondCurveControlPoint1.x.toFloat(), mSecondCurveControlPoint1.y.toFloat(),
            mSecondCurveControlPoint2.x.toFloat(), mSecondCurveControlPoint2.y.toFloat(),
            mSecondCurveEndPoint.x.toFloat(), mSecondCurveEndPoint.y.toFloat()
        )
        mPath.lineTo(scrWidth, ySpacing * 7)
        mPath.lineTo(scrWidth, scrHeight)
        mPath.lineTo(0f, scrHeight)
        mPath.close()

        //底部灰色
//        paintBessel.shader = LinearGradient(
//            0f,
//            ySpacing * 5f - ySpacing * ((heartData.maxOrNull()?.minus(40))?.div(45f) ?: 0f),
//            0f,
//            ySpacing * 6f,
//            intArrayOf(
//                resources.getColor(R.color.c_start),
//                resources.getColor(R.color.c_end)
//            ),
//            null,
//            Shader.TileMode.MIRROR
//        )
        canvas.drawPath(mPath, paintBessel)
        //底部滑块
        canvas.drawCircle(xSlider, ySpacing * 7 + 5f, curveCircleRadius, paintRound)
    }


    /**
     * 绘制指定Path对应的阴影
     */
    private fun drawPolyShadow(path: Path, canvas: Canvas) {
        val mLinearGradient = LinearGradient(
            0f,
            ySpacing * 5f - ySpacing * ((heartData.maxOrNull()?.minus(40))?.div(45f) ?: 0f),
            0f,
            ySpacing * 6f,
            intArrayOf(
                if (isRest) {
                    resources.getColor(R.color.c_f1f1f1_1e1e1e)
                } else {
                    resources.getColor(R.color.c_fecbd5_400c17)
                },
                resources.getColor(R.color.c_f1f1f1_1e1e1e)
            ),
            null,
            Shader.TileMode.MIRROR
        )
        // resources.getColor(R.color.c_fc355c_fc3159)
        paintPolyShadow.strokeWidth = 0f
        paintPolyShadow.shader = mLinearGradient
        canvas.drawPath(path, paintPolyShadow)
    }

    private var time = 0//当前时间
    fun setValue(value: MutableList<Int>, restHeart: Int, isNoData: Boolean): HeartDayChart {
        this.isNoData = isNoData
        if (!isNoData) {
            this.restHeart = restHeart
            heartData.clear()
            heartDataMirror.clear()
            heartData.addAll(value)

            var preHeartRate = 0
            // 从非0的心率开始，为0的心率计数
            var zeroHeartRateCount = 0
            heartData.forEachIndexed { index, hr ->
                if (hr > 0) {
                    this.heartDataMirror.add(hr)
                    preHeartRate = hr
                    zeroHeartRateCount = 0
                    this.time = index
                } else {
                    zeroHeartRateCount += 1
                    if (zeroHeartRateCount >= heartRateGapForZero || (index == heartData.size - 1)) {
                        this.heartDataMirror.add(0)
                        for (tempIndex in 0..<zeroHeartRateCount) {
                            heartDataMirror[index - tempIndex] = 0
                        }
                    } else {
                        this.heartDataMirror.add(preHeartRate)
                    }
                }
            }
            heartDataSplit = splitHeartData(heartData, heartRateGapForZero)
        }
        onSizeChanged(this.w, this.h, this.oldw, this.oldh)
        postInvalidate()
        return this
    }

    fun setRest(isRest: Boolean) {
        this.isRest = isRest
        postInvalidate()
    }

    private fun drawGradientLine(canvas: Canvas) {
        var mLinearGradient = LinearGradient(
            xSlider, ySpacing, xSlider, ySpacing * 6,
            intArrayOf(
                resources.getColor(R.color.indicator_line_start),
                resources.getColor(R.color.indicator_line_center),
                resources.getColor(R.color.indicator_line_start)
            ), null, Shader.TileMode.MIRROR
        )
        if (isNoData)
            mLinearGradient = LinearGradient(
                xSlider, ySpacing, xSlider, ySpacing * 6,
                intArrayOf(
                    resources.getColor(R.color.indicator_grey_line_start),
                    resources.getColor(R.color.indicator_grey_line_center),
                    resources.getColor(R.color.indicator_grey_line_start)
                ), null, Shader.TileMode.MIRROR
            )
        paintGradientLine.shader = mLinearGradient
        if (ySpacing > 0) {
            canvas.drawLine(xSlider, ySpacing, xSlider, ySpacing * 6, paintGradientLine)
            // 绘制线
            if(indexSlider>=0 && indexSlider< heartData.size) {
                var tempHR = heartData[indexSlider]
                if(tempHR<=0){
                    tempHR = -1
                    var isDrawLine = isDrawLineOfIndex(indexSlider)
                    if(!isDrawLine){
                        onDaySelectListener?.invoke(indexSlider, tempHR)
                    }
                }else {
                    onDaySelectListener?.invoke(indexSlider, tempHR)
                }
            }
        }
    }

    /**
     * 判断心率点是否划线了
     */
    private fun isDrawLineOfIndex(index:Int): Boolean {
        var isDrawLine = false
        heartDataSplit.forEachIndexed { _, pair ->
            if (index >= pair.first && index <= pair.second) {
                isDrawLine = true
                return@forEachIndexed
            }
        }
        return isDrawLine
    }

    private var onDaySelectListener: ((index: Int, heart: Int) -> Unit)? = null

    fun setOnDaySelectListener(l: ((index: Int, heart: Int) -> Unit)): HeartDayChart {
        this.onDaySelectListener = l
        return this
    }

    private var onXTextSelectListener: ((index: Int, xText: String) -> Unit)? = null

    fun setOnXTextSelectListener(l: ((index: Int, xText: String) -> Unit)): HeartDayChart {
        this.onXTextSelectListener = l
        return this
    }

    private var onDayMoveListener: ((isPre: Boolean) -> Unit)? = null

    fun setOnDayMoveListener(l: ((index: Boolean) -> Unit)): HeartDayChart {
        this.onDayMoveListener = l
        return this
    }

    private fun adjustAlpha(color: Int, alpha: Int = 255): Int {
        return Color.argb(alpha, color.red, color.green, color.blue)
    }


    /**
     * 将心率数据分段  依据为连续0的数量和heartRateGapForZero对比
     * 返回值为每一段开始、束所对应心率列表的索引值
     */
    fun splitHeartData(
        heartData: MutableList<Int>,
        minZeroLength: Int
    ): MutableList<Pair<Int, Int>> {
        val segments = mutableListOf<Pair<Int, Int>>()
        var start = 0
        //过滤前面为0的
        for (i in heartData.indices) {
            if (heartData[i] != 0) {
                start = i
                break
            }
        }
        var zeroStart = -1
        var zeroCount = 0
        for (i in start..heartData.size - 1) {
            if (heartData[i] == 0) {
                if (zeroStart == -1) {
                    zeroStart = i
                }
                zeroCount++
            } else {
                if (zeroCount >= minZeroLength) {
                    segments.add(Pair(start, zeroStart - 1))
                    start = i
                }
                zeroStart = -1
                zeroCount = 0
            }
        }
        if (zeroCount > minZeroLength) {
            segments.add(Pair(start, zeroStart - 1))
        } else {
            segments.add(Pair(start, heartData.size - 1))
        }
        return segments
    }


}