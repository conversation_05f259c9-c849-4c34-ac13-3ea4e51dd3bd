package com.healthlink.hms.views

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import com.healthlink.hms.R

/**
 * Created by imaginedays on 2024/6/8
 *
 *
 */
class HMSCircleView : View {
    constructor(context: Context?) : this(context, null) {
    }
    constructor(context: Context?, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(context, attrs)
    }

    private fun init(context: Context?, attrs: AttributeSet?) {
        // 获取自定义属性
        context?.theme?.obtainStyledAttributes(
            attrs,
            R.styleable.HMSCircleView,
            0, 0
        )?.apply {
            try {
                // 设置画笔颜色
                paint.color = getColor(R.styleable.HMSCircleView_circleColor, Color.RED)
            } finally {
                recycle()
            }
        }
    }



    private val paint = Paint().apply {
        color = Color.RED
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val radius = width.coerceAtMost(height) / 2f
        canvas.drawCircle(width / 2f, height / 2f, radius, paint)
    }
}