package com.healthlink.hms.views.charts

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Point
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.Typeface
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import com.healthlink.hms.utils.ChartTouchEventDelegate
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.TimeUtils.getXDataWithStep
import com.healthlink.hms.utils.ViewDrawUtils
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.abs

/**
 *@Author: 付仁秀
 *@Description：
 **/
class BloodPressureChart(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    private val mTag = "BloodPressureChart"

    //屏幕宽高
    private var scrWidth = 0f
    private var scrHeight = 0f
    private var perDp = 0f //1dp所占的值
    private var xData = arrayListOf("00:00", "06:00", "12:00", "18:00", "24:00")
    private var yDataRight: ArrayList<String> =
        arrayListOf("200", "160", "120", "", "40")
    private var highPressureStandard = "140"
    private var lowPressureStandard = "90"
    private var type: Int = 0
    private var xCount = 30 //三种横坐标类型
    private var bloodPressData = emptyList<Pair<Int, Int>?>()
    private lateinit var paintLine: Paint // y轴线
    private lateinit var paintGradientLine: Paint //指示渐变线
    private lateinit var paintXText: Paint // x轴坐标
    private lateinit var paintYText: Paint // y轴坐标
    private lateinit var paintPolyline: Paint // 海拔折线
    private lateinit var paintPolyShadow: Paint // 海拔折线阴影
    private lateinit var paintPillar: Paint //柱子
    private lateinit var paintRound: Paint  // 指示滑块圆
    private lateinit var paintBessel: Paint  // 滑块底部
    private lateinit var paintCircle: Paint  // 底圆
    private var xSlider = 0f //滑块的x轴位置
    private var mLinePath: Path  //折线路径
    private val curveCircleRadius = 22f.dp
    private var mPath: Path  //滑块贝塞尔
    private var isDayType = true
    private var wmyTypeStr = TimeCode.TIME_CODE_DAY.timeCode
    private var w = 0
    private var h = 0
    private var oldw = 0
    private var oldh = 0

    private var prePointPair = Pair(Point(0, 0), Point(0, 0))
    private var iRadiusSelected = 6f.dp
    private var oRadiusSelected = 10f.dp
    private var iRadius = 3f.dp
    private var oRadius = 5f.dp
    private var lineWidth = 3f.dp

    private val hightLineColor = resources.getColor(R.color.high_pressure_100)
    private val highStandardColor = resources.getColor(R.color.high_pressure_80)
    private val highGradientStartColor = resources.getColor(R.color.high_pressure_20)
    private val highGradientEndColor = resources.getColor(R.color.high_pressure_0)
    private val lowLineColor = resources.getColor(R.color.low_pressure_100)
    private val lowStandardColor = resources.getColor(R.color.low_pressure_80)
    private val lowGradientStartColor = resources.getColor(R.color.low_pressure_20)
    private val lowGradientEndColor = resources.getColor(R.color.low_pressure_0)


    //第一条曲线的坐标
    private val mFirstCurveStartPoint = Point()
    private val mFirstCurveEndPoint = Point()
    private val mFirstCurveControlPoint1 = Point()
    private val mFirstCurveControlPoint2 = Point()

    //第二条曲线的坐标
    private var mSecondCurveStartPoint = Point()
    private val mSecondCurveEndPoint = Point()
    private val mSecondCurveControlPoint1 = Point()
    private val mSecondCurveControlPoint2 = Point()

    private var restHeart = 0 //静息值


    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        mLinePath = Path()
        mPath = Path()
        initPaint()
    }

    /**
     * 初始化画笔
     */
    private fun initPaint() {
        // Y轴刻度线
        paintLine = Paint()
        paintLine.style = Paint.Style.STROKE
        paintLine.strokeWidth = 1f
        paintLine.color = resources.getColor(R.color.color_y_line)
        paintLine.isAntiAlias = true

        // 指示渐变线
        paintGradientLine = Paint()
        paintGradientLine.style = Paint.Style.STROKE
        paintGradientLine.strokeWidth = 2f

        // 折现阴影
        paintPolyShadow = Paint()
        paintPolyShadow.style = Paint.Style.FILL

        // X轴上文字
        paintXText = Paint()
        paintXText.isAntiAlias = true
        paintXText.strokeWidth = 1f
        paintXText.textSize = 22f.sp
        paintXText.textAlign = Paint.Align.CENTER
        paintXText.color =
            resources.getColor(R.color.text_color_fc_40);//context.colorCompat(R.color.color_on_surface)

        // Y轴上文字
        paintYText = Paint()
        paintYText.isAntiAlias = true
        paintYText.textSize = 22f.sp
        paintYText.strokeWidth = 1f
        paintYText.textAlign = Paint.Align.RIGHT
        paintYText.color =
            resources.getColor(R.color.text_color_fc_40)//resources.getColor(R.color.c_666666_808080);//context.colorCompat(R.color.secondary_666666_808080)

        // 柱状图
        paintPillar = Paint()
        paintPillar.style = Paint.Style.FILL
        paintPillar.isAntiAlias = true
        paintPillar.color = resources.getColor(R.color.heart_month_poly_line)

        paintCircle = Paint()
        paintCircle.style = Paint.Style.FILL
        paintCircle.isAntiAlias = true


        // 海拔折线
        paintPolyline = Paint()
        paintPolyline.style = Paint.Style.FILL
        paintPolyline.strokeWidth = 4f
        paintPolyline.isAntiAlias = true
        paintPolyline.color =
            resources.getColor(R.color.heart_poly_line);//context.colorCompat(R.color.fc355c_fc3159)

        // 指示滑块圆
        paintRound = Paint()
        paintRound.style = Paint.Style.FILL
        paintRound.isAntiAlias = true
        paintRound.color =
            resources.getColor(R.color.slider_round);//context.colorCompat(R.color.ffffff_6e6e6e)

        // 滑块底部 通过shader设置渐变色
        paintBessel = Paint()
        paintBessel.style = Paint.Style.FILL
        paintBessel.isAntiAlias = true
        paintBessel.color =
            resources.getColor(R.color.slider_round_bessel_bg)
    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        this.w = w
        this.h = h
        this.oldh = oldh
        this.oldw = oldw
        scrWidth = width.toFloat()
        scrHeight = height.toFloat()
        ySpacing = scrHeight / 8f //y轴分8份
        perDp = scrHeight / 450

        // 底部圆滑块可以滑动的范围
        if (wmyTypeStr == TimeCode.TIME_CODE_WEEK.timeCode) {
            xWithStart = margin + paintXText.measureText("0000") / 2
        }
        else if (wmyTypeStr == TimeCode.TIME_CODE_MONTH.timeCode){
            xWithStart = margin + paintXText.measureText("0000") / 2
        }
        else {
            xWithStart = margin + paintXText.measureText("0000") / 2
        }
        xWithEnd = scrWidth - xWithStart - paintYText.measureText(yDataRight[0]) * 1f
        xSpacing = (xWithEnd - xWithStart) / xCount
        if (wmyTypeStr == TimeCode.TIME_CODE_DAY.timeCode) {
            xTextSpacing = (xWithEnd - xWithStart) / 4
        } else {
            xTextSpacing = (xWithEnd - xWithStart) / xCount
        }
        if (!this.bloodPressData.isNullOrEmpty()) {
            var lastIndex = 0
            for (i in bloodPressData.size - 1 downTo 0) {
                if (bloodPressData[i] == null) continue
                if (bloodPressData[i]!!.first != 0) {
                    lastIndex = i
                    break
                }
            }
            if (lastIndex > 0 || bloodPressData[0]?.first != 0) {
                xSlider = xWithStart + xSpacing * lastIndex
            } else {
                xSlider = scrWidth / 2
            }
        } else {
            xSlider = scrWidth / 2
        }
    }

    private val touchEventDelegate by lazy {
        ChartTouchEventDelegate(
            setSliderX = {
                xSlider = it
                invalidate()
            }
        )
    }
    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return touchEventDelegate.onTouchEvent(
            event,
            this,
            xSlider,
            xSpacing,
            ySpacing,
            xWithStart,
            xWithEnd,
            bloodPressData.size,
            actionUp = { x, y ->
                when{
                    touchEventDelegate.isSlider -> false
                    Math.abs(x - touchEventDelegate.mDownX) > xSpacing -> {
                        onDayMoveListener?.invoke(x > touchEventDelegate.mDownX)
                        true
                    }
                    else -> {
                        // 处理左侧越界问题,需要修正坐标，下发选择事件
                        if(event.x < xWithStart){
                            xSlider = xWithStart
                            if(bloodPressData.size>0) {
                                onDaySelectListener?.invoke(0, bloodPressData[0])
                            }
                            invalidate()
                        }

                        // 处理右侧越界问题，需要需要修正坐标，下发选择事件
                        else if(event.x > xWithEnd){
                            xSlider = xWithEnd
                            if(bloodPressData.size>0) {
                                var lastIndex = bloodPressData.size - 1
                                onDaySelectListener?.invoke(
                                    lastIndex, bloodPressData[lastIndex]
                                )
                            }
                            invalidate()
                        }
                        false
                    }
                }
            }
        )
    }

    private val margin = 50f.dp//20f.dp //左右两边距离
    private var xWithStart = 0f //x轴的起始点
    private var xWithEnd = 0f  //x轴结束点
    private var ySpacing = 0f //高度分割份数后间距
    private var xSpacing = 0f //x轴柱子分割份数后间距
    private var xTextSpacing = 0f //x轴文字分割份数后间距

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // X轴背景渐变
        drawGradientBackgroundUnderXText(canvas)
        //画y轴方向横线与文字
        drawY(canvas)
        //垂直渐变线
        drawGradientLine(canvas)
        // 画线上的圆点
        drawPillar(canvas)
        //底部
        drawBessel(canvas)
        //画x轴方向文字
        drawX(canvas)
    }

    private val paint = Paint().apply {
        style = Paint.Style.FILL
    }

    // 颜色和渐变预先计算，避免在 onDraw 中重复生成
    private val gradientColors by lazy {
        intArrayOf(
            resources.getColor(R.color.under_x_text_gr_start),
            resources.getColor(R.color.under_x_text_gr_end)
        )
    }

    // 提前缓存渐变色，减少对象创建
//    private val highPressureGradient by lazy {
//        LinearGradient(
//            scrWidth / 2,
//            ySpacing * 2.5f,
//            scrWidth / 2,
//            ySpacing * 3.75f,
//            highGradientStartColor,
//            highGradientEndColor,
//            Shader.TileMode.CLAMP
//        )
//    }

//    private val lowPressureGradient by lazy {
//        LinearGradient(
//            scrWidth / 2,
//            ySpacing * 3.75f,
//            scrWidth / 2,
//            ySpacing * 5f,
//            lowGradientStartColor,
//            lowGradientEndColor,
//            Shader.TileMode.CLAMP
//        )
//    }

    // 计算矩形区域，避免每次绘制时重新创建对象
    private fun getRectangles(): List<Rect> {
        val firstRect = Rect(0, 231f.dp.toInt(), scrWidth.toInt(), (7 * ySpacing).toInt())
        val secondRect = Rect(
            margin.toInt(),
            (ySpacing * 2.5f + 0.5f).toInt(),
            (scrWidth - margin).toInt(),
            (ySpacing * 3.75f - 0.5f).toInt()
        )
        val thirdRect = Rect(
            margin.toInt(),
            (ySpacing * 3.75f + 0.5f).toInt(),
            (scrWidth - margin).toInt(),
            (ySpacing * 5f).toInt()
        )
        return listOf(firstRect, secondRect, thirdRect)
    }

    private fun drawGradientBackgroundUnderXText(canvas: Canvas) {
        // 获取计算好的矩形
        var rectangles = getRectangles()
        ViewDrawUtils.drawGradientBackgroundUnderXText(canvas,context,scrWidth,ySpacing,7)

        // 根据 type 动态调整高压区间的矩形
        val hY = if (type == 0) ySpacing * 2.5f else ySpacing * 3.5f
        val lY = if (type == 0) ySpacing * 3.75f else ySpacing * 4f + ySpacing / 80 * 10f
        // 高压区矩形
        rectangles[1].set(margin.toInt(), (hY + 0.5f).toInt(), (scrWidth - margin).toInt(), (lY - 0.5f).toInt())
        // 低压区矩形
        rectangles[2].set(margin.toInt(),lY.toInt(),(scrWidth - margin).toInt(),(ySpacing * 5f).toInt())

        var highPressureGradient = LinearGradient(
            scrWidth / 2,
            hY,
            scrWidth / 2,
            lY,
            highGradientStartColor,
            highGradientEndColor,
            Shader.TileMode.CLAMP
        )
        paint.shader = highPressureGradient
        canvas.drawRect(rectangles[1], paint)

        // 绘制低压区间
        val lowPressureGradient by lazy {
            LinearGradient(
                scrWidth / 2,
                lY,
                scrWidth / 2,
                ySpacing * 5f,
                lowGradientStartColor,
                lowGradientEndColor,
                Shader.TileMode.CLAMP
            )
        }
        paint.shader = lowPressureGradient
        canvas.drawRect(rectangles[2], paint)
    }
    
//    private fun drawGradientBackgroundUnderXText(canvas: Canvas) {
//        // 画渐变颜色
//        var myRect = Rect(0, 231f.dp.toInt(), scrWidth.toInt(), 7 * ySpacing.toInt())
//        var paint = Paint()
//        var lg = LinearGradient(
//            scrWidth / 2,
//            myRect.top.toFloat(),
//            scrWidth / 2,
//            myRect.bottom.toFloat(),
//            intArrayOf(
//                resources.getColor(R.color.under_x_text_gr_start),
//                resources.getColor(R.color.under_x_text_gr_end)
//            ),
//            null,
//            Shader.TileMode.CLAMP
//        )
//        var hY = ySpacing * 2.5f
//        var lY = ySpacing * 3.75f
//        if (type == 0) {
//            hY = ySpacing * 2.5f
//            lY = ySpacing * 3.75f
//        } else {
//            hY = ySpacing * 3.5f
//            lY = ySpacing * 4f + ySpacing / 80 * 10f
//        }
//
//
//        paint.shader = lg
//        paint.style = Paint.Style.FILL
//        canvas.drawRect(myRect, paint)
//        myRect = Rect(
//            margin.toInt(),
//            (hY + 0.5f).toInt(),
//            (scrWidth - margin).toInt(),
//            (lY - 0.5f).toInt()
//        )
//        lg = LinearGradient(
//            scrWidth / 2,
//            myRect.top.toFloat(),
//            scrWidth / 2,
//            myRect.bottom.toFloat(),
//            intArrayOf(
//                highGradientStartColor,
//                highGradientEndColor
//            ),
//            null,
//            Shader.TileMode.CLAMP
//        )
//        paint.shader = lg
//        canvas.drawRect(myRect, paint)
//
//        myRect = Rect(
//            margin.toInt(),
//            (lY + 0.5f).toInt(),
//            (scrWidth - margin).toInt(),
//            (ySpacing * 5f).toInt()
//        )
//        lg = LinearGradient(
//            scrWidth / 2,
//            myRect.top.toFloat(),
//            scrWidth / 2,
//            myRect.bottom.toFloat(),
//            intArrayOf(
//                lowGradientStartColor,
//                lowGradientEndColor
//            ),
//            null,
//            Shader.TileMode.CLAMP
//        )
//        paint.shader = lg
//        canvas.drawRect(myRect, paint)
//    }


    private fun drawX(canvas: Canvas) {
        xData.forEachIndexed { index, s ->
            val x = xWithStart + xTextSpacing * index
            val dis = Math.abs(x - xSlider)
            var y = ySpacing * 7 - 45f
            if (dis < xTextSpacing / 2) {
                paintXText.typeface = Typeface.DEFAULT_BOLD
//                y -= 40f * (1 - dis / xTextSpacing)
                if (wmyTypeStr == TimeCode.TIME_CODE_YEAR.timeCode && index == 0) {
                    y -= ((30f.dp - 22.sp) * (1 - dis / xTextSpacing))
                } else {
                    y -= 30f.dp * (1 - dis / xTextSpacing)
                }
                paintXText.color = resources.getColor(R.color.text_color_fc_100)
                onXTextMoveListener?.invoke(index, s)
            } else {
                paintXText.typeface = Typeface.DEFAULT
                paintXText.color = resources.getColor(R.color.text_color_fc_40)

            }
            if (wmyTypeStr == TimeCode.TIME_CODE_YEAR.timeCode) {
                if (index == 0) {
                    val tp = TextPaint()
                    tp.color = paintXText.color
                    tp.style = paintXText.style
                    tp.textSize = paintXText.textSize
                    tp.typeface = paintXText.typeface
                    tp.isAntiAlias = true
                    tp.strokeWidth = 1f
                    val point = Point(x.toInt()+13.dp.toInt(), (y.toInt() - 20.dp).toInt())
                    textCenter(
                        s,
                        tp,
                        canvas,
                        point,
                        100,
                        Layout.Alignment.ALIGN_NORMAL,
                        0.8f,
                        0f,
                        false
                    )
                } else {
                    canvas.drawText(s, x, y, paintXText)
                }
            } else {
                canvas.drawText(s, x, y, paintXText)
            }
        }
    }
    private fun textCenter(
        string: String, textPaint: TextPaint, canvas: Canvas, point: Point, width: Int,
        align: Layout.Alignment, spacingmult: Float, spacingadd: Float, includepad: Boolean
    ) {
        val staticLayout =
            StaticLayout(string, textPaint, width, align, spacingmult, spacingadd, includepad)
        canvas.save()
        canvas.translate(
            (-staticLayout.width / 2 + point.x).toFloat(),
            (-staticLayout.height / 2 + point.y).toFloat()
        )
        staticLayout.draw(canvas)
        canvas.restore()
    }

    private fun drawY(canvas: Canvas) {
        val y = ySpacing * 5f - ySpacing * ((restHeart - 40) / 45f)
        paintLine.strokeWidth = 1f
        paintLine.color = resources.getColor(R.color.color_y_line)
        for (i in 0..4) {
            canvas.drawLine(
                margin, ySpacing * (i + 1), scrWidth - margin,
                ySpacing * (i + 1), paintLine
            )
            paintYText.color =
                resources.getColor(R.color.c_666666_808080);//context.colorCompat(R.color.secondary_666666_808080)

            canvas.drawText(
                yDataRight[i], scrWidth - margin, ySpacing * (i + 1) - 8f, paintYText
            )

        }
        var hY = ySpacing * 2.5f
        var lY = ySpacing * 3.75f
        if (type == 0) {
            hY = ySpacing * 2.5f
            lY = ySpacing * 3.75f
        } else {
            hY = ySpacing * 3.5f
            lY = ySpacing * 4f + ySpacing / 80 * 7f
        }

        paintLine.color = highStandardColor
        canvas.drawLine(
            margin, hY, scrWidth - margin,
            hY, paintLine
        )

        paintYText.color = hightLineColor
        canvas.drawText(
            highPressureStandard, scrWidth - margin, hY - 5f, paintYText
        )

        paintLine.color = lowStandardColor
        canvas.drawLine(
            margin, lY, scrWidth - margin,
            lY, paintLine
        )

        paintYText.color = lowLineColor
        if (type==0)
        canvas.drawText(
            lowPressureStandard, scrWidth - margin, lY - 5f, paintYText
        )
        else
            canvas.drawText(
                lowPressureStandard, scrWidth - margin, lY + 22f.sp, paintYText
            )

    }

    // 绘制滑块底部
    private fun drawBessel(canvas: Canvas) {
        // 第一条曲线开始点
        mFirstCurveStartPoint[(xSlider - curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()
        // 第一条曲线结束点
        mFirstCurveEndPoint[xSlider.toInt()] =
            (ySpacing * 7 - curveCircleRadius - curveCircleRadius / 4).toInt()
        // 第二条开始点
        mSecondCurveStartPoint = mFirstCurveEndPoint
        mSecondCurveEndPoint[(xSlider + curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()

        // 第一条控制点
        mFirstCurveControlPoint1[(mFirstCurveStartPoint.x + curveCircleRadius + curveCircleRadius / 4).toInt()] =
            mFirstCurveStartPoint.y
        mFirstCurveControlPoint2[(mFirstCurveEndPoint.x - curveCircleRadius * 2 + curveCircleRadius).toInt()] =
            mFirstCurveEndPoint.y
        // 第二条控制点
        mSecondCurveControlPoint1[(mSecondCurveStartPoint.x + curveCircleRadius * 2 - curveCircleRadius).toInt()] =
            mSecondCurveStartPoint.y
        mSecondCurveControlPoint2[(mSecondCurveEndPoint.x - curveCircleRadius - curveCircleRadius / 4).toInt()] =
            mSecondCurveEndPoint.y
        mPath.reset()
        mPath.moveTo(0f, ySpacing * 7)
        mPath.lineTo(mFirstCurveStartPoint.x.toFloat(), mFirstCurveStartPoint.y.toFloat())
        mPath.cubicTo(
            mFirstCurveControlPoint1.x.toFloat(), mFirstCurveControlPoint1.y.toFloat(),
            mFirstCurveControlPoint2.x.toFloat(), mFirstCurveControlPoint2.y.toFloat(),
            mFirstCurveEndPoint.x.toFloat(), mFirstCurveEndPoint.y.toFloat()
        )
        mPath.cubicTo(
            mSecondCurveControlPoint1.x.toFloat(), mSecondCurveControlPoint1.y.toFloat(),
            mSecondCurveControlPoint2.x.toFloat(), mSecondCurveControlPoint2.y.toFloat(),
            mSecondCurveEndPoint.x.toFloat(), mSecondCurveEndPoint.y.toFloat()
        )
        mPath.lineTo(scrWidth, ySpacing * 7)
        mPath.lineTo(scrWidth, scrHeight)
        mPath.lineTo(0f, scrHeight)
        mPath.close()

        canvas.drawPath(mPath, paintBessel)
        //底部滑块
        canvas.drawCircle(xSlider, ySpacing * 7 + 5f, curveCircleRadius, paintRound)
    }

    private fun drawPillar(canvas: Canvas) {
        if (bloodPressData.isNullOrEmpty())
            return
        var highY = 200
        var yP = ySpacing / 40f
        if (type == 1) {
            highY = 340
            yP = ySpacing / 80f
        }
        bloodPressData.forEachIndexed { index, pair ->
            if (pair == null) return@forEachIndexed
            if (pair.first == 0 && pair.second == 0) return@forEachIndexed
            val x = xWithStart + xSpacing * index
            val y1 = (highY - pair.first) * yP + ySpacing
            val y2 = (highY - pair.second) * yP + ySpacing
            //连接线
            if (prePointPair.first.x != 0) {
                //高压first 低压second
                val highPoint = prePointPair.first
                var lowPoint = prePointPair.second
                paintLine.strokeWidth = lineWidth
                paintLine.color = hightLineColor
                canvas.drawLine(highPoint.x.toFloat(), highPoint.y.toFloat(), x, y1, paintLine)
                paintLine.color = lowLineColor
                canvas.drawLine(lowPoint.x.toFloat(), lowPoint.y.toFloat(), x, y2, paintLine)
            }
            if (index != bloodPressData.size - 1) {
                prePointPair = Pair(Point(x.toInt(), y1.toInt()), Point(x.toInt(), y2.toInt()))
            } else {
                prePointPair = Pair(Point(0, 0), Point(0, 0))
            }


        }
        prePointPair = Pair(Point(0, 0), Point(0, 0))
        var forCount = xCount
        if (this.wmyTypeStr == TimeCode.TIME_CODE_YEAR.timeCode) {
            forCount = 12
        }
        for (index in 0..< forCount) {
            val x = xWithStart + xSpacing * index
            if (index < bloodPressData.size) {
                var pair = bloodPressData[index]
                var isSelected =
                    xSlider < xWithStart + xSpacing * index + xSpacing / 2 && xSlider > xWithStart + xSpacing * index - xSpacing / 2
                if (pair != null && pair.first != 0) {
                    val y1 = (highY - pair.first) * yP + ySpacing
                    val y2 = (highY - pair.second) * yP + ySpacing
                    if (isSelected) {
                        onDaySelectListener?.invoke(index, pair)
                    }
                    if (isSelected) {
                        paintPillar.color = resources.getColor(R.color.out_circle)
                        canvas.drawCircle(x, y1, oRadiusSelected, paintPillar)
                        canvas.drawCircle(x, y2, oRadiusSelected, paintPillar)
                        canvas.save()
                        paintPillar.color = hightLineColor
                        canvas.drawCircle(x, y1, iRadiusSelected, paintPillar)
                        paintPillar.color = lowLineColor
                        canvas.drawCircle(x, y2, iRadiusSelected, paintPillar)
                    } else {
                        paintPillar.color = resources.getColor(R.color.out_circle)
                        canvas.drawCircle(x, y1, oRadius, paintPillar)
                        canvas.drawCircle(x, y2, oRadius, paintPillar)
                        paintPillar.color = hightLineColor
                        canvas.drawCircle(x, y1, iRadius, paintPillar)
                        paintPillar.color = lowLineColor
                        canvas.drawCircle(x, y2, iRadius, paintPillar)
                    }

                } else {
                    if (isSelected) {
                        onDaySelectListener?.invoke(index, null)
                    }
                }
            } else {
                var isSelected =
                    xSlider < xWithStart + xSpacing * index + xSpacing / 2 && xSlider > xWithStart + xSpacing * index - xSpacing / 2
                if (isSelected) {
                    onDaySelectListener?.invoke(index, null)
                }
            }
        }
    }

    fun setXData(dateList: ArrayList<String>?, wMYType: String) {
        this.wmyTypeStr = wMYType
        when (wMYType) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                xCount = 24
                xData = arrayListOf("00:00", "06:00", "12:00", "18:00", "24:00")
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                dateList?.let {
                    xCount = it.size - 1
                    xData = it
                }
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                dateList?.let {
                    xCount = it.size - 1
                    xData = TimeUtils.getXDataWithStep(it, 7)
                }
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                xCount = 11
                xData = arrayListOf(
                    "${TimeUtils.getCurrentYearStr()}\n01",
                    "02",
                    "03",
                    "04",
                    "05",
                    "06",
                    "07",
                    "08",
                    "09",
                    "10",
                    "11",
                    "12"
                )
            }
        }
        onSizeChanged(this.w, this.h, this.oldw, this.oldh)
        postInvalidate()
    }

    fun setValue(
        value: MutableList<Pair<Int, Int>?>?,
        xStringList: ArrayList<String>?,
        wMYType: String,
        type: Int
    ): BloodPressureChart {
        this.wmyTypeStr = wMYType
        this.type = type
        if (value!=null){
            this.bloodPressData = value as ArrayList<Pair<Int, Int>>
            if (type == 1) {
                yDataRight = arrayListOf("340", "260", "180", "", "20")
            }
        }
        when (wMYType) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                this.xCount = 48
                xData = arrayListOf("00:00", "06:00", "12:00", "18:00", "24:00")
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {

                xStringList?.let {
                    this.xCount = it.size - 1
                    xData = it
                }

            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                xStringList?.let {
                    this.xCount = it.size - 1
                    xData = getXDataWithStep(it, 7)
                }
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                this.xCount = 11
                xData = arrayListOf(
                    "${TimeUtils.getCurrentYearStr()}\n01",
                    "02",
                    "03",
                    "04",
                    "05",
                    "06",
                    "07",
                    "08",
                    "09",
                    "10",
                    "11",
                    "12"
                )
            }
        }
        onSizeChanged(this.w, this.h, this.oldw, this.oldh)
        postInvalidate()
        return this
    }


    private fun drawGradientLine(canvas: Canvas) {
        var mLinearGradient = LinearGradient(
            xSlider, ySpacing, xSlider, ySpacing * 6,
            intArrayOf(
                resources.getColor(R.color.indicator_grey_line_start),
                resources.getColor(R.color.indicator_grey_line_center),
                resources.getColor(R.color.indicator_grey_line_start)
            ), null, Shader.TileMode.MIRROR
        )
        if (bloodPressData.isNullOrEmpty())
            mLinearGradient = LinearGradient(
                xSlider, ySpacing, xSlider, ySpacing * 6,
                intArrayOf(
                    resources.getColor(R.color.indicator_grey_line_start),
                    resources.getColor(R.color.indicator_grey_line_center),
                    resources.getColor(R.color.indicator_grey_line_start)
                ), null, Shader.TileMode.MIRROR
            )
        paintGradientLine.shader = mLinearGradient

        if (ySpacing > 0) {
            canvas.drawLine(xSlider, ySpacing, xSlider, ySpacing * 6, paintGradientLine)
        }
    }

    private var onDaySelectListener: ((index: Int, bloodPressure: Pair<Int, Int>?) -> Unit)? = null

    fun setOnDaySelectListener(l: ((index: Int, bloodPressure: Pair<Int, Int>?) -> Unit)): BloodPressureChart {
        this.onDaySelectListener = l
        return this
    }

    private var onXTextMoveListener:  ((index: Int,xText: String) -> Unit)? = null

    fun setOnXTextSelectListener(l: ((index: Int, xText: String) -> Unit)): BloodPressureChart {
        this.onXTextMoveListener = l
        return this
    }

    private var onDayMoveListener: ((isPre: Boolean) -> Unit)? = null

    fun getDate(ori: String): String {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val dateTime = LocalDateTime.parse(ori, formatter)
        return String.format("%02d", dateTime.dayOfMonth)
    }


}