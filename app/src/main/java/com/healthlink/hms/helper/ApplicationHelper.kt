package com.healthlink.hms.helper

import android.util.Log
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.core.common.utils.Constants
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.core.common.utils.SystemPropertyUtils
import com.healthlink.hms.core.data.repository.InitRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@Singleton
class ApplicationHelper @Inject constructor(
    private val initRepository: InitRepository
) {
    suspend fun sendInitInfoReq(
        reqFrom: String = "HmsApplication",
        onResult: (Boolean) -> Unit
    ) = withContext(Dispatchers.IO) {
        val vin = SystemPropertyUtils.getSystemPropertyString(Constants.KEY_VECHILE_VIN, Constants.DEFAULT_VECHILE_VIN)
        MMKVUtil.storeVinCode(vin)
        val params = mutableMapOf(
            "vin" to vin,
            "sn" to SystemPropertyUtils.getSystemPropertyString(Constants.VECHILE_SN, "")
        )
        MMKVUtil.getUserId()?.let { params["userId"] = it }

        try {
            initRepository.getInitInfo(params).collect { result ->
                result.fold(
                    onSuccess = { response ->
                        response.data?.let {
                            HmsApplication.isInitSuccess = true
                            MMKVUtil.storeUserId(it.userId)
                            // 是否备案电话医生
                            MMKVUtil.setBindDoctorService(it.isPhoneDoctorMember)
                            withContext(Dispatchers.Main) {
                                onResult(true)
                            }
                        }
                        Log.d(HmsApplication.TAG, "$reqFrom 获取初始化信息成功")
                    },
                    onFailure = { e ->
                        HmsApplication.isInitSuccess = false
                        withContext(Dispatchers.Main) {
                            onResult(false)
                        }
                        Log.i(HmsApplication.TAG, "$reqFrom 获取初始化信息失败: ${e.message}")
                    }
                )
            }
        } catch (e: Exception) {
            HmsApplication.isInitSuccess = false
            withContext(Dispatchers.Main) {
                onResult(false)
            }
            Log.e(HmsApplication.TAG, "$reqFrom 获取初始化信息异常: ${e.message}")
        }
    }
} 