package com.healthlink.hms.activity

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanFilter
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import com.healthlink.hms.R
import com.healthlink.hms.utils.ToastUtil


class BlueToothManagerActivity : HMSBaseActivity() {

    private lateinit var bluetoothAdapter : BluetoothAdapter
    private lateinit var mContext : Context

    private lateinit var tvScanBTLog : TextView
    private var btScanLog : String = ""

    private var REQUEST_ENABLE_BT = 1021

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_blue_tooth_manager)
        this.tvScanBTLog = findViewById(R.id.bt_scan_log)

        val bluetoothManager = getSystemService(BLUETOOTH_SERVICE) as BluetoothManager
        bluetoothAdapter = bluetoothManager.adapter

        if (bluetoothAdapter == null) {
            // 设备不支持蓝牙
            ToastUtil.makeText(mContext , "设备不支持蓝牙", Toast.LENGTH_SHORT).show()
            btScanLog += "蓝牙模块检查：此设备不支持蓝牙。\r\n"
            tvScanBTLog.text = btScanLog
            Log.i("BT Scan", "蓝牙模块检查：此设备不支持蓝牙...")
        }else{
            btScanLog += "蓝牙模块检查：此设备支持蓝牙模块。\r\n"
            tvScanBTLog.text = btScanLog
            Log.i("BT Scan", "蓝牙模块检查：此设备支持蓝牙模块...")
        }

        if (!bluetoothAdapter.isEnabled) {
            // 请求开启蓝牙
            btScanLog += "蓝牙模块是否开启检查：蓝牙模块未开启。\r\n"
            tvScanBTLog.text = btScanLog
            Log.i("BT Scan", "蓝牙模块是否开启检查：蓝牙模块未开启。...")

            val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            if (ActivityCompat.checkSelfPermission(
                    this,
                    Manifest.permission.BLUETOOTH_CONNECT
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                // TODO: Consider calling
                //    ActivityCompat#requestPermissions
                // here to request the missing permissions, and then overriding
                //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                //                                          int[] grantResults)
                // to handle the case where the user grants the permission. See the documentation
                // for ActivityCompat#requestPermissions for more details.
                ToastUtil.makeText(mContext , "未授权蓝牙链接，请授权", Toast.LENGTH_SHORT).show()
                btScanLog += "蓝牙链接授权检查：未授权蓝牙链接，请授权。\r\n"
                tvScanBTLog.text = btScanLog
                Log.i("BT Scan", "蓝牙链接授权检查：未授权蓝牙链接，请授权..")
                var permissions = arrayOf(Manifest.permission.BLUETOOTH)
                ActivityCompat.requestPermissions(this,  permissions, 1)
//                return
            }else{
                btScanLog += "蓝牙链接授权检查：已授权。\r\n"
                tvScanBTLog.text = btScanLog
                Log.i("BT Scan", "蓝牙链接授权检查：已授权。")
            }

            startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT)
        }else{
            btScanLog += "蓝牙模块是否开启检查：蓝牙模块已开启。\r\n"
            tvScanBTLog.text = btScanLog
            Log.i("BT Scan", "蓝牙模块是否开启检查：蓝牙模块已开启。")
        }

        val scanner = bluetoothAdapter.bluetoothLeScanner
        val settings = ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
            .build()
        val filter: ScanFilter = ScanFilter.Builder().build()
//        filter.s(ScanFilter.SCAN_MODE_LOW_LATENCY)

        if(ActivityCompat.checkSelfPermission(
            this,
            Manifest.permission.BLUETOOTH_CONNECT
        ) != PackageManager.PERMISSION_GRANTED){
            Log.i("BT Scan", "不具备蓝牙扫描权限...")
            btScanLog += "已经开始蓝牙扫描...\r\n"
            tvScanBTLog.text = btScanLog
        }else{
            scanner.startScan(listOf(filter),settings,scanCallback)
            Log.i("BT Scan", "具备蓝牙扫描权限，已经开始蓝牙扫描...")
            btScanLog += "已经开始蓝牙扫描...\r\n"
            tvScanBTLog.text = btScanLog
        }
    }

    /**
     * 扫描回调函数
     */
    private val scanCallback: ScanCallback = object : ScanCallback() {
        override fun onScanResult(callbackType: Int, result: ScanResult) {
            super.onScanResult(callbackType, result)
            ToastUtil.makeText(mContext, "蓝牙链接", Toast.LENGTH_SHORT).show()
            btScanLog += "扫描到蓝牙设备...\r\n"
            tvScanBTLog.text = btScanLog

            val device: BluetoothDevice = result.device
           if (ActivityCompat.checkSelfPermission(
                    mContext,
                    Manifest.permission.BLUETOOTH_CONNECT
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                // TODO: Consider calling
                //    ActivityCompat#requestPermissions
                // here to request the missing permissions, and then overriding
                //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                //                                          int[] grantResults)
                // to handle the case where the user grants the permission. See the documentation
                // for ActivityCompat#requestPermissions for more details.
                return
            }else{
               val deviceName = device.name
           }
            val deviceAddress = device.address
            // 处理设备信息，例如显示在列表中
        }

        override fun onBatchScanResults(results: List<ScanResult?>?) {
            super.onBatchScanResults(results)
            // 处理一批扫描结果
            Log.i("BT Scan", "获得批量的蓝牙设备")
            btScanLog += "批量扫描到蓝牙设备...\r\n"
            tvScanBTLog.text = btScanLog
        }

        override fun onScanFailed(errorCode: Int) {
            super.onScanFailed(errorCode)
            // 处理扫描失败的情况
            Log.i("BT Scan", "扫描蓝牙失败")
            btScanLog += "扫描蓝牙设备失败...\r\n"
            tvScanBTLog.text = btScanLog
        }
    }
}