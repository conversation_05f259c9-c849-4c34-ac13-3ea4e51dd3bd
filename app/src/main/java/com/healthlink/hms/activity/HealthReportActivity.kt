package com.healthlink.hms.activity


import android.os.Bundle
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.activity.card.HMSCardFragmentInteractWithAcInterface
import com.healthlink.hms.fragment.ReportDayFragment
import com.healthlink.hms.utils.DataTrackUtil


class HealthReportActivity :  HMSBaseCardDetailActivity() , HMSCardFragmentInteractWithAcInterface {
    override fun initFragments() {
        fragmentList.add(ReportDayFragment.newInstance(TimeCode.TIME_CODE_DAY, userId,this))
        fragmentList.add(ReportDayFragment.newInstance(TimeCode.TIME_CODE_WEEK, userId,this))
        fragmentList.add(ReportDayFragment.newInstance(TimeCode.TIME_CODE_MONTH, userId,this))
        fragmentList.add(ReportDayFragment.newInstance(TimeCode.TIME_CODE_YEAR, userId,this))
    }


    override fun getCardAuthStatus(): Boolean {
        return true
    }

    override fun setTabVisibilityforNetErrorOrSettingView(visibility: Int) {
        binding.tabLayout.visibility = visibility
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        DataTrackUtil.dtEnterPage("Health_Healthreports_PV", DataTrackUtil.userIDMap(userId))
    }
    override fun onDestroy() {
        super.onDestroy()
        DataTrackUtil.dtExitPage("Health_Healthreports_Close", DataTrackUtil.userIDMap(userId))
    }
    override fun backToMain() {
        DataTrackUtil.dtClick(
            "Health_Healthreports_Return_Click",
            DataTrackUtil.userIDMap(userId)
        )
        super.backToMain()
    }
}