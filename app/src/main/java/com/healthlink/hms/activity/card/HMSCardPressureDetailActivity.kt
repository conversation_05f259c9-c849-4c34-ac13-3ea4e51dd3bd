package com.healthlink.hms.activity.card

import android.os.Bundle
import com.healthlink.hms.activity.HMSBaseCardDetailActivity
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.fragment.CardPressureFragment
import com.healthlink.hms.utils.DataTrackUtil

/**
 * Created by imaginedays on 2024/6/24
 * 压力二级图表
 */
class HMSCardPressureDetailActivity : HMSBaseCardDetailActivity(),HMSCardFragmentInteractWithAcInterface {
    override fun initFragments() {
        fragmentList.add(CardPressureFragment.newInstance(TimeCode.TIME_CODE_DAY, userId, this))
        fragmentList.add(CardPressureFragment.newInstance(TimeCode.TIME_CODE_WEEK, userId, this))
        fragmentList.add(CardPressureFragment.newInstance(TimeCode.TIME_CODE_MONTH, userId, this))
        fragmentList.add(CardPressureFragment.newInstance(TimeCode.TIME_CODE_YEAR, userId, this))
    }

    override fun getCardAuthStatus(): Boolean {
        return true
    }

    override fun setTabVisibilityforNetErrorOrSettingView(visibility: Int) {
        binding.tabLayout.visibility = visibility
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        DataTrackUtil.dtEnterPage("Health_Stressreports_PV",DataTrackUtil.userIDMap(userId))
    }

    override fun onDestroy() {
        DataTrackUtil.dtExitPage("Health_Stressreports_Close",DataTrackUtil.userIDMap(userId))
        super.onDestroy()
    }

    override fun backToMain() {
        DataTrackUtil.dtClick(
            "Health_Stressreports_Return_Click",
            DataTrackUtil.userIDMap(userId)
        )
        super.backToMain()
    }
}