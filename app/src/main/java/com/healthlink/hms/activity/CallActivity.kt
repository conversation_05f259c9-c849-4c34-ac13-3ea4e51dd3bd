package com.healthlink.hms.activity

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.healthlink.hms.base.Constants
import com.healthlink.hms.utils.NotificationUtil

/**
 * Created by imaginedays on 2024/7/27
 *
 *
 */
class CallActivity : AppCompatActivity()  {
    private val REQUEST_CALL_PHONE = 1
    private var phoneNumber: String? = null
    private var notificationId: Int? = null
    private var cancelNotification: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        phoneNumber = intent.getStringExtra("PHONE_NUMBER")
        notificationId = intent.getIntExtra(Constants.NOTIFICATION_ID_PHONE_DOCTOR_SERVICE,0)
        if (phoneNumber != null) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.CALL_PHONE), REQUEST_CALL_PHONE)
            } else {
                makePhoneCall()
            }
        } else {
            finish() // 没有电话号码，直接关闭活动
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_CALL_PHONE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                makePhoneCall()
            } else {
                // 权限被拒绝，处理相应的逻辑
            }
        }
        finish()
    }

    private fun makePhoneCall() {
        phoneNumber?.let {
            val intent = Intent(Intent.ACTION_CALL, Uri.parse("tel:$phoneNumber"))
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.CALL_PHONE) == PackageManager.PERMISSION_GRANTED) {
                startActivity(intent)
            }
        }
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        notificationId?.let {
            closeNotification(Constants.NOTIFICATION_ID_PHONE_DOCTOR_SERVICE, this)
        }
    }

    /**
     * 关闭通知
     * @param notificationIdSharedPrefKey 存在sharedPreferences中的通知id
     */
    private fun closeNotification(notificationIdSharedPrefKey: String, context: Context?) {
        if (context != null) {
            var notificationId  = context.getSharedPreferences("hms.data", AppCompatActivity.MODE_PRIVATE)
                .getInt(notificationIdSharedPrefKey, 0 )
            NotificationUtil(context).cancelNotification(notificationId)
        }
    }
}