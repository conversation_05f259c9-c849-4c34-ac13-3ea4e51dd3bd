package com.healthlink.hms.activity

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Layout
import android.util.Log
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.databinding.ActivityHmsupgradeDialogBinding
import com.healthlink.hms.ktExt.setUpStatusBar
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.viewmodels.MainViewModel
import com.hieupt.android.standalonescrollbar.attachTo

class HMSUpgradeDialogActivity :
    BaseVBVMActivity<ActivityHmsupgradeDialogBinding, MainViewModel>() {

    private val TAG = "HMSUpgradeDialogActivity"
    private lateinit var mainViewModel: MainViewModel
    private var contentMinLines = 2  //更新内容最小行数设置
    private var contentMaxLines = 9  //更新内容最大行数设置
    override fun getLayoutId(): Int {
        return R.layout.activity_hmsupgrade_dialog
    }

    private var isForceUpgrade = false

    override fun createViewModel(): MainViewModel {
        return ViewModelProvider(this)[MainViewModel::class.java]
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mainViewModel = ViewModelProvider(this).get(MainViewModel::class.java)
        binding.lifecycleOwner = this
        setUpStatusBar()
        val upgradeTitle = intent.getStringExtra("title")
        val upgradeContent = intent.getStringExtra("content")
        if (savedInstanceState != null) {
            // 从保存实例中恢复
            isForceUpgrade = savedInstanceState.getBoolean("isForceUpgrade")
        } else {
            // 外部传入
            isForceUpgrade = intent.getBooleanExtra("force", false)  // 第二个参数是默认值
        }

        val defaultTitle = getString(R.string.dialog_upgrade_title_default)

        upgradeContent?.let {
            initView(upgradeTitle ?: defaultTitle, it, isForceUpgrade)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean("isForceUpgrade", isForceUpgrade)
    }

    private fun initView(title: String, content: String, forceUpgrade: Boolean = false) {
        val contentTextView = binding.hmsDialogMessage
        val titleTextView = binding.hmsDialogTitle
        val scrollView = binding.dataUsageContainerScroll
        val container = binding.dialogUpgradeContainer
        val contentView = binding.dialogContent
        val scrollBar = binding.scrollbar
        scrollView.isEnabled = false
        contentTextView.text = content
        titleTextView.text = title
        contentTextView.post {
            //这里需要判断文本行数  用于限制高度
            val currentLines = getMaxLinesInTextView(contentTextView)
            Log.d(TAG, "contentTextView currentLines -> ${currentLines}")
            Log.d(TAG, "contentTextView maxLines -> ${contentMaxLines}")
            // 超过最小行数，未超过最大行数时，调整高度
            if (currentLines in (contentMinLines + 1)..contentMaxLines) {
                val lineHeight = contentTextView.lineHeight
                val maxHeight = lineHeight * currentLines
                val layoutParams = scrollView.layoutParams
                layoutParams.height = maxHeight
                scrollView.layoutParams = layoutParams
                scrollView.isEnabled = true
                val scrollBarLayoutParams = scrollBar.layoutParams
                scrollBarLayoutParams.height = maxHeight
//                scrollBar.layoutParams = scrollBarLayoutParams
//                scrollBar.visibility = View.VISIBLE
                // 对话框高度自适应
                val layoutParamsDialog = contentView.layoutParams
                layoutParamsDialog.height += (maxHeight - 2 * lineHeight)
                contentView.layoutParams = layoutParamsDialog
            }
            // 超过最大行数时，使用最大行数
            else if (currentLines > contentMaxLines) {
                val lineHeight = contentTextView.lineHeight
                val maxHeight = lineHeight * contentMaxLines
                val layoutParams = scrollView.layoutParams
                layoutParams.height = maxHeight
                scrollView.layoutParams = layoutParams
                scrollView.isEnabled = true
                val scrollBarLayoutParams = scrollBar.layoutParams
                scrollBarLayoutParams.height = maxHeight
                scrollBar.layoutParams = scrollBarLayoutParams
                scrollBar.visibility = View.VISIBLE
                scrollBar.attachTo(scrollView)
                scrollBar.customTrackDrawable = null
                // 对话框高度自适应
                val layoutParamsDialog = contentView.layoutParams
                layoutParamsDialog.height += (maxHeight - 2 * lineHeight)
                contentView.layoutParams = layoutParamsDialog
            }
        }
        container.setOnTouchListener { view, event ->
            // if (!container.isClickable) return@setOnTouchListener true
            if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                if (!isForceUpgrade) negativeButtonAction(forceUpgrade)
                true
            }
            false
        }



        binding.positiveButton.setOnClickListener {
            //跳转应用市场  应该是个action  直接跳转就行
            GwmAdapterManagerKotCoroutines.launchToGwmAppStoreDetail(HmsApplication.appContext)
            if(!forceUpgrade){finish()}
        }
        binding.negativeButton.setOnClickListener {
            negativeButtonAction(forceUpgrade)
        }
    }


    fun getMaxLinesInTextView(textView: TextView): Int {
        val layout: Layout? = textView.layout
        return layout?.lineCount ?: 0 // 如果 layout 不为 null，则返回行数，否则返回 0
    }

    fun negativeButtonAction(forceUpgrade: Boolean) {
        if (forceUpgrade) {
            //强制升级  直接退出 退出dialog
            finishAffinity()

        } else {
            finish()
            overridePendingTransition(R.anim.activity_stay, R.anim.activity_exit_dialog)
        }
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
       // super.onBackPressed()
    }

    private fun shouldInterceptBackPress(): Boolean {
        return isForceUpgrade
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (shouldInterceptBackPress()) {
                return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }
}