package com.healthlink.hms.activity

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.ViewModel
import com.healthlink.hms.ktExt.setUpSystemBar
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.ToastUtil

/**
 * Created by imaginedays on 2024/6/24
 * 基础Activity 使用databinding与ViewModel
 */
abstract class BaseVBVMActivity<VB: ViewDataBinding, V: ViewModel> : AppCompatActivity() {
    protected lateinit var binding: VB
    protected lateinit var viewModel: V

    protected abstract fun getLayoutId(): Int
    protected abstract fun createViewModel(): V

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, getLayoutId())
        viewModel = createViewModel()
        binding.lifecycleOwner = this
        setUpSystemBar()
    }

    /**
     * 回收dialog分配的资源
     */
    private fun recycleDialogAllocated() {
        HMSDialogUtils.clearDialog()
    }

    override fun onDestroy() {
        super.onDestroy()
        ToastUtil.cancelToast()
        recycleDialogAllocated()
    }
}