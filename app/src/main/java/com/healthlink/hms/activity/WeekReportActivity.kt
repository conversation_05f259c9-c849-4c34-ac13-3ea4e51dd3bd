package com.healthlink.hms.activity

//import com.blankj.utilcode.util.LogUtils
import android.os.Build
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.healthlink.hms.R
import com.healthlink.hms.base.Constants
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.databinding.ActivityHealthReportBinding
import com.healthlink.hms.fragment.HealthReportWeekFragment
import com.healthlink.hms.ktExt.setUpSystemBar
import com.healthlink.hms.viewmodels.MainViewModel


class WeekReportActivity : AppCompatActivity() {
    private lateinit var binding:ActivityHealthReportBinding
    private val fragmentList = arrayListOf<Fragment>()
    private lateinit var mainViewModel: MainViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 数据绑定视图
        binding = DataBindingUtil.setContentView(this, R.layout.activity_health_report)
        mainViewModel = ViewModelProvider(this).get(MainViewModel::class.java)
        setUpSystemBar()
            // 初始化UI
        initUI()
    }
    private fun initUI() {
        if (fragmentList.isNotEmpty()) {
            fragmentList.clear()
        }
        fragmentList.add(HealthReportWeekFragment.newInstance(TimeCode.TIME_CODE_DAY))
        fragmentList.add(HealthReportWeekFragment.newInstance(TimeCode.TIME_CODE_WEEK))
        fragmentList.add(HealthReportWeekFragment.newInstance(TimeCode.TIME_CODE_MONTH))
        fragmentList.add(HealthReportWeekFragment.newInstance(TimeCode.TIME_CODE_YEAR))

        // 设置返回
        binding.llNavBackContainer.setOnClickListener() {
            backToMain()
        }
        initViewPager()
        //设置Tab
        initTabLayout()

    }

    /**
     * 初始化健康报告下的 ViewPager
     */
    private fun initViewPager() {
//        val adapter: FragmentStateAdapter = object : FragmentStateAdapter(this) {
//            @NonNull
//            override fun createFragment(position: Int): Fragment {
//                return fragmentList[position]
//            }
//            override fun getItemCount(): Int {
//                return fragmentList.size
//            }
//        }
//        binding.viewPager.adapter = adapter
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText("日"))
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText("周"))
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText("月"))
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText("年"))
        binding.tabLayout.selectTab(binding.tabLayout.getTabAt(1))

        // 禁用选项卡点击事件
        for (i in 0 until binding.tabLayout.tabCount) {
            val tab = binding.tabLayout.getTabAt(i)
            tab?.view?.isClickable = false
        }
    }

    private fun initTabLayout() {
//        TabLayoutMediator(binding.tabLayout, binding.viewPager){ tab, position ->
//            tab.text = timeName[position]
//        }.attach()
        //选中第2个标签
//        binding.tabLayout.selectTab(binding.tabLayout.getTabAt(1))

    }


    private fun backToMain(){
        getSharedPreferences(Constants.SHARE_IS_USER_RETURN_TO_MAIN, MODE_PRIVATE).edit().putBoolean(
            Constants.BACK_TO_MAIN, true).commit()
        finish()
        overridePendingTransition(R.anim.activity_enter_slide_in_left,R.anim.activity_enter_slide_out_right)

    }

    override fun onResume() {
        super.onResume()
    }
}