package com.healthlink.hms.activity

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import com.healthlink.hms.compose.theme.HmsTheme
import com.healthlink.hms.compose.ui.HealthDashboard

class HealthDashboardActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            HmsTheme {
                // 使用Surface作为背景
                Surface(
                    modifier = Modifier.Companion.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    // 显示健康仪表盘
                    HealthDashboard()
                }
            }
        }
    }
}