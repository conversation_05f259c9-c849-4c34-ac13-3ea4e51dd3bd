package com.healthlink.hms.activity

import android.annotation.SuppressLint
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.NonNull
import androidx.compose.ui.text.style.TextAlign
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.healthlink.hms.Contants.timeName
import com.healthlink.hms.R
import com.healthlink.hms.base.Constants
import com.healthlink.hms.databinding.BaseActivityDetailBinding
import com.healthlink.hms.fragment.BaseCardFragment
import com.healthlink.hms.ktExt.addClickScale
import com.healthlink.hms.ktExt.reduceDragSensitivity
import com.healthlink.hms.viewmodels.MainViewModel
import java.lang.ref.WeakReference
import kotlin.properties.Delegates


/**
 * Created by imaginedays on 2024/6/24
 * 卡片二级详情页
 */
abstract class HMSBaseCardDetailActivity : BaseVBVMActivity<BaseActivityDetailBinding, MainViewModel>() {

    private val TAG = "HMSBaseCardDetailActivity"
    private var authStatus by Delegates.notNull<Boolean>()
    private lateinit var title: String
    private var cardType: Int = -1
    protected lateinit var userId: String
    protected val fragmentList = arrayListOf<BaseCardFragment<*,*>>()
    private var adapter: FragmentStateAdapter? = null
    private var onPageChangeListener: ViewPager2.OnPageChangeCallback? = null
    protected var currentTabIndex: Int = -1
    // 无授权
    private var noDataAuthView: View? = null

    private var isBackEnabled=false
    private var isChangeTab = false // 是否切换tab
    private var changedTabIndex = 0 // 默认值 0

    private var mediator: TabLayoutMediator? = null

    protected val handler = Handler(Looper.getMainLooper())

    protected abstract fun initFragments()
    protected abstract fun getCardAuthStatus(): Boolean?

    override fun getLayoutId(): Int {
        return R.layout.base_activity_detail
    }

    override fun createViewModel(): MainViewModel {
        return ViewModelProvider(this)[MainViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 设置状态栏颜色
//        setStatusBarBgColor()
        val mSharePreference = getSharedPreferences("user_info", MODE_PRIVATE)
        userId = mSharePreference.getString("userId","").toString()
        // 设置标题
        title = intent.getStringExtra("title") ?: "标题"
        // 设置卡片类型
        cardType = intent.getIntExtra("cardType", -1)
        // 设置页面授权状态
        authStatus = intent.getBooleanExtra("authStatus", false)
        // 是否切换tab
        isChangeTab = intent.getBooleanExtra("is_change_tab", false)
        // 切换tab的下标
        changedTabIndex = intent.getIntExtra("changed_tab_index", 0)
        // binding与viewmodel绑定
        binding.viewModel = viewModel
        binding.tvNavTitle.text = title
        // 设置返回
        binding.llNavBackContainer.setOnClickListener() {
            backToMain()
        }
        // 初始化UI
        initUI()

        // 检测数据授权
        if (!checkDataAuth()) {
            showNoDataAuthView()
            return
        } else {
            hideNoDataAuthView()
        }
        //设置动画结束再允许返回
        handler.postDelayed({
            isBackEnabled = true
        }, 500)

    }


    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        if (isBackEnabled) {
            backToMain()// 允许返回
        }
    }
    /**
     * 初始化UI
     */
    private fun initUI() {
        if (fragmentList.isNotEmpty()) {
            fragmentList.clear()
        }
        // 子类赋值所有的fragment
        initFragments()

        // 创建ViewPager2所使用的适配器，FragmentStateAdapter抽象类的实现类对象
        val fm: FragmentManager = supportFragmentManager
        val lifecycle: Lifecycle = lifecycle

        adapter = object : FragmentStateAdapter(fm, lifecycle){
            override fun createFragment(position: Int): Fragment {
                return fragmentList[position]
            }

            override fun getItemCount(): Int {
                return fragmentList.size
            }
        }
//        adapter = object : FragmentStateAdapter(this) {
//            @NonNull
//            override fun createFragment(position: Int): Fragment {
//                return fragmentList[position]
//            }
//
//            override fun getItemCount(): Int {
//                return fragmentList.size
//            }
//        }
        // 设置tab
        val weakReference = WeakReference(this)
        binding.tabLayout.addOnTabSelectedListener(object: TabLayout.OnTabSelectedListener{
            override fun onTabSelected(tab: TabLayout.Tab?) {
                weakReference.get()?.let {
                    // 设置当前点击的tab下标
                    it.currentTabIndex = tab!!.position
                    // 第二个参数表示是否启用动画，这里设置为 false 禁用动画
                    it.binding.contentViewPager.setCurrentItem(it.currentTabIndex, false)

                    val textView = tab.customView as? TextView
                    textView?.setTextSize(TypedValue.COMPLEX_UNIT_SP, 30f)
                    textView?.setTextColor(getColorStateList(R.color.text_color_333))
                }
            }
            override fun onTabUnselected(tab: TabLayout.Tab?) {
                weakReference.get()?.let {
                    val textView = tab?.customView as? TextView
                    textView?.setTextSize(TypedValue.COMPLEX_UNIT_SP, 26f)
                    textView?.setTextColor(getColorStateList(R.color.text_color_666))
                }
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }
        })
        binding.contentViewPager.adapter = adapter
        binding.contentViewPager.isUserInputEnabled = true

        // 判断是否切换tab
        if (isChangeTab && changedTabIndex != 0) {
            isChangeTab = false
            binding.contentViewPager.setCurrentItem(changedTabIndex, false)
        }

        try {
            // 设置 Item View 缓存大小
            var recyclerView = binding.contentViewPager.getChildAt(0) as RecyclerView
            recyclerView?.setItemViewCacheSize(4)
        }catch(ex:Exception){
            Log.i(TAG,"设置 Item View 缓存大小失败，原因：${ex.message}")
        }

        // 设置滚动监听 没有对监听使用
        onPageChangeListener = object : ViewPager2.OnPageChangeCallback() {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
            }

            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
            }
        }

        // 将滚动监听器设置给 ViewPager
        binding.contentViewPager.registerOnPageChangeCallback(onPageChangeListener!!)

        mediator = TabLayoutMediator(binding.tabLayout, binding.contentViewPager){ tab, position ->
            tab.text = timeName[position]

            // 创建一个 TextView 并设置样式
            val textView = TextView(this).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                text = timeName[position]
                setTextSize(TypedValue.COMPLEX_UNIT_SP, 26f) // 设置默认文字大小
                setTextColor(getColorStateList(R.color.text_color_fc_80)) // 使用颜色选择器
                gravity = Gravity.CENTER
            }
            tab.customView = textView // 设置 TextView 为 Tab 的自定义视图
        }
        mediator!!.attach()

        // 禁用标签的Tooltip
        for (i in 0 until binding.tabLayout.tabCount) {
            val tabView = (binding.tabLayout.getChildAt(0) as ViewGroup).getChildAt(i)
            tabView.setOnLongClickListener { v: View? -> true }
            tabView.addClickScale()
        }

        binding.contentViewPager.reduceDragSensitivity()
    }

//    private fun changeTabSelect(tab: TabLayout.Tab?) {
//        val view = tab!!.customView
//        val anim = ObjectAnimator
//            .ofFloat(view, "scaleAll", 1.0f, 0.9f)
//            .setDuration(250)
//        anim.start()
//        anim.addUpdateListener { animation ->
//            val cVal = animation.animatedValue as Float
//            view?.scaleX = cVal
//            view?.scaleY = cVal
//        }
//    }

    //region 数据授权
    private fun showNoDataAuthView() {
        if (noDataAuthView != null) {
            return
        }
        // 动态加载自定义布局文件
        val inflater = LayoutInflater.from(this)
        noDataAuthView =
            inflater.inflate(R.layout.activity_detail_no_auth, binding.container, false)
        // 创建 layoutparam 并设置位置
        val params = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            RelativeLayout.LayoutParams.MATCH_PARENT
        )
        params.addRule(RelativeLayout.BELOW, binding.llNavBackContainer.id)
    }

    private fun hideNoDataAuthView() {
        if (noDataAuthView != null) {
            binding.container.removeView(noDataAuthView)
            noDataAuthView = null;
        }
    }
    //endregion

    private fun checkDataAuth(): Boolean {
        return getCardAuthStatus() ?: true
    }

    // 设置状态栏颜色
//    fun setStatusBarBgColor() {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
//            window.statusBarColor = ContextCompat.getColor(this, R.color.view_color_bg)
//        }
//    }

     open fun backToMain(){
        getSharedPreferences(Constants.SHARE_IS_USER_RETURN_TO_MAIN, MODE_PRIVATE).edit().putBoolean(
            Constants.BACK_TO_MAIN, true).apply()
        finish()
        overridePendingTransition(R.anim.activity_enter_slide_in_left,R.anim.activity_enter_slide_out_right)
    }

    override fun onDestroy() {
        super.onDestroy()
        onPageChangeListener?.let {
            binding.contentViewPager.unregisterOnPageChangeCallback(it)
            onPageChangeListener = null
        }

        mediator?.detach()
        mediator = null

        binding.contentViewPager.adapter = null
        fragmentList.clear()
        adapter = null

        if (binding.tabLayout != null && binding.tabLayout.childCount > 0) (binding.tabLayout.removeAllTabs())
        handler.removeCallbacksAndMessages(null)

        hideNoDataAuthView()
    }

}