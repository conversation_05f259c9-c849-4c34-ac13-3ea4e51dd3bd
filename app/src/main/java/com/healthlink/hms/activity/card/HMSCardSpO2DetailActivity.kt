package com.healthlink.hms.activity.card


import android.os.Bundle
import com.healthlink.hms.activity.HMSBaseCardDetailActivity
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.fragment.SpO2TimeFragment
import com.healthlink.hms.fragment.SpO2TimeFragment2
import com.healthlink.hms.utils.DataTrackUtil

class HMSCardSpO2DetailActivity :  HMSBaseCardDetailActivity() , HMSCardFragmentInteractWithAcInterface{
    override fun initFragments() {
        fragmentList.add(SpO2TimeFragment.newInstance(TimeCode.TIME_CODE_DAY, userId,this))
        fragmentList.add(SpO2TimeFragment2.newInstance(TimeCode.TIME_CODE_WEEK, userId,this))
        fragmentList.add(SpO2TimeFragment2.newInstance(TimeCode.TIME_CODE_MONTH, userId,this))
        fragmentList.add(SpO2TimeFragment2.newInstance(TimeCode.TIME_CODE_YEAR, userId,this))
    }


    override fun getCardAuthStatus(): <PERSON><PERSON><PERSON> {
        return true
    }

    override fun setTabVisibilityforNetErrorOrSettingView(visibility: Int) {
        binding.tabLayout.visibility = visibility
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        DataTrackUtil.dtEnterPage("Health_Bloodoxygenreports_PV",DataTrackUtil.userIDMap(userId))
    }

    override fun onDestroy() {
        DataTrackUtil.dtExitPage("Health_Bloodoxygenreports_Close",DataTrackUtil.userIDMap(userId))
        super.onDestroy()

    }

    override fun backToMain() {
        DataTrackUtil.dtClick(
            "Health_Bloodoxygenreports_Return_Click",
            DataTrackUtil.userIDMap(userId)
        )
        super.backToMain()
    }
}