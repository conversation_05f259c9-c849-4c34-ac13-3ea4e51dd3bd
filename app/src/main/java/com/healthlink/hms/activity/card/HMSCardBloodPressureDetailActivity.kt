package com.healthlink.hms.activity.card

import android.os.Bundle
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.activity.HMSBaseCardDetailActivity
import com.healthlink.hms.fragment.BloodPressureFragment
import com.healthlink.hms.utils.DataTrackUtil

/**
 *@Author: 付仁秀
 *@Description：
 **/
class HMSCardBloodPressureDetailActivity  :  HMSBaseCardDetailActivity() , HMSCardFragmentInteractWithAcInterface{
    override fun initFragments() {
        fragmentList.add(BloodPressureFragment.newInstance(TimeCode.TIME_CODE_DAY, userId,this))
        fragmentList.add(BloodPressureFragment.newInstance(TimeCode.TIME_CODE_WEEK, userId,this))
        fragmentList.add(BloodPressureFragment.newInstance(TimeCode.TIME_CODE_MONTH, userId,this))
        fragmentList.add(BloodPressureFragment.newInstance(TimeCode.TIME_CODE_YEAR, userId,this))
    }


    override fun getCardAuthStatus(): Boolean {
        return true
    }

    override fun setTabVisibilityforNetErrorOrSettingView(visibility: Int) {
        binding.tabLayout.visibility = visibility
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        DataTrackUtil.dtEnterPage("Health_Bloodpressurereports_PV",DataTrackUtil.userIDMap(userId))
    }

    override fun onDestroy() {
        DataTrackUtil.dtExitPage("Health_Bloodpressurereports_Close",DataTrackUtil.userIDMap(userId))
        super.onDestroy()
    }

    override fun backToMain() {
        DataTrackUtil.dtClick(
            "Health_Bloodpressurereports_Return_Click",
            DataTrackUtil.userIDMap(userId)
        )
        super.backToMain()
    }
}