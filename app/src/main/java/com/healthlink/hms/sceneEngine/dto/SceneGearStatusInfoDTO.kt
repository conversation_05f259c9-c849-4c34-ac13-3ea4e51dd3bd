package com.healthlink.hms.sceneEngine.dto

/**

 * 档位信息：
 * GEAR_STATUS_NEUTRAL = O;//N挡、
 * GEAR_STATUS_SPORT = 1; //S挡
 * GEAR_STATUS_DRIVE = 2; //D挡
 * GEAR_STATUS_PARKING = 3;//P挡
 * GEAR_STATUS_REVERSE = 4;//R挡
 * GEAR_STATUS_MANUAL = 5;//M挡
 * 6;//Reserved
 */
data class SceneGearStatusInfoDTO(
    /**
     * GEAR_STATUS_NEUTRAL = O;//N挡、
     * GEAR_STATUS_SPORT = 1; //S挡
     * GEAR_STATUS_DRIVE = 2; //D挡
     * GEAR_STATUS_PARKING = 3;//P挡
     * GEAR_STATUS_REVERSE = 4;//R挡
     * GEAR_STATUS_MANUAL = 5;//M挡
     * 6;//Reserved
     */
    var status: String = "-1",
    /**
     * 档位发生变化时间
     */
    var timestamp: Long = 0L,
    /**
     * 一段形成内的累计休息时间
     */
    var totalRestTimeInJourney: Long = 0L
)
