package com.healthlink.hms.sceneEngine

import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.dto.SceneWelcomeDTO
import java.time.LocalTime

/**
 * Created by imaginedays on 2024/8/2
 *
 *
 */
class SceneWelcomeScript {
    // "良好","90-100","一般","75-89","不佳","60-74","较差", "45-59"
    private val healthStatusMap = mutableMapOf<String,IntRange>(
        "良好" to 90..100,
        "一般" to 75..89,
        "不佳" to 60..74,
        "较差" to 45..59,
        "未测量" to 0..44
    )
    // "很强","10-15","强","7-9","中","5-6","弱","3-4","最弱","0-2"
    private val uvIndexList = mutableMapOf(
        "很强" to "10到15",
        "强" to "7到9",
        "中" to "5到6",
        "弱" to "3到4",
        "最弱" to "0到2"
    )

    // "少发",1,"较易发",2,"易发",3,"极易发",4
    private val fluIndexList = arrayListOf<String>(
        "少发",
        "较易发",
        "易发",
        "极易发"
    )

    // "优",1,"良",2,"中",3,"较差",4,"很差",5
    private val airIndexList = arrayListOf<String>(
        "优",
        "良",
        "中",
        "较差",
        "很差"
    )

    /**
     * 问候语
     * 上午时间： 06：00-12：00；
     * 中午时间：12：01-14：00；
     * 下午时间：14：01-18：00；
     * 晚上时间：18：01-次日05：59；
     */
    private fun greeting(now: LocalTime = LocalTime.now()): String {
        return when {
            now.isAfter(LocalTime.of(6, 0)) && now.isBefore(LocalTime.of(12, 1)) -> "上午好"
            now.isAfter(LocalTime.of(12, 0)) && now.isBefore(LocalTime.of(14, 1)) -> "中午好"
            now.isAfter(LocalTime.of(14, 0)) && now.isBefore(LocalTime.of(18, 1)) -> "下午好"
            else -> "晚上好"
        }
    }

    /**
     * 健康状态
     */
    private fun calHealthStatus(healthScore: Int?): String {
        return if (healthScore == null || (healthScore < 0 || healthScore > 100)) {
            "未测量"
        } else {
            healthStatusMap.entries.find { (_, range) -> healthScore in range }!!.key
        }
    }

    /**
     * UV指数
     */
    private fun calUVIndex(uvKey: String?): String? {
        return uvIndexList[uvKey]
    }

    /**
     * 生成欢迎语
     */
    fun gernarateWelcomeTip(dto: SceneWelcomeDTO): String {
        val greetingDesc = greeting()
        /*
         * 空为默认值表示未测量
         */
        var healthStatusDesc = ""
        /*
         * 空为默认值 表示天气三类指标无数据（uv == null && flu == null && air == null）或者
         * 数据内三类指标不符合生成文案规则（ uv == "弱 | 最弱" && flu == "少发 | 较易发" && air == "优 | 良"）
         */
        var weatherDesc = ""
        val healthStatus = calHealthStatus(dto.healthScore)
        when (healthStatus) {
            "良好", "一般" -> {
                healthStatusDesc = healthStatus
            }
            "较差", "不佳" -> {
                return "${greetingDesc},${HmsApplication.appContext.getString(R.string.notification_play_tts_healthAbnormalPrecondition)}。&HMS_FUNC_DOCTOR"
            }
            else -> {
                // 未测量
            }
        }

        // 天气 无数据
        if (dto.uv == null && dto.flu == null && dto.air == null) {
            weatherDesc = ""
        } else {
            val uvTripTips = if(healthStatus == "未测量") "请做好出行防晒。" else "加强防晒措施，保护皮肤健康。"
            val fluTripTips = if(healthStatus == "未测量") "建议打开空气内循环。" else "驾车出行时，建议打开空气内循环。"
            // 紫外线指数
            val uvValue = calUVIndex(dto.uv)
            if (dto.uv != null && uvValue != null && dto.uv != "最弱" && dto.uv != "弱") {
                weatherDesc = "当前紫外线指数为${uvValue}，等级为${dto.uv}，${uvTripTips}"
            } else if (dto.flu != null && dto.flu != "少发" && dto.flu != "较易发") {
                weatherDesc = "当前天气${dto.flu}感冒，请注意保暖，避免着凉，预防感冒。"
            } else if (dto.flu != null && dto.air != "优" && dto.air != "良" && dto.air != "轻度污染") {
                weatherDesc = "当前空气质量为${dto.air}，${fluTripTips}"
            }
        }

        var welcomeTips = ""
        // 1、天气有 + 健康有
        if (weatherDesc.isNotBlank() && healthStatusDesc.isNotBlank()) {
            welcomeTips = "$greetingDesc，$weatherDesc"
        }

        // 2、天气无 + 健康有
        else if (weatherDesc.isBlank() && healthStatusDesc.isNotBlank()) {
            welcomeTips =  "$greetingDesc，${HmsApplication.appContext.getString(R.string.notification_play_tts_trip_advice_default)}"
        }
        // 3、 天气有 + 健康无
        else if(weatherDesc.isNotBlank() && healthStatusDesc.isBlank()) {
            welcomeTips =  "$greetingDesc，$weatherDesc"
        }
        
        return welcomeTips
    }
}