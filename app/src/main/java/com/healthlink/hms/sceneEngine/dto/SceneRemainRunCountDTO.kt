package com.healthlink.hms.sceneEngine.dto

import com.healthlink.hms.sceneEngine.SceneNameEnum

/**
 * Created by imaginedays on 2024/8/3
 * 每次行程内 每个场景的触发次数，关怀类触发一次， 干预类最多触发三次
 */
data class SceneRemainRunCountDTO(
    var careTriggerCount:Int = 1,
    var interventionTriggerCount:Int = 3,
    var sceneRunCountMap:MutableMap<String,Int> = mutableMapOf(
        SceneNameEnum.HEALTH_SEVERE_EXCEPTION.name to 3,
        SceneNameEnum.HIGH_REACTION_CARE_2.name to 3,
        SceneNameEnum.HEALTH_WEEK_REPORT.name to 1,
        SceneNameEnum.AFTER_WORK_CARE_2.name to 1,
        SceneNameEnum.LONG_DRIVE_CARE_1.name to 1,
        SceneNameEnum.LONG_DRIVE_CARE_2.name to 1,
        SceneNameEnum.HIGH_REACTION_CARE_1.name to 1
    ))
{
}

