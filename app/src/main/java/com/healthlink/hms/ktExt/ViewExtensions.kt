package com.healthlink.hms.ktExt

import android.annotation.SuppressLint
import android.util.Log
import android.view.MotionEvent
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.blankj.utilcode.util.LogUtils
import com.healthlink.hms.base.Constants

/**
 * Created by imaginedays on 2024/6/19
 * View点击缩放扩展
 */


/**
 * 添加点击缩放效果
 */
@SuppressLint("ClickableViewAccessibility")
// 参数为：缩小比例、缩小的变化时间
fun View.addClickScale(scale: Float = 0.9f, duration: Long = 80) {
    if (this.layoutParams.width == 0 || this.layoutParams.height == 0) {
        return
    }
    var  finalScale = scale
    if (context.pxToDp(this.layoutParams.width) <= 240f) {
        finalScale = 0.9f
    } else if (context.pxToDp(this.layoutParams.width) <= 420f) {
        finalScale = 0.92f
    } else if (context.pxToDp(this.layoutParams.width) <= 600f) {
        finalScale = 0.94f
    } else {
        finalScale = 0.96f
    }

//    LogUtils.i("view dp_width: ${context.pxToDp(this.layoutParams.width)} px_width: ${this.layoutParams.height} scale: $finalScale")
    this.setOnTouchListener { _, event ->
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                this.animate().scaleX(finalScale).scaleY(finalScale).setDuration(duration).start()
                if (this.tag == Constants.ECAllViewTag) {
                    return@setOnTouchListener true
                }
                return@setOnTouchListener false
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                this.animate().scaleX(1f).scaleY(1f).setDuration(duration).start()
                return@setOnTouchListener false
            }
        }
        this.onTouchEvent(event)
    }
}

// 扩展方法：禁用 RecyclerView 的多点触控
fun RecyclerView.disableMultiTouch() {
    this.addOnItemTouchListener(object : RecyclerView.OnItemTouchListener {
        override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
            // 拦截多点触控事件，忽略处理
            return e.pointerCount > 1
        }

        override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
            // 处理触控事件时不做任何处理
        }

        override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
            // 不需要特别处理
        }
    })
}

/**
 * 扩展方法：ViewPager2 的拖动灵敏度
 */
fun ViewPager2.reduceDragSensitivity() {
    val recyclerViewField = ViewPager2::class.java.getDeclaredField("mRecyclerView")
    recyclerViewField.isAccessible = true
    val recyclerView = recyclerViewField.get(this) as RecyclerView
    val touchSlopField = RecyclerView::class.java.getDeclaredField("mTouchSlop")
    touchSlopField.isAccessible = true
    val touchSlop = touchSlopField.get(recyclerView) as Int
    touchSlopField.set(recyclerView, touchSlop * 3) // "2" was obtained experimentally
}