package com.healthlink.hms.journey

import android.annotation.SuppressLint
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.util.Log
import com.google.gson.Gson
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.JourneySignalAddParam
import com.healthlink.hms.core.network.NetworkApi
import com.healthlink.hms.core.network.api.ApiService
import com.healthlink.hms.core.network.rx.BaseObserver
import com.healthlink.hms.utils.TimeUtils

/**
 * 驾驶行程统计管理类，处理行程数据采集的相关逻辑
 */
class JourneyManager {

    /**
     * 行程开始的处理
     */
    fun onJourneyStart(mileage: Float){
        //TODO 模拟里程，实际要删除掉
        var signalTime = TimeUtils.formatDateString(System.currentTimeMillis()) //  信号时间
        //构造请求对象
        var requestParams = JourneySignalAddParam()
        requestParams.vin = MMKVUtil.getVinCode()!!
        requestParams.userId = MMKVUtil.getUserId()
        requestParams.signalName = SIGNAL_TYPE_POWER_ON
        requestParams.signalTime = signalTime
        requestParams.mileage =mileage

        //缓存数据到本地
        MMKVUtil.storeData(CACHE_ID_LAST_POWER_ON , Gson().toJson(requestParams))
//        SceneManager.resetJourneySceneTriggerCount()

        //上报数据
        JourneyManager().uploadJourneyEvent(requestParams)
    }

    /**
     * 行程结束的处理
     */
    fun onJourneyEnd(mileage: Float){
        // 信号时间
        var signalTime = TimeUtils.formatDateString(System.currentTimeMillis()) //  信号时间

        //构造请求对象
        var requestParams = JourneySignalAddParam()
        requestParams.vin = MMKVUtil.getVinCode()!!
        requestParams.userId = MMKVUtil.getUserId()
        requestParams.signalName = SIGNAL_TYPE_POWER_OFF
        requestParams.signalTime = signalTime
        requestParams.mileage =mileage

        //缓存数据到本地
        MMKVUtil.storeData(CACHE_ID_LAST_POWER_OFF , Gson().toJson(requestParams))


        //上报数据
        JourneyManager().uploadJourneyEvent(requestParams)
    }


    /**
     * 启动行程采集程序
     */
    fun startJourneyCollectService(context: Context, from: String){
        Log.i(mTag, "startJourneyCollectService from $from")
        //  记录行程开启
//        onJourneyStart(context)
        // 启动服务，监听行程结束信号
        val serviceIntent = Intent()
        var serviceName = JourneyCollectService::class.java.name
        serviceIntent.component = ComponentName(
            context.packageName,
            serviceName
        )
        context.startForegroundService(serviceIntent)

    }

    /**
     * 启动保护服务
     */
    fun startJourneyCollectProtectService(context: Context,from: String){
        Log.i(mTag, "startJourneyCollectProtectService from $from")
        // 启动服务，监听行程结束信号
        val serviceIntent = Intent()
        var serviceName = JourneyCollectProtectService::class.java.name
        serviceIntent.component = ComponentName(
            context.packageName,
            serviceName
        )
        context.startForegroundService(serviceIntent)

    }

    /**
     * 上报行程事件
     */
    @SuppressLint("CheckResult")
    private fun uploadJourneyEvent(requestParams: JourneySignalAddParam ){
        val apiService = NetworkApi.createService(
            ApiService::class.java
        )
        apiService.JourneySignalAdd(requestParams)
            .compose(NetworkApi.applySchedulers(
                object : BaseObserver<BaseResponse<Boolean>>() {
                    override fun onSuccess(response: BaseResponse<Boolean>?) {
                        Log.d(mTag, "upload journey event success, $requestParams")
                    }
                    override fun onFailure(e: Throwable?) {
                        Log.i(mTag, "uploadJourneyEvent success")
                    }
                }
            )
        )
    }

    companion object{
        val mTag = "JourneyManager"
        // 行程开始信号名称
        const val SIGNAL_TYPE_POWER_ON = "powerOn"
        // 行程结束信号名称
        const val SIGNAL_TYPE_POWER_OFF = "powerOff"
        // 行程开始数据本地存储ID
        const val CACHE_ID_LAST_POWER_ON = "CACHE_ID_LAST_POWER_ON"
        // 行程结束数据本地存储ID
        const val CACHE_ID_LAST_POWER_OFF = "CACHE_ID_LAST_POWER_OFF"
    }
}