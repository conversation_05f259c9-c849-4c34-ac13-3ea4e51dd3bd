package com.healthlink.hms.journey

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.ComponentName
import android.content.Intent
import android.content.ServiceConnection
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.healthlink.hms.R
import com.healthlink.hms.activity.MainActivity


class JourneyCollectProtectService : Service() {

    private var isServiceRunning = false
    private var handler: Handler? = null

    override fun onCreate() {
        super.onCreate()
        handler = Handler()
        createNotificationChannel();
        updateNotification()

        bindService(Intent(this, JourneyCollectService::class.java), serviceConnection, BIND_AUTO_CREATE)
    }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        if (!isServiceRunning) {
            isServiceRunning = true
            startForeground(NOTIFICATION_ID, createNotification())
            handler!!.post(updateNotificationRunnable)
        }
        Log.d(mTag, "onStartCommand")
        return START_STICKY_COMPATIBILITY
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name: CharSequence = "Foreground Service Channel"
            val description = "Channel for foreground service"
            val importance = NotificationManager.IMPORTANCE_DEFAULT
            val channel = NotificationChannel(CHANNEL_ID, name, importance)
            channel.description = description
            val notificationManager = getSystemService(
                NotificationManager::class.java
            )
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val notificationIntent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this,
            0, notificationIntent, PendingIntent.FLAG_IMMUTABLE
        )
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Ai健康管家")
            .setContentText("正在守护您的行车健康...")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .build()
    }

    private val updateNotificationRunnable: Runnable = object : Runnable {
        override fun run() {
            updateNotification()
            handler!!.postDelayed(this, 500000) // Update every second
        }
    }

    private fun updateNotification() {
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)
    }

    override fun onDestroy() {
        super.onDestroy()
        isServiceRunning = false
        handler!!.removeCallbacks(updateNotificationRunnable)
        stopForeground(true)
        unbindService(serviceConnection)
    }

    override fun onBind(intent: Intent): IBinder? {
        return null
    }

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.d(mTag, "JourneyCollectProtectService connected")
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Log.d(mTag, "JourneyCollectProtectService disconnected. Restarting JourneyCollectProtectService...")
            // 重新启动 Service2
//            startForegroundService(Intent(this@JourneyCollectProtectService, JourneyCollectService::class.java))
            JourneyManager().startJourneyCollectService(this@JourneyCollectProtectService,"JourneyCollectProtectService")
            bindService(Intent(this@JourneyCollectProtectService, JourneyCollectService::class.java), this, BIND_AUTO_CREATE)
        }
    }

    companion object {
        private var mTag = "JourneyCollectProtectService"
        private const val NOTIFICATION_ID = 2
        private const val CHANNEL_ID = "JournaryCollectProtectService"
    }
}
