package com.healthlink.hms.adapter.card.itemBinder

import android.app.Activity
import android.graphics.Color
import android.graphics.PorterDuff
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ClickUtils
import com.drakeet.multitype.ItemViewBinder
import com.healthlink.hms.R
import com.healthlink.hms.adapter.OnItemClickListener
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.core.model.bean.HomeCardDTO
import com.healthlink.hms.core.model.dto.Temperature
import com.healthlink.hms.ktExt.addClickScale
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.views.CustomProgressBar
import java.lang.ref.WeakReference
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date

/**
 * Created by imaginedays on 2024/5/24
 *
 *
 */
class CardTemperatureBinder(private var onItemClick: ((cardType: Int, cardDTO: HomeCardDTO<*>) -> Unit)?) :
    ItemViewBinder<HomeCardDTO<*>, CardTemperatureBinder.ViewHolder>() {
// 使用 WeakReference 存储当前 ViewHolder，避免长时间持有强引用
    private var currentHolder: WeakReference<CardTemperatureBinder.ViewHolder>? = null
//    private var cardDTO: HomeCardDTO<*>? = null
    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): ViewHolder {
        val view = inflater.inflate(R.layout.card_temperature, parent, false)
        return ViewHolder(view,onItemClick)
    }

    override fun onBindViewHolder(holder: CardTemperatureBinder.ViewHolder, item: HomeCardDTO<*>) {
        holder.itemView.addClickScale()

//        cardDTO = item
        currentHolder = WeakReference(holder)
        holder.itemView.tag = item

        // 体温
        //私密模式
        if (HmsApplication.isPrivacyModeEnabled()) {
            holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                View.VISIBLE
            holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                View.GONE
            holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility = View.GONE
        } else {
            if (item.data != null && (item.data as Temperature).temperature != null) {
                val temperature = item.data as Temperature
                holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                    View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                    View.VISIBLE
                holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility = View.GONE
                val lastUpdateTime =
                    holder.itemView.findViewById<TextView>(R.id.tv_data_year_or_day)
                val timeStr = temperature.createTime
                lastUpdateTime.text = TimeUtils.getLastUpdateDateText(timeStr)
                setDataGray(timeStr,holder.itemView)
                initTemperatureUI(item.data as Temperature, holder)
            } else {
                holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                    View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                    View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility =
                    View.VISIBLE
            }
        }
    }

    private fun initTemperatureUI(model: Temperature, holder: CardTemperatureBinder.ViewHolder) {
        // 体温度数
        val tv_value = holder.itemView.findViewById<TextView>(R.id.tv_main_body_temperature)
        if (model.temperature != null) {
            tv_value.text = model.temperature
        } else {
            tv_value.text = "36.5"
        }

        // 体温状态
        val tv_status = holder.itemView.findViewById<TextView>(R.id.tv_main_body_temperature_status)
        if (model.temperature != null && !model.temperatureType.isNullOrBlank()) {
            tv_status.text = model.temperatureType
        } else {
            tv_status.text = "正常"
        }

        // 体温状态背景颜色 temperatureCode  0：正常 1：疑似低热 2：疑似中等度热及以上
        if (model.temperature != null && model.temperatureCode == 0) {
            tv_status.setBackgroundResource(R.drawable.health_index_status_nice_bg_fill)
            changeCardToException(false, holder)
        } else if (model.temperature != null && model.temperatureCode == 1) {
            tv_status.setBackgroundResource(R.drawable.health_index_status_warning_bg_fill)
            changeCardToException(false, holder)
        } else if (model.temperature != null && model.temperatureCode == 2 ) {
            tv_status.setBackgroundResource(R.drawable.health_index_status_danger_bg_fill)
            changeCardToException(true, holder)
        }

        val progressBar = holder.itemView.findViewById<ProgressBar>(R.id.temperature_progress_bar) as CustomProgressBar
//        val progressDot = holder.itemView.findViewById<ImageView>(R.id.temperature_progress_bar_dot)
        // 根据进度计算圆点的位置
        // 更新圆点的位置
        val currTemperatrue =
            if (model.temperature != null) model.temperature!!.toFloat() else 36.5f
        // 计算百分比
        val percent = calculatePercentage(currTemperatrue, 30f, 50f)
        progressBar.setProgressAnimation((percent*100).toInt())

//        val x = (progressBar.layoutParams.width * precent) - progressDot.layoutParams.width / 2
//        progressDot.visibility = View.VISIBLE
//        progressDot.x = 0F
//        progressDot.animate().x(x).duration = 1000
    }

    fun calculatePercentage(value: Float, min: Float, max: Float): Float {
        return ((value - min) / (max - min))
    }

    class ViewHolder(view: View,onItemClick: ((cardType: Int, cardDTO: HomeCardDTO<*>) -> Unit)?) : RecyclerView.ViewHolder(view) {
        val normalDataLayout: FrameLayout
        val noDataLayout: FrameLayout

        init {
            normalDataLayout = view.findViewById(R.id.card_normal_data)
            noDataLayout = view.findViewById(R.id.card_no_data)

            // 使用 ClickUtils 进行防抖处理，同时仅在点击时通过 view.tag 获取最新数据
            ClickUtils.applySingleDebouncing(view, 1000) {
                // 从 itemView 的 tag 中获取当前数据
                val cardDTO = view.tag as? HomeCardDTO<*>
                if (cardDTO != null) {
                    // 调用外部传入的 onItemClick lambda，避免直接引用外部对象
                    onItemClick?.invoke(cardDTO.cardType, cardDTO)
                }
            }
        }
    }

    fun setDataGray(time: String?, view: View) {
        val text = view.findViewById<TextView>(R.id.tv_data_year_or_day)
        text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_40))
        if (time != null) {
            try {
                val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                val time = sdf.parse(time)
                val calendar: Calendar = Calendar.getInstance()
                calendar.add(Calendar.DATE,0)
                val now: Date = calendar.getTime()
                val isBefore24Hours = (now.time - time.time) < 24 * 60 * 60 * 1000
                if (isBefore24Hours) {
                    text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_100))
                } else {
                    text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_40))
                }
            } catch (e: ParseException) {
                e.printStackTrace()
            }
        }
    }


    fun privacyUi() {
        currentHolder?.let { holder ->
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_privacy_data)?.visibility = View.GONE
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_normal_data)?.visibility = View.GONE
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_no_data)?.visibility = View.VISIBLE
        }
    }
}