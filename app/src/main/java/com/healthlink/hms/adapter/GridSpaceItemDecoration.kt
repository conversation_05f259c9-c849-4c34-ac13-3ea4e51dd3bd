package com.healthlink.hms.adapter

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

class GridSpaceItemDecoration(val spanCount: Int, val rowSpacing: Int, val columnSpacing: Int) : RecyclerView.ItemDecoration(){

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        val position = parent.getChildAdapterPosition(view)
        val column = position % spanCount
        outRect.left = column * columnSpacing / spanCount
        outRect.right = columnSpacing - (column + 1) * columnSpacing / spanCount
        if (position >= spanCount) {
            outRect.top = rowSpacing
        }
    }
}