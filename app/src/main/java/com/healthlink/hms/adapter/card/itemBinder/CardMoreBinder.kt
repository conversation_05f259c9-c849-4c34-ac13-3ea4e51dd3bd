package com.healthlink.hms.adapter.card.itemBinder

import android.app.Activity
import android.graphics.Color
import android.graphics.PorterDuff
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.drakeet.multitype.ItemViewBinder
import com.healthlink.hms.R
import com.healthlink.hms.adapter.OnItemClickListener
import com.healthlink.hms.core.model.bean.HomeCardDTO
import com.healthlink.hms.ktExt.addClickScale
import com.healthlink.hms.utils.TimeUtils

/**
 * Created by imaginedays on 2024/5/24
 * 更多
 */
class CardMoreBinder(context: Activity, onItemClick: OnItemClickListener) : ItemViewBinder<HomeCardDTO<*>, CardMoreBinder.ViewHolder>() {
    private var _onItemClick: OnItemClickListener = onItemClick
    private var cardDTO: HomeCardDTO<*>? = null
    private var mContext : Activity = context

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): ViewHolder {
        val view = inflater.inflate(R.layout.card_more, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: CardMoreBinder.ViewHolder, item: HomeCardDTO<*>) {
        holder.itemView.addClickScale()

        cardDTO = item
    }

    inner class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
//        val normalDataLayout: FrameLayout
//        val noDataLayout: FrameLayout

        init {
//            normalDataLayout = view.findViewById(R.id.card_normal_data)
//            noDataLayout = view.findViewById(R.id.card_no_data)
            // 迎宾模式场景
            var tvSceneWelcome = view.findViewById<Button>(R.id.tv_scene_welcome)
            tvSceneWelcome.setOnClickListener {
                _onItemClick.doScene("1")
            }
            // 下班压力关怀
            var tvSceneForOffOffice = view.findViewById<Button>(R.id.tv_scene_for_off_office)
            tvSceneForOffOffice.setOnClickListener{
                _onItemClick.doScene("2")
            }
        }
    }
}