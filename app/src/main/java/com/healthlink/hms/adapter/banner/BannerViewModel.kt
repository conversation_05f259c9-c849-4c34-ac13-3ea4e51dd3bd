package com.healthlink.hms.adapter.banner

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

class BannerViewModel : ViewModel() {
    // 使用 LiveData 存储 noTips 的选中状态
    private val _isNoTipsSelected = MutableLiveData<Boolean>()
    var bannerPosition: Int = 0

    // 公共 LiveData 供外部观察
    val isNoTipsSelected: LiveData<Boolean> get() = _isNoTipsSelected

    init {
        // 初始化状态，默认值为 false 或者从本地存储中恢复状态
        _isNoTipsSelected.value = false
    }

    // 更新选中状态
    fun setNoTipsSelected(isSelected: Boolean) {
        _isNoTipsSelected.value = isSelected
    }

    // 获取当前选中状态
    fun getNoTipsSelected(): Boolean {
        return _isNoTipsSelected.value ?: false
    }
}
