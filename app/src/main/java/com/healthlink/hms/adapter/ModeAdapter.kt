package com.healthlink.hms.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.gwm.widget.animation.AnimationUtils
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.core.model.dto.VehicleServiceModeDTO
import com.healthlink.hms.utils.ImageUtil
import me.jessyan.autosize.AutoSizeCompat

/**
 * Created by imaginedays on 2024/7/12
 * 车机服务模式列表
 */

interface ModeItemClick {
    fun onItemClick(serviceMode: VehicleServiceModeDTO,position: Int)
}

class ModeAdapter(private val modes: List<VehicleServiceModeDTO>, private val itemClick:ModeItemClick) : RecyclerView.Adapter<ModeAdapter.ModeViewHolder>() {
    private val TAG = "ModeAdapter"

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ModeViewHolder {
        AutoSizeCompat.autoConvertDensityOfGlobal(parent.context.resources)
        val view = LayoutInflater.from(parent.context).inflate(R.layout.fragment_service_item, parent, false)
        return ModeViewHolder(view)
    }

    override fun onBindViewHolder(holder: ModeViewHolder, position: Int) {
        holder.bind(modes[position],itemClick,position)
        holder.itemView.scaleX = 1.0f
        holder.itemView.scaleY = 1.0f
    }

    // 切换选择状态
    fun toggleSelection(position: Int) {
        val item = modes[position]
        item.isOpen = !item.isOpen
        notifyItemChanged(position)
    }

    fun changeModelOpenStatus(position: Int,isOpen : Boolean) {
        val item = modes[position]
        item.isOpen = isOpen
        notifyItemChanged(position)
    }

    override fun getItemCount() = modes.size

    inner class ModeViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        fun bind(serviceMode: VehicleServiceModeDTO, itemClick: ModeItemClick, position: Int = 0) {
            // 设置模式名称
            itemView.findViewById<TextView>(R.id.mode_name).text = serviceMode.modeName

            // 设置模式打开图片效果（点击与未点击）
            if(serviceMode.isOpen){
                setCardBg(itemView.findViewById(R.id.mode_container),true)
            } else {
                setCardBg(itemView.findViewById(R.id.mode_container),false)
            }
//            if (serviceMode.isOpen) {
//                serviceMode.bgIconOpenName?.let {
//                    setCardBg(it, itemView.findViewById(R.id.mode_container))
//                }
//            } else {
//                serviceMode.bgIconName?.let {
//                    setCardBg(it, itemView.findViewById(R.id.mode_container))
//                }
//            }

            // 设置 icon
            val image = itemView.findViewById<ImageView>(R.id.mode_icon)
            serviceMode.bgIconName?.let {
                image.setImageResource(ImageUtil.getImageResourceId(image.context,it))
            }



            AnimationUtils.setViewPressedAnimation(itemView)
            itemClick?.let {
                itemView.setOnClickListener {
//                    itemView.addClickScale()
                    itemClick.onItemClick(serviceMode,position)
                }
            }
        }

        private fun setCardBg(bgName: String, view: LinearLayout) {
            bgName?.let { ImageUtil.getDrawable(view.context, it)?.let { view.background = it } }
        }

        private fun setCardBg(v: LinearLayout,isOpen: Boolean) {
            v.background = if(isOpen) HmsApplication.appContext.getDrawable(R.drawable.mode_normal_bg) else HmsApplication.appContext.getDrawable(R.drawable.mode_selected_bg)
        }
    }
}