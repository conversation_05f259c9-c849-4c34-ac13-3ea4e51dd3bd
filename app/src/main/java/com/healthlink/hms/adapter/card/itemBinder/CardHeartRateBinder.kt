package com.healthlink.hms.adapter.card.itemBinder

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ClickUtils
import com.drakeet.multitype.ItemViewBinder
import com.github.mikephil.charting.animation.Easing
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.Description
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.utils.Utils
import com.healthlink.hms.R
import com.healthlink.hms.adapter.OnItemClickListener
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.ktExt.addClickScale
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.utils.TimeUtils
import java.lang.ref.WeakReference
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import androidx.core.graphics.toColorInt
import com.healthlink.hms.core.model.bean.HomeCardDTO
import com.healthlink.hms.core.model.dto.ChartDataEntry
import com.healthlink.hms.core.model.dto.HeartRate

/**
 * Created by imaginedays on 2024/5/24
 * 心率
 */
class CardHeartRateBinder(private var onItemClick: ((cardType: Int, cardDTO: HomeCardDTO<*>) -> Unit)?) :
    ItemViewBinder<HomeCardDTO<*>, CardHeartRateBinder.ViewHolder>() {
    private var cardDTO: HomeCardDTO<*>? = null
    private var currentHolder: WeakReference<CardHeartRateBinder.ViewHolder>? = null

    private val TAG = "CardHeartRateBinder"
    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): ViewHolder {
        val view = inflater.inflate(R.layout.card_heart_rate, parent, false)
        return ViewHolder(view, onItemClick)
    }

    override fun onBindViewHolder(holder: CardHeartRateBinder.ViewHolder, item: HomeCardDTO<*>) {
        holder.itemView.addClickScale()

        cardDTO = item
        currentHolder = WeakReference(holder) // 缓存 holder
        holder.itemView.tag = item

        // 心率
        if (item.cardType == 1) {
            if (HmsApplication.isPrivacyModeEnabled()) {
                holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                    View.VISIBLE
                holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                    View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility = View.GONE
                val lastUpdateTime =
                    holder.itemView.findViewById<TextView>(R.id.tv_data_year_or_day)
                initHeartRateUI(item, holder)
            } else {
                if (item.data != null && item.dataList != null
                    //心率大于0
                    && ((item.data as HeartRate).heartRate!=null && (item.data as HeartRate).heartRate!! > 0)
                    ) {

                    holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                        View.GONE
                    holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                        View.VISIBLE
                    holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility =
                        View.GONE
                    val lastUpdateTime =
                        holder.itemView.findViewById<TextView>(R.id.tv_data_year_or_day)
                    val timeStr = (item.data as HeartRate).createTime
                    lastUpdateTime.text = TimeUtils.getLastUpdateDateText(timeStr)
                    setDataGray(timeStr,holder.itemView)
                    initHeartRateUI(item, holder)
                } else {
                    holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                        View.GONE
                    holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                        View.GONE
                    holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility =
                        View.VISIBLE
                }
            }
        }
    }

    // 无参数的 privacyUi 方法，直接使用缓存的 holder 和 item
    fun privacyUi() {
        currentHolder?.let { holder ->
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_privacy_data)?.visibility = View.GONE
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_normal_data)?.visibility = View.GONE
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_no_data)?.visibility = View.VISIBLE
        }
    }

    /**
     * 心率相关
     */
    private fun initHeartRateUI(cardDTO: HomeCardDTO<*>, holder: CardHeartRateBinder.ViewHolder) {
        val lineCharPrivacy = holder.itemView.findViewById<LineChart>(R.id.chart_heart_rate_line_privacy)
//        lineCharPrivacy.setBackgroundDrawable(
//            ContextCompat.getDrawable(
//                mContext,
//                R.mipmap.img_heart_rate_line_data
//            )
//        )
        if (HmsApplication.isPrivacyModeEnabled()) {

            val heartRateList = arrayListOf<ChartDataEntry>()
            initHeartRateChart(lineCharPrivacy, heartRateList, 100)
//            doChartAnim(lineCharPrivacy)
            return
        }
        val model = cardDTO.data as HeartRate
        // 心率值
        val tvRateValue = holder.itemView.findViewById<TextView>(R.id.tv_main_body_heart_rate)
        if (model.heartRate != null) {
            tvRateValue.text = model.heartRate.toString()
        }
        // 心率状态
        val tvRateValueStatus =
            holder.itemView.findViewById<TextView>(R.id.tv_main_body_heart_rate_status)
        if (model.heartRate != null && !model.heartRateType.isNullOrBlank()) {
            tvRateValueStatus.text = model.heartRateType
        } else {
            tvRateValueStatus.text = "未知"
        }

        // 心率状态背景颜色  心率类型代码，心率类型，-1：测量值不在评估范围内； 0:正常； 1:疑似心动过缓； 2:疑似心动过速；
        if (model.heartRate != null && model.heartRateCode == 0) {
            // 正常
            tvRateValueStatus.setBackgroundResource(R.drawable.health_index_status_nice_bg_fill)
            changeCardToException(false, holder)
        } else if (model.heartRate != null && model.heartRateCode == 1 || model.heartRateCode == 2) {
            // code = 2 心动过速 code = 3 心动过缓
            tvRateValueStatus.setBackgroundResource(R.drawable.health_index_status_warning_bg_fill)
            changeCardToException(false, holder)
        } else if (model.heartRate != null && model.heartRateCode == -1) {
            // 测量值不在评估范围内
            tvRateValueStatus.setBackgroundResource(R.drawable.health_index_status_danger_bg_fill)
            changeCardToException(true, holder)
        }
        // 图
        val lineChar = holder.itemView.findViewById<LineChart>(R.id.chart_heart_rate_line)
//        lineChar.setBackgroundDrawable(
//            ContextCompat.getDrawable(
//                mContext,
//                R.mipmap.img_heart_rate_line_data
//            )
//        )
        lineChar.setNoDataText("")
        if (cardDTO.dataList != null) {
            val heartRateList = cardDTO.dataList as List<ChartDataEntry>
            initHeartRateChart(lineChar, heartRateList, model.heartRate)
            doChartAnim(lineChar)
        }


    }

    /**
     * 根据心率数据画心跳图
     */
    private fun initHeartRateChart(
        lineChar: LineChart,
        dataList: List<ChartDataEntry>,
        heartRate: Int?
    ) {
        //TODO 临时方法 模拟数据 如果 dataList.size < 0 自己绘制假数据
        var entries = ArrayList<Entry>()

        if (HmsApplication.isPrivacyModeEnabled()) {
            for (i in 0..59) {
                entries.add((Entry(i.toFloat(), 100f)))
            }
        }else {
            // 创建 LineDataSet
            if (dataList.isNotEmpty()) {
                if(dataList.size == 1) {
                    for (i in 0..59) {
                        entries.add((Entry(i.toFloat(), dataList[0].value!!.toFloat())))
                    }
                } else {
                    for ((index, value) in dataList.withIndex()) {
                        entries.add((Entry(index.toFloat(), value.value!!.toFloat())))
                    }
                }
            } else {
                // 最新数据和历史数据都没有
                if (heartRate == null) {
                    for (i in 0..59) {
                        entries.add((Entry(i.toFloat(), 100f)))
                    }
                } else {
                    for (i in 0..59) {
                        entries.add((Entry(i.toFloat(), heartRate!!.toFloat())))
                    }
                }
//                heartRate?.let {
//                    val total = 49
//                    for (i in 0..total) {
//                        entries.add(Entry(i.toFloat(), 50 + (Math.random() * 60).toFloat()))
//                        if (i == total) {
//                            entries.add(Entry(i.toFloat(), heartRate!!.toFloat()))
//                        }
//                    }
//                }
            }
        }
        // 创建 LineDataSet
        var dataSet = LineDataSet(entries, "Label");
        dataSet.setDrawFilled(true);
        if (Utils.getSDKInt() >= 18) {
            //仅在api级别18及以上版本上支持drawinables，渐变色drawable
            var drawable = ContextCompat.getDrawable(HmsApplication.appContext, R.drawable.fade_red);
            dataSet.fillDrawable = drawable;
        } else {
            //纯色
            dataSet.fillColor = R.color.hms_chart_fill_color_start;
        }
        dataSet.setDrawCircles(false)
        dataSet.color = Color.parseColor(HmsApplication.appContext.getString(R.string.card_heart_main_color_str) )
        dataSet.lineWidth = 3F.dp
        dataSet.mode = LineDataSet.Mode.CUBIC_BEZIER
        // 创建 LineData 并设置到 LineChart 中
        var lineData = LineData(dataSet);
        lineData.setDrawValues(false)

        lineChar.setBackgroundColor(Color.TRANSPARENT)
        lineChar.setBorderColor(Color.TRANSPARENT); //是否展示网格线
        lineChar.setDrawBorders(false); //是否显示边界
        lineChar.setScaleEnabled(false); // 是否可以缩放
        lineChar.setTouchEnabled(false); //是否有触摸事件
        lineChar.legend.isEnabled = false
        //设置XY轴描述
        var description = Description();
        description.isEnabled = false;
        description.text = "s";
        lineChar.description = description;
        var yAxisLeft = lineChar.axisLeft
        yAxisLeft.setDrawGridLines(false)
        yAxisLeft.isEnabled = true
        yAxisLeft.axisMinimum = 0F
        yAxisLeft.axisLineColor = "#00000000".toColorInt()
        yAxisLeft.textColor = "#00FFFFFF".toColorInt()
        yAxisLeft.axisMaximum = dataSet.yMax + 5

        lineChar.axisRight.setDrawGridLines(false)
        lineChar.xAxis.setDrawGridLines(true)
        lineChar.axisRight.isEnabled = false
        lineChar.xAxis.isEnabled = false
        // 取消均值
//        val average = calculateAverage(dataList)
//        var avgLine = LimitLine(86F, "均值");
//        avgLine.label = "";
//        avgLine.textColor = Color.parseColor("#00FFFFFF");
//        avgLine.lineWidth = 1f;
//        avgLine.isEnabled = true;
//        avgLine.lineColor = Color.parseColor("#FF9C3C3C");
//        avgLine.enableDashedLine(5f, 5f, 0f);//三个参数，第一个线宽长度，第二个线段之间宽度，第三个一般为0，是个补偿
//        avgLine.labelPosition = LimitLine.LimitLabelPosition.RIGHT_BOTTOM;//标签位置
//        avgLine.textSize = 10f;
//        lineChar.axisLeft.addLimitLine(avgLine);

        lineChar.data = lineData;
        lineChar.setExtraOffsets(0F, 0F, 0F, 0F)
        lineChar.notifyDataSetChanged()
    }

    private fun doChartAnim(lineChar: LineChart) {
        // 获取 ChartAnimator
        val animator = lineChar.animator
        animator.animateX(1000, Easing.Linear)
        lineChar.invalidate()
    }

    class ViewHolder(view: View,onItemClick: ((cardType: Int, cardDTO: HomeCardDTO<*>) -> Unit)?) : RecyclerView.ViewHolder(view) {
        val normalDataLayout: FrameLayout
        val noDataLayout: FrameLayout

        init {
            normalDataLayout = view.findViewById(R.id.card_normal_data)
            noDataLayout = view.findViewById(R.id.card_no_data)

            // 使用 ClickUtils 进行防抖处理，同时仅在点击时通过 view.tag 获取最新数据
            ClickUtils.applySingleDebouncing(view, 1000) {
                // 从 itemView 的 tag 中获取当前数据
                val cardDTO = view.tag as? HomeCardDTO<*>
                if (cardDTO != null) {
                    // 调用外部传入的 onItemClick lambda，避免直接引用外部对象
                    onItemClick?.invoke(cardDTO.cardType, cardDTO)
                }
            }

//            ClickUtils.applySingleDebouncing(view,1000) {
//                _onItemClick?.onItemClick(
//                    <EMAIL>?.cardType!!,
//                    <EMAIL>!!
//                )
//            }
//            view.setOnClickListener {
//                if (_onItemClick != null) {
//                    _onItemClick?.onItemClick(
//                        <EMAIL>?.cardType!!,
//                        <EMAIL>!!
//                    )
//                }
//            }
        }
    }
    fun setDataGray(time: String?, view: View) {
        val text = view.findViewById<TextView>(R.id.tv_data_year_or_day)
        text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_40))
        if (time != null) {
            try {
                val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                val time = sdf.parse(time)
                val calendar: Calendar = Calendar.getInstance()
                calendar.add(Calendar.DATE, 0) // 将当前时间回退一天
                val now: Date = calendar.getTime()
                val isBefore24Hours = (now.time - time.time) < 24 * 60 * 60 * 1000

                if (isBefore24Hours) {
                    text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_100))
                } else {
                    text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_40))
                }
            } catch (e: ParseException) {
                e.printStackTrace()
            }
        }
    }
}