package com.healthlink.hms.adapter.banner;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;
import android.view.DisplayUtil;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.healthlink.hms.R;
import com.youth.banner.adapter.BannerAdapter;

import java.util.List;

/**
 * 自定义布局，下面是常见的图片样式，更多实现可以看demo，可以自己随意发挥
 */
public class ImageAdapter extends BannerAdapter<BannerBean, ImageAdapter.BannerViewHolder> {
    private final int imageWidthPixels = 2952;
    private final int imageHeightPixels = 1680;
    private final Context context;

    public ImageAdapter(List<BannerBean> mDatas, Context context) {
        //设置数据，也可以调用banner提供的方法,或者自己在adapter中实现
        super(mDatas);
        this.context = context;
    }

    //创建ViewHolder，可以用viewType这个字段来区分不同的ViewHolder
    @Override
        public BannerViewHolder onCreateHolder(ViewGroup parent, int viewType) {
        // 返回持有 LinearLayout 的 ViewHolder
        return new BannerViewHolder(LayoutInflater.from(context).inflate(R.layout.dialog_banner_item, parent, false));
    }

    @SuppressLint("RestrictedApi")
    @Override
    public void onBindView(BannerViewHolder holder, BannerBean data, int position, int size) {
        //图片加载自己实现
        Glide.with(holder.itemView)
                .load(data.url)
                .override(imageWidthPixels, imageHeightPixels)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(holder.imageView);

    }

    class BannerViewHolder extends RecyclerView.ViewHolder {
        ImageView imageView;

        public BannerViewHolder(@NonNull View view) {
            super(view);
            this.imageView = view.findViewById(R.id.iv);
        }
    }
}
