package com.healthlink.hms.adapter.card.itemBinder

import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.drakeet.multitype.ItemViewBinder
import com.healthlink.hms.R

/**
 * Created by imaginedays on 2024/7/30
 * 显示卡片异常背景
 */

fun <T, VH : RecyclerView.ViewHolder> ItemViewBinder<T, VH>.changeCardToException(
    isChange: Boolean = false,
    holder: VH
) {
    // 图片 动画
    val view = holder.itemView.findViewById<View>(R.id.iv_bg_card_exception)
    if (isChange) {
        // 执行图片动画
        view.visibility = ViewGroup.VISIBLE
    } else {
        // 设置为默认
        view.visibility = ViewGroup.INVISIBLE
    }
}