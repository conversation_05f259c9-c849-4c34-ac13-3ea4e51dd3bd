package com.healthlink.hms.adapter.card.itemBinder

import android.animation.ObjectAnimator
import android.app.Activity
import android.graphics.Color
import android.graphics.PorterDuff
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ClickUtils
import com.drakeet.multitype.ItemViewBinder
import com.healthlink.hms.R
import com.healthlink.hms.adapter.OnItemClickListener
import com.healthlink.hms.adapter.card.itemBinder.CardHeartRateBinder.ViewHolder
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.core.model.bean.HomeCardDTO
import com.healthlink.hms.core.model.dto.BloodOxygen
import com.healthlink.hms.ktExt.addClickScale
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.views.CustomProgressBar
import java.lang.ref.WeakReference
import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date

/**
 * Created by imaginedays on 2024/5/24
 *
 *
 */
class CardBloodOxygenBinder(private var onItemClick: ((cardType: Int, cardDTO: HomeCardDTO<*>) -> Unit)?) :
    ItemViewBinder<HomeCardDTO<*>, CardBloodOxygenBinder.ViewHolder>() {
    private var currentHolder: WeakReference<CardBloodOxygenBinder.ViewHolder>? = null
    private var cardDTO: HomeCardDTO<*>? = null
    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): ViewHolder {
        val view = inflater.inflate(R.layout.card_blood_oxygen, parent, false)
        return ViewHolder(view,onItemClick)
    }

    override fun onBindViewHolder(holder: CardBloodOxygenBinder.ViewHolder, item: HomeCardDTO<*>) {
        holder.itemView.addClickScale()

        cardDTO = item
        currentHolder = WeakReference(holder) // 缓存 holder
        holder.itemView.tag = item
        // 血氧
        //私密模式
        if (HmsApplication.isPrivacyModeEnabled()) {
            holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                View.VISIBLE
            holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                View.GONE
            holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility = View.GONE
        } else {
            // && item.dataList != null 血氧不需要 dataList
            if (item.data != null
                //血氧值大于0
                &&((item.data as BloodOxygen).bloodOxygen!=null && (item.data as BloodOxygen).bloodOxygen!!>0)
                ) {
                holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                    View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                    View.VISIBLE
                holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility = View.GONE
                val lastUpdateTime =
                    holder.itemView.findViewById<TextView>(R.id.tv_data_year_or_day)
                val timeStr = (item.data as BloodOxygen).createTime
                lastUpdateTime.text = TimeUtils.getLastUpdateDateText(timeStr)
                setDataGray(timeStr, holder.itemView)
                initBloodOxygenUI(item, holder)
            } else {
                holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                    View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                    View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility =
                    View.VISIBLE
            }
        }
    }

    private fun initBloodOxygenUI(
        cardDTO: HomeCardDTO<*>,
        holder: CardBloodOxygenBinder.ViewHolder
    ) {
        val model = cardDTO.data as BloodOxygen
        // 血氧
        val tv_value = holder.itemView.findViewById<TextView>(R.id.tv_main_body_blood_oxygen)
        if (model.bloodOxygen != null) {
            tv_value.text = model.bloodOxygen.toString()
//            mTvBloodOxygenChart.setImageResource(R.mipmap.img_oxygen_saturation)
        } else {
            tv_value.text = "95"
        }

        // 血氧状态
        val tv_status =
            holder.itemView.findViewById<TextView>(R.id.tv_main_body_blood_oxygen_status)

        if (model.bloodOxygen != null && !model.bloodOxygenType.isNullOrBlank()) {
            tv_status.text = model.bloodOxygenType
        } else {
            tv_status.text = "血氧饱和度过低"
        }

        // 血氧状态背景颜色 血氧状态代码，0：正常 1：较低 2：过低
        if (model.bloodOxygen != null && model.bloodOxygenCode == 0) {
            tv_status.setBackgroundResource(R.drawable.health_index_status_nice_bg_fill)
            changeCardToException(false, holder)
        } else if (model.bloodOxygen != null && model.bloodOxygenCode == 1) {
            tv_status.setBackgroundResource(R.drawable.health_index_status_warning_bg_fill)
            changeCardToException(false, holder)
        } else if (model.bloodOxygen != null && model.bloodOxygenCode == 2) {
            tv_status.setBackgroundResource(R.drawable.health_index_status_danger_bg_fill)
            changeCardToException(true, holder)
        }

        initBloodOxygenChart(cardDTO, holder)
    }

    /**
     * 根据血氧数据画体温图表
     */
    private fun initBloodOxygenChart(
        cardDTO: HomeCardDTO<*>,
        holder: CardBloodOxygenBinder.ViewHolder
    ) {
        val model = cardDTO.data as BloodOxygen
        if (model.bloodOxygen == null) {
            return
        }
        val progressBar = holder.itemView.findViewById<ProgressBar>(R.id.blood_oxygen_progress_bar) as CustomProgressBar
        progressBar.startColor = Color.parseColor(HmsApplication.appContext.getString(R.string.card_status_danger_bg_color))
        progressBar.middleColor = Color.parseColor(HmsApplication.appContext.getString(R.string.card_status_warning_bg_color))
        progressBar.endColor = Color.parseColor(HmsApplication.appContext.getString(R.string.card_status_normal_bg_color))
        if(model.bloodOxygen!=null){
            progressBar.setProgressAnimation(model.bloodOxygen!!)
        }
    }

    class ViewHolder(view: View,private var onItemClick: ((cardType: Int, cardDTO: HomeCardDTO<*>) -> Unit)?) : RecyclerView.ViewHolder(view) {
        val normalDataLayout: FrameLayout
        val noDataLayout: FrameLayout

        init {
            normalDataLayout = view.findViewById(R.id.card_normal_data)
            noDataLayout = view.findViewById(R.id.card_no_data)

            // 使用 ClickUtils 进行防抖处理，同时仅在点击时通过 view.tag 获取最新数据
            ClickUtils.applySingleDebouncing(view, 1000) {
                // 从 itemView 的 tag 中获取当前数据
                val cardDTO = view.tag as? HomeCardDTO<*>
                if (cardDTO != null) {
                    // 调用外部传入的 onItemClick lambda，避免直接引用外部对象
                    onItemClick?.invoke(cardDTO.cardType, cardDTO)
                }
            }
        }
    }

    fun setDataGray(time: String?, view: View) {
        val text = view.findViewById<TextView>(R.id.tv_data_year_or_day)
        text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_40))
        if (time != null) {
            try {
                val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                val time = sdf.parse(time)
                val calendar: Calendar = Calendar.getInstance()
                calendar.add(Calendar.DATE, 0) // 将当前时间回退一天
                val now: Date = calendar.getTime()
                val isBefore24Hours = (now.time - time.time) < 24 * 60 * 60 * 1000
                if (isBefore24Hours) {
                    text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_100))
                } else {
                    text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_40))
                }

            } catch (e: ParseException) {
                e.printStackTrace()
            }
        }
    }

    fun privacyUi() {
        currentHolder?.let { holder ->
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_privacy_data)?.visibility = View.GONE
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_normal_data)?.visibility = View.GONE
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_no_data)?.visibility = View.VISIBLE
        }
    }
}