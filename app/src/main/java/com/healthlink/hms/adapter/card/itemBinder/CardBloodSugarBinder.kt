package com.healthlink.hms.adapter.card.itemBinder

import android.app.Activity
import android.graphics.Color
import android.graphics.PorterDuff
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.drakeet.multitype.ItemViewBinder
import com.healthlink.hms.R
import com.healthlink.hms.adapter.OnItemClickListener
import com.healthlink.hms.ktExt.addClickScale
import com.healthlink.hms.core.model.bean.HomeCardDTO

/**
 * Created by imaginedays on 2024/6/23
 * 血糖
 */
class CardBloodSugarBinder(context: Activity, onItemClick: OnItemClickListener) : ItemViewBinder<HomeCardDTO<*>, CardBloodSugarBinder.ViewHolder>() {
    private var _onItemClick: OnItemClickListener = onItemClick
    private var cardDTO: HomeCardDTO<*>? = null
    private var mContext : Activity = context
    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): ViewHolder {
        val view = inflater.inflate(R.layout.card_blood_sugar, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: CardBloodSugarBinder.ViewHolder, item: HomeCardDTO<*>) {
        holder.itemView.addClickScale()

        cardDTO = item
        if (item.cardType == 7) {
            // 血糖
            if (item.data != null) {
                holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility = View.VISIBLE
                holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility = View.GONE
            } else {
                holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility = View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility = View.VISIBLE
            }
        }
    }


    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val normalDataLayout: FrameLayout
        val noDataLayout: FrameLayout

        init {
            normalDataLayout = view.findViewById(R.id.card_normal_data)
            noDataLayout = view.findViewById(R.id.card_no_data)
        }
    }
}