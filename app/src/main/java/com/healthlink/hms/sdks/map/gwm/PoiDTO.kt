package com.healthlink.hms.sdks.map.gwm

/**
 * Created by imaginedays on 2024/7/16
 * 示例数据
 * "poiId": "B000A80WEC"
 * "name": "和家宾馆连锁(北京北四环店)"
 * "address": "成府路华清嘉园22号楼"
 * "source": 0
 * "typeCode ": "34564"
 * "lon": 116.334404
 * "lat": 39.990135
 * "distance": 11811
 */
data class PoiDTO(
    val poiId: String, // 具体id
    val name: String, // 名称
    val address: String, // 地址
    val source: Int, // Poi类型
    val typeCode: String, // 类型
    val lon: Double, // 经度
    val lat: Double, // 纬度
    val distance: Double // 距离(单位:米)
)