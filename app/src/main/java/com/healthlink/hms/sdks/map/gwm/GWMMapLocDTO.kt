package com.healthlink.hms.sdks.map.gwm

/**
 * Created by imaginedays on 2024/7/16
 * 示例数据：{"data":{"adcode":110101,"areacode":"010","city":"北京市","cityadcode":110000,"district":"东城区","districtadcode":110101,"province":"北京市","provinceadcode":110000,"address":"北京市东城区南河沿大街69号","distance":0.0,"lat":39.908286,"lon":116.397482,"name":"在外金水桥附近, 在西长安街旁边, 靠近西长安街--广场西侧路路口","poiId":"B000A60DA1","source":0,"tag":null,"typeCode":"110202"},"pkgName":"com.healthlink.hms","protocolId":"14041","reqId":null,"statusCode":20000,"verName":"2.0.4"}
 */
data class GWMMapLocDTO(
    val adcode: String,
    val areacode: String,
    val city: String,
    val cityadcode: String,
    val district: String,
    val districtadcode: String,
    val province: String,
    val provinceadcode: String,
    val address: String,
    val distance: Double,
    var lat: Double,
    var lon: Double,
    val name: String,
    val poiId: String,
    val source: Int,
    val tag: String,
    val typeCode: String
){}
