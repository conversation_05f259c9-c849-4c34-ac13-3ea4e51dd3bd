package com.healthlink.hms.sdks.gwmadapter

object IVIPowerMode {

    const val POWER_MODE_RESERVE= "0" //预留模式，未使用
    const val POWER_MODE_SHUWDOWN= "1"  //关机模式
    const val POWER_MODE_WAITING= "2"  //待机模式
    const val POWER_MODE_NORMAL= "3"  //正常模式
    const val POWER_MODE_10_MIN= "4"  //十分钟模式
    const val POWER_MODE_ACCIDENT= "5"  //故障节电模式
    const val POWER_MODE_STATIC_SAVING_POWER_1= "6"  //静态节电等级一
    const val POWER_MODE_STATIC_SAVING_POWER_2= "7"  //静态节电等级二
    const val POWER_MODE_STARTING= "8"  //启动模式【预留模式，未使用】
    const val POWER_MODE_REMOTE= "9"  //远程模式
    const val POWER_MODE_DEMO= "10"  //Demo 模式
    const val POWER_MODE_AWAKE_HOLD= "11"  //AwakeHold 模式
    const val POWER_MODE_STR_ENTER= "12"  //进入STR(即将调用suspend)
    const val POWER_MODE_STR_EXIT= "13"  //退出STR
    const val POWER_MODE_PENDING_SHUWDOWN= "14"  //准备关机(即将进入str)
}