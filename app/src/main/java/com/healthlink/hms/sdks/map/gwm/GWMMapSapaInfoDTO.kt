package com.healthlink.hms.sdks.map.gwm

/**
 * SapaInfo 数据结构类型定义
 * {"isCharge":null,"sapaDist":4011,"sapaName":"三元里收费站","sapaRemainTime":810,"sapaRemianMile":null,"sapaType":1}
 */
data class GWMMapSapaInfoDTO(
    val isCharge: String? = null, // 是否支持充电 1 支持 0 不支持
    val sapaDist: Int? = null, // 距离最近服务区的距离，单位:米
    val sapaName: String? = null, // 服务区名称
    val sapaRemainTime: Long? = null, // 当前车位距离sapa的剩余时间。单位:秒
    val sapaRemianMile: Int? = null, // 到达服务区剩余里程
    val sapaType: Int? = null // 服务区类型 0:速服务区 1:收费站 2:检查站
) {

}
