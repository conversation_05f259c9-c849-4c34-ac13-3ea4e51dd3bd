package com.healthlink.hms.biz

object HealthDataProcessor {

    fun getHealthStatusFromScore(score: String):String{
        var healthStatus = "很棒"
        if (score.toInt() in 90..100) {
            healthStatus = "很棒"
        } else if (score.toInt() in 75..<90) {
            healthStatus = "良好"
        } else if (score.toInt() in 60..<75) {
            healthStatus = "不佳"
        } else if (score.toInt() in 45..<60) {
            healthStatus = "较差"
        }
        return healthStatus
    }
}