package com.healthlink.hms.reciever

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Handler
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.healthlink.hms.Contants.VehicleServiceModeType
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.base.Constants
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.sdks.map.gwm.GWMMapManagerKotCoroutines
import com.healthlink.hms.utils.NotificationUtil
import com.healthlink.hms.utils.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * 主要的广播接收器
 */
class HMSBroadcastReciever : BroadcastReceiver(){
    companion object {
        private const val REQUEST_CALL_PHONE_CODE = 1
        private val mTag = "HMSBroadcastReciever"
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        // 打开座椅通风
        if (HMSAction.ACTION_OPEN_SEAT_VENTILATION_MODE == intent!!.action) {
            if(context!=null) {
                GwmAdapterManagerKotCoroutines.setSeatVentilationConfigEnable()
                closeNotification(Constants.NOTIFICATION_ID_SEAT_WAIST_DIRECTION, context)
                Log.d(mTag, "已为您开启座椅通风")
            }
        }
        // 取消打开座椅通风
        else if (HMSAction.ACTION_OPEN_SEAT_VENTILATION_MODE_CANCEL == intent!!.action) {
                if(context!=null) {
                    GwmAdapterManagerKotCoroutines.setSeatVentilationConfigDisable()
                    closeNotification(Constants.NOTIFICATION_ID_SEAT_WAIST_DIRECTION, context)
                    Log.d(mTag, "已取消开启座椅通风")
                }
        } else if (HMSAction.OPEN_CALL_DOCTOR_NOTIFICATION == intent!!.action) {
            // 拨打电话医生号码
            if (context != null) {
                if (intent.getBooleanExtra("DOCTOR_SERVICE", false)) {
                    checkIsBindDoctorPhone(context){
                        dialPhone(MMKVUtil.getPhoneDoctorNumber() ?: context.getString(R.string.phone_doctor_default),context)
                    }
                }
                closeNotification(Constants.NOTIFICATION_ID_PHONE_DOCTOR_SERVICE, context)
                Log.d(mTag, "已拨打电话")
            }
        } else if (HMSAction.CLOSE_CALL_DOCTOR_NOTIFICATION == intent!!.action) {
            // 关闭拨打电话医生通知
            if (context != null) {
                closeNotification(Constants.NOTIFICATION_ID_PHONE_DOCTOR_SERVICE, context)
                if (intent.getBooleanExtra("OPEN_APP", false)) {
                    launchAppByPackageName(context)
                }
                Log.d(mTag, "已取消拨打电话")
            }
        } else if (HMSAction.ACTION_OPEN_RELAX_MODE == intent!!.action) {
            // 打开放松模式
            if(context!=null) {
//                CoroutineScope(Dispatchers.Main).launch {
//                    // 在IO线程执行耗时任务
//                    val openResult = withContext(Dispatchers.IO) {
//                        GwmAdapterManagerKotCoroutines.openRelaxMode("3")
//                    }
//                    if (!openResult) {
//                        showOpenModeFailToast(context,"${VehicleServiceModeType.RELAX.modeName}开启失败,请重试")
//                    }
//                }
                val openLevel = "3"
                GwmAdapterManagerKotCoroutines.openRelaxMode(openLevel)
                Handler().postDelayed({
                    val status = GwmAdapterManagerKotCoroutines.getRelaxModeStatus()
                    val okRes = (status != null) && (status == openLevel)
                    if (!okRes) showOpenModeFailToast(context,"${VehicleServiceModeType.RELAX.modeName}开启失败，请重试")
                }, 2000)
                closeNotification(Constants.NOTIFICATION_ID_OPEN_RELAX_MODE, context)
                Log.d(mTag, "已为您开启放松模式")
            }
        } else if (HMSAction.ACTION_CLOSE_RELAX_MODE == intent!!.action) {
            // 取消打开放松模式
            if(context!=null) {
//                GwmAdapterManagerKotCoroutines.setSeatMessageLevelConfigUnable(context)
                closeNotification(Constants.NOTIFICATION_ID_OPEN_RELAX_MODE, context)
                Log.d(mTag, "已取消开启放松模式")
            }
        } else if (HMSAction.ACTION_OPEN_NAVIGATION_SEARCH_SERVICE == intent!!.action) {
            // 打开导航搜索关键字
            if(context!=null) {
                val keyword = intent.getStringExtra("search_keyword")
                GlobalScope.launch(Dispatchers.IO) {
                    GWMMapManagerKotCoroutines.sendQueryKeywordAsyncReq(keyword!!)
                    closeNotification(Constants.NOTIFICATION_ID_NAVIGATION_SEARCH_SERVICE, context)
                }
                Log.d(mTag, "已打开导航搜索$keyword")
            }
        } else if (HMSAction.ACTION_CLOSE_NAVIGATION_SEARCH_SERVICE == intent!!.action) {
            // 取消打开导航搜索关键字
            if(context!=null) {
                closeNotification(Constants.NOTIFICATION_ID_NAVIGATION_SEARCH_SERVICE, context)
                Log.d(mTag, "已取导航搜索服务区")
            }
        }

    }

    /**
     * 关闭通知
     * @param notificationIdSharedPrefKey 存在sharedPreferences中的通知id
     */
    private fun closeNotification(notificationIdSharedPrefKey: String, context: Context?) {
        if (context != null) {
            var notificationId  = context.getSharedPreferences("hms.data", AppCompatActivity.MODE_PRIVATE)
                .getInt(notificationIdSharedPrefKey, 0 )
            NotificationUtil(context).cancelNotification(notificationId)
        }
    }

    private fun showOpenModeFailToast(context: Context,message: String) {
        ToastUtil.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    private fun dialPhone(phoneNumber: String,context: Context?) {
        context?.let {
            val dialIntent = Intent(Intent.ACTION_DIAL, Uri.parse("tel:$phoneNumber"))
            dialIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (dialIntent.resolveActivity(context.packageManager) != null) {
                context.startActivity(dialIntent)
            } else {
                Log.i(mTag, "No activity found to handle the intent")
            }
        }
    }

    // 检测是否进行电话医生备案
    private fun checkIsBindDoctorPhone(context: Context?,callback: () -> Unit) {
        if (MMKVUtil.getBindDoctorService()) {
            callback()
        } else {
          if (HmsApplication.isInForeground) {
            // 打开绑定电话医生页面
              HmsApplication.openBindDoctorServiceAcInApp()
          }  else {
//              val intent = Intent(context, BindPhoneCallDoctorActivity::class.java)
//              intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//              context?.startActivity(intent)
          }
        }
    }

    /**
     * 打开主App
     */
    private fun launchAppByPackageName(context:Context) {
        // 判断是否在前台
        if (!HmsApplication.isInForeground) {
            val launchIntent = context.packageManager.getLaunchIntentForPackage(context.packageName)
            try {
                if (launchIntent != null) {
                    context.startActivity(launchIntent)
                } else {
                    Log.i(mTag, "App ${context.packageName} is not installed")
                }
            } catch (e: Exception) {
                Log.i(mTag, "Failed to launch app: ${e.message}")
            }
        }
    }

}