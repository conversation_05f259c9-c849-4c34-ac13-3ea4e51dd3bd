package com.healthlink.hms.reciever

object HMSAction {
    /**
     * 取消健康提示播报
     */
    const val ACTION_CANCEL_PLAY_HEALTH_TIPS = "com.hl.hms.action.CANCEL_PLAY_HEALTH_TIPS"
    /**
     * 打开电话医生通知
     */
    const val OPEN_CALL_DOCTOR_NOTIFICATION = "com.hl.hms.action.OPEN_CALL_DOCTOR_NOTIFICATION"
    /**
     * 关闭电话医生通知
     */
    const val CLOSE_CALL_DOCTOR_NOTIFICATION = "com.hl.hms.action.CLOSE_CALL_DOCTOR_NOTIFICATION"

    /**
     * 打开智能腰托Action
     */
    const val ACTION_OPEN_SEAT_WAIST_DIRECTION_MODE = "com.hl.hms.action.OPEN_SEAT_WAIST_DIRECTION_MODE"
    /**
     * 取消打开智能腰托Action
     */
    const val ACTION_OPEN_SEAT_WAIST_DIRECTION_MODE_CANCEL = "com.hl.hms.action.OPEN_SEAT_WAIST_DIRECTION_MODE_CANCEL"

    /**
     * 打开座椅通风Action
     */
    const val ACTION_OPEN_SEAT_VENTILATION_MODE = "com.hl.hms.action.OPEN_SEAT_VENTILATION_MODE"
    /**
     * 取消打开座椅通风Action
     */
    const val ACTION_OPEN_SEAT_VENTILATION_MODE_CANCEL = "com.hl.hms.action.OPEN_SEAT_VENTILATION_MODE_CANCEL"

    /**
     * 打开放松模式Action
     */
    const val ACTION_OPEN_RELAX_MODE = "com.hl.hms.action.OPEN_RELAX_MODE"
    /**
     * 取消打开方式模式Action
     */
    const val ACTION_CLOSE_RELAX_MODE = "com.hl.hms.action.CLOSE_RELAX_MODE"

    /**
     * 打开导航App搜索服务
     */
    const val ACTION_OPEN_NAVIGATION_SEARCH_SERVICE = "com.hl.hms.action.OPEN_NAVIGATION_SEARCH_SERVICE"
    /**
     * 取消打开导航App搜索服务
     */
    const val ACTION_CLOSE_NAVIGATION_SEARCH_SERVICE = "com.hl.hms.action.CLOSE_NAVIGATION_SEARCH_SERVICE"

    /**
     * 卡片数据自动更新时，发送更新事件
     */
    const val ACTION_HMS_WIDGET_DATA_UPDATED = "com.hl.hms.action.HMS_WIDGET_DATA_UPDATED"

    /**
     * 更新首页车机服务卡片
     */
    const val ACTION_HMS_UPDATE_VEHICLE_SERVICE = "com.healthlink.hms.action.UPDATE_VEHICLE_SERVICE"

    /**
     * 拨打健康医生电话
     */
    const val ACTION_OPEN_PHONE_DOCTOR_SERVICE = "com.healthlink.hms.action.CALL_HEALTH_DOCTOR"
}