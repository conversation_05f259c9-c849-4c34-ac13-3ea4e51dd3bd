package com.healthlink.hms.widget

import android.app.UiModeManager
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.Typeface
import com.healthlink.hms.R
import com.healthlink.hms.ktExt.dp
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin


/**
 * 制作桌面卡片的分值效果图片
 */
object WidgetScoreBitmapFactory{

    var isShowAssistRect = false
    var originalWidth = 320
    var originalHeight = 320

    private var scoresMap: MutableMap<String,Array<Float>> = mutableMapOf(
        "normal" to arrayOf(100F,90F),
        "low" to arrayOf(90F,75F),
        "middle" to arrayOf(75F,60F),
        "high" to arrayOf(60F,40F)
    )

    //分数值圆点颜色值
    private val scorePointShadowColorMap: MutableMap<String, Int> = mutableMapOf(
        "normal" to Color.parseColor("#80000000"),
        "low" to Color.parseColor("#80000000"),
        "middle" to Color.parseColor("#4D21273D"),
        "high" to Color.parseColor("#4D21273D")
    )

    private var scoreStartAngleMap: MutableMap<String,Float> = mutableMapOf(
        "normal" to 0F,
        "low" to 270F,
        "middle" to 180F,
        "high" to 90F
    )

    var basicBitmapMap: MutableMap<String,Int> = mutableMapOf(
        "normal" to R.drawable.widget_health_status_basic_normal,
        "low" to R.drawable.widget_health_status_basic_low,
        "middle" to R.drawable.widget_health_status_basic_middle,
        "high" to R.drawable.widget_health_status_basic_high
    )

    fun drawScoreBitmap(context:Context,score:Int,drawable:Int,healthStatusCode:String):Bitmap{

        var drawable = basicBitmapMap.get(healthStatusCode)!!
        var basicBitmap = BitmapFactory.decodeResource(context.resources,drawable)
//        val targetBitmap: Bitmap = basicBitmap.copy(Bitmap.Config.ARGB_8888, true)

        val targetBitmap: Bitmap = Bitmap.createBitmap(basicBitmap.width,basicBitmap.height,Bitmap.Config.ARGB_8888)

        var basicWidth = basicBitmap.width
        var basicHeight = basicBitmap.height
        basicBitmap.recycle()

        var scale = basicWidth * 1F / originalWidth

        var strokeWidth = 16F * scale
        var padding = 44F * scale
        var deltaPadding = 0 * scale

        var canvas = Canvas(targetBitmap)

        var paintRing = Paint()
        paintRing.style = Paint.Style.STROKE
        paintRing.strokeWidth = strokeWidth * 1.2F
        paintRing.isAntiAlias = true
        paintRing.strokeCap = Paint.Cap.ROUND
        paintRing.color = Color.WHITE

        val ring = RectF(
            padding + strokeWidth/2,
            padding + strokeWidth/2,
            basicWidth - deltaPadding - padding - strokeWidth/2,
            basicHeight - deltaPadding - padding - strokeWidth/2
        )
        var deltaAngle = 360F * (strokeWidth)/(Math.PI*basicWidth)

        var scores:Array<Float>? = scoresMap.get(healthStatusCode)
        var deltaPoint:Float = 0F
        var nextHealthStatusPointPercent = 0F
        if(scores!=null){
            deltaPoint = scores[0] - scores[1]
            nextHealthStatusPointPercent = ((score -  scores[1])/deltaPoint)
        }else{
            nextHealthStatusPointPercent = 0F
        }

        var startAngle = scoreStartAngleMap[healthStatusCode]
        // 应分配给分数段的角度
        var totalPointAngle = 90f - 2 * deltaAngle.toFloat()
        // 分数指示圆点的开始位置（分数段内满分位置）
        var pointCircleInitAngle = startAngle!! + totalPointAngle +  deltaAngle.toFloat() * 1F
        // 分数指示圆点的偏移量
        var pointCircleDeltaAngle = totalPointAngle * (1 - nextHealthStatusPointPercent)
        // 分数指示圆点的实际角度
        var pointCircleAngle = pointCircleInitAngle - pointCircleDeltaAngle

        var centerX = ring.centerX()
        var centerY = ring.centerY()
        var ringR = ring.width() / 2
        var pointCenterX = centerX + ringR * cos(pointCircleAngle * PI /180)
        var pointCenterY = centerY + ringR * sin(pointCircleAngle * PI /180)

        // 纯黑30%透明度，Y轴2px，模糊4px
        var shadowPaint = Paint()
//        shadowPaint.color = Color.argb(77,0,0,0)
        var shadowColor = scorePointShadowColorMap[healthStatusCode]
        if(shadowColor!=null) {
            shadowPaint.color = Color.parseColor("#00000000")
            shadowPaint!!.setShadowLayer(5F,0F,0F,shadowColor)
        }
        shadowPaint.strokeWidth = paintRing.strokeWidth
        shadowPaint!!.isAntiAlias = true
        shadowPaint!!.style = Paint.Style.STROKE
        shadowPaint!!.setShadowLayer(3F,0F,0F,Color.argb(77,0,0,0))
        canvas.drawCircle(pointCenterX.toFloat(),pointCenterY.toFloat(),strokeWidth*0.3F / 2,shadowPaint)

        canvas.drawArc(ring, pointCircleAngle ,.1F, false, paintRing!!)

        //画分数
//        var paint = Paint()
//        paint.color = getFontColor(context)//context.resources.getColor(R.color.text_color_fc_100)
//        paint.textSize = 48F.dp
//        paint.setTypeface(Typeface.create(Typeface.DEFAULT_BOLD,700,false))
//        paint.isAntiAlias = true
//        var scoreText = "${score}分"
//        var textWidth = paint.measureText(scoreText)
//
//        canvas.drawText(scoreText,basicWidth/2 - textWidth/2,(basicHeight/2 + paint.textSize * 1.1).toFloat(), paint)

        if(isShowAssistRect) {
            paintRing.color = Color.RED
            paintRing.strokeWidth = 1F
            canvas.drawRect(ring, paintRing)

            var alPaint = Paint()
            alPaint.color = Color.RED
            canvas.drawLine(0F,basicHeight/2F,basicWidth/1F,basicHeight/2F,alPaint)
            canvas.drawLine(basicWidth/2F,0F,basicWidth/2F,basicHeight/1F,alPaint)
        }

//        var scaleWidth = 160F.dp
//        var scaleFactor =scaleWidth/targetBitmap.getWidth()
//        val matrix = Matrix()
//        matrix.setScale(scaleFactor, scaleFactor)
//        var scaledBitmap = Bitmap.createBitmap(
//            targetBitmap,
//            0,
//            0,
//            targetBitmap.getWidth(),
//            targetBitmap.getHeight(),
//            matrix,
//            true
//        )


        return targetBitmap
    }

}