package com.healthlink.hms.widget

import android.app.AlarmManager
import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.reciever.HMSWidgetUpdateDataReceiver

/**
 * Widget管理类
 * 创建一个闹钟一分钟执行一次
 * 发送ACTION_HMS_WIDGET_UPDATE_DATE广播
 */
object HMSWidgetManager {
    private const val REFRESH_INTERVAL_MILLIS = 1 * 60 * 1000L  // 5分钟刷新间隔
    private const val REQUEST_CODE = 0
    private var alarmManager:  AlarmManager? = null
    private var pendingIntent: PendingIntent? = null
    private const val TAG = "HMSWidgetManager"

    public fun scheduleTask(context: Context, from: String) {
        synchronized(
            HMSWidgetManager::class.java
        ) {

            if (alarmManager != null || pendingIntent != null) {
                Log.i(TAG, "setAlarm: ${from},but,AlarmManager is already set, returning")
                return
            }

            if (alarmManager == null) {
                alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            }

            if (pendingIntent == null) {
                pendingIntent = createLoadDataWorkerIntent(context)
            }

            val triggerAtMillis = SystemClock.elapsedRealtime()

            // 设置重复的定时任务
            alarmManager!!.setRepeating(
                AlarmManager.ELAPSED_REALTIME,
                triggerAtMillis,
                REFRESH_INTERVAL_MILLIS,
                pendingIntent!!
            )

            Log.i(TAG, "Schedule widget worker from [$from] Finished")
        }

    }

    private fun createLoadDataWorkerIntent(context: Context): PendingIntent {
        val intent = Intent(HMSWidgetUpdateDataReceiver.ACTION_HMS_WIDGET_UPDATE_DATE).apply {
            setPackage(context.packageName)
        }

        return PendingIntent.getBroadcast(
            context,
            REQUEST_CODE,
            intent,
            PendingIntent.FLAG_MUTABLE
        )
    }

    /**
     * 统一更新桌面健康卡片入口
     */
    fun updateHmsWidget(applicationContext : Context){
        // 获取 AppWidgetManager 实例
        val appWidgetManager = AppWidgetManager.getInstance(applicationContext)
        // 获取当前小部件的所有 AppWidgetId
        val appWidgetIds = appWidgetManager.getAppWidgetIds(
            ComponentName(applicationContext, HMSWidgetProvider::class.java)
        )
        val hmsWidgetProvider = HMSWidgetProvider()
        hmsWidgetProvider.updateHmsWidget(applicationContext,appWidgetIds)
    }
}

//class HMSWidgetManager {
//
//    val mTag = "HMSWidgetManager"
//
//    /**
//     * 创建调度，定时获取健康数据
//     */
//    public fun scheduleTask(context: Context, from: String){
//        var pendingIntent = createLoadDataWorkerIntent(context)
//        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
//        val triggerAtMillis =  SystemClock.elapsedRealtime()  + 0 * 1000L
//        alarmManager.setRepeating(AlarmManager.ELAPSED_REALTIME, triggerAtMillis,5 * 1000L,pendingIntent)
//        Log.i(mTag, "schedule widget worker from [$from] Finished")
//    }
//
//    private fun createLoadDataWorkerIntent(context: Context): PendingIntent {
//        //定义回调广播
//        val intent = Intent(HMSWidgetUpdateDataReceiver.ACTION_HMS_WIDGET_UPDATE_DATE)
//        intent.setPackage(context.packageName)
//        return PendingIntent.getBroadcast(
//            context, 0, intent, PendingIntent.FLAG_MUTABLE
//        )
//    }
//}