package com.healthlink.hms.utils.network.okhttp

import android.util.Log
import com.google.gson.Gson
import okhttp3.Call
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import java.io.File
import java.io.IOException


class HttpUtil {

    private var TAG= HttpUtil::class.simpleName

    /**
     * Get请求
     */
    fun makeOkHttpGetRequest(urlString: String): String? {
        var responseData = ""
        try {
            val client = OkHttpClient()
            val request: Request = Request.Builder()
                .url(urlString)
                .build()
            var response = client.newCall(request).execute()
            // 如果你不再需要响应，可以关闭它以释放资源。否则，它将由OkHttp自动管理。
            response.close()
            // 获取响应字符串。如果响应是JSON，你可以使用Gson等库将其解析为对象。
            responseData = response.body!!.string()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return responseData
    }

    /**
     * Post请求
     */
    fun makeOkHttpPostRequest(urlString: String, postData: Any): String? {
        var responseData = ""
        var requestData = Gson().toJson(postData)
        try {
            val client = OkHttpClient()
            val requestBody = RequestBody.create(
                "application/json; charset=utf-8".toMediaTypeOrNull(),
                requestData
            )
            val request: Request = Request.Builder()
                .url(urlString)
                .post(requestBody)
                .build()
            var response = client.newCall(request).execute()
            // 获取响应字符串。如果响应是JSON，你可以使用Gson等库将其解析为对象。
            responseData = response.body!!.string()
            // 如果你不再需要响应，可以关闭它以释放资源。否则，它将由OkHttp自动管理。
            response.close()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return responseData
    }

    /**
     * 以postdata的形式上传文件
     */
    fun makeOkHttpUploadFileRequest(url: String, postData: Map<String,String?>, files: Map<String,String?>): String?{
        val client = OkHttpClient()
        val multipartBodyBuilder = MultipartBody.Builder().setType(MultipartBody.FORM)
        Log.i(TAG, "上传文件请求参数： ${postData.toString()}")
        //  写入参数
        for((key,value) in postData) {
            if (value != null) {
                multipartBodyBuilder.addFormDataPart(key, value)
            }
        }
        // 写入文件
        for((filename,filepath) in files) {
            var file = File(filepath)
            if(file.exists()) {
                multipartBodyBuilder.addFormDataPart(
                    filename,
                    File(filepath).name,
                    RequestBody.create(
                        "application/octet-stream".toMediaTypeOrNull(),
                        File(filepath)
                    )
                )
            }
        }
        var multipartBody = multipartBodyBuilder.build()

        val request = Request.Builder()
            .url(url)
            .post(multipartBody)
            .build()
        var response = client.newCall(request).execute()
        var responseBody = response.body?.string()
        if (response.isSuccessful) {
            Log.i(TAG,"upload file success , $responseBody")
        } else {
            Log.i(TAG, "upload file error, url is($url)}, \r\n" +
                    " request info: postData ($postData), files data  ($files) ,\r\n " +
                    " response info:${response.code} ${response.message}")
        }

        return responseBody
    }
}