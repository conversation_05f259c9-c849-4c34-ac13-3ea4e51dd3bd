package com.healthlink.hms.utils;

import android.content.Context;

import com.healthlink.hms.adapter.banner.BannerDialog;

import java.lang.ref.WeakReference;


/**
 * <AUTHOR>
 * @create 2019/7/15
 * @Describe 弹窗工具类
 */
public class AlertDialogUtil {

    private WeakReference<Context> mContext;

    private BannerDialog bannerDialog;

    public AlertDialogUtil(Context context) {
        this.mContext = new WeakReference<Context>(context);
    }

    public void showBannerDialog() {
        showBannerDialog(null);
    }

    public void showBannerDialog(BannerDialog.BannerDialogDismissListener bannerDialogDismissListener) {
        bannerDialog = new BannerDialog(mContext.get()).setBannerDialogDismissListener(bannerDialogDismissListener).builder();
        bannerDialog.show();
    }

    /**
     * 对话框是否展示中
     * @return
     */
    public boolean isDialogShowing(){
        return this.bannerDialog!=null && this.bannerDialog.isShowing();
    }


}
