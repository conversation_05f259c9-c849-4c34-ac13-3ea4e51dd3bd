package com.healthlink.hms.utils

import android.util.Log
import android.view.MotionEvent
import android.view.View
import androidx.dynamicanimation.animation.FloatValueHolder
import androidx.dynamicanimation.animation.SpringAnimation
import androidx.dynamicanimation.animation.SpringForce
import com.healthlink.hms.ktExt.dp
import kotlin.math.abs
import kotlin.math.min


class ChartTouchEventDelegate(private val setSliderX: (Float) -> Unit) {
    var mDownX = 0f
        private set
    private var mDownY = 0f
    var isSlider = false
        private set

    var anim: SpringAnimation? = null

    fun onTouchEvent(
        event: MotionEvent,
        view: View,
        xSlider: Float,
        xSpacing: Float,
        ySpacing: Float,
        xWithStart: Float,
        xWithEnd: Float,
        dataLength: Int,
        actionUp: ((x: Float, y: Float) -> Boolean)? = null,
        onDataSelected: ((index: Int) -> Unit)? = null,
        bottomExtraSpace: Float = 0f
    ): Boolean {
        val boundSpacing = min(xWithStart, 60f)
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                anim?.cancel()
                mDownX = event.x
                mDownY = event.y

                // 无效点击区域
                if (mDownX < xWithStart - 60 || mDownX > xWithEnd + 60 ) {
                    return false
                }

                isSlider =
                    Math.abs(event.x - xSlider) < 60f && Math.abs(event.y - ySpacing * 7) < 60f + bottomExtraSpace

                view.parent.requestDisallowInterceptTouchEvent(true)
            }

            MotionEvent.ACTION_MOVE -> {
                val deltaX = event.x - mDownX
                val deltaY = event.y - mDownY
                val deltaDistance = Math.sqrt((deltaX * deltaX + deltaY * deltaY).toDouble())
                if (deltaDistance > 10 && !isSlider) {
                    view.parent.requestDisallowInterceptTouchEvent(false)
                    return false
                }
                //  if (Math.abs(event.y - mDownY) < Math.abs(event.x - mDownX)) {
                if (isSlider) {
                    when {
                        event.x < xWithStart - boundSpacing -> setSliderX(xWithStart - boundSpacing)
                        event.x <= xWithEnd + boundSpacing -> setSliderX(event.x)
                        else -> setSliderX(xWithEnd + boundSpacing)
                    }

                    var index = ((event.x - xWithStart) / xSpacing).toInt()
                    if (index < 0) {
                        index = 0
                    } else if (index >= dataLength) {
                        index = dataLength - 1
                    }
                    onDataSelected?.invoke(index)

                    view.invalidate()
                }
                //  }
            }

            MotionEvent.ACTION_UP -> {
                view.parent.requestDisallowInterceptTouchEvent(false)
                if (actionUp?.invoke(event.x, event.y) == true) {
                    return true
                }
                var targetX = event.x
                if (dataLength > 0) {
                    (0..dataLength).forEach {
                        val x = xWithStart + xSpacing * it
                        val dis = abs(x - event.x)
                        if (dis < xSpacing / 2) {
                            targetX = x
                            return@forEach
                        }
                    }
                }
                if (targetX < xWithStart) {
                    targetX = xWithStart
                }
                if (targetX > xWithEnd) {
                    targetX = xWithEnd
                }

                anim?.cancel()
                val spring = SpringForce(targetX)
                    .setDampingRatio(SpringForce.DAMPING_RATIO_NO_BOUNCY)
                    .setStiffness(400f)
                anim = SpringAnimation(FloatValueHolder())
                    .setSpring(spring)
                    .setStartValue(xSlider)
                    .addUpdateListener { _, value, _ ->
                        setSliderX(value)
                    }
                anim?.start()

            }
        }
        return true
    }
}