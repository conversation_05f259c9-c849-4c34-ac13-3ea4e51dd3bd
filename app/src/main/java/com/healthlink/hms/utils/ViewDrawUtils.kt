package com.healthlink.hms.utils

import android.content.Context
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.Shader
import androidx.core.content.ContextCompat
import com.healthlink.hms.R

/**
 * 视图绘制工具类
 */
object ViewDrawUtils {
    /**
     * 绘制带圆角的渐变背景（从上到下的渐变）
     *
     * @param canvas 画布
     * @param width 宽度
     * @param height 高度
     * @param startColor 渐变起始颜色
     * @param endColor 渐变结束颜色
     * @param cornerRadius 圆角半径
     */
    private fun drawGradientBackground(
        canvas: Canvas,
        width: Float,
        height: Float,
        startColor: Int,
        endColor: Int,
        cornerRadius: Float = 30f
    ) {
        val rect = RectF(0f, 0f, width, height)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        val linearGradient = LinearGradient(
            width / 2,
            rect.top,
            width / 2,
            rect.bottom,
            intArrayOf(startColor, endColor),
            null,
            Shader.TileMode.CLAMP
        )

        paint.shader = linearGradient
        paint.style = Paint.Style.FILL
        canvas.drawRoundRect(rect, cornerRadius, cornerRadius, paint)
    }

    /**
     * 绘制X轴文本下方的渐变背景
     *
     * @param canvas 画布
     * @param context 上下文
     * @param width 视图宽度
     * @param ySpacing Y轴间距
     */
    fun drawGradientBackgroundUnderXText(
        canvas: Canvas,
        context: Context,
        width: Float,
        ySpacing: Float,
        spaceUnit: Int = 7
    ) {
        val height = spaceUnit * ySpacing
        val startColor = ContextCompat.getColor(context, R.color.under_x_text_gr_start)
        val endColor = ContextCompat.getColor(context, R.color.under_x_text_gr_end)

        drawGradientBackground(canvas, width, height, startColor, endColor)
    }
}