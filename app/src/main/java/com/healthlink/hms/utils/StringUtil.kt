package com.healthlink.hms.utils

/**
 * Created by imaginedays on 2024/7/29
 * 字符串工具类
 */
object StringUtil {
    fun formatDouble(value: Double): Double {
        return truncateDouble(value)
    }

    private fun truncateDouble(value: Double, maxDecimals: Int = 6): Double {
        val valueStr = value.toString()
        val decimalIndex = valueStr.indexOf(".")

        return if (decimalIndex >= 0 && valueStr.length - decimalIndex - 1 > maxDecimals) {
            // 小数点后的位数超过 maxDecimals，需要截断
            val truncatedStr = valueStr.substring(0, decimalIndex + maxDecimals + 1)
            truncatedStr.toDouble()
        } else {
            // 小数点后的位数不超过 maxDecimals，不需要截断
            value
        }
    }

}