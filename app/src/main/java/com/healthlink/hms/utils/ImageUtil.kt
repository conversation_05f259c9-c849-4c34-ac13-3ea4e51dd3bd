package com.healthlink.hms.utils

import android.content.Context
import android.graphics.drawable.Drawable

/**
 * Created by imaginedays on 2024/7/15
 *
 *
 */
object ImageUtil {
    fun getImageResourceId(context: Context, imageName: String): Int {
        val drawableResId = context.resources.getIdentifier(imageName, "drawable", context.packageName)
        if (drawableResId != 0) {
            return drawableResId
        }

        val mipmapResId = context.resources.getIdentifier(imageName, "mipmap", context.packageName)
        if (mipmapResId != 0) {
            return mipmapResId
        }

        return 0
    }

    fun getDrawable(context: Context, imageName: String): Drawable? {
        val resId = getImageResourceId(context, imageName)
        if (resId != 0) {
            return context.resources.getDrawable(resId, null)
        }
        return null
    }
}