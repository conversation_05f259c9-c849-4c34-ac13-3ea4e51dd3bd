package com.healthlink.hms.utils.files

import android.content.Context
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader

/**
 * 资源文件工具类
 */
object AssetsUtil {
    /**
     * 从资源文件总读取内容
     */
    fun readTextFromAsset(context: Context, fileName: String): String? {
        var result: String? = null
        try {
            val inputStream = context.assets.open(fileName)
            val reader = BufferedReader(InputStreamReader(inputStream))
            val stringBuilder = StringBuilder()
            var line: String? = reader.readLine()
            while (line != null) {
                stringBuilder.append(line).append('\n')
                line = reader.readLine()
            }
            result = stringBuilder.toString()
            reader.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return result
    }
}
