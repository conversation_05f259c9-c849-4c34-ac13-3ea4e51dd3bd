package com.healthlink.hms.utils

import com.healthlink.hms.Contants.TimeCode
import java.util.Calendar
import java.util.Locale

fun getPrivacyModeDate(timeCode: String): String {
    val calendar = Calendar.getInstance()
    val month = calendar[Calendar.MONTH] + 1 // 月份是从0开始的，需要加1
    val day = calendar[Calendar.DAY_OF_MONTH]
    val hour = calendar[Calendar.HOUR_OF_DAY]
    val minute = calendar[Calendar.MINUTE]
    val year = calendar[Calendar.YEAR]

    return when (timeCode) {
        TimeCode.TIME_CODE_DAY.timeCode -> String.format(Locale.getDefault(), "%d月%d日 %02d:%02d", month, day, hour, minute)
        TimeCode.TIME_CODE_YEAR.timeCode -> "${year}年${month}月"
        else -> "${month}月${day}日"
    }
}