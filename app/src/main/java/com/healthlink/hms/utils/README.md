# NetworkMonitor.kt 使用
## 通过`ViewModel` 使用
1. 在`ViewModel`中使用
```kotlin
// 使用示例 - 在ViewModel中
class MainViewModel(application: Context) {
    private val networkMonitor = NetworkMonitor(application)

    // 作为StateFlow公开给UI层
    val networkState = networkMonitor.networkStateFlow
        .stateIn(
            scope = CoroutineScope(Dispatchers.Default),
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = NetworkMonitor.NetworkState.Unavailable
        )

    // 简化的布尔值StateFlow
    val isConnected = networkMonitor.isConnectedFlow
        .stateIn(
            scope = CoroutineScope(Dispatchers.Default),
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false
        )
}
```

2. `Activity` 或 `Fragment`中结合`lifecycleScope`使用 `ViewModel`
    ```kotlin
    // 在Activity或Fragment中使用
    class MainActivity : AppCompatActivity() {
        private val viewModel: MainViewModel by viewModels()
        
        override fun onCreate(savedInstanceState: Bundle?) {
            super.onCreate(savedInstanceState)
            setContentView(R.layout.activity_main)
            
            // 使用lifecycleScope.launch收集Flow
            lifecycleScope.launch {
                // 使用repeatOnLifecycle自动处理生命周期
                lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                    // 收集详细的网络状态
                    viewModel.networkState.collect { state ->
                        when (state) {
                            is NetworkMonitor.NetworkState.Available -> {
                                // 更新UI以显示网络已连接
                                Toast.makeText(this@MainActivity, "网络已连接", Toast.LENGTH_SHORT).show()
                            }
                            is NetworkMonitor.NetworkState.Unavailable -> {
                                // 更新UI以显示网络已断开
                                Toast.makeText(this@MainActivity, "网络已断开", Toast.LENGTH_SHORT).show()
                            }
                        }
                    }
                }
            }
            
            // 或者直接收集布尔值Flow
            lifecycleScope.launch {
                lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                    viewModel.isConnected.collect { isConnected ->
                        if (isConnected) {
                            // 网络已连接
                            binding.statusText.text = "网络已连接"
                            binding.statusIcon.setImageResource(R.drawable.ic_network_available)
                        } else {
                            // 网络已断开
                            binding.statusText.text = "网络已断开"
                            binding.statusIcon.setImageResource(R.drawable.ic_network_unavailable)
                        }
                    }
                }
            }
        }
    }
    ```

## 直接使用`NetworkMonitor`
1. 直接在**Activity/Fragment**中使用
   ```kotlin
    class MainActivity : AppCompatActivity() {
        private lateinit var networkMonitor: NetworkMonitor
    
        override fun onCreate(savedInstanceState: Bundle?) {
            super.onCreate(savedInstanceState)
            setContentView(R.layout.activity_main)
            
            networkMonitor = NetworkMonitor(applicationContext)
            
            lifecycleScope.launch {
                lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                    networkMonitor.isConnectedFlow.collect { isConnected ->
                        updateNetworkUI(isConnected)
                    }
                }
            }
        }
    }
   ```
   
2. 作为应用级别的单例
   ```kotlin
   // 在Application类中
    class MyApplication : Application() {
    // 使用lazy委托确保单例延迟初始化
    val networkMonitor by lazy { NetworkMonitor(applicationContext) }
    
        companion object {
            lateinit var instance: MyApplication
                private set
        }
        
        override fun onCreate() {
            super.onCreate()
            instance = this
        }
    }
    
    // 在任何地方使用
    class SomeActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    
            lifecycleScope.launch {
                MyApplication.instance.networkMonitor.isConnectedFlow.collect { isConnected ->
                    // 使用网络状态
                }
            }
        }
    }
   ```
   
3. 通过依赖注入提供（Hilt/Dagger）
    ```kotlin
   // 在模块中提供
   
    @Module
    @InstallIn(SingletonComponent::class)
    object NetworkModule {
        @Provides
        @Singleton
        fun provideNetworkMonitor(@ApplicationContext context: Context): NetworkMonitor {
            return NetworkMonitor(context)
        }
    }
    
    // 在需要的地方注入
    @AndroidEntryPoint
    class SomeFragment : Fragment() {
        @Inject
        lateinit var networkMonitor: NetworkMonitor
    
        override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
            super.onViewCreated(view, savedInstanceState)
            
            viewLifecycleOwner.lifecycleScope.launch {
                viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                    networkMonitor.networkStateFlow.collect { state ->
                        // 使用网络状态
                    }
                }
            }
        }
    }
   ```

4. 通过自定义Composable在Jetpack Compose中使用：
    ```kotlin
    @Composable
    fun NetworkAwareContent(
    networkMonitor: NetworkMonitor = remember { NetworkMonitor(LocalContext.current) },
    content: @Composable (isConnected: Boolean) -> Unit
    ) {
        val isConnected by networkMonitor.isConnectedFlow
        .collectAsState(initial = false)
    
        content(isConnected)
    }
    
    // 在Compose界面中使用
    @Composable
    fun MyScreen() {
        NetworkAwareContent { isConnected ->
            if (isConnected) {
                // 显示正常内容
                MainContent()
            } else {
                // 显示网络断开UI
                NoNetworkContent()
            }
        }
    }
    ```

5. 作为协程Flow组合管道的一部分：
    ```Kotlin
    class DataRepository(
        private val networkMonitor: NetworkMonitor,
        private val apiService: ApiService,
        private val localDb: LocalDatabase
    ) {
        fun getDataFlow(): Flow<Resource<List<Item>>> = networkMonitor.isConnectedFlow.flatMapLatest { isConnected ->
            if (isConnected) {
                // 有网络时从API获取数据并缓存
                flow {
                    emit(Resource.Loading())
                    try {
                        val remoteData = apiService.fetchData()
                        localDb.saveItems(remoteData)
                        emit(Resource.Success(remoteData))
                    } catch (e: Exception) {
                        emit(Resource.Error(e.message ?: "Unknown error"))
                    }
                }
            } else {
                // 无网络时从本地数据库获取
                localDb.getItemsFlow().map { Resource.Success(it) }
            }
        }
    }
   ```