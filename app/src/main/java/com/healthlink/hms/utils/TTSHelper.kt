package com.healthlink.hms.utils

import android.content.Context
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.util.Log
import java.lang.ref.WeakReference
import java.util.Locale
import java.util.UUID

object TTSHelper : TextToSpeech.OnInitListener {

    private const val TAG = "TTSHelper"

    // 使用弱引用持有 Context
    private var contextRef: WeakReference<Context>? = null

    private var tts: TextToSpeech? = null
    private var isInitialized = false

    // TTS 是否忙线
    @Volatile
    private var mBusy = false

    private var currentLocale: Locale = Locale.getDefault()

    // 使用弱引用持有监听器，防止内存泄漏
    private var statusListenerRef: WeakReference<OnTTSStatusListener>? = null

    /**
     * 初始化 TTS Helper
     * 在使用其他方法前必须先调用此方法
     */
    fun init(context: Context) {
        contextRef = WeakReference(context.applicationContext)
        initialize()
    }

    /**
     * 设置状态监听器
     */
    fun setOnTTSStatusListener(listener: OnTTSStatusListener?) {
        this.statusListenerRef = listener?.let { WeakReference(it) }
    }

    /**
     * 获取状态监听器
     */
    private fun getStatusListener(): OnTTSStatusListener? {
        return statusListenerRef?.get()
    }

    /**
     * 获取 Context
     */
    private fun getContext(): Context? {
        return contextRef?.get()
    }

    /**
     * 初始化 TTS 引擎
     */
    private fun initialize() {
        val context = getContext() ?: return

        if (tts == null) {
            try {
                Log.d(TAG, "初始化 TextToSpeech")
                tts = TextToSpeech(context, this)
            } catch (e: Exception) {
                Log.e(TAG, "TextToSpeech 初始化异常: ${e.message}")
                getStatusListener()?.onTTSError("初始化异常: ${e.message}")
            }
        } else if (isInitialized) {
            getStatusListener()?.onTTSInitialized(true)
        }
    }

    /**
     * TTS 初始化完成回调
     */
    override fun onInit(status: Int) {
        if (status == TextToSpeech.SUCCESS) {
            Log.d(TAG, "TextToSpeech 初始化成功")

            // 设置发音完成的监听器
            setupUtteranceListener()

            // 设置默认语言
            setLanguage(Locale.CHINESE)

            // 更新初始化状态
            isInitialized = true
            getStatusListener()?.onTTSInitialized(true)
        } else {
            Log.e(TAG, "TextToSpeech 初始化失败，状态码: $status")
            isInitialized = false
            getStatusListener()?.onTTSInitialized(false)
            getStatusListener()?.onTTSError("初始化失败，状态码: $status")
        }
    }

    /**
     * 设置语言
     * @return true 如果语言设置成功，false 如果语言不支持
     */
    fun setLanguage(locale: Locale): Boolean {
        if (!isInitialized || tts == null) {
            Log.e(TAG, "TTS 未初始化，无法设置语言")
            return false
        }

        val result = tts!!.setLanguage(locale)

        return if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
            Log.e(TAG, "语言不支持: $locale")
            getStatusListener()?.onTTSError("语言不支持: ${locale.displayLanguage}")
            false
        } else {
            currentLocale = locale
            Log.d(TAG, "语言设置成功: $locale")
            true
        }
    }

    /**
     * 设置发音相关参数
     */
    fun setSpeechParams(speechRate: Float = 1.0f, pitch: Float = 1.0f) {
        if (!isInitialized || tts == null) return

        tts!!.setSpeechRate(speechRate)
        tts!!.setPitch(pitch)
    }

    /**
     * 设置发音完成的监听器
     */
    private fun setupUtteranceListener() {
        tts?.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
            override fun onStart(utteranceId: String?) {
                Log.d(TAG, "开始朗读: $utteranceId")
                mBusy = true
                getStatusListener()?.onTTSSpeakStart(utteranceId)
            }

            override fun onDone(utteranceId: String?) {
                Log.d(TAG, "朗读完成: $utteranceId")
                mBusy = false
                getStatusListener()?.onTTSSpeakDone(utteranceId)
            }

            override fun onStop(utteranceId: String?, interrupted: Boolean) {
                Log.d(TAG, "朗读被停止: $utteranceId interrupted: $interrupted")
                mBusy = false
                getStatusListener()?.onTTSSpeakStop(utteranceId,interrupted)
            }

            @Deprecated("不再使用")
            override fun onError(utteranceId: String?) {
                Log.e(TAG, "朗读错误: $utteranceId")
                mBusy = false
                getStatusListener()?.onTTSSpeakError(utteranceId,"朗读出错")
            }

            override fun onError(utteranceId: String?, errorCode: Int) {
                super.onError(utteranceId, errorCode)
                Log.e(TAG, "朗读错误: $utteranceId, 错误码: $errorCode")
                getStatusListener()?.onTTSSpeakError(utteranceId,"朗读出错，错误码: $errorCode")
            }
        })
    }

    /**
     * 朗读文本
     * @param text 要朗读的文本
     * @param queueMode 朗读模式（默认清除队列后朗读）
     * @return true 如果请求成功，false 如果失败
     */
    fun speak(text: String, queueMode: Int = TextToSpeech.QUEUE_FLUSH): Boolean {
        if (!isInitialized || tts == null) {
            Log.e(TAG, "TTS 未初始化，无法朗读")
            getStatusListener()?.onTTSError("TTS 未初始化，无法朗读")
            return false
        }

        // 使用参数确保收到回调
        val params = HashMap<String, String>()
        params[TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID] = UUID.randomUUID().toString()

        val result = tts!!.speak(text, queueMode, params)

        if (result == TextToSpeech.ERROR) {
            Log.e(TAG, "朗读请求失败")
            getStatusListener()?.onTTSError("朗读请求失败")
            return false
        }

        return true
    }

    /**
     * 是否在播放中
     */
    fun isPlaying() : Boolean = mBusy

    /**
     * 停止朗读
     */
    fun stop() {
        if (isInitialized && tts != null) {
            tts!!.stop()
        }
    }

    /**
     * 检查 TTS 是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized

    /**
     * 获取当前语言
     */
    fun getCurrentLanguage(): Locale = currentLocale

    /**
     * 获取可用语言列表
     */
    fun getAvailableLanguages(): Set<Locale>? {
        return if (isInitialized && tts != null) {
            tts!!.availableLanguages
        } else null
    }

    /**
     * 释放资源
     */
    fun release() {
        stop()
        tts?.shutdown()
        tts = null
        isInitialized = false
        statusListenerRef = null // 清除监听器引用
        contextRef = null // 清除上下文引用
    }
}