package com.healthlink.hms.utils

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.TextView
import android.widget.Toast
import com.healthlink.hms.BuildConfig
import com.healthlink.hms.R
import com.healthlink.hms.ktExt.dp
import me.jessyan.autosize.AutoSizeCompat
import java.lang.ref.WeakReference

/**
 * 自定义Toast类，实现字体大小的设定
 */
object ToastUtil {

    private var currentToast: Toast? = null

    /**
     * 重新实现Toast的makeText函数
     */
    fun makeText(context: Context,text:CharSequence,duration:Int):Toast{

        if(currentToast!=null){
            currentToast?.cancel()
        }
        AutoSizeCompat.autoConvertDensityOfGlobal(context.resources) // 如果没有自定义需求用这个方法

        var inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        var layout = inflater.inflate(R.layout.toast, null);
        var textView = layout.findViewById<TextView>(R.id.toast_text);
        textView.text = text;

        val weakContext = WeakReference<Context>(context.applicationContext)
        var toast = Toast(weakContext.get());
        toast.duration = duration
        // Gravity.TOP 在 v4 显示在坐标 0 点 和 v35 在状态栏下
        val yOffset = if(BuildConfig.PLATFORM_CODE == "V35")  100.dp.toInt() else 176.dp.toInt()
        toast.setGravity(Gravity.TOP or Gravity.CENTER_VERTICAL,0,yOffset)
        toast.setView(layout)
        this.currentToast = toast
        return toast
    }


    /**
     * 重新实现Toast的makeText函数
     */
//    fun makeText(context: Context,text:CharSequence,duration:Int):Toast{
//
//        if(currentToast!=null){
//            currentToast?.cancel()
//        }
//        val view = LayoutInflater.from(context).inflate(com.gwm.widget.R.layout.layout_widget_toast, null)
//        val messageView = view.findViewById<TextView>(com.gwm.widget.R.id.toast_message)
//        messageView.text = text
//        val layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
//        view.layoutParams = layoutParams
//
//        val toast = Toast(context)
//        toast.setGravity(Gravity.TOP,0,176.dp.toInt())
//        toast.duration = duration
//        toast.view = view
//        this.currentToast = toast
//        return toast
//    }


    fun isToastShowing(): Boolean {
        return currentToast != null
    }

    fun cancelToast() {
        if(currentToast != null) {
            currentToast?.cancel()
            currentToast = null
        }
    }
}