package com.healthlink.hms.utils

import android.util.Log
import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.time.temporal.TemporalAdjusters
import java.util.Calendar
import java.util.concurrent.TimeUnit

/**
 * Created by imaginedays on 2024/6/5
 *
 *
 */
object TimeUtils {
    private const val TAG = "TimeUtils"

    /**
     * 传入分钟数返回多少小时多少分钟
     */
    @JvmStatic
    fun formatMinutes(totalMinutes: String?): String {
        val default = "0小时0分"
        if (totalMinutes == null || totalMinutes == "0") {
            return default
        }

        try {
            val allMinutes = totalMinutes.toInt()

            val days = allMinutes / (24 * 60)
            val hours = (allMinutes % (24 * 60)) / 60
            val minutes = allMinutes % 60

            return if (allMinutes < 60) {
                // 小于60分钟
                "${if (minutes < 10) "0小时0${minutes}" else "0小时$minutes"}分"
            } else if (allMinutes < 24 * 60) {
                // 小于一天（60分钟以上）
                "${hours}小时${if (minutes < 10) "0$minutes" else "$minutes"}分"
            } else {
                // 大于一天
                "${days}天${hours}小时${if (minutes < 10) "0$minutes" else "$minutes"}分"
            }
        } catch (e: Exception) {
            return default
        }
    }

    /**
     * 获取当前一周的每一天 日期格式为 MM-dd
     */
//    fun getCurrentWeekDatesForCard(pattern: String = "yyyy年MM月dd日"): List<String> {
//        val dates = mutableListOf<String>()
//        val today = LocalDate.now()
//        val formatter = DateTimeFormatter.ofPattern(pattern)
//
//        // 获取本周的周一
//        val startOfWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
//
//        // 从周一开始遍历到周日
//        for (i in 0..6) {
//            val currentDate = startOfWeek.plusDays(i.toLong())
//            dates.add(currentDate.format(formatter))
//        }
//
//        return dates
//    }

    /**
     * 获取当前一周的每一天 日期格式为 MM-dd
     */
    fun getCurrentWeekDates(pattern: String = "MM-dd"): List<String> {
        val dates = mutableListOf<String>()
        val today = LocalDate.now()
        val formatter = DateTimeFormatter.ofPattern(pattern)

        // 获取本周的周一
        val startOfWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))

        // 从周一开始遍历到周日
        for (i in 0..6) {
            val currentDate = startOfWeek.plusDays(i.toLong())
            dates.add(currentDate.format(formatter))
        }

        return dates
    }

    /**
     * 获取当前月的所有天 日期格式为 MM-dd
     */
    fun getCurrentMonthDates(pattern: String? = "MM-dd"): List<String> {
        val dates = mutableListOf<String>()
        val today = LocalDate.now()
        val formatter = DateTimeFormatter.ofPattern(pattern)

        // 获取当前月的第一天
        val startOfMonth = today.withDayOfMonth(1)
        // 获取当前月的最后一天
        val endOfMonth = today.withDayOfMonth(today.lengthOfMonth())

        // 从月初开始遍历到月末
        var currentDate = startOfMonth
        while (!currentDate.isAfter(endOfMonth)) {
            dates.add(currentDate.format(formatter))
            currentDate = currentDate.plusDays(1)
        }

        return dates
    }

    /**
     * 获取当前的年的所有月份 日期格式为 yyyy-MM 2024-01
     */
    fun getAllMonthsOfCurrentYear(pattern: String? = "yyyy-MM"): List<String> {
        val months = mutableListOf<String>()
        val currentYear = YearMonth.now().year
        val formatter = DateTimeFormatter.ofPattern(pattern)

        // 遍历当前年的所有月份
        for (month in 1..12) {
            val yearMonth = YearMonth.of(currentYear, month)
            months.add(yearMonth.format(formatter))
        }

        return months
    }

    /**
     * 获取当前年 格式为 yyyy 2024
     */
    private fun getCurrentYear(): String {
        return YearMonth.now().year.toString()
    }

    fun getLastUpdateDateText(timeStr: String?): String {
        var time = ""
        timeStr?.let {
            if (it.length > 10) {
                time = if (it.contains(getCurrentYear())) {
                    timeStr.substring(5, 10)
                } else {
                    timeStr.substring(0, 10)
                }
            }
        }
        return time
    }

    /**
     * @param inputDate 输入日期字符串
     * @param inputFormat 输入日期格式
     * @param outputFormat 输出日期格式
     */
    fun formatDateString(
        inputDate: String,
        inputFormat: String? = "yyyy-MM-dd",
        outputFormat: String? = "MM-dd"
    ): String {
        return try {
            // 定义输入日期格式
            val inputFormatter = DateTimeFormatter.ofPattern(inputFormat)
            // 解析输入日期字符串
            val date = LocalDate.parse(inputDate, inputFormatter)
            // 定义输出日期格式
            val outputFormatter = DateTimeFormatter.ofPattern(outputFormat)
            // 格式化日期为输出格式字符串
            date.format(outputFormatter)
        } catch (e: DateTimeParseException) {
            // 日期解析失败，返回默认的输入日期字符串
            inputDate
        }
    }

    /**
     * 将时间戳转换为指定格式的字符串
     */
    fun formatDateString(timestamp: Long, format: String? = "yyyy-MM-dd HH:mm:ss"): String {
        val formatter = DateTimeFormatter.ofPattern(format)
        val dateTime =
            LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault())
        return dateTime.format(formatter)
    }

    /**
     * 今天是否是周五
     */
    fun isTodayFriday(): Boolean {
        val today = LocalDate.now()
        return today.dayOfWeek == DayOfWeek.FRIDAY
    }

    /**
     * 是否在指定时间段内
     * @param startHour 开始小时
     * @param startMinute 开始分钟
     * @param endHour 结束小时
     * @param endMinute 结束分钟
     */
    fun isCurrentTimeInRange(
        startHour: Int,
        startMinute: Int,
        endHour: Int,
        endMinute: Int
    ): Boolean {

        val calendar = Calendar.getInstance()

        // 获取当前时间的小时和分钟（24小时制）
        val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
        val currentMinute = calendar.get(Calendar.MINUTE)
        Log.i("TimeUtils", "currentHour: $currentHour, currentMinute: $currentMinute")

        // 检查当前时间是否在开始时间到午夜12点之间
        if (currentHour > startHour || (currentHour == startHour && currentMinute >= startMinute)) {
            if (currentHour < 24 || (currentHour == 24 && currentMinute == 0)) {
                return true
            }
        }
        // 检查当前时间是否在午夜12点到结束时间之间
        if (currentHour < endHour || (currentHour == endHour && currentMinute < endMinute)) {
            if (currentHour >= 0 || (currentHour == 0 && currentMinute == 0)) {
                return true
            }
        }

        return false
    }

    /**
     * 包括16点和21点
     */
    fun isCurrentTimeInTodayRange(): Boolean {
        // 获取当前时间
        val currentTime = LocalTime.now()
        // 定义时间范围
        val startTime = LocalTime.of(16, 0) // 下午 4 点
        val endTime = LocalTime.of(21, 0)   // 晚上 9 点
        Log.i("TimeUtils", "currentTime: $currentTime, startTime: $startTime, endTime: $endTime")
        // 判断当前时间是否在范围内
        return (currentTime.isAfter(startTime) || currentTime == startTime) &&
                (currentTime.isBefore(endTime) || currentTime == endTime && currentTime.minute == 0)
    }

    /**
     * 获取当前周的x轴日期list
     */
    fun getWeekListStr(): ArrayList<String> {
        var list = mutableListOf<String>()
        val today = LocalDate.now()
        val monday = today.with(DayOfWeek.MONDAY)
        list.add(formatWeekDate(monday))
        for (i in 1..6) {
            list.add(formatWeekDate(monday.plusDays(i.toLong())))
        }
        return list as ArrayList
    }

    fun getWeekListStrChn(): ArrayList<String> {
        var list = mutableListOf<String>()
        val today = LocalDate.now()
        val monday = today.with(DayOfWeek.MONDAY)
        list.add(formatWeekDate(monday))
        for (i in 1..6) {
            list.add(formatWeekDateChn(monday.plusDays(i.toLong())))
        }
        return list as ArrayList
    }

    fun formatWeekDate(date: LocalDate): String {
        val month = String.format("%02d", date.monthValue)
        val day = String.format("%02d", date.dayOfMonth)
        return "$month-$day"
    }

    fun formatWeekDateChn(date: LocalDate): String {
        val month = String.format("%d", date.monthValue)
        val day = String.format("%02d", date.dayOfMonth)
        return "$month-$day"
    }

    /**
     * 获取当前月的x轴日期list
     */
    fun getMonthListStr(): ArrayList<String> {
        var list = mutableListOf<String>()
        val now = LocalDate.now()
        val month = now.monthValue
        val lastDay = now.with(TemporalAdjusters.lastDayOfMonth()).dayOfMonth
        for (i in 1..lastDay) {
            list.add(formatMonthDate(month, i))
        }
        return list as ArrayList
    }

    fun getMonthListStrChn(): ArrayList<String> {
        var list = mutableListOf<String>()
        val now = LocalDate.now()
        val month = now.monthValue
        val lastDay = now.with(TemporalAdjusters.lastDayOfMonth()).dayOfMonth
        for (i in 1..lastDay) {
            list.add(formatMonthDateChn(month, i))
        }
        return list as ArrayList
    }

    fun formatMonthDate(month: Int, day: Int): String {
        val month = String.format("%02d", month)
        val day = String.format("%02d", day)
        return "$month-$day"
    }

    fun formatMonthDateChn(month: Int, day: Int): String {
        val month = String.format("%d", month)
        val day = String.format("%02d", day)
        return "${month}月${day}日"
    }

    /**
     * 获取当前年
     */
    fun getCurrentYearStr():String{
        return Calendar.getInstance().get(Calendar.YEAR).toString()
    }

    /**
     * 根据不同步进获取日期list
     */
    fun getXDataWithStep(list: ArrayList<String>, step: Int): ArrayList<String> {
        var dataSet = ArrayList<String>()
        val fisrtMonday = getFirstMondayOfMonth()
        var temp=0
        list.forEachIndexed { index, item ->
            if (index < fisrtMonday-1) {
                dataSet.add("")
            } else {
                if (temp == 0){
                    dataSet.add(item)
                    temp++
                    return@forEachIndexed
                }
                if (temp % step == 0) {
                    dataSet.add(item)
                } else
                    dataSet.add("")
                temp++
            }
        }
        return dataSet
    }

    /**
     * 无数据返回日期 周月
     */
    fun resetWMDateTime(oriDate: String): String {
        try {
            val md = oriDate.split("-")
            return "${md[0].toInt()}月${md[1].toInt()}日"
        } catch (e: Exception) {
            e.printStackTrace()
            return ""
        }
    }


    /**
     * 无数据返回日期 年
     */
    fun resetYDateTime(oriMonth: String): String {
        try {
            var tempMonth=oriMonth
            if (oriMonth.contains("\n")){
                tempMonth=oriMonth.split("\n")[1]
            }
            val year = Calendar.getInstance()[Calendar.YEAR]
            return "${year}年${tempMonth.toInt()}月"
        } catch (e: Exception) {
            e.printStackTrace()
            return ""
        }
    }

    fun getFirstMondayOfMonth(): Int {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        while (calendar.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY) {
            calendar.add(Calendar.DAY_OF_MONTH, 1)
        }
        return calendar.get(Calendar.DAY_OF_MONTH)
    }

    /**
     * 计算两个时间戳的插值
     * 换算成时分秒
     */
    fun calculateTimeDifference(startTimestamp: Long, endTimestamp: Long): String {
        val diffInMillis = endTimestamp - startTimestamp

        // 转换为小时、分钟、秒
        val hours = TimeUnit.MILLISECONDS.toHours(diffInMillis)
        val minutes = TimeUnit.MILLISECONDS.toMinutes(diffInMillis) % 60
        val seconds = TimeUnit.MILLISECONDS.toSeconds(diffInMillis) % 60

        return String.format("%02d小时 %02d分钟 %02d秒", hours, minutes, seconds)
    }

    /**
     * 把毫秒数格式化为时分秒
     */
    fun formatMilliseconds(milliSeconds: Long): String{
        // 转换为小时、分钟、秒
        val hours = TimeUnit.MILLISECONDS.toHours(milliSeconds)
        val minutes = TimeUnit.MILLISECONDS.toMinutes(milliSeconds) % 60
        val seconds = TimeUnit.MILLISECONDS.toSeconds(milliSeconds) % 60

        return String.format("%02d小时 %02d分钟 %02d秒", hours, minutes, seconds)
    }


}