package com.healthlink.hms.utils

import android.content.Intent
import android.database.ContentObserver
import android.os.Handler
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.fragment.HMSServiceListFragment

/**
 *@Author: 付仁秀
 *@Description：
 **/
class PrivacyModeObserver(handler: Handler?) : ContentObserver(handler) {
    override fun onChange(selfChange: Boolean) {
        super.onChange(selfChange)
        Log.d("PrivacyModeObserver", "onChange: ->"+ HmsApplication.isPrivacyModeEnabled())
        val intent = Intent("com.healthlink.hms.privacymode")
        intent.putExtra("pm",  HmsApplication.isPrivacyModeEnabled())
        LocalBroadcastManager.getInstance(HmsApplication.appContext).sendBroadcast(intent)

        HMSServiceListFragment().refreshWidget()
    }

}