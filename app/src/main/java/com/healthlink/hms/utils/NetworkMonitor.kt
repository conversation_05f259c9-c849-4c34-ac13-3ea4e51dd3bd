package com.healthlink.hms.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map

class NetworkMonitor(private val context: Context) {
    private val connectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

    // 网络状态数据模型
    sealed class NetworkState {
        object Available : NetworkState()
        object Unavailable : NetworkState()
    }

    // 创建网络状态Flow
    val networkStateFlow: Flow<NetworkState> = callbackFlow {
        val networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                connectivityManager.getNetworkCapabilities(network)?.let {
                    if (it.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)) {
                        trySend(NetworkState.Available)
                    }
                }
            }

            override fun onLost(network: Network) {
                trySend(NetworkState.Unavailable)
            }

            override fun onUnavailable() {
                trySend(NetworkState.Unavailable)
            }

            override fun onCapabilitiesChanged(
                network: Network,
                capabilities: NetworkCapabilities
            ) {
                if (capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
                    trySend(NetworkState.Available)
                } else {
                    trySend(NetworkState.Unavailable)
                }
            }
        }

        // 构建网络请求
        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
            .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
            .addTransportType(NetworkCapabilities.TRANSPORT_ETHERNET)
            .build()

        // 注册网络回调
        connectivityManager.registerNetworkCallback(networkRequest, networkCallback)

        // 发送初始状态
        val currentState = getCurrentNetworkState()
        trySend(currentState)

        // 当Flow收集器取消时执行清理
        awaitClose {
            connectivityManager.unregisterNetworkCallback(networkCallback)
        }
    }.distinctUntilChanged() // 仅当状态变化时才发出

    // 简单的Boolean流，表示是否有网络连接
    val isConnectedFlow: Flow<Boolean> = networkStateFlow.map { state ->
        state == NetworkState.Available
    }

    // 获取当前网络状态
    private fun getCurrentNetworkState(): NetworkState {
        val activeNetwork = connectivityManager.activeNetwork ?: return NetworkState.Unavailable
        val capabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return NetworkState.Unavailable

        return if (capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
            capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
            NetworkState.Available
        } else {
            NetworkState.Unavailable
        }
    }

    // 检查当前是否有网络连接
    fun isNetworkAvailable(): Boolean {
        return getCurrentNetworkState() == NetworkState.Available
    }

}