package com.healthlink.hms.utils

import android.app.Notification
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.drawable.Icon
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity.MODE_PRIVATE
import com.healthlink.hms.BuildConfig
import com.healthlink.hms.R
import com.healthlink.hms.activity.HealthReportActivity
import com.healthlink.hms.activity.MainActivity
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.base.Constants
import com.healthlink.hms.business.doctorcall.DoctorCallManager
import com.healthlink.hms.reciever.HMSAction
import com.petterp.floatingx.FloatingX

class NotificationUtil(private val context: Context) {
    //渠道Id
    private val channelId = HmsApplication.channelId
    private val TAG = "NotificationUtil"
    /**
     * 发送系统消息通知
     */
    private fun sendNotification(context: Context, notification: Notification, notificationId: Int) {
        Log.i(TAG, "sendNotification notificationId = $notificationId")
        val manager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        // id必须大于0，id是每个通知的唯一标识，建议每个通知的id唯一
        manager.notify(notificationId, notification)
        // 发送广播通知健康Tips取消
        context.sendBroadcast(Intent(HMSAction.ACTION_CANCEL_PLAY_HEALTH_TIPS))
    }

    /**
     *  取消通知
     */
    fun cancelNotification(notificationId: Int){
        val manager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        // 使用创建通知时的id来取消
        manager.cancel(notificationId)
    }

    /**
     * 开机迎宾 无健康异常通知
     */
    fun sendWelcomeNotification(ttsMsg: String,contentText: String){
        var notificationId = createNotificationId()
        val bundle = createTTSBundle(ttsMsg)
        Log.i(TAG,"sendWelcomeNotification notificationId = $notificationId")

        val mBuild = Notification.Builder(context, channelId).setSmallIcon(R.mipmap.ic_launcher)
            .addExtras(bundle)
            .setContentTitle("健康提醒")
            .setContentText(splitContentText(contentText))
            .setWhen(System.currentTimeMillis())
            .setShowWhen(true)
            .setAutoCancel(true)
            .setContentIntent(PendingIntent.getActivity(context, 0, Intent(), PendingIntent.FLAG_IMMUTABLE))
        var notification = mBuild.build()
        notification.flags = Notification.FLAG_AUTO_CANCEL // 设置点击通知后，通知自动消失
        sendNotification(context,notification, notificationId)
    }

    /**
     * 场景:开机迎宾 有健康异常通知
     * 应用内和应用外调用方式不一样，健康异常通知 左侧按钮：电话医生、 右侧按钮：查看详情
     * “点击电话医生”：判断是否完成了电话医生备案，完成：进入系统蓝牙拨号，未完成：进入App电话医生备案界面
     * “点击查看详情”：唤起App
     */
    fun sendCallDoctorNotification(ttsMsg: String,contentText: String, okTitleText: String? = "在线医生", cancelTitleText: String? =  "查看详情") {
        var notificationId = createNotificationId()
        val bundle = createTTSBundle(ttsMsg)
        Log.i(TAG,"sendCallDoctorNotification notificationId = $notificationId")
        var pendingIntentCancel: PendingIntent? = null
        var notification : Notification? = null

        // 左边 电话医生 是否绑定电话医生服务 是： 唤起蓝牙拨号键盘 否： 唤起绑定电话医生界面
        // 唤起蓝牙拨号键盘
        var pendingIntentDoctor = createDoctorPendingIntent()

        // 右边 查看详情
        val intent = Intent(context, MainActivity::class.java)
        pendingIntentCancel = PendingIntent.getActivity(
            context, 1, intent, PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        val mBuild = Notification.Builder(context, channelId).setSmallIcon(R.mipmap.ic_launcher)
            .addExtras(bundle)
            .setContentTitle("健康提醒")
            .setContentText(splitContentText(contentText))
            .setWhen(System.currentTimeMillis())
            .setShowWhen(true)
            .setAutoCancel(true)
        // 如果获取到了PendingIntent，则进行功能添加
        if(pendingIntentDoctor!=null) {
            mBuild.addAction(
                Notification.Action.Builder(
                    Icon.createWithResource(context, R.mipmap.ic_launcher),
                    //TODO 确认pendingIntentDoctor是否可null
                    okTitleText, pendingIntentDoctor
                ).build()
            )
        }
        // 如果获取到了PendingIntent，则进行功能添加
        if(pendingIntentCancel!=null) {
            mBuild.addAction(
                Notification.Action.Builder(
                    Icon.createWithResource(context, R.mipmap.ic_launcher),
                    cancelTitleText, pendingIntentCancel
                ).build()
            )
        }

        notification = mBuild.build()
        notification.flags = Notification.FLAG_AUTO_CANCEL // 设置点击通知后，通知自动消失

        context.getSharedPreferences("hms.data", MODE_PRIVATE)
            .edit()
            .putInt(Constants.NOTIFICATION_ID_PHONE_DOCTOR_SERVICE,notificationId)
            .commit()

        sendNotification(context,notification, notificationId)
    }

    private fun createDoctorPendingIntent(): PendingIntent? {
        val intent = Intent(context, MainActivity::class.java)
        intent.action = HMSAction.ACTION_OPEN_PHONE_DOCTOR_SERVICE
        return PendingIntent.getActivity(
            context,
            1,
            intent,
            PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )
    }

    /**
     * 场景二：下班关怀1：发送智能腰托打开的系统消息通知
     */
    fun sendNoSeatWaistDirectionNotification(ttsMsg: String) {

        var notificationId = createNotificationId()
        val bundle = createTTSBundle(ttsMsg)

        //打开智能腰托的处理类
        val intentForSeatWaist = Intent(HMSAction.ACTION_OPEN_SEAT_VENTILATION_MODE)
        intentForSeatWaist.setPackage(context.packageName)
        intentForSeatWaist.putExtra(Constants.NOTIFICATION_ID_SEAT_VENTILATION,notificationId)
        val pendingIntentForSeatWaist = PendingIntent.getBroadcast(
            context, 0, intentForSeatWaist, PendingIntent.FLAG_MUTABLE
        )

        //取消智能腰托的处理
        val intentForSeatWaistCancel = Intent(HMSAction.ACTION_OPEN_SEAT_VENTILATION_MODE_CANCEL)
        intentForSeatWaistCancel.setPackage(context.packageName)
        intentForSeatWaistCancel.putExtra(Constants.NOTIFICATION_ID_SEAT_VENTILATION,notificationId)
        val pendingIntentForSeatWaistCancel = PendingIntent.getBroadcast(
            context, 0, intentForSeatWaistCancel, PendingIntent.FLAG_MUTABLE
        )

        // 创建通知builder
        var notificationForSeatWaistDirection = createNotification(
            "健康提醒", bundle,
            "需要帮您打开座椅通风吗？",
            pendingIntentForSeatWaist,"好的",pendingIntentForSeatWaistCancel,"不需要")

        context.getSharedPreferences("hms.data", MODE_PRIVATE)
            .edit()
            .putInt(Constants.NOTIFICATION_ID_SEAT_VENTILATION,notificationId)
            .commit()

        sendNotification(context,notificationForSeatWaistDirection, notificationId)
    }
    /**
     * 场景三：下班关怀2：打开放松模式
     */
    fun sendOpenRelaxModeNotification(ttsMsg: String) {
        var notificationId = createNotificationId()
        val bundle = createTTSBundle(ttsMsg)
        Log.i(TAG,"sendOpenRelaxModeNotification notificationId = $notificationId")

        // 打开放松模式
        val intentForSeatWaist = Intent(HMSAction.ACTION_OPEN_RELAX_MODE)
        intentForSeatWaist.setPackage(context.packageName)
        intentForSeatWaist.putExtra(Constants.NOTIFICATION_ID_OPEN_RELAX_MODE,notificationId)
        val pendingIntentForSeatWaist = PendingIntent.getBroadcast(
            context, 0, intentForSeatWaist, PendingIntent.FLAG_MUTABLE
        )

        //关闭放松模式
        val intentForSeatWaistCancel = Intent(HMSAction.ACTION_CLOSE_RELAX_MODE)
        intentForSeatWaistCancel.setPackage(context.packageName)
        intentForSeatWaistCancel.putExtra(Constants.NOTIFICATION_ID_OPEN_RELAX_MODE,notificationId)
        val pendingIntentForSeatWaistCancel = PendingIntent.getBroadcast(
            context, 0, intentForSeatWaistCancel, PendingIntent.FLAG_MUTABLE
        )

        // 创建通知builder
        var notificationForSeatWaistDirection = createNotification(
            "健康提醒", bundle,
            "需要帮您放松一下吗？",
            pendingIntentForSeatWaist,"放松一下",pendingIntentForSeatWaistCancel,"不需要")

        context.getSharedPreferences("hms.data", MODE_PRIVATE)
            .edit()
            .putInt(Constants.NOTIFICATION_ID_OPEN_RELAX_MODE,notificationId)
            .commit()

        sendNotification(context,notificationForSeatWaistDirection, notificationId)
    }
    /**
     * 场景四： 点确认发送通知，导航App搜索沿途服务区/药店
     */
    fun sendOpenNavigationAppNotification(ttsMsg: String, isServiceAare: Boolean) {
        var notificationId = createNotificationId()
        val bundle = createTTSBundle(ttsMsg)
        Log.i(TAG,"sendOpenNavigationAppNotification notificationId = $notificationId")

        val intentForOK = Intent(HMSAction.ACTION_OPEN_NAVIGATION_SEARCH_SERVICE)
        intentForOK.setPackage(context.packageName)
        val keyWord = if (isServiceAare) "服务区" else "药店"
        intentForOK.putExtra("search_keyword", keyWord)
        Log.i("HMSBroadcastReciever","sendOpenNavigationAppNotification: $keyWord")
        intentForOK.putExtra(Constants.NOTIFICATION_ID_NAVIGATION_SEARCH_SERVICE,notificationId)
        val pendingIntentForOK = PendingIntent.getBroadcast(
            context, 0, intentForOK, PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        val intentForCancel = Intent(HMSAction.ACTION_CLOSE_NAVIGATION_SEARCH_SERVICE)
        intentForCancel.setPackage(context.packageName)
        intentForCancel.putExtra(Constants.NOTIFICATION_ID_NAVIGATION_SEARCH_SERVICE,notificationId)
        val pendingIntentForCancel = PendingIntent.getBroadcast(
            context, 0, intentForCancel, PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        // 创建通知builder
        var notification = createNotification(
            "健康提醒", bundle,
            "需要帮您搜索沿途的${keyWord}吗？",
            pendingIntentForOK,"搜索",pendingIntentForCancel,"不需要")

        context.getSharedPreferences("hms.data", MODE_PRIVATE)
            .edit()
            .putInt(Constants.NOTIFICATION_ID_NAVIGATION_SEARCH_SERVICE,notificationId)
            .commit()

        sendNotification(context,notification, notificationId)
    }

    /**
     * 健康严重异常关怀
     */
    fun sendHealthAbnormalNotification(ttsMsg: String,contentText: String,okTitleText: String,cancelTitleText: String? = "查看详情") {
        var notificationId = createNotificationId()
        val bundle = createTTSBundle(ttsMsg)

        Log.i(TAG,"sendHealthAbnormalNotification notificationId = $notificationId")

        // 左边 在线医生
        var pendingIntentOK  = createDoctorPendingIntent()
        // 右边 查看详情
        var pendingIntentCancel: PendingIntent? = null

        // 查看详情
        val intent = Intent(context, MainActivity::class.java)
        pendingIntentCancel = PendingIntent.getActivity(
            context, 1, intent, PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        // 创建通知builder
        val mBuild = Notification.Builder(context, channelId).setSmallIcon(R.mipmap.ic_launcher)
            .addExtras(bundle)
            .setContentTitle("健康提醒")
            .setContentText(splitContentText(contentText))
            .setWhen(System.currentTimeMillis())
            .setShowWhen(true)
            .setAutoCancel(true)

        // 如果还获取到了pendingIntent，则添加功能
        if(pendingIntentOK!=null) {
            mBuild.addAction(
                Notification.Action.Builder(
                    Icon.createWithResource(context, R.mipmap.ic_launcher),
                    okTitleText, pendingIntentOK
                ).build()
            )
        }
        // 如果还获取到了pendingIntent，则添加功能
        if(pendingIntentCancel!=null) {
            mBuild.addAction(
                Notification.Action.Builder(
                    Icon.createWithResource(context, R.mipmap.ic_launcher),
                    cancelTitleText, pendingIntentCancel
                ).build()
            )
        }

        var notification = mBuild.build()
        notification.flags = Notification.FLAG_AUTO_CANCEL // 设置点击通知后，通知自动消失
        // 发送通知
        sendNotification(context,notification, notificationId)
    }

    /**
     * 高反关怀2
     */
    fun sendCallDoctorOrEmergencyNotification(ttsMsg: String,contentText: String,okTitleText: String,cancelTitleText: String? = "不需要") {
        var notificationId = createNotificationId()
        val bundle = createTTSBundle(ttsMsg)

        Log.i(TAG,"sendCallDoctorOrEmergencyNotification notificationId = $notificationId")
        // 在线医生
        var pendingIntentOK  = createDoctorPendingIntent()

        // 取消
        var intentCancel: Intent? = null
        var pendingIntentCancel: PendingIntent? = null
        intentCancel = Intent(HMSAction.CLOSE_CALL_DOCTOR_NOTIFICATION).apply {
            `package` = context.packageName
            putExtra(Constants.NOTIFICATION_ID_PHONE_DOCTOR_SERVICE,notificationId)
        }
        pendingIntentCancel = PendingIntent.getBroadcast(context, 0, intentCancel, PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_UPDATE_CURRENT)

        // 创建通知builder
//        var notification = createNotification("健康提醒",bundle, contentText, pendingIntentOK,okTitleText, pendingIntentCancel,cancelTitleText!!)
        val mBuild = Notification.Builder(context, channelId).setSmallIcon(R.mipmap.ic_launcher)
            .addExtras(bundle)
            .setContentTitle("健康提醒")
            .setContentText(splitContentText(contentText))
            .setWhen(System.currentTimeMillis())
            .setShowWhen(true)
            .setAutoCancel(true)

        // 如果还获取到了pendingIntent，则添加功能
        if(pendingIntentOK!=null) {
            mBuild.addAction(
                Notification.Action.Builder(
                    Icon.createWithResource(context, R.mipmap.ic_launcher),
                    okTitleText, pendingIntentOK
                ).build()
            )
        }
        // 如果还获取到了pendingIntent，则添加功能
        if(pendingIntentCancel!=null) {
            mBuild.addAction(
                Notification.Action.Builder(
                    Icon.createWithResource(context, R.mipmap.ic_launcher),
                    cancelTitleText, pendingIntentCancel
                ).build()
            )
        }

       var notification = mBuild.build()
        notification.flags = Notification.FLAG_AUTO_CANCEL // 设置点击通知后，通知自动消失

        context.getSharedPreferences("hms.data", MODE_PRIVATE)
            .edit()
            .putInt(Constants.NOTIFICATION_ID_PHONE_DOCTOR_SERVICE,notificationId)
            .commit()

        sendNotification(context,notification, notificationId)
    }

    /**
     * 场景8：发送健康周报通知
     * @param ttsMsg 通知内容
     * @param contentText 通知内容
     */
    fun sendOpenHealthReportNotification(ttsMsg: String,contentText: String) {
        var notificationId = createNotificationId()
        val bundle = createTTSBundle(ttsMsg)

        Log.i(TAG,"sendOpenHealthReportNotification notificationId = $notificationId")

        // 打开健康周报界面
        val intent = Intent(context, HealthReportActivity::class.java)
        intent.putExtra("title", context.getString(R.string.health_report))
        intent.putExtra("changed_tab_index", 1) // 周
        intent.putExtra("is_change_tab",true)

        var okPendingIntent = PendingIntent.getActivity(
            context,
            1,
            intent,
            PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        // 创建通知builder
        var notification = createNotification(
            "健康提醒", bundle,
            contentText,
            okPendingIntent,"查看",null,"")
        notification.flags = Notification.FLAG_AUTO_CANCEL // 设置点击通知后，通知自动消失

        sendNotification(context,notification, notificationId)
    }
    /**
     * 创建通知ID
     */
    private fun createNotificationId(): Int {
        return (System.currentTimeMillis() / 1000).toInt()
    }
    /**
     * 创建TTS语音播报的Bundle
     */
    private fun createTTSBundle(ttsMsg: String): Bundle {
        Log.i(TAG,"notification播放TTS $ttsMsg")
        val bundle = Bundle()
        /**
         * 通知的优先级，等级： ，等级低的会被等级高的打断，并且某些场景下，低等级的 4>3>2>1
         * 无法弹出浮窗。
         * （比如在D挡位下，优先级为1的通知无法弹出）
         * 具体设计请参见通知管理 的优先级设定，此值不能随意设置，需要产品来定义，
         */
        bundle.putInt("notification_priority", 4)   // TODO 优先级应该是使用3。 4太高了。
        // 设置TTS语音播报 如果在通话中仅仅展示通知内容，而不播放TTS
        val finalTTS = if (DoctorCallManager.inCallSession || FloatingX.isInstalled(DoctorCallManager.TAG)) "" else ttsMsg
        bundle.putString("notification_tts_content", finalTTS)
        bundle.putString("notification_tts_priority", "1")
        //        bundle.putString("notification_tts_time_effective", "0")
        // 设置是否进入通知中心
        bundle.putBoolean("notification_transient", false)
        // 设置通知弹窗停留时间
        bundle.putInt("notification_stay_time", 12)
        return bundle
    }

    /**
     * 创建通知对象
     */
    private fun createNotification(contentTitle: String,
        bundle: Bundle,
        contentText: String,
        pendingIntentOK: PendingIntent?, okTitleText: String,
        pendingIntentCancel: PendingIntent?,cancelTitleText:String
    ): Notification {

        val mBuild = Notification.Builder(context, channelId).setSmallIcon(R.mipmap.ic_launcher)
            .addExtras(bundle)
            .setContentTitle(contentTitle)
            .setContentText(splitContentText(contentText))
            .setWhen(System.currentTimeMillis())
            .setShowWhen(true)
            // 左边按钮
            pendingIntentOK?.let {
                mBuild.addAction(
                Notification.Action.Builder(
                    Icon.createWithResource(context, R.mipmap.ic_launcher),
                    okTitleText, pendingIntentOK
                ).build())
            }

            // 右边按钮
            pendingIntentCancel?.let {
                mBuild.addAction(
                    Notification.Action.Builder(
                        Icon.createWithResource(context, R.mipmap.ic_launcher),
                        cancelTitleText, pendingIntentCancel
                    ).build()
                )
            }

        var notification = mBuild.build()
        return notification
    }

    private fun splitContentText(contentText: String) : String {
        var subText: String = contentText
        // 29 个字 + 3 个省略号 V35 26个字 + 3个省略号
        val maxCount = if(BuildConfig.PLATFORM_CODE == "V35") 26 else 29
        if(contentText.length > maxCount) {
            val sub =  contentText.substring(0, maxCount)
            subText = sub.plus("...")
        }
        return subText
    }

}