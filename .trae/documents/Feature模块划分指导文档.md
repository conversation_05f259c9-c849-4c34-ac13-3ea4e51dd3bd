# HMS Android 模块化架构 - Feature模块划分指导文档

## 文档概述

本文档基于对HMS项目的深入分析，提供了完整的Android模块化架构设计方案。通过合理的模块划分，实现代码的高内聚、低耦合，提升开发效率和代码质量。

## 当前项目状态分析

### 已实现的模块化结构

#### Core模块（基础设施层）

* **core:model** - 数据模型和实体定义

* **core:network** - 网络服务和API接口

* **core:data** - 数据仓库和本地存储（DataStore替代MMKV）

* **core:common** - 通用工具类和扩展函数

* **core:ui** - 可复用UI组件库

#### Feature模块（功能层）

* **feature:setting** - 系统设置功能（已实现）

### 13. 设置模块 (feature:setting)

**功能范围：**

* 应用设置管理和用户偏好配置

* 系统参数调整和隐私设置管理

* 设置界面和配置项管理

* 用户个性化选项

* 应用行为配置

**代码出处：**

* `feature/setting/src/main/java/com/healthlink/hms/feature/setting/ui/HMSPersonalActivity.kt` - 个人设置主界面

* `feature/setting/src/main/java/com/healthlink/hms/feature/setting/ui/DialogTransparentActivity.kt` - 透明对话框Activity

* `feature/setting/src/main/java/com/healthlink/hms/feature/setting/viewmodels/SettingsViewModel.kt` - 设置ViewModel

* `feature/setting/src/main/java/com/healthlink/hms/feature/setting/di/SettingsRepository.kt` - 设置数据仓库接口

* `feature/setting/src/main/java/com/healthlink/hms/feature/setting/di/DefaultSettingsRepository.kt` - 设置数据仓库实现

* `feature/setting/src/main/java/com/healthlink/hms/feature/setting/di/SettingsModule.kt` - Hilt依赖注入模块

* `feature/setting/src/main/res/` - 设置相关的UI资源文件

**包含文件：**

```
feature:setting/
├── src/main/java/com/healthlink/hms/feature/setting/
│   ├── di/
│   │   ├── DefaultSettingsRepository.kt (项目文件)
│   │   ├── SettingsModule.kt (项目文件)
│   │   └── SettingsRepository.kt (项目文件)
│   ├── ui/
│   │   ├── DialogTransparentActivity.kt (项目文件)
│   │   └── HMSPersonalActivity.kt (项目文件)
│   └── viewmodels/
│       └── SettingsViewModel.kt (项目文件)
├── src/main/res/
│   ├── drawable/
│   │   ├── setting_item_bg.xml (项目文件)
│   │   ├── setting_item_pressed.xml (项目文件)
│   │   └── setting_item_selector.xml (项目文件)
│   ├── layout/
│   │   ├── activity_dialog_transparent.xml (项目文件)
│   │   └── feature_setting_activity_personal.xml (项目文件)
│   └── values/ (项目文件)
├── build.gradle.kts (项目文件)
├── consumer-rules.pro (项目文件)
└── proguard-rules.pro (项目文件)
```

> 注：标记为(项目文件)的文件是实际存在于项目中的文件。model和utils相关功能已迁移到core模块中，符合模块化架构的最佳实践。

**依赖关系：**

* **core:model** - 使用健康数据相关的DTO类，如`BloodPressureResponseDTO`、`HeartRateDTO`、`SleepDayDataDTO`、`SpO2ItemDTO`、`TempItemDTO`、`Pressure1HourSummaryDTO`等健康指标数据模型

* **core:data** - 依赖`HealthDataRepository`、`HealthAuthorityRepository`等数据仓库接口进行健康数据的获取和存储

* **core:ui** - 使用通用的图表组件、健康相关图标资源（如`health_*.png`）、卡片背景样式（如`card_*.xml`）

* **core:common** - 使用`BaseVBVMActivity`作为基础Activity、`Constants`常量定义、`DateUtil`日期处理、`LogUtil`日志记录等工具类

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

### 待模块化的功能（当前在app模块中）

通过代码分析，发现以下功能模块仍在app模块中，需要进行模块化重构：

## 推荐的Feature模块划分方案

### 1. 健康监测模块 (feature:health)

**功能范围：**

* 健康数据监测（心率、血压、血氧、睡眠、压力、体温）

* 健康数据图表可视化和详情展示

* 健康权限管理

* 设备数据同步和管理

* 各类健康指标的详细分析页面

**代码出处：**

* `app/src/main/java/com/healthlink/hms/activity/card/HMSCardHeartRateDetailActivity.kt` - 心率详情页

* `app/src/main/java/com/healthlink/hms/activity/card/HMSCardBloodPressureDetailActivity.kt` - 血压详情页

* `app/src/main/java/com/healthlink/hms/activity/card/HMSCardSleepDetailActivity.kt` - 睡眠详情页

* `app/src/main/java/com/healthlink/hms/activity/card/HMSCardSpO2DetailActivity.kt` - 血氧详情页

* `app/src/main/java/com/healthlink/hms/activity/card/HMSCardTempDetailActivity.kt` - 体温详情页

* `app/src/main/java/com/healthlink/hms/activity/card/HMSCardPressureDetailActivity.kt` - 压力详情页

* `app/src/main/java/com/healthlink/hms/fragment/BloodPressureFragment.kt` - 血压数据展示

* `app/src/main/java/com/healthlink/hms/fragment/HMSHealthDetailFragment.kt` - 健康详情通用组件

* `app/src/main/java/com/healthlink/hms/fragment/SleepTimeFragment.kt` - 睡眠时间展示

* `app/src/main/java/com/healthlink/hms/fragment/SpO2TimeFragment.kt` - 血氧时间展示

* `app/src/main/java/com/healthlink/hms/fragment/TempTimeFragment.kt` - 体温时间展示

* `app/src/main/java/com/healthlink/hms/fragment/viewmodel/` - 各健康指标的ViewModel实现

**包含文件：**

```
feature:health/
├── src/main/java/com/healthlink/hms/feature/health/
│   ├── ui/
│   │   ├── activities/
│   │   │   ├── HMSCardHeartRateDetailActivity.kt (项目文件)
│   │   │   ├── HMSCardBloodPressureDetailActivity.kt (项目文件)
│   │   │   ├── HMSCardSleepDetailActivity.kt (项目文件)
│   │   │   ├── HMSCardSpO2DetailActivity.kt (项目文件)
│   │   │   ├── HMSCardTempDetailActivity.kt (项目文件)
│   │   │   └── HMSCardPressureDetailActivity.kt (项目文件)
│   │   ├── fragments/
│   │   │   ├── BloodPressureFragment.kt (项目文件)
│   │   │   ├── HMSHealthDetailFragment.kt (项目文件)
│   │   │   ├── SleepTimeFragment.kt (项目文件)
│   │   │   ├── SpO2TimeFragment.kt (项目文件)
│   │   │   └── TempTimeFragment.kt (项目文件)
│   │   └── charts/
│   │       ├── HealthDataChart.kt (AI创造)
│   │       └── HealthTrendChart.kt (AI创造)
│   ├── viewmodel/
│   │   ├── HealthDetailViewModel.kt (AI创造)
│   │   └── HealthDataViewModel.kt (AI创造)
│   ├── repository/
│   │   └── HealthDataRepository.kt (AI创造)
│   └── domain/
│       └── HealthDataUseCase.kt (AI创造)
```

> 注：标记为(AI创造)的文件是为模块化架构设计的建议文件结构，实际实施时需要根据具体需求创建。

**AI创造文件设计依据：**

* **HealthDataChart.kt/HealthTrendChart.kt** - 基于现有健康详情Activity和Fragment的数据可视化需求，创建专门的图表组件，实现健康数据的统一图表展示

* **HealthDetailViewModel.kt/HealthDataViewModel.kt** - 遵循MVVM架构模式，为现有的健康详情Activity和Fragment提供对应的ViewModel，实现业务逻辑与UI的分离

* **HealthDataRepository.kt** - 实现Repository模式，统一管理健康数据的获取和缓存，为现有的健康监测功能提供清晰的数据访问接口

* **HealthDataUseCase.kt** - 采用Clean Architecture的Domain层设计，封装健康数据的业务逻辑，支持现有的心率、血压、血氧、睡眠、体温、压力等健康指标的处理

**依赖关系：**

* **core:model** - 使用健康数据相关的DTO类，如`BloodPressureResponseDTO`、`HeartRateDTO`、`SleepDayDataDTO`、`SpO2ItemDTO`、`TempItemDTO`、`Pressure1HourSummaryDTO`等健康指标数据模型

* **core:data** - 依赖`HealthDataRepository`、`HealthAuthorityRepository`等数据仓库接口进行健康数据的获取和存储

* **core:ui** - 使用通用的图表组件、健康相关图标资源（如`health_*.png`）、卡片背景样式（如`card_*.xml`）

* **core:common** - 使用`BaseVBVMActivity`作为基础Activity、`Constants`常量定义、`DateUtil`日期处理、`LogUtil`日志记录等工具类

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

### 2. 香薰控制模块 (feature:aromatherapy)

**功能范围：**

* 香薰设备蓝牙连接和控制

* 香薰配方和强度管理

* 设备状态监控和电源控制

* 个性化香薰设置

* XH008协议的蓝牙通信管理

**代码出处：**

* `app/src/main/java/com/healthlink/hms/business/aromatherapy/AromatherapyActivity.kt` - 香薰控制主界面

* `app/src/main/java/com/healthlink/hms/business/aromatherapy/viewmodel/DeviceControlViewModel.kt` - 设备控制ViewModel

* `app/src/main/java/com/healthlink/hms/business/aromatherapy/model/DeviceState.kt` - 设备状态模型

* `app/src/main/java/com/healthlink/hms/business/aromatherapy/model/FragranceCombo.kt` - 香薰配方模型

* `app/src/main/java/com/healthlink/hms/business/aromatherapy/model/FragranceType.kt` - 香薰类型模型

* `app/src/main/java/com/healthlink/hms/business/aromatherapy/model/IntensityLevel.kt` - 强度等级模型

* `app/src/main/java/com/healthlink/hms/business/aromatherapy/model/PowerControlResult.kt` - 电源控制结果

* `app/src/main/java/com/healthlink/hms/business/aromatherapy/utils/XH008BluetoothManager.kt` - XH008蓝牙管理器

* `app/build/generated/data_binding_base_class_source_out/V35Debug/out/com/healthlink/hms/databinding/ActivityAromatherapyBinding.java` - 界面绑定

**包含文件：**

```
feature:aromatherapy/
├── src/main/java/com/healthlink/hms/feature/aromatherapy/
│   ├── ui/
│   │   └── AromatherapyActivity.kt (项目文件)
│   ├── model/
│   │   ├── DeviceState.kt (项目文件)
│   │   ├── FragranceCombo.kt (项目文件)
│   │   ├── FragranceType.kt (项目文件)
│   │   ├── IntensityLevel.kt (项目文件)
│   │   └── PowerControlResult.kt (项目文件)
│   ├── viewmodel/
│   │   └── DeviceControlViewModel.kt (项目文件)
│   ├── repository/
│   │   └── AromatherapyRepository.kt (AI创造)
│   └── bluetooth/
│       └── XH008BluetoothManager.kt (项目文件)
```

> 注：标记为(AI创造)的文件是为模块化架构设计的建议文件结构，实际实施时需要根据具体需求创建。

**AI创造文件设计依据：**

* **AromatherapyRepository.kt** - 实现Repository模式，统一管理香薰设备的配置数据和状态信息，为现有的AromatherapyActivity和DeviceControlViewModel提供统一的数据访问接口，支持设备状态、香薰配方、强度等级等数据的管理

**依赖关系：**

* **core:model** - 使用设备状态相关的数据模型，可能需要扩展香薰设备特定的DTO类

* **core:data** - 通过Repository模式管理香薰设备的配置数据和状态信息

* **core:ui** - 使用通用的UI组件、对话框样式、设备控制相关的图标资源

* **core:common** - 使用`BaseVBVMActivity`、`MMKVUtil`进行设备配置存储、`ToastUtil`显示操作反馈

* **feature:bluetooth** - 依赖蓝牙模块进行XH008协议的设备连接和通信

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

### 3. 医生通话模块 (feature:doctorcall)

**功能范围：**

* 远程医疗咨询和SIP网络电话

* 音频焦点管理和权限检查

* 通话悬浮窗和界面控制

* 通话状态管理和错误处理

* 麦克风可用性检测

* 二次拨号（DTMF）功能

* 通话录音服务

**代码出处：**

* `app/src/main/java/com/healthlink/hms/business/doctorcall/DoctorCallManager.kt` - 医生通话核心管理器（967行）

* `app/src/main/java/com/healthlink/hms/business/doctorcall/DoctorCallModel.kt` - 通话数据模型

* `app/src/main/java/com/healthlink/hms/business/doctorcall/DoctorCallService.kt` - 通话录音后台服务

* `app/src/main/java/com/healthlink/hms/business/doctorcall/DoctorCallUtils.kt` - 通话工具类

* `app/src/main/java/com/healthlink/hms/business/doctorcall/view/DoctorCallView.kt` - 通话界面组件

* `app/src/main/java/com/healthlink/hms/business/doctorcall/view/DoctorCallDialButton.kt` - 拨号按钮

* `app/src/main/java/com/healthlink/hms/business/doctorcall/view/DoctorCallDialPan.kt` - 拨号盘

* `app/src/main/java/com/healthlink/hms/activity/CallActivity.kt` - 通话Activity

**包含文件：**

```
feature:doctorcall/
├── src/main/java/com/healthlink/hms/feature/doctorcall/
│   ├── ui/
│   │   └── DoctorCallActivity.kt (AI创造)
│   ├── manager/
│   │   └── DoctorCallManager.kt (项目文件)
│   ├── model/
│   │   ├── DoctorCallModel.kt (项目文件)
│   │   ├── DoctorCallState.kt (AI创造)
│   │   └── DoctorCallViewState.kt (AI创造)
│   ├── viewmodel/
│   │   └── DoctorCallViewModel.kt (AI创造)
│   ├── repository/
│   │   └── DoctorCallRepository.kt (AI创造)
│   ├── service/
│   │   └── DoctorCallService.kt (项目文件)
│   ├── utils/
│   │   └── DoctorCallUtils.kt (项目文件)
│   └── view/
│       ├── DoctorCallView.kt (项目文件)
│       ├── DoctorCallDialButton.kt (项目文件)
│       └── DoctorCallDialPan.kt (项目文件)
```

> 注：标记为(AI创造)的文件是为模块化架构设计的建议文件结构，实际实施时需要根据具体需求创建。

**AI创造文件设计依据：**

* **DoctorCallActivity.kt** - 基于现有CallActivity的功能，创建专门的医生通话界面，实现UI层的模块化分离

* **DoctorCallState.kt/DoctorCallViewState.kt** - 将现有DoctorCallManager中的状态管理逻辑抽象为独立的状态模型，提高状态管理的清晰度和可测试性

* **DoctorCallViewModel.kt** - 遵循MVVM架构，为通话界面提供ViewModel，实现业务逻辑与UI的解耦

* **DoctorCallRepository.kt** - 实现Repository模式，统一管理通话记录和用户信息，为业务层提供数据访问抽象

**依赖关系：**

* **core:model** - 使用`BaseResponse`基础响应类、通话相关的数据模型和状态管理DTO

* **core:data** - 通过Repository模式管理通话记录、用户信息等数据

* **core:network** - 使用`ApiServiceKot`进行SIP服务器通信、`NetworkApi`处理网络请求、`HttpErrorHandler`处理网络异常

* **core:ui** - 使用通话界面相关的UI资源、对话框组件、动画效果

* **core:common** - 使用`BaseVBVMActivity`、权限管理工具、音频焦点管理等通用功能

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

### 4. 蓝牙设备管理模块 (feature:bluetooth)

**功能范围：**

* 蓝牙设备扫描和配对

* 蓝牙适配器状态检查

* 蓝牙权限管理和授权

* 设备连接状态监控

* 低功耗蓝牙（BLE）扫描

* 扫描结果处理和设备信息展示

**代码出处：**

* `app/src/main/java/com/healthlink/hms/activity/BlueToothManagerActivity.kt` - 蓝牙管理主界面（162行）

* `app/src/main/java/com/healthlink/hms/bluetooth/` - 蓝牙相关功能目录

* 蓝牙扫描回调实现：`scanCallback: ScanCallback`

* 蓝牙权限检查：`Manifest.permission.BLUETOOTH_CONNECT`

* 蓝牙适配器管理：`BluetoothManager` 和 `BluetoothAdapter`

* 扫描设置：`ScanSettings.SCAN_MODE_LOW_LATENCY`

**包含文件：**

```
feature:bluetooth/
├── src/main/java/com/healthlink/hms/feature/bluetooth/
│   ├── ui/
│   │   └── BlueToothManagerActivity.kt (项目文件)
│   ├── manager/
│   │   └── XH008BluetoothManager.kt (AI创造)
│   ├── model/
│   │   ├── BluetoothDeviceInfo.kt (AI创造)
│   │   └── ConnectionState.kt (AI创造)
│   ├── viewmodel/
│   │   └── BluetoothViewModel.kt (AI创造)
│   ├── repository/
│   │   └── BluetoothRepository.kt (AI创造)
│   └── scanner/
│       └── BluetoothScanner.kt (AI创造)
```

> 注：标记为(AI创造)的文件是为模块化架构设计的建议文件结构，实际实施时需要根据具体需求创建。

**AI创造文件设计依据：**

* **XH008BluetoothManager.kt** - 基于香薰模块中的XH008协议需求，创建专门的蓝牙管理器，实现蓝牙通信协议的标准化封装

* **BluetoothDeviceInfo.kt/ConnectionState.kt** - 将蓝牙设备信息和连接状态抽象为独立的数据模型，提高蓝牙功能的可维护性

* **BluetoothViewModel.kt** - 遵循MVVM架构，为蓝牙管理界面提供ViewModel，实现蓝牙业务逻辑的统一管理

* **BluetoothRepository.kt** - 实现Repository模式，管理蓝牙设备的配对信息和连接历史

* **BluetoothScanner.kt** - 封装蓝牙扫描逻辑，提供统一的设备发现接口

**依赖关系：**

* **core:model** - 使用蓝牙设备相关的数据模型，如设备信息、连接状态等DTO类

* **core:data** - 通过Repository模式管理蓝牙设备的配对信息和连接历史

* **core:ui** - 使用设备扫描界面的UI组件、设备列表样式、连接状态指示器

* **core:common** - 使用`BaseVBVMActivity`、权限管理工具、`DeviceUtil`设备信息获取、`LogUtil`调试日志

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

### 5. 认证授权模块 (feature:auth)

**功能范围：**

* 华为OAuth 2.0认证流程

* WebView登录界面管理

* 主题适配（白天/夜间模式）

* 登录状态管理和回调处理

* JavaScript桥接接口

* 网络异常和加载状态处理

* 用户授权和权限管理

**代码出处：**

* `app/src/main/java/com/healthlink/hms/activity/HuaweiOAuthActivity.kt` - 华为OAuth登录主界面（830行）

* `app/src/main/java/com/healthlink/hms/utils/AuthorizationUtil.kt` - 授权工具类

* OAuth URL配置：`https://oauth-login.cloud.huawei.com/oauth2/v3/authorize`

* 应用ID配置：`BuildConfig.HUAWEI_APP_ID`

* WebView客户端：`HuaweiOAuthWebViewClient`

* JavaScript接口：`AndroidInterface`

**包含文件：**

```
feature:auth/
├── src/main/java/com/healthlink/hms/feature/auth/
│   ├── ui/
│   │   └── HuaweiOAuthActivity.kt (项目文件)
│   ├── manager/
│   │   └── AuthManager.kt (AI创造)
│   ├── model/
│   │   ├── UserInfo.kt (AI创造)
│   │   ├── AuthState.kt (AI创造)
│   │   └── HWAuthStatusDTO.kt (AI创造)
│   ├── viewmodel/
│   │   └── AuthViewModel.kt (AI创造)
│   ├── repository/
│   │   ├── UserRepository.kt (AI创造)
│   │   └── AuthRepository.kt (AI创造)
│   └── utils/
│       └── AuthorizationUtil.kt (项目文件)
```

> 注：标记为(AI创造)的文件是为模块化架构设计的建议文件结构，实际实施时需要根据具体需求创建。

**AI创造文件设计依据：**

* **AuthManager.kt** - 基于现有HuaweiOAuthActivity的认证流程，创建专门的认证管理器，封装OAuth 2.0认证逻辑

* **UserInfo.kt/AuthState.kt/HWAuthStatusDTO.kt** - 将认证相关的数据结构抽象为独立的模型类，提高认证状态管理的清晰度

* **AuthViewModel.kt** - 遵循MVVM架构，为认证界面提供ViewModel，实现认证业务逻辑与UI的分离

* **UserRepository.kt/AuthRepository.kt** - 实现Repository模式，分别管理用户数据和认证状态，提供清晰的数据访问接口

**依赖关系：**

* **core:model** - 使用`HWAuthStatusDTO`华为认证状态、`UserInfoDTO`用户信息、`BaseResponse`等认证相关数据模型

* **core:data** - 依赖`UserRepository`进行用户数据管理和认证状态存储

* **core:network** - 使用`ApiServiceKot`进行OAuth认证请求、`NetworkApi`处理认证服务器通信、网络拦截器处理认证token

* **core:ui** - 使用WebView相关的UI资源、登录界面样式、加载动画效果

* **core:common** - 使用`BaseVBVMActivity`、`MMKVUtil`存储认证信息、`UIModeUtils`主题适配、`NetInfo`网络状态检查

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

### 6. 车载集成模块 (feature:vehicle)

**功能范围：**

* 车载平台适配器管理

* 车辆信息获取和状态监控

* 车载服务集成和协程支持

* VIP电源模式管理

* 车机适配器连接状态管理

**代码出处：**

* `app/src/main/java/com/healthlink/hms/sdks/gwmadapter/GwmAdapterManager.kt` - 车机适配器管理

* `app/src/main/java/com/healthlink/hms/sdks/gwmadapter/GwmAdapterManagerKotCoroutines.kt` - 协程版适配器管理

* `app/src/main/java/com/healthlink/hms/sdks/gwmadapter/IVIPowerMode.kt` - VIP电源模式接口

* 车载适配器客户端：`GwmAdapterClient`

* 场景引擎中的车载集成：`SceneAfterWorkCare1Impl`、`SceneLongDriveCare1Impl`等

**包含文件：**

```
feature:vehicle/
├── src/main/java/com/healthlink/hms/feature/vehicle/
│   ├── ui/
│   │   └── SeatAdjustmentFragment.kt (AI创造)
│   ├── adapter/
│   │   ├── GwmAdapterManager.kt (项目文件)
│   │   └── GwmAdapterManagerKotCoroutines.kt (项目文件)
│   ├── model/
│   │   ├── VehicleInfo.kt (AI创造)
│   │   ├── SeatPosition.kt (AI创造)
│   │   └── VehicleServiceModeDTO.kt (AI创造)
│   ├── viewmodel/
│   │   └── SeatViewModel.kt (AI创造)
│   ├── repository/
│   │   └── VehicleRepository.kt (AI创造)
│   └── service/
│       └── VehicleDataService.kt (AI创造)
```

> 注：标记为(AI创造)的文件是为模块化架构设计的建议文件结构，实际实施时需要根据具体需求创建。

**AI创造文件设计依据：**

* **SeatAdjustmentFragment.kt** - 基于车载集成的用户交互需求，提供座椅调节的专门界面，实现车载功能的UI模块化

* **VehicleInfo.kt/SeatPosition.kt/VehicleServiceModeDTO.kt** - 将车辆相关的数据结构抽象为独立的模型类，提高车载数据管理的规范性

* **SeatViewModel.kt** - 遵循MVVM架构，为座椅调节功能提供ViewModel，实现车载业务逻辑的统一管理

* **VehicleRepository.kt** - 实现Repository模式，管理车辆状态数据和配置信息

* **VehicleDataService.kt** - 封装车载数据服务逻辑，提供车辆信息的统一访问接口

**依赖关系：**

* **core:model** - 使用`VehicleServiceModeDTO`车辆服务模式、车辆信息相关的数据模型

* **core:data** - 通过Repository模式管理车辆状态数据和座椅调节配置

* **core:ui** - 使用车载界面相关的UI组件、座椅调节控制界面

* **core:common** - 使用`BaseVBVMActivity`、`SystemPropertyUtils`系统属性获取、车载适配器相关工具类

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

### 7. 地图导航模块 (feature:navigation)

**功能范围：**

* 地图服务集成

* 导航功能和路径规划

* 位置信息管理

* 场景引擎中的导航信息处理

**代码出处：**

* `app/src/main/java/com/healthlink/hms/sdks/map/gwm/` - 地图SDK集成目录

* `app/src/main/java/com/healthlink/hms/sceneEngine/dto/SceneNavigationInfoDTO.kt` - 导航信息数据模型

* 场景引擎中的导航集成：各个Scene实现类中的导航相关逻辑

**包含文件：**

```
feature:navigation/
├── src/main/java/com/healthlink/hms/feature/navigation/
│   ├── ui/
│   │   └── NavigationActivity.kt (AI创造)
│   ├── manager/
│   │   └── GWMMapManager.kt (AI创造)
│   ├── model/
│   │   ├── LocationInfo.kt (AI创造)
│   │   ├── NavigationRoute.kt (AI创造)
│   │   └── ServiceAreaInfo.kt (AI创造)
│   ├── viewmodel/
│   │   └── NavigationViewModel.kt (AI创造)
│   ├── repository/
│   │   └── NavigationRepository.kt (AI创造)
│   └── service/
│       └── MapService.kt (AI创造)
```

> 注：标记为(AI创造)的文件是为模块化架构设计的建议文件结构，实际实施时需要根据具体需求创建。

**依赖关系：**

* **core:model** - 使用`SceneNavigationInfoDTO`导航信息数据模型、位置相关的DTO类

* **core:data** - 通过Repository模式管理地图数据和导航路径信息

* **core:network** - 使用`ApiServiceKot`进行地图服务API调用、`NetworkApi`处理导航服务器通信

* **core:ui** - 使用地图显示相关的UI组件、导航界面样式、路径指示器

* **core:common** - 使用`BaseVBVMActivity`、位置权限管理工具、`LocationUtil`位置信息获取

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

### 8. 语音助手模块 (feature:voice)

**功能范围：**

* TTS语音合成和播报管理

* 车机TTS服务集成

* 语音播报状态监听

* 多语言支持和语音参数配置

* 语音播报队列管理

* 健康提示语音播报

**代码出处：**

* `app/src/main/java/com/healthlink/hms/utils/TTSHelper.kt` - TTS助手核心类

* `app/src/main/java/com/healthlink/hms/thirdLibUse/HMSTTSProvider.java` - HMS TTS提供者（Java实现）

* `app/src/main/java/com/healthlink/hms/utils/OnTTSStatusListener.kt` - TTS状态监听接口

* TTS播报实现：`HMSServiceListFragment.kt` 中的 `playTts()` 方法

* 车机TTS管理：`GwmTTSManager.getInstance()`

* 小部件TTS：`HMSWidgetProvider.kt` 中的语音播报功能

* TTS请求模型：`TTSRequest` 和优先级管理

**包含文件：**

```
feature:voice/
├── src/main/java/com/healthlink/hms/feature/voice/
│   ├── ui/
│   │   └── VoiceSettingsFragment.kt (AI创造)
│   ├── manager/
│   │   └── TTSHelper.kt (项目文件)
│   ├── model/
│   │   ├── TTSRequest.kt (AI创造)
│   │   ├── VoiceCommand.kt (AI创造)
│   │   └── SpeechConfig.kt (AI创造)
│   ├── listener/
│   │   └── OnTTSStatusListener.kt (项目文件)
│   ├── viewmodel/
│   │   └── VoiceViewModel.kt (AI创造)
│   ├── repository/
│   │   └── VoiceRepository.kt (AI创造)
│   └── service/
│       └── TTSService.kt (AI创造)
```

> 注：标记为(AI创造)的文件是为模块化架构设计的建议文件结构，实际实施时需要根据具体需求创建。

**AI创造文件设计依据：**

* **VoiceSettingsFragment.kt** - 基于TTS功能的用户配置需求，提供语音设置的专门界面，实现语音功能的UI模块化

* **TTSRequest.kt/VoiceCommand.kt/SpeechConfig.kt** - 基于现有TTSHelper的功能，将TTS请求和语音配置抽象为独立的模型类，提高语音功能的可维护性

* **VoiceViewModel.kt** - 遵循MVVM架构，为语音功能提供ViewModel，实现语音业务逻辑的统一管理

* **VoiceRepository.kt** - 实现Repository模式，管理语音配置和播报历史数据

* **TTSService.kt** - 封装TTS服务逻辑，提供语音合成的统一访问接口

**依赖关系：**

* **core:model** - 使用语音播报相关的数据模型、TTS配置参数DTO

* **core:data** - 通过Repository模式管理语音配置和播报历史数据

* **core:ui** - 使用语音控制相关的UI组件、音频可视化效果

* **core:common** - 使用`BaseVBVMActivity`、音频权限管理、`AudioUtil`音频焦点控制、`LogUtil`调试日志

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

### 9. 通知管理模块 (feature:notification)

**功能范围：**

* 系统通知管理和通道配置

* 健康提醒通知

* 服务状态通知

* 通知权限管理

* 通知样式和内容定制

**代码出处：**

* `app/src/main/java/com/healthlink/hms/utils/NotificationUtil.kt` - 通知工具类

* 通知相关的广播接收器：`app/src/main/java/com/healthlink/hms/reciever/` 目录

* 小部件通知更新：`HMSWidgetUpdateDataReceiver.kt`

* 系统启动通知：`BootCompleteReceiver.kt`

* HMS广播接收器：`HMSBroadcastReciever.kt`

**包含文件：**

```
feature:notification/
├── src/main/java/com/healthlink/hms/feature/notification/
│   ├── ui/
│   │   └── NotificationSettingsFragment.kt (AI创造)
│   ├── manager/
│   │   └── NotificationUtil.kt (项目文件)
│   ├── model/
│   │   ├── NotificationConfig.kt (AI创造)
│   │   ├── NotificationChannel.kt (AI创造)
│   │   └── NotificationData.kt (AI创造)
│   ├── receiver/
│   │   ├── HMSBroadcastReceiver.kt (项目文件)
│   │   ├── BootCompleteReceiver.kt (项目文件)
│   │   └── HMSWidgetUpdateDataReceiver.kt (项目文件)
│   ├── viewmodel/
│   │   └── NotificationViewModel.kt (AI创造)
│   ├── repository/
│   │   └── NotificationRepository.kt (AI创造)
│   └── service/
│       └── NotificationService.kt (AI创造)
```

> 注：标记为(AI创造)的文件是为模块化架构设计的建议文件结构，实际实施时需要根据具体需求创建。

**AI创造文件设计依据：**

* **NotificationSettingsFragment.kt** - 基于健康提醒功能的用户配置需求，提供通知设置的专门界面，实现通知功能的UI模块化

* **NotificationConfig.kt/NotificationChannel.kt/NotificationData.kt** - 基于现有NotificationUtil的功能，将通知配置和数据抽象为独立的模型类，提高通知功能的可配置性

* **NotificationViewModel.kt** - 遵循MVVM架构，为通知管理提供ViewModel，实现通知业务逻辑的统一管理

* **NotificationRepository.kt** - 实现Repository模式，管理通知配置和历史记录数据

* **NotificationService.kt** - 封装通知服务逻辑，提供健康提醒的统一访问接口

**依赖关系：**

* **core:model** - 使用通知相关的数据模型、健康提醒配置DTO

* **core:data** - 通过Repository模式管理通知配置和历史记录

* **core:ui** - 使用通知样式相关的UI资源、图标和布局

* **core:common** - 使用`BaseVBVMActivity`、通知权限管理、`SystemUtil`系统服务获取、`LogUtil`调试日志

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

### 10. 场景引擎模块 (feature:scene)

**功能范围：**

* 智能场景识别和管理

* 多种关怀场景实现（长途驾驶、下班关怀、高反关怀等）

* 场景自动化执行和优先级管理

* 场景状态和执行条件管理

* 欢迎场景和健康异常场景

* 海拔管理和环境感知

**代码出处：**

* `app/src/main/java/com/healthlink/hms/sceneEngine/SceneManager.kt` - 场景管理器核心类

* `app/src/main/java/com/healthlink/hms/sceneEngine/Scene.kt` - 场景基类和枚举定义

* `app/src/main/java/com/healthlink/hms/sceneEngine/scenes/Scene1WelcomeAfterBootImpl.kt` - 开机欢迎场景

* `app/src/main/java/com/healthlink/hms/sceneEngine/scenes/SceneAfterWorkCare1Impl.kt` - 下班关怀场景1

* `app/src/main/java/com/healthlink/hms/sceneEngine/scenes/SceneAfterWorkCare2Impl.kt` - 下班关怀场景2

* `app/src/main/java/com/healthlink/hms/sceneEngine/scenes/SceneLongDriveCare1Impl.kt` - 长途驾驶关怀场景1

* `app/src/main/java/com/healthlink/hms/sceneEngine/scenes/SceneLongDriveCare2Impl.kt` - 长途驾驶关怀场景2

* `app/src/main/java/com/healthlink/hms/sceneEngine/scenes/SceneHighCare1Impl.kt` - 高反关怀场景1

* `app/src/main/java/com/healthlink/hms/sceneEngine/scenes/SceneHighCare2Impl.kt` - 高反关怀场景2

* `app/src/main/java/com/healthlink/hms/sceneEngine/scenes/SceneHealthAbnormalImpl.kt` - 健康异常场景

* `app/src/main/java/com/healthlink/hms/sceneEngine/scenes/SceneHealthWeekReportImpl.kt` - 健康周报场景

* `app/src/main/java/com/healthlink/hms/sceneEngine/utils/AltitudeManager.kt` - 海拔管理器

* `app/src/main/java/com/healthlink/hms/sceneEngine/dto/` - 场景数据传输对象

**包含文件：feature:health**

```
feature:scene/
├── src/main/java/com/healthlink/hms/feature/scene/
│   ├── engine/
│   │   ├── SceneManager.kt (项目文件)
│   │   └── SceneEngine.kt (AI创造)
│   ├── scenes/
│   │   ├── Scene1WelcomeAfterBootImpl.kt (项目文件)
│   │   ├── SceneAfterWorkCare1Impl.kt (项目文件)
│   │   ├── SceneAfterWorkCare2Impl.kt (项目文件)
│   │   ├── SceneLongDriveCare1Impl.kt (项目文件)
│   │   ├── SceneLongDriveCare2Impl.kt (项目文件)
│   │   ├── SceneHighCare1Impl.kt (项目文件)
│   │   ├── SceneHighCare2Impl.kt (项目文件)
│   │   ├── SceneHealthAbnormalImpl.kt (项目文件)
│   │   └── SceneHealthWeekReportImpl.kt (项目文件)
│   ├── model/
│   │   ├── SceneConfig.kt (AI创造)
│   │   ├── SceneState.kt (AI创造)
│   │   └── SceneAction.kt (AI创造)
│   ├── viewmodel/
│   │   └── SceneViewModel.kt (AI创造)
│   ├── repository/
│   │   └── SceneRepository.kt (AI创造)
│   ├── utils/
│   │   └── AltitudeManager.kt (项目文件)
│   └── detector/
│       └── SceneDetector.kt (AI创造)
```

> 注：标记为(AI创造)的文件是为模块化架构设计的建议文件结构，实际实施时需要根据具体需求创建。

**AI创造文件设计依据：**

* **SceneEngine.kt** - 基于现有SceneManager的功能，创建专门的场景引擎，封装场景识别和执行的核心逻辑，提供统一的场景管理接口

* **SceneConfig.kt/SceneState.kt/SceneAction.kt** - 将现有各个Scene实现类中的配置、状态和动作抽象为独立的模型类，提高场景管理的可维护性和可扩展性

* **SceneViewModel.kt** - 遵循MVVM架构，为场景管理提供ViewModel，实现场景业务逻辑的统一管理和状态控制

* **SceneRepository.kt** - 实现Repository模式，管理场景配置和执行历史数据，为场景引擎提供数据支持

* **SceneDetector.kt** - 封装场景检测逻辑，基于车辆状态、时间、位置、海拔等信息提供智能场景识别功能，补充现有场景实现的自动化能力

**依赖关系：**

* **core:model** - 使用`SceneNavigationInfoDTO`导航信息、`VehicleServiceModeDTO`车辆服务模式、健康数据相关DTO

* **core:data** - 通过Repository模式管理场景配置和执行历史数据

* **core:ui** - 使用场景相关的UI组件、提示界面、动画效果

* **core:common** - 使用`BaseVBVMActivity`、`DateUtil`时间处理、`LocationUtil`位置信息、`SystemPropertyUtils`系统属性

* **feature:vehicle** - 依赖车载模块获取车辆状态和服务模式信息

* **feature:navigation** - 依赖导航模块获取路径和位置信息

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

### 11. 小部件模块 (feature:widget)

**功能范围：**

* 桌面小部件提供和更新

* 健康数据快捷显示

* 迎宾模式和语音播报

* 小部件数据工作器

* 氛围灯控制集成

* 小部件配置和状态管理

**代码出处：**

* `app/src/main/java/com/healthlink/hms/widget/` - 小部件相关目录

* 小部件提供者：`HMSWidgetProvider.kt`（包含TTS播报、氛围灯控制等功能）

* 小部件数据更新：`HMSWidgetUpdateDataReceiver.kt`

* 数据工作器：`app/src/main/java/com/healthlink/hms/service/WidgetHmsDataWorker.kt`

* 小部件布局和样式：`app/src/main/res/` 中的widget相关资源

**包含文件：**

```
feature:widget/
├── src/main/java/com/healthlink/hms/feature/widget/
│   ├── provider/
│   │   └── HMSWidgetProvider.kt (项目文件)
│   ├── model/
│   │   ├── WidgetConfig.kt (AI创造)
│   │   ├── WidgetData.kt (AI创造)
│   │   └── WidgetState.kt (AI创造)
│   ├── viewmodel/
│   │   └── WidgetViewModel.kt (AI创造)
│   ├── repository/
│   │   └── WidgetRepository.kt (AI创造)
│   ├── ui/
│   │   └── WidgetConfigActivity.kt (AI创造)
│   └── service/
│       └── WidgetUpdateService.kt (AI创造)
```

> 注：标记为(AI创造)的文件是为模块化架构设计的建议文件结构，实际实施时需要根据具体需求创建。

**AI创造文件设计依据：**

* **WidgetConfig.kt/WidgetData.kt/WidgetState.kt** - 基于现有HMSWidgetProvider的功能，将小部件的配置和数据抽象为独立的模型类，提高小部件功能的可维护性

* **WidgetViewModel.kt** - 遵循MVVM架构，为小部件数据管理提供ViewModel，实现小部件业务逻辑的统一管理

* **WidgetRepository.kt** - 实现Repository模式，管理小部件的配置数据和显示内容

* **WidgetConfigActivity.kt** - 提供小部件配置界面，实现小部件个性化设置功能

* **WidgetUpdateService.kt** - 封装小部件更新的后台服务逻辑，提供定时更新功能

**依赖关系：**

* **core:model** - 使用健康数据相关的DTO类，如`HeartRateDTO`、`BloodPressureResponseDTO`等用于小部件数据显示

* **core:data** - 通过Repository模式获取健康数据和小部件配置信息

* **core:ui** - 使用小部件布局资源、图标、样式文件（如`widget_*.xml`布局）

* **core:common** - 使用`BaseVBVMActivity`、`DateUtil`时间格式化、`MMKVUtil`配置存储、`LogUtil`调试日志

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

### 12. 健康报告模块 (feature:health-report)

**功能范围：**

* 综合健康报告生成和展示

* 多时间维度数据分析（日报、周报、月报、年报）

* 健康评分系统和风险评估

* 健康趋势对比分析

* 个性化健康建议生成

* 健康数据处理和统计图表

**代码出处：**

* `app/src/main/java/com/healthlink/hms/activity/HealthReportActivity.kt` - 健康报告主界面

* `app/src/main/java/com/healthlink/hms/fragment/ReportDayFragment.kt` - 日报Fragment实现

* `app/src/main/java/com/healthlink/hms/fragment/HealthReportWeekFragment.kt` - 周报Fragment

* `app/src/main/java/com/healthlink/hms/fragment/viewmodel/HealthReportFragmentDataModel.kt` - 健康报告数据模型

* `app/src/main/java/com/healthlink/hms/views/StatisticsView.kt` - 统计图表视图

* `app/src/main/java/com/healthlink/hms/biz/HealthDataProcessor.kt` - 健康数据处理器

* 健康报告DTO：`HealthReportDTO` 数据传输对象

* 数据追踪：`DataTrackUtil` 用于报告页面的数据埋点

**包含文件：**

```
feature:health-report/
├── src/main/java/com/healthlink/hms/feature/healthreport/
│   ├── ui/
│   │   ├── activities/
│   │   │   └── HealthReportActivity.kt (项目文件)
│   │   ├── fragments/
│   │   │   └── ReportDayFragment.kt (项目文件)
│   │   └── charts/
│   │       └── StatisticsView.kt (项目文件)
│   ├── viewmodel/
│   │   ├── HealthReportViewModel.kt (AI创造)
│   │   └── HealthReportFragmentDataModel.kt (项目文件)
│   ├── repository/
│   │   └── HealthReportRepository.kt (AI创造)
│   ├── model/
│   │   ├── HealthReportDTO.kt (AI创造)
│   │   ├── HealthScore.kt (AI创造)
│   │   ├── RiskLevel.kt (AI创造)
│   │   └── ReportTimeRange.kt (AI创造)
│   ├── domain/
│   │   ├── GenerateHealthReportUseCase.kt (AI创造)
│   │   ├── CalculateHealthScoreUseCase.kt (AI创造)
│   │   └── AssessHealthRiskUseCase.kt (AI创造)
│   └── utils/
│       ├── ReportDataProcessor.kt (AI创造)
│       └── HealthAnalyzer.kt (AI创造)
```

> 注：标记为(AI创造)的文件是为模块化架构设计的建议文件结构，实际实施时需要根据具体需求创建。

**依赖关系：**

* **core:model** - 使用`HealthReportDTO`健康报告数据模型、各种健康指标DTO（如`HeartRateDTO`、`BloodPressureResponseDTO`、`SleepDayDataDTO`等）

* **core:data** - 通过Repository模式获取健康历史数据和统计信息

* **core:ui** - 使用图表组件、报告界面样式、健康评分显示组件

* **core:common** - 使用`BaseVBVMActivity`、`DateUtil`时间处理、`StatisticsUtil`数据统计、`LogUtil`调试日志

* **feature:health** - 依赖健康监测模块获取原始健康数据

> 详细的Core模块说明请参考：[HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

## 模块依赖关系图

```mermaid
graph TD
    A[app] --> B[feature:health]
    A --> C[feature:aromatherapy]
    A --> D[feature:doctorcall]
    A --> E[feature:bluetooth]
    A --> F[feature:auth]
    A --> G[feature:vehicle]
    A --> H[feature:navigation]
    A --> I[feature:voice]
    A --> J[feature:notification]
    A --> K[feature:scene]
    A --> L[feature:widget]
    A --> M[feature:setting]
    A --> S[feature:health-report]
    
    B --> N[core:data]
    B --> O[core:ui]
    B --> P[core:model]
    B --> Q[core:common]
    
    C --> E
    C --> N
    C --> O
    C --> P
    C --> Q
    
    D --> R[core:network]
    D --> N
    D --> O
    D --> P
    D --> Q
    
    E --> N
    E --> O
    E --> P
    E --> Q
    
    F --> R
    F --> N
    F --> O
    F --> P
    F --> Q
    
    G --> N
    G --> O
    G --> P
    G --> Q
    
    H --> R
    H --> N
    H --> O
    H --> P
    H --> Q
    
    I --> N
    I --> O
    I --> P
    I --> Q
    
    J --> N
    J --> O
    J --> P
    J --> Q
    
    K --> G
    K --> H
    K --> N
    K --> O
    K --> P
    K --> Q
    
    L --> N
    L --> O
    L --> P
    L --> Q
    
    M --> N
    M --> O
    M --> P
    M --> Q
    
    S --> B
    S --> N
    S --> O
    S --> P
    S --> Q
    
    N --> R
    N --> P
    
    R --> P
    
    O --> P
```

## 模块化迁移实施计划

### 阶段一：基础模块完善（1-2周）

1. 完善现有core模块
2. 补充core:common工具类
3. 优化core:ui组件库
4. 完成数据存储迁移（MMKV → DataStore）

### 阶段二：独立功能模块迁移（3-4周）

1. **优先级1**：feature:bluetooth（基础设施）
2. **优先级2**：feature:auth（用户管理）
3. **优先级3**：feature:voice（通用服务）
4. **优先级4**：feature:notification（通用服务）

### 阶段三：业务功能模块迁移（4-6周）

1. **优先级1**：feature:health（核心业务）
2. **优先级2**：feature:health-report（健康报告）
3. **优先级3**：feature:aromatherapy（特色功能）
4. **优先级4**：feature:doctorcall（重要功能）
5. **优先级5**：feature:vehicle（平台集成）

### 阶段四：高级功能模块迁移（2-3周）

1. **优先级1**：feature:navigation（地图服务）
2. **优先级2**：feature:scene（智能场景）
3. **优先级3**：feature:widget（桌面组件）

### 阶段五：测试和优化（1-2周）

1. 模块间集成测试
2. 性能优化
3. 文档完善
4. 代码审查

## 模块化最佳实践

### 1. 模块设计原则

* **单一职责**：每个模块只负责一个明确的功能领域

* **高内聚低耦合**：模块内部紧密相关，模块间依赖最小

* **接口隔离**：通过接口定义模块间的交互

* **依赖倒置**：高层模块不依赖低层模块，都依赖抽象

### 2. 模块间通信

* 使用Repository模式管理数据

* 通过Hilt依赖注入管理模块依赖

* 使用Flow进行响应式数据流

* 避免直接的模块间调用

### 3. 代码组织结构

```
feature:module-name/
├── src/main/java/com/healthlink/hms/feature/modulename/
│   ├── ui/                    # UI层（Activity、Fragment、Compose）
│   ├── viewmodel/             # ViewModel层
│   ├── repository/            # 数据仓库层
│   ├── model/                 # 模块特定的数据模型
│   ├── domain/                # 业务逻辑层（UseCase）
│   ├── di/                    # 依赖注入配置
│   └── utils/                 # 模块工具类
├── src/main/res/              # 资源文件
├── src/test/                  # 单元测试
├── src/androidTest/           # 集成测试
└── build.gradle.kts           # 模块构建配置
```

### 4. 构建配置模板

```kotlin
// feature模块的build.gradle.kts模板
plugins {
    alias(libs.plugins.hms.android.feature)
    alias(libs.plugins.hms.hilt)
}

android {
    namespace = "com.healthlink.hms.feature.modulename"
}

dependencies {
    implementation(project(":core:model"))
    implementation(project(":core:data"))
    implementation(project(":core:ui"))
    implementation(project(":core:common"))
    
    // 其他特定依赖
}
```

### 5. 测试策略

* **单元测试**：测试ViewModel和Repository

* **集成测试**：测试模块间交互

* **UI测试**：测试用户界面和交互

* **端到端测试**：测试完整的用户流程

## 模块化收益

### 1. 开发效率提升

* **并行开发**：不同团队可以同时开发不同模块

* **独立测试**：模块可以独立进行单元测试

* **快速构建**：只构建修改的模块，提升构建速度

* **代码复用**：通用功能可以在多个模块间复用

### 2. 代码质量改善

* **职责清晰**：每个模块职责明确，易于理解和维护

* **依赖管理**：清晰的依赖关系，避免循环依赖

* **接口设计**：强制进行良好的接口设计

* **代码隔离**：模块间代码隔离，减少意外修改

### 3. 项目维护性

* **模块化升级**：可以独立升级某个功能模块

* **功能开关**：可以通过配置启用/禁用某些功能

* **A/B测试**：可以针对特定模块进行A/B测试

* **渐进式重构**：可以逐步重构和优化代码

### 4. 团队协作

* **代码所有权**：明确的模块所有权

* **减少冲突**：减少代码合并冲突

* **知识共享**：模块化的代码更容易理解和分享

* **新人上手**：新团队成员可以专注于特定模块

## 注意事项和风险

### 1. 过度模块化

* 避免创建过小的模块

* 平衡模块粒度和复杂性

* 考虑维护成本

### 2. 依赖管理

* 避免循环依赖

* 控制依赖深度

* 定期审查依赖关系

### 3. 性能考虑

* 模块化可能增加APK大小

* 考虑动态功能模块

* 优化模块加载时间

### 4. 团队协调

* 建立模块接口规范

* 定期进行架构评审

* 保持文档更新

## 总结

通过合理的模块化设计，HMS项目可以实现：

* 更好的代码组织和管理

* 提升开发效率和代码质量

* 增强项目的可维护性和可扩展性

* 支持团队的并行开发和协作

建议按照本文档的实施计划，分阶段进行模块化迁移，确保项目的稳定性和连续性。在迁移过程中，要注意保持良好的测试覆盖率，确保功能的正确性。

***

*文档版本：v1.0*\
*最后更新：2025年*\
*维护团队：HMS架构组*
