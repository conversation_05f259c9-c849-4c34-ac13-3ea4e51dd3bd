# HMS项目Core模块划分指导文档

## 1. 概述

HMS（Health Management System）项目采用模块化架构设计，其中`core`模块作为整个项目的核心基础设施层，为其他业务模块提供通用功能和服务。`core`模块位于`/Users/<USER>/Documents/work/android/hms/core/`目录下，包含5个子模块：`common`、`data`、`model`、`network`、`ui`。

## 2. Core模块架构图

```mermaid
graph TD
    A[App Module] --> B[Core Module]
    C[Feature Modules] --> B
    
    subgraph "Core Module"
        D[Common]
        E[Data]
        F[Model]
        G[Network]
        H[UI]
    end
    
    D --> H
    E --> F
    E --> G
    G --> F
```

## 3. 子模块详细说明

### 3.1 Common模块

**功能职责：**

* 提供项目通用的基础组件和工具类

* 定义应用程序的基础架构和扩展功能

* 提供依赖注入配置

* 包含通用UI组件和对话框

**目录结构：**

```
core/common/src/main/java/com/healthlink/hms/core/common/
├── base/
│   └── BaseVBVMActivity.kt          # MVVM架构基础Activity ✓ 已存在
├── di/
│   └── ContextModule.kt             # 依赖注入上下文模块 ✓ 已存在
├── extensions/
│   ├── ActivityExtensions.kt        # Activity扩展函数 ✓ 已存在
│   ├── CommonExtensions.kt          # 通用扩展函数 ✓ 已存在
│   └── ViewExtensions.kt            # 视图扩展函数 ✓ 已存在
├── ui/
│   ├── AnyViewOutlineProvider.kt    # 视图轮廓提供器 ✓ 已存在
│   ├── HmsDialog.kt                 # HMS对话框组件 ✓ 已存在
│   └── ImmersiveDialog.kt           # 沉浸式对话框 ✓ 已存在
└── utils/
    ├── AlertDialogUtil.java         # 警告对话框工具 ✓ 已存在
    ├── AppContext.kt                # 应用上下文工具 ✓ 已存在
    ├── ApplicationHelper.kt         # 应用程序辅助类 ✓ 已存在
    ├── AssetsUtil.kt                # 资源文件工具 ✓ 已存在
    ├── AuthorizationUtil.kt         # 授权工具 ✓ 已存在
    ├── BaseContext.java             # 基础上下文 ✓ 已存在
    ├── ChartUtils.kt                # 图表工具 ✓ 已存在
    ├── Constants.kt                 # 常量定义 ✓ 已存在
    ├── CommonContants.kt            # 通用常量 ✓ 已存在
    ├── DataTrackUtil.kt             # 数据追踪工具 ✓ 已存在
    ├── DateUtil.java                # 日期工具 ✓ 已存在
    ├── DeviceInfo.java              # 设备信息 ✓ 已存在
    ├── DeviceUtil.java              # 设备工具 ✓ 已存在
    ├── HMSDialogUtils.kt            # HMS对话框工具 ✓ 已存在
    ├── IContextProvider.kt          # 上下文提供器接口 ✓ 已存在
    ├── ImageUtil.kt                 # 图片工具 ✓ 已存在
    ├── JsonUtil.java                # JSON工具 ✓ 已存在
    ├── LaunchAfterBootManager.kt    # 启动后管理器 ✓ 已存在
    ├── ListEntity.java              # 列表实体 ✓ 已存在
    ├── ListUtil.java                # 列表工具 ✓ 已存在
    ├── LogUtil.java                 # 日志工具 ✓ 已存在
    ├── MMKVLaunchStorage.kt         # MMKV启动存储 ✓ 已存在
    ├── MMKVUtil.kt                  # MMKV工具 ✓ 已存在
    ├── DataStoreUtil.kt             # DataStore工具 ✓ 已存在
    ├── NetInfo.java                 # 网络信息 ✓ 已存在
    ├── NotificationUtil.kt          # 通知工具 ✓ 已存在
    ├── OnTTSStatusListener.kt       # TTS状态监听器 ✓ 已存在
    ├── PrivacyModeObserver.kt       # 隐私模式观察者 ✓ 已存在
    ├── PrivacyModeUtils.kt          # 隐私模式工具 ✓ 已存在
    ├── StorageUtil.java             # 存储工具 ✓ 已存在
    ├── StringUtil.kt                # 字符串工具 ✓ 已存在
    ├── SystemPropertiesUtil.java   # 系统属性工具 ✓ 已存在
    ├── SystemPropertyUtils.kt       # 系统属性工具 ✓ 已存在
    ├── TimeUtils.kt                 # 时间工具 ✓ 已存在
    ├── ToastUtil.kt                 # Toast工具 ✓ 已存在
    ├── TTSHelper.kt                 # TTS辅助类 ✓ 已存在
    ├── UIModeUtils.kt               # UI模式工具 ✓ 已存在
    └── ViewDrawUtils.kt             # 视图绘制工具 ✓ 已存在
```

**外部依赖的第三方库：**

* GWMDataTrackSDK-2.5.10.aar

* GWMMapSDK-2.0.4.aar

* NumberPicker-2.4.13.aar

* both-1.1.3.aar

* gwmhmiframework-0.4.0.jar

* libVrWidget-2.0.8.jar

* libWidgetCux-1.1.5-SOP-20240816.031318-1.aar

* reduce-release-sit-20250422v1.aar

* tts.client-1.2.2.aar

* tts.commonlib-1.2.2.aar

**使用示例：**

```kotlin
// 在app模块和feature模块中的使用
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.core.common.utils.DataStoreUtil
import com.healthlink.hms.core.common.utils.Constants
import com.healthlink.hms.core.common.base.BaseVBVMActivity
import com.healthlink.hms.core.common.utils.AppContext
```



### 3.2 Data模块

**功能职责：**

* 数据访问层的抽象和实现

* 提供Repository模式的数据仓库

* 管理本地数据存储（DataStore）

* 定义数据层的依赖注入配置

**目录结构：**

```
core/data/src/main/java/com/healthlink/hms/core/data/
├── datastore/
│   ├── DataStoreModule.kt           # DataStore模块配置 ✓ 已存在
│   └── DataStorePreKeysConfig.kt    # DataStore预设键配置 ✓ 已存在
├── di/
│   ├── DataModule.kt                # 数据模块依赖注入 ✓ 已存在
│   └── LocalDataModule.kt           # 本地数据模块 ✓ 已存在
└── repository/
    ├── DefaultHealthAuthorityRepository.kt    # 健康权限仓库实现 ✓ 已存在
    ├── DefaultHealthDataRepository.kt         # 健康数据仓库实现 ✓ 已存在
    ├── DefaultInitRepository.kt               # 初始化仓库实现 ✓ 已存在
    ├── DefaultNavigationRepository.kt         # 导航仓库实现 ✓ 已存在
    ├── DefaultSceneEngineRepository.kt        # 场景引擎仓库实现 ✓ 已存在
    ├── DefaultSystemRepository.kt             # 系统仓库实现 ✓ 已存在
    ├── DefaultUserRepository.kt               # 用户仓库实现 ✓ 已存在
    ├── HealthAuthorityRepository.kt           # 健康权限仓库接口 ✓ 已存在
    ├── HealthDataRepository.kt                # 健康数据仓库接口 ✓ 已存在
    ├── InitRepository.kt                      # 初始化仓库接口 ✓ 已存在
    ├── NavigationRepository.kt                # 导航仓库接口 ✓ 已存在
    ├── SceneEngineRepository.kt               # 场景引擎仓库接口 ✓ 已存在
    ├── SystemRepository.kt                    # 系统仓库接口 ✓ 已存在
    └── UserRepository.kt                      # 用户仓库接口 ✓ 已存在
```

#### MVVM-Repository架构实施

**实施状态：✅ 已完成基础架构，🔄 正在实施业务层改造**

为了提升数据存储的类型安全性、协程支持和架构一致性，项目采用MVVM-Repository架构模式完全替换MMKVUtil。

**详细实施方案请参考：** [DataStore替换MMKVUtil方案](./DataStore替换MMKVUtil方案.md)

**架构特点：**
- **MVVM分层架构**：Activity -> ViewModel -> Repository -> DataStore
- **响应式编程**：使用Flow提供响应式数据流，UI自动响应数据变化
- **职责分离**：完全移除`*Utils`辅助类，各层职责清晰独立
- **现代化技术栈**：协程、Flow、Hilt依赖注入、DataStore存储

**核心优势：**
- 职责分离清晰，提升代码可维护性
- 响应式数据流，提升用户体验
- 统一架构模式，降低开发复杂度
- 测试友好，支持单元测试和集成测试

**使用示例：**

```kotlin
// ViewModel中使用Repository
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val systemRepository: SystemRepository
) : ViewModel() {
    
    val userInfo: StateFlow<UserPreferences> = userRepository.getUserInfo()
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), UserPreferences())
    
    fun updateUserInfo(userInfo: UserPreferences) {
        viewModelScope.launch {
            userRepository.storeUserInfo(userInfo)
        }
    }
}

// Activity中观察ViewModel数据
@AndroidEntryPoint
class SettingsActivity : BaseVBVMActivity<ActivitySettingsBinding>() {
    
    private val viewModel: SettingsViewModel by viewModels()
    
    private fun setupObservers() {
        lifecycleScope.launch {
            viewModel.userInfo.collect { userInfo ->
                updateUI(userInfo)
            }
        }
    }
}
```

### 3.3 Model模块

**功能职责：**

* 定义项目中所有的数据模型和实体类

* 提供网络请求和响应的DTO（Data Transfer Object）

* 定义本地存储的数据结构

* 包含图表数据模型

**目录结构：**

```
core/model/src/main/java/com/healthlink/hms/core/model/
├── BaseResponse.kt                  # 基础响应类 ✓ 已存在
├── BaseResponseCallback.kt          # 基础响应回调 ✓ 已存在
├── BloodPressure.kt                 # 血压模型 ✓ 已存在
├── JourneySignalAddParam.kt         # 行程信号添加参数 ✓ 已存在
├── MainDataModel.kt                 # 主数据模型 ✓ 已存在
├── bean/
│   ├── BaseDTO.kt                   # 基础DTO ✓ 已存在
│   ├── HeartRateDTO.kt              # 心率DTO ✓ 已存在
│   └── HomeCardDTO.kt               # 首页卡片DTO ✓ 已存在
├── dto/
│   ├── Activity4HourSummaryDTO.kt   # 4小时活动摘要DTO ✓ 已存在
│   ├── AltitudeDataItem.kt          # 海拔数据项 ✓ 已存在
│   ├── AltitudeDatas.kt             # 海拔数据集合 ✓ 已存在
│   ├── AltitudeDayStatDTO.kt        # 海拔日统计DTO ✓ 已存在
│   ├── AltitudeDetailDTO.kt         # 海拔详情DTO ✓ 已存在
│   ├── AltitudeItemDTO.kt           # 海拔项DTO ✓ 已存在
│   ├── AnnengInfluenceFactor.kt     # 安能影响因子 ✓ 已存在
│   ├── AnnengProportion.kt          # 安能比例 ✓ 已存在
│   ├── BloodOxygen.kt               # 血氧模型 ✓ 已存在
│   ├── BloodPressureCard1DTO.kt     # 血压卡片1DTO ✓ 已存在
│   ├── BloodPressureCard2DTO.kt     # 血压卡片2DTO ✓ 已存在
│   ├── BloodPressureItemDTO.kt      # 血压项DTO ✓ 已存在
│   ├── BloodPressureResponseDTO.kt  # 血压响应DTO ✓ 已存在
│   ├── BloodPressureSummaryItemDTO.kt # 血压摘要项DTO ✓ 已存在
│   ├── ChartDataDTO.kt              # 图表数据DTO ✓ 已存在
│   ├── ChartDataEntry.kt            # 图表数据条目 ✓ 已存在
│   ├── GPSDTO.kt                    # GPS DTO ✓ 已存在
│   ├── HWAuthStatusDTO.kt           # 华为认证状态DTO ✓ 已存在
│   ├── HealthBloodpressureSummaryDTO.kt # 健康血压摘要DTO ✓ 已存在
│   ├── HealthDataStatusDTO.kt       # 健康数据状态DTO ✓ 已存在
│   ├── HealthHistoryDataStatusDTO.kt # 健康历史数据状态DTO ✓ 已存在
│   ├── HealthHistoryDataStatusReportDTO.kt # 健康历史数据状态报告DTO ✓ 已存在
│   ├── HealthReportDTO.kt           # 健康报告DTO ✓ 已存在
│   ├── HealthSpO2SummaryDTO.kt      # 健康血氧摘要DTO ✓ 已存在
│   ├── HealthSummarizeDTO.kt        # 健康摘要DTO ✓ 已存在
│   ├── HealthTempSummaryDTO.kt      # 健康体温摘要DTO ✓ 已存在
│   ├── HealthTipsDTO.kt             # 健康提示DTO ✓ 已存在
│   ├── HealthTipsEntry.kt           # 健康提示条目 ✓ 已存在
│   ├── HeartRate.kt                 # 心率模型 ✓ 已存在
│   ├── HolidayDTO.kt                # 假期DTO ✓ 已存在
│   ├── LaunchAfterBootDTO.kt        # 启动后DTO ✓ 已存在
│   ├── LiveHealthStatusDTO.kt       # 实时健康状态DTO ✓ 已存在
│   ├── PhysiologicalPeriod.kt       # 生理周期 ✓ 已存在
│   ├── Pressure.kt                  # 压力模型 ✓ 已存在
│   ├── Pressure1HourSummaryDTO.kt   # 1小时压力摘要DTO ✓ 已存在
│   ├── QtGetHealthInfoDTO.kt        # Qt获取健康信息DTO ✓ 已存在
│   ├── ReportHealthPartDTO.kt       # 报告健康部分DTO ✓ 已存在
│   ├── ReportTripPort.kt            # 报告行程端口 ✓ 已存在
│   ├── Sleep.kt                     # 睡眠模型 ✓ 已存在
│   ├── SleepCardShow2DTO.kt         # 睡眠卡片显示2DTO ✓ 已存在
│   ├── SleepCardShowDTO.kt          # 睡眠卡片显示DTO ✓ 已存在
│   ├── SleepDayDataDTO.kt           # 睡眠日数据DTO ✓ 已存在
│   ├── SleepDayItemDTO.kt           # 睡眠日项DTO ✓ 已存在
│   ├── SleepDayResponseDTO.kt       # 睡眠日响应DTO ✓ 已存在
│   ├── SleepMonthResponseDTO.kt     # 睡眠月响应DTO ✓ 已存在
│   ├── SleepStatDTO.kt              # 睡眠统计DTO ✓ 已存在
│   ├── SleepWMItemDTO.kt            # 睡眠周月项DTO ✓ 已存在
│   ├── SleepWMYDataDTO.kt           # 睡眠周月年数据DTO ✓ 已存在
│   ├── SleepWeekResponseDTO.kt      # 睡眠周响应DTO ✓ 已存在
│   ├── SleepYearDTO.kt              # 睡眠年DTO ✓ 已存在
│   ├── SleepYearDataDTO.kt          # 睡眠年数据DTO ✓ 已存在
│   ├── SleepYearResponseDTO.kt      # 睡眠年响应DTO ✓ 已存在
│   ├── SpBaseDTO.kt                 # 血氧基础DTO ✓ 已存在
│   ├── SpGetHealthInfoDTO.kt        # 血氧获取健康信息DTO ✓ 已存在
│   ├── SpO2Card1DTO.kt              # 血氧卡片1DTO ✓ 已存在
│   ├── SpO2Card2DTO.kt              # 血氧卡片2DTO ✓ 已存在
│   ├── SpO2Card3DTO.kt              # 血氧卡片3DTO ✓ 已存在
│   ├── SpO2ItemDTO.kt               # 血氧项DTO ✓ 已存在
│   ├── SpO2ItemResponseDTO.kt       # 血氧项响应DTO ✓ 已存在
│   ├── TempCard1DTO.kt              # 体温卡片1DTO ✓ 已存在
│   ├── TempCard2DTO.kt              # 体温卡片2DTO ✓ 已存在
│   ├── TempItemDTO.kt               # 体温项DTO ✓ 已存在
│   ├── TempItemResponseDTO.kt       # 体温项响应DTO ✓ 已存在
│   ├── Temperature.kt               # 体温模型 ✓ 已存在
│   ├── UpgradeVersionDTO.kt         # 升级版本DTO ✓ 已存在
│   ├── UserInfoDTO.kt               # 用户信息DTO ✓ 已存在
│   ├── VehicleServiceModeDTO.kt     # 车辆服务模式DTO ✓ 已存在
│   ├── charts/                      # 图表相关DTO ✓ 已存在
│   │   ├── CDBloodOxygenDTO.kt      # 图表血氧DTO ✓ 已存在
│   │   ├── CDPressureDTO.kt         # 图表压力DTO ✓ 已存在
│   │   ├── HealthSleepSummeryDTO.kt # 健康睡眠摘要DTO ✓ 已存在
│   │   ├── HealthSummeryBaseDTO.kt  # 健康摘要基础DTO ✓ 已存在
│   │   ├── HealthSummeryDTO.kt      # 健康摘要DTO ✓ 已存在
│   │   ├── heartrate/               # 心率图表DTO ✓ 已存在
│   │   └── pressure/                # 压力图表DTO ✓ 已存在
│   └── init/                        # 初始化相关DTO ✓ 已存在
│       ├── CallDoctorPhoneDTO.kt    # 呼叫医生电话DTO ✓ 已存在
│       ├── DoctorServiceDTO.kt      # 医生服务DTO ✓ 已存在
│       ├── InitInfoDTO.kt           # 初始化信息DTO ✓ 已存在
│       ├── VehicleCapacityDTO.kt    # 车辆容量DTO ✓ 已存在
│       └── VehicleCapacityModeFunsDTO.kt # 车辆容量模式功能DTO ✓ 已存在
└── store/
    ├── LaunchStorage.kt             # 启动存储 ✓ 已存在
    └── UserPreferences.kt           # 用户偏好设置 ✓ 已存在
```

**使用示例：**

```kotlin
// 在app模块中广泛使用
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.BloodPressureResponseDTO
import com.healthlink.hms.core.model.dto.HealthBloodpressureSummaryDTO
import com.healthlink.hms.core.model.bean.HomeCardDTO

// 在feature/setting模块中使用
import com.healthlink.hms.core.model.dto.UserInfoDTO
```

### 3.4 Network模块

**功能职责：**

* 网络请求的封装和管理

* API接口定义和实现

* 网络拦截器和错误处理

* 网络相关的依赖注入配置

* RxJava响应式编程支持

**目录结构：**

```
core/network/src/main/java/com/healthlink/hms/core/network/
├── INetworkRequiredInfo.java       # 网络必需信息接口 ✓ 已存在
├── NetworkApi.java                  # 网络API ✓ 已存在
├── NetworkRequiredInfo.java         # 网络必需信息实现 ✓ 已存在
├── api/
│   ├── ApiService.java              # API服务接口 ✓ 已存在
│   └── ApiServiceKot.kt             # API服务Kotlin实现 ✓ 已存在
├── di/
│   ├── DispatcherModule.kt          # 调度器模块 ✓ 已存在
│   └── NetworkModule.kt             # 网络模块依赖注入 ✓ 已存在
├── errorhandler/
│   ├── ExceptionHandle.java         # 异常处理 ✓ 已存在
│   └── HttpErrorHandler.java        # HTTP错误处理 ✓ 已存在
├── interceptor/
│   ├── LogInterceptor.java          # 日志拦截器 ✓ 已存在
│   ├── NetworkInterceptor.kt        # 网络拦截器 ✓ 已存在
│   ├── ReqRetryInterceptor.java     # 请求重试拦截器 ✓ 已存在
│   ├── RequestInterceptor.java      # 请求拦截器 ✓ 已存在
│   └── ResponseInterceptor.java     # 响应拦截器 ✓ 已存在
├── model/
│   ├── BaseRequestParam.kt          # 基础请求参数 ✓ 已存在
│   ├── HealthInfoRequestParam.kt    # 健康信息请求参数 ✓ 已存在
│   └── HealthInfoResponse.kt        # 健康信息响应 ✓ 已存在
├── utils/
│   ├── HttpUtil.kt                  # HTTP工具类 ✓ 已存在
│   └── NetworkMonitor.kt            # 网络监控工具 ✓ 已存在
└── rx/
    └── BaseObserver.java            # 基础观察者 ✓ 已存在
```

**使用示例：**

```kotlin
// 在app模块中的使用
import com.healthlink.hms.core.network.model.HealthInfoRequestParam
import com.healthlink.hms.core.network.NetworkApi
import com.healthlink.hms.core.network.api.ApiServiceKot

// 在feature/setting模块中的使用
import com.healthlink.hms.core.network.api.ApiServiceKot
```

### 3.5 UI模块

**功能职责：**

* 提供通用的UI组件和视图

* 管理应用的UI资源（图片、布局、样式等）

* 定义应用的视觉设计规范

* 提供动画和过渡效果

**目录结构：**

```
core/ui/src/main/
├── AndroidManifest.xml              # Android清单文件 ✓ 已存在
├── java/com/healthlink/hms/core/ui/
│   └── views/
│       ├── AnyViewOutlineProvider.kt    # 视图轮廓提供器 ✓ 已存在
│       ├── ClearableEditText.kt         # 可清除文本框 ✓ 已存在
│       ├── CustomProgressBar.kt         # 自定义进度条 ✓ 已存在
│       ├── ImmersiveDialog.kt           # 沉浸式对话框 ✓ 已存在
│       ├── MiddleEllipsesTextView.kt     # 中间省略号文本视图 ✓ 已存在
│       ├── StretchScrollView.kt         # 可拉伸滚动视图 ✓ 已存在
│       └── dialog/
│           └── HmsDialog.kt             # HMS对话框基类 ✓ 已存在
└── res/
    ├── anim/                    # 动画资源文件
    ├── drawable/                # 图片和XML图形资源
    │   ├── btn_search_clean.xml ✓ 已存在 (ClearableEditText使用)
    │   ├── dialog_bg.xml        ✓ 已存在 (ImmersiveDialog使用)
    │   └── ...                  # 其他drawable资源
    ├── layout/                  # 布局文件
    ├── mipmap-*/               # 应用图标资源
    ├── values/                  # 字符串、颜色、尺寸等值资源
    │   ├── strings.xml          # 字符串资源
    │   │   ├── card_status_normal_bg_color   ✓ 已存在 (CustomProgressBar使用)
    │   │   ├── card_status_warning_bg_color  ✓ 已存在 (CustomProgressBar使用)
    │   │   └── card_status_danger_bg_color   ✓ 已存在 (CustomProgressBar使用)
    │   ├── colors.xml           # 颜色资源
    │   │   └── progress_bar_ring ✓ 已存在 (CustomProgressBar使用)
    │   ├── bools.xml            # 布尔值资源
    │   │   ├── lightStatusBar    ✓ 已存在 (ImmersiveDialog使用)
    │   │   └── lightNavigationBar ✓ 已存在 (ImmersiveDialog使用)
    │   └── dimens.xml           # 尺寸资源
    └── values-night/           # 夜间模式资源

**资源文件迁移说明：**

当UI组件从app模块迁移到Core UI模块时，必须同时迁移相关的资源文件，以确保组件的完整性和独立性：

1. **Drawable资源迁移**：
   - ClearableEditText使用的`btn_search_clean.xml`需要迁移到Core UI模块的drawable目录
   - ImmersiveDialog使用的对话框背景资源需要一并迁移

2. **颜色和字符串资源迁移**：
   - CustomProgressBar使用的颜色资源`progress_bar_ring`需要迁移到colors.xml
   - 相关的字符串资源如`card_status_*_bg_color`需要迁移到strings.xml

3. **布尔值资源迁移**：
   - ImmersiveDialog使用的`lightStatusBar`和`lightNavigationBar`需要迁移到bools.xml

4. **资源冲突避免策略**：
   - 迁移前检查Core UI模块是否已存在同名资源
   - 如存在冲突，需要重命名资源并更新引用
   - 建议使用统一的命名前缀（如`core_ui_`）来避免冲突

5. **依赖关系管理**：
   - 确保所有被迁移UI组件引用的资源都完整迁移
   - 更新app模块中的资源引用，避免重复定义
   - 通过模块依赖关系确保资源的正确访问
    ├── anim/                        # 动画资源 ✓ 已存在
    │   ├── activity_enter_dialog.xml ✓ 已存在
    │   ├── activity_enter_fade_in.xml ✓ 已存在
    │   ├── activity_enter_slide_in_left.xml ✓ 已存在
    │   ├── activity_enter_slide_in_right.xml ✓ 已存在
    │   ├── activity_enter_slide_out_left.xml ✓ 已存在
    │   ├── activity_enter_slide_out_right.xml ✓ 已存在
    │   ├── activity_exit_dialog.xml ✓ 已存在
    │   ├── activity_exit_fade_out.xml ✓ 已存在
    │   ├── activity_stay.xml ✓ 已存在
    │   ├── dialog_activity_enter_anim.xml ✓ 已存在
    │   └── dialog_activity_exit_anim.xml ✓ 已存在
    ├── drawable/                    # 图片和形状资源 ✓ 已存在
    │   ├── animation_play_icon.xml ✓ 已存在
    │   ├── banner_box_focus.xml ✓ 已存在
    │   ├── banner_box_normal.xml ✓ 已存在
    │   ├── bg_agent_web.xml ✓ 已存在
    │   ├── bg_app.xml ✓ 已存在
    │   ├── card_*.png/xml           # 各种卡片背景 ✓ 已存在
    │   ├── dialog_*.xml/png         # 对话框相关资源 ✓ 已存在
    │   ├── health_*.png/xml         # 健康相关图标 ✓ 已存在
    │   ├── ic_*.xml/png             # 通用图标 ✓ 已存在
    │   ├── icon_*.xml/png           # 功能图标 ✓ 已存在
    │   ├── img_*.png/xml            # 图片资源 ✓ 已存在
    │   ├── loading*.xml/png/gif     # 加载动画 ✓ 已存在
    │   ├── mode_*.xml               # 模式相关背景 ✓ 已存在
    │   ├── point_*.xml              # 指示点 ✓ 已存在
    │   ├── progress_*.xml           # 进度条 ✓ 已存在
    │   ├── radio_*.xml              # 单选按钮 ✓ 已存在
    │   ├── setting_*.xml            # 设置相关 ✓ 已存在
    │   ├── shape_*.xml              # 形状定义 ✓ 已存在
    │   ├── style_*.xml              # 样式定义 ✓ 已存在
    │   ├── tab_*.xml                # 标签相关 ✓ 已存在
    │   ├── text_*.xml               # 文本背景 ✓ 已存在
    │   ├── toast_*.xml              # Toast背景 ✓ 已存在
    │   ├── transparent.xml          # 透明背景 ✓ 已存在
    │   ├── week_report_*.png        # 周报告图片 ✓ 已存在
    │   └── widget_*.png             # 小部件图片 ✓ 已存在
    ├── layout/                      # 布局文件 ✓ 已存在
    ├── mipmap-*/                    # 不同分辨率的图标 ✓ 已存在
    └── values/                      # 值资源（颜色、字符串、样式等） ✓ 已存在
```

**使用示例：**

```kotlin
// 在common模块中引用ui资源
import com.healthlink.hms.core.ui.R

// 在feature/setting模块中使用
import com.healthlink.hms.core.ui.R.*
```

## 4. 模块间依赖关系

### 4.1 依赖关系图

```mermaid
graph TD
    A[App Module] --> B[Common]
    A --> C[Data]
    A --> D[Model]
    A --> E[Network]
    A --> F[UI]
    
    G[Feature Modules] --> B
    G --> C
    G --> D
    G --> E
    G --> F
    
    B --> F
    C --> D
    C --> E
    E --> D
    
    subgraph "Core Module Dependencies"
        B
        C
        D
        E
        F
    end
```

### 4.2 内部依赖说明

1. **Common模块依赖UI模块**：Common模块中的ToastUtil等工具类需要引用UI模块的资源文件
2. **Data模块依赖Model模块**：Repository实现需要使用Model模块定义的数据结构
3. **Data模块依赖Network模块**：数据仓库需要通过Network模块进行网络请求
4. **Network模块依赖Model模块**：网络请求和响应需要使用Model模块定义的DTO

### 4.3 外部模块使用统计

**App模块使用频率：**

* Model模块：高频使用，主要用于数据传输和业务逻辑

* Common模块：高频使用，主要用于工具类和基础组件

* Network模块：中频使用，主要用于网络请求参数

* Data模块：低频使用，主要用于Repository接口

* UI模块：间接使用，通过Common模块引用

**Feature模块使用频率：**

* Common模块：高频使用，基础架构和工具类

* Model模块：中频使用，数据传输对象

* Network模块：中频使用，API服务接口

* Data模块：中频使用，数据仓库

* UI模块：低频使用，主要是资源引用

## 5. 最佳实践和使用指导

### 5.1 模块使用原则

1. **单一职责原则**：每个子模块只负责特定的功能领域
2. **依赖倒置原则**：高层模块不应该依赖低层模块，都应该依赖抽象
3. **开闭原则**：对扩展开放，对修改关闭
4. **接口隔离原则**：使用多个专门的接口，而不是单一的总接口

### 5.2 开发规范

#### 5.2.1 Common模块开发规范

* **工具类命名**：以`Util`结尾，如`MMKVUtil`、`DateUtil`

* **扩展函数**：放在`extensions`包下，按功能分类

* **基础组件**：继承自`BaseVBVMActivity`，遵循MVVM架构

* **常量定义**：统一放在`Constants.kt`中

#### 5.2.2 Model模块开发规范

* **DTO命名**：以`DTO`结尾，如`UserInfoDTO`、`HealthReportDTO`

* **响应类**：继承自`BaseResponse`

* **数据类**：使用Kotlin的`data class`

* **包结构**：按业务功能分包，如`dto`、`bean`、`store`

#### 5.2.3 Network模块开发规范

* **API接口**：使用Retrofit注解定义

* **拦截器**：实现特定功能，如日志、重试、认证

* **错误处理**：统一使用`HttpErrorHandler`

* **响应式编程**：使用RxJava的`BaseObserver`

#### 5.2.4 Data模块开发规范

* **Repository模式**：接口定义在repository包，实现类以`Default`开头

* **数据存储**：使用DataStore进行本地数据持久化

* **依赖注入**：使用Hilt进行依赖管理

#### 5.2.5 UI模块开发规范

* **资源命名**：遵循Android命名规范

* **图片资源**：提供多分辨率版本

* **颜色和样式**：统一定义在values中

* **动画**：放在anim目录下

### 5.3 性能优化建议

1. **懒加载**：对于大型对象和资源，使用懒加载模式
2. **缓存策略**：合理使用MMKV进行数据缓存
3. **网络优化**：使用拦截器进行请求优化和缓存
4. **内存管理**：及时释放不需要的资源

### 5.4 测试策略

1. **单元测试**：每个模块都应该有对应的测试用例
2. **集成测试**：测试模块间的交互
3. **UI测试**：使用Espresso进行UI自动化测试
4. **网络测试**：使用MockWebServer进行网络请求测试

### 5.5 版本管理

1. **语义化版本**：遵循语义化版本规范
2. **向后兼容**：保持API的向后兼容性
3. **变更日志**：记录每次版本的变更内容
4. **依赖管理**：使用Gradle版本目录统一管理依赖版本

## 6. 常见问题和解决方案

### 6.1 循环依赖问题

**问题**：模块间出现循环依赖
**解决方案**：

1. 重新设计模块架构，确保依赖关系是单向的
2. 使用接口抽象，通过依赖注入解决循环依赖
3. 将共同依赖的部分提取到更底层的模块

### 6.2 资源冲突问题

**问题**：不同模块间资源名称冲突
**解决方案**：

1. 使用模块前缀命名资源
2. 在UI模块中统一管理公共资源
3. 使用资源命名空间

### 6.3 版本兼容性问题

**问题**：模块间版本不兼容
**解决方案**：

1. 建立统一的版本管理策略
2. 使用API版本控制
3. 提供向后兼容的适配器

## 7. 系统性检查机制

### 7.1 模块完整性检查流程

为确保Core模块划分的完整性和准确性，建立以下系统性检查机制：

#### 7.1.1 定期检查清单

**每月检查项目：**
1. **新增文件扫描**：检查app模块中新增的文件是否应归属到Core模块
2. **依赖关系验证**：确认模块间依赖关系是否合理
3. **重复代码检测**：识别可能的重复实现
4. **资源文件同步**：检查资源文件是否正确迁移

**每季度检查项目：**
1. **架构一致性审查**：验证实际代码结构与文档的一致性
2. **性能影响评估**：分析模块化对性能的影响
3. **开发效率评估**：收集开发团队反馈

#### 7.1.2 自动化检查工具

**建议开发的检查工具：**
1. **模块依赖分析器**：自动检测循环依赖和不合理依赖
2. **代码归属检查器**：基于代码特征自动建议文件归属
3. **资源引用验证器**：检查资源文件的引用关系
4. **API兼容性检查器**：确保模块接口的向后兼容性

#### 7.1.3 检查执行步骤

```bash
# 1. 扫描app模块新增文件
find app/src/main/java -name "*.kt" -o -name "*.java" | xargs grep -l "工具\|Util\|Helper\|Manager"

# 2. 检查依赖关系
./gradlew :app:dependencies --configuration implementation

# 3. 分析代码重复度
./gradlew :app:cpd

# 4. 验证模块结构
./gradlew :core:common:dependencies
./gradlew :core:data:dependencies
./gradlew :core:model:dependencies
./gradlew :core:network:dependencies
./gradlew :core:ui:dependencies
```

### 7.2 文件归属判断标准

#### 7.2.1 Common模块归属标准

**应归属到Common模块的文件特征：**
- 文件名包含：`Util`、`Helper`、`Manager`、`Provider`、`Extension`
- 功能特征：通用工具类、基础组件、扩展函数、常量定义
- 依赖特征：不依赖具体业务逻辑，可被多个模块复用
- 示例：`StringUtil.kt`、`TimeUtils.kt`、`ImageUtil.kt`、`ViewExtensions.kt`

#### 7.2.2 Network模块归属标准

**应归属到Network模块的文件特征：**
- 文件名包含：`Http`、`Network`、`Api`、`Interceptor`、`Request`、`Response`
- 功能特征：网络请求、API定义、网络监控、错误处理
- 依赖特征：使用OkHttp、Retrofit、网络相关库
- 示例：`HttpUtil.kt`、`NetworkMonitor.kt`、`ApiService.java`

#### 7.2.3 Model模块归属标准

**应归属到Model模块的文件特征：**
- 文件名包含：`DTO`、`Entity`、`Model`、`Bean`、`Data`
- 功能特征：数据传输对象、实体类、数据结构定义
- 依赖特征：纯数据类，无业务逻辑
- 示例：`UserInfoDTO.kt`、`HealthReportDTO.kt`、`BaseResponse.kt`

#### 7.2.4 Data模块归属标准

**应归属到Data模块的文件特征：**
- 文件名包含：`Repository`、`DataSource`、`Dao`、`Database`
- 功能特征：数据访问、数据存储、数据转换
- 依赖特征：使用Room、DataStore、SharedPreferences
- 示例：`UserRepository.kt`、`LocalDataRepository.kt`

#### 7.2.5 UI模块归属标准

**应归属到UI模块的文件特征：**
- 文件名包含：`View`、`Dialog`、`Widget`、`Component`
- 功能特征：自定义视图、通用UI组件、对话框
- 依赖特征：继承自View或其子类
- 示例：`CustomProgressBar.kt`、`ClearableEditText.kt`

### 7.3 迁移执行指南

#### 7.3.1 文件迁移步骤

**步骤1：准备工作**
1. 创建功能分支：`git checkout -b refactor/core-module-migration`
2. 备份当前代码：`git tag backup-before-migration`
3. 确认目标模块结构

**步骤2：文件迁移**
1. 移动文件到目标模块
2. 更新包名和导入语句
3. 修复编译错误
4. 更新资源文件引用

**步骤3：依赖关系调整**
1. 更新build.gradle依赖
2. 调整模块间依赖关系
3. 验证循环依赖

**步骤4：测试验证**
1. 运行单元测试
2. 执行集成测试
3. 进行功能测试
4. 性能回归测试

#### 7.3.2 风险控制措施

**迁移风险识别：**
1. **编译错误风险**：包名变更导致的导入错误
2. **运行时错误风险**：资源文件引用错误
3. **性能影响风险**：模块依赖增加导致的性能下降
4. **功能回归风险**：迁移过程中的功能丢失

**风险缓解策略：**
1. **分批迁移**：按模块逐步迁移，降低单次变更风险
2. **自动化测试**：建立完善的测试覆盖
3. **回滚机制**：保持代码可快速回滚的能力
4. **监控告警**：建立性能和错误监控

## 8. 完善的最佳实践

### 8.1 模块设计原则

#### 8.1.1 核心设计原则

1. **单一职责原则（SRP）**
   - 每个模块只负责一个特定的功能领域
   - 避免模块功能过于复杂和耦合

2. **依赖倒置原则（DIP）**
   - 高层模块不应依赖低层模块，都应依赖抽象
   - 使用接口和抽象类定义模块间契约

3. **开闭原则（OCP）**
   - 对扩展开放，对修改关闭
   - 通过接口和抽象实现功能扩展

4. **接口隔离原则（ISP）**
   - 使用多个专门的接口，而不是单一的总接口
   - 避免接口污染和不必要的依赖

#### 8.1.2 模块边界定义

**清晰的模块边界：**
- **Common模块**：提供基础设施和通用工具
- **Model模块**：定义数据结构和传输对象
- **Network模块**：处理网络通信和API调用
- **Data模块**：管理数据访问和存储
- **UI模块**：提供通用UI组件和资源

**模块间通信规范：**
- 通过定义良好的接口进行通信
- 避免直接访问其他模块的内部实现
- 使用依赖注入管理模块间依赖

### 8.2 代码组织规范

#### 8.2.1 包结构规范

```
com.healthlink.hms.core.{module}
├── api/                    # 对外接口定义
├── internal/              # 内部实现（不对外暴露）
├── model/                 # 模块内部数据模型
├── util/                  # 模块内部工具类
└── di/                    # 依赖注入配置
```

#### 8.2.2 命名规范

**类命名规范：**
- 工具类：以`Util`或`Utils`结尾
- 管理类：以`Manager`结尾
- 提供者：以`Provider`结尾
- 扩展函数：以`Extensions`结尾
- 接口：以`I`开头或使用描述性名词

**文件命名规范：**
- 使用PascalCase命名
- 文件名应清晰表达其功能
- 避免使用缩写和简写

### 8.3 依赖管理策略

#### 8.3.1 依赖层次结构

```
App Module
    ↓
Feature Modules
    ↓
Core Modules (Common, Data, Model, Network, UI)
    ↓
Third-party Libraries
```

#### 8.3.2 依赖注入最佳实践

**使用Hilt进行依赖注入：**
```kotlin
// 模块级别的依赖注入
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    @Provides
    @Singleton
    fun provideOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(LoggingInterceptor())
            .build()
    }
}
```

### 8.4 测试策略

#### 8.4.1 测试金字塔

1. **单元测试（70%）**
   - 测试单个类或方法的功能
   - 使用JUnit和Mockito
   - 每个Core模块都应有对应的测试

2. **集成测试（20%）**
   - 测试模块间的交互
   - 使用TestContainers进行数据库测试
   - 使用MockWebServer进行网络测试

3. **UI测试（10%）**
   - 测试用户界面和交互
   - 使用Espresso进行自动化测试

#### 8.4.2 测试组织结构

```
src/test/java/
├── unit/                  # 单元测试
│   ├── common/           # Common模块测试
│   ├── data/             # Data模块测试
│   ├── model/            # Model模块测试
│   ├── network/          # Network模块测试
│   └── ui/               # UI模块测试
├── integration/          # 集成测试
└── fixtures/             # 测试数据和工具
```

### 8.5 性能优化指南

#### 8.5.1 模块加载优化

1. **懒加载策略**
   - 对于大型对象使用懒加载
   - 延迟初始化非关键组件

2. **缓存策略**
   - 合理使用内存缓存
   - 实现磁盘缓存机制

3. **资源优化**
   - 压缩图片资源
   - 使用矢量图标
   - 优化字符串资源

#### 8.5.2 内存管理

1. **避免内存泄漏**
   - 正确管理生命周期
   - 及时释放资源
   - 使用弱引用避免循环引用

2. **对象池化**
   - 对频繁创建的对象使用对象池
   - 复用视图组件

### 8.6 版本管理和兼容性

#### 8.6.1 API版本控制

1. **语义化版本**
   - 主版本号：不兼容的API修改
   - 次版本号：向后兼容的功能性新增
   - 修订号：向后兼容的问题修正

2. **向后兼容性**
   - 保持公共API的稳定性
   - 使用@Deprecated标记过时API
   - 提供迁移指南

#### 8.6.2 变更管理

1. **变更日志**
   - 记录每次版本的变更内容
   - 说明破坏性变更和迁移方法

2. **影响评估**
   - 评估变更对现有代码的影响
   - 提供自动化迁移工具

## 9. 未来发展规划

### 9.1 模块化进一步优化

1. **微服务化**：将部分功能模块独立为微服务
2. **插件化**：支持动态加载功能模块
3. **组件化**：进一步细化组件粒度

### 9.2 技术栈升级

1. **Kotlin协程**：逐步替换RxJava
2. **Jetpack Compose**：引入现代化UI框架
3. **KMP**：支持跨平台开发

### 9.3 开发效率提升

1. **代码生成**：使用注解处理器生成样板代码
2. **自动化测试**：完善CI/CD流程
3. **文档自动化**：使用工具自动生成API文档

***

**文档版本**：v1.0\
**最后更新**：2025年8月\
**维护者**：HMS开发团队
