# Health-Report模块重构计划

## 1. 现状分析

### 1.1 Core模块现状

* **core:common**: 包含BaseVBVMActivity、工具类、扩展函数等基础组件

* **core:model**: 包含完整的DTO类定义，包括HealthReportDTO、HistoryStatusDTO等

* **core:data**: 包含Repository接口和实现，支持DataStore

* **core:network**: 包含网络层实现，支持Retrofit和协程

* **core:ui**: 包含UI资源和自定义视图组件

### 1.2 当前health-report模块问题

1. **架构不够清晰**: 缺少明确的层次分离
2. **依赖关系混乱**: 直接依赖具体实现而非接口
3. **状态管理不统一**: 混用LiveData和传统回调
4. **缺少完整的错误处理**: 网络异常处理不完善
5. **Fragment与Activity交互复杂**: 缺少清晰的通信机制
6. **数据缓存机制不完善**: 缺少有效的本地缓存策略

## 2. 重构目标

### 2.1 架构目标

* 采用Clean Architecture架构模式

* 实现完整的MVVM模式

* 使用现代Android技术栈（Hilt、Coroutines、StateFlow）

* 提升代码可测试性和可维护性

### 2.2 功能目标

* 保持与原有功能的完全兼容

* 提升用户体验和性能

* 增强错误处理和异常恢复能力

* 完善数据埋点和分析功能

## 3. 重构架构设计

### 3.1 目录结构优化

```
feature/health-report/src/main/java/com/healthlink/hms/feature/healthreport/
├── ui/
│   ├── activity/
│   │   └── HealthReportActivity.kt
│   ├── fragment/
│   │   └── ReportDayFragment.kt
│   ├── adapter/
│   │   └── HealthReportPagerAdapter.kt
│   ├── charts/
│   │   ├── StatisticsView.kt
│   │   └── HealthChartRenderer.kt
│   └── viewmodel/
│       └── HealthReportViewModel.kt
├── domain/
│   ├── usecase/
│   │   ├── GetHealthReportUseCase.kt
│   │   ├── GetHealthHistoryUseCase.kt
│   │   ├── CalculateHealthScoreUseCase.kt
│   │   └── AssessHealthRiskUseCase.kt
│   ├── repository/
│   │   └── HealthReportRepository.kt
│   └── model/
│       ├── HealthReportData.kt
│       ├── HealthScore.kt
│       ├── RiskLevel.kt
│       └── ReportTimeRange.kt
├── data/
│   ├── repository/
│   │   └── DefaultHealthReportRepository.kt
│   ├── remote/
│   │   ├── HealthReportApi.kt
│   │   └── dto/
│   │       └── HealthReportRequestDTO.kt
│   ├── local/
│   │   ├── HealthReportCache.kt
│   │   └── HealthReportPreferences.kt
│   └── mapper/
│       └── HealthReportMapper.kt
├── analytics/
│   └── HealthReportAnalytics.kt
└── di/
    └── HealthReportModule.kt
```

### 3.2 依赖关系设计

```
UI Layer (Activity/Fragment/ViewModel)
    ↓
Domain Layer (UseCase/Repository Interface)
    ↓
Data Layer (Repository Implementation/API/Cache)
    ↓
Core Modules (model/network/data/common/ui)
```

## 4. 详细重构计划

### 4.1 Phase 1: 基础架构重构

1. **重构目录结构**: 按照Clean Architecture重新组织代码
2. **标准化数据模型**: 确保与core:model的一致性
3. **重构Repository层**: 实现数据访问抽象
4. **重构Domain层**: 实现UseCase和业务逻辑封装

### 4.2 Phase 2: UI层现代化

1. **重构ViewModel**: 使用StateFlow替代LiveData
2. **优化Activity和Fragment**: 简化交互逻辑
3. **实现依赖注入**: 使用Hilt替换手动依赖管理
4. **优化网络请求**: 统一错误处理机制

### 4.3 Phase 3: 功能完善

1. **实现数据埋点**: 重新设计分析体系
2. **添加单元测试**: 提升代码质量
3. **性能优化**: 缓存和预加载机制
4. **验证功能**: 确保与原有功能一致

## 5. 技术实现要点

### 5.1 状态管理

```kotlin
// 使用UiState封装UI状态
sealed class UiState<out T> {
    object Loading : UiState<Nothing>()
    data class Success<T>(val data: T) : UiState<T>()
    data class Error(val exception: Throwable) : UiState<Nothing>()
}

// ViewModel中使用StateFlow
class HealthReportViewModel @Inject constructor(
    private val getHealthReportUseCase: GetHealthReportUseCase
) : ViewModel() {
    private val _uiState = MutableStateFlow<UiState<HealthReportData>>(UiState.Loading)
    val uiState: StateFlow<UiState<HealthReportData>> = _uiState.asStateFlow()
}
```

### 5.2 依赖注入

```kotlin
@Module
@InstallIn(SingletonComponent::class)
abstract class HealthReportModule {
    
    @Binds
    abstract fun bindHealthReportRepository(
        defaultHealthReportRepository: DefaultHealthReportRepository
    ): HealthReportRepository
}
```

### 5.3 网络请求优化

```kotlin
// 统一的Result封装
sealed class Result<out T> {
    data class Success<T>(val data: T) : Result<T>()
    data class Error(val exception: Throwable) : Result<Nothing>()
}

// UseCase中的错误处理
class GetHealthReportUseCase @Inject constructor(
    private val repository: HealthReportRepository
) {
    suspend operator fun invoke(userId: String, timeCode: String): Result<HealthReportData> {
        return try {
            val response = repository.getHealthReportData(userId, timeCode)
            Result.Success(response)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }
}
```

### 5.4 Fragment与Activity交互

```kotlin
// 使用接口定义交互协议
interface HealthReportInteraction {
    fun setTabVisibility(visibility: Int)
    fun showError(message: String)
    fun getAuthStatus(): Boolean
}

// Activity实现接口
class HealthReportActivity : AppCompatActivity(), HealthReportInteraction {
    override fun setTabVisibility(visibility: Int) {
        binding.tabLayout.visibility = visibility
    }
}
```

## 6. 数据埋点重构

### 6.1 分析事件定义

```kotlin
object HealthReportAnalytics {
    const val PAGE_ENTER = "Health_Healthreports_PV"
    const val PAGE_EXIT = "Health_Healthreports_Close"
    const val RETURN_CLICK = "Health_Healthreports_Return_Click"
    const val HEART_RATE_CLICK = "Health_Healthreports_HeartRate_Click"
    const val SLEEP_CLICK = "Health_Healthreports_Sleep_Click"
    const val SPO2_CLICK = "Health_Healthreports_SpO2_Click"
    const val PRESSURE_CLICK = "Health_Healthreports_Pressure_Click"
    const val TEMPERATURE_CLICK = "Health_Healthreports_Temperature_Click"
    const val BLOOD_PRESSURE_CLICK = "Health_Healthreports_BloodPressure_Click"
}
```

### 6.2 分析管理器

```kotlin
@Singleton
class HealthReportAnalyticsManager @Inject constructor() {
    
    fun trackPageEnter(userId: String) {
        DataTrackUtil.dtEnterPage(
            HealthReportAnalytics.PAGE_ENTER,
            DataTrackUtil.userIDMap(userId)
        )
    }
    
    fun trackChartClick(chartType: String, userId: String) {
        val eventName = when (chartType) {
            "heart_rate" -> HealthReportAnalytics.HEART_RATE_CLICK
            "sleep" -> HealthReportAnalytics.SLEEP_CLICK
            "spo2" -> HealthReportAnalytics.SPO2_CLICK
            "pressure" -> HealthReportAnalytics.PRESSURE_CLICK
            "temperature" -> HealthReportAnalytics.TEMPERATURE_CLICK
            "blood_pressure" -> HealthReportAnalytics.BLOOD_PRESSURE_CLICK
            else -> return
        }
        DataTrackUtil.dtClick(eventName, DataTrackUtil.userIDMap(userId))
    }
}
```

## 7. 测试策略

### 7.1 单元测试

* Repository层测试

* UseCase层测试

* ViewModel层测试

* 数据映射测试

### 7.2 集成测试

* API接口测试

* 数据库操作测试

* 缓存机制测试

### 7.3 UI测试

* Activity启动测试

* Fragment交互测试

* 用户操作流程测试

## 8. 性能优化

### 8.1 数据缓存

* 实现多级缓存策略

* 智能缓存失效机制

* 预加载关键数据

### 8.2 UI优化

* 图表渲染优化

* 列表滚动优化

* 内存泄漏防护

### 8.3 网络优化

* 请求去重机制

* 智能重试策略

* 离线数据支持

## 9. 迁移策略

### 9.1 渐进式迁移

1. 保持原有接口不变
2. 逐步替换内部实现
3. 分阶段验证功能
4. 最终移除旧代码

### 9.2 兼容性保证

* 保持API接口稳定

* 数据格式向后兼容

* 用户体验一致性

### 9.3 风险控制

* 功能开关控制

* 灰度发布策略

* 快速回滚机制

## 10. 验收标准

### 10.1 功能验收

* [ ] 所有原有功能正常工作

* [ ] 数据展示准确无误

* [ ] 用户交互流畅自然

* [ ] 错误处理完善

### 10.2 性能验收

* [ ] 启动时间不超过原版本

* [ ] 内存使用优化

* [ ] 网络请求效率提升

* [ ] 用户体验改善

### 10.3 代码质量验收

* [ ] 代码覆盖率达到80%以上

* [ ] 静态代码分析通过

* [ ] 架构设计文档完整

* [ ] 技术债务清理完成

## 11. 时间计划

* **Week 1**: Phase 1 - 基础架构重构

* **Week 2**: Phase 2 - UI层现代化

* **Week 3**: Phase 3 - 功能完善

* **Week 4**: 测试验证和优化

## 12. 风险评估

### 12.1 技术风险

* 新架构学习成本

* 数据迁移复杂性

* 性能回归风险

### 12.2 业务风险

* 功能兼容性问题

* 用户体验变化

* 发布时间延迟

### 12.3 缓解措施

* 充分的测试覆盖

* 分阶段发布策略

* 快速响应机制

