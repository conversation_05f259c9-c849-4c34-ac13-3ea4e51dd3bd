# HMS - 健康管理系统 (Health Management System)

## 项目概述

HMS是一个基于Android平台的智能健康管理系统，专为车载环境设计。该系统集成了健康监测、设备管理、AI助手等多项功能，为用户提供全方位的健康管理服务。项目采用现代化的模块化架构，支持多平台部署（V4/V35），并具备完善的数据存储和网络通信能力。

## 核心功能特性

### 🏥 健康监测模块
- **多维度健康数据监测**：心率、血压、血氧、睡眠、压力等
- **健康数据图表可视化**：实时数据图表展示和趋势分析
- **健康权限管理**：细粒度的健康数据访问控制
- **设备数据同步**：多种健康设备数据采集和同步

### 📊 健康报告模块
- **综合健康报告**：日报、周报、月报等多维度数据分析
- **健康评分系统**：基于多项指标的综合健康评分
- **风险评估分析**：健康风险等级评估和预警
- **个性化建议**：基于数据分析的健康改善建议
- **趋势对比分析**：不同时间段的健康数据对比

### 🌸 香薰功能模块
- **智能香薰机控制**：支持蓝牙连接和设备管理
- **香薰配方管理**：多种香型和强度级别配置
- **设备状态监控**：实时监控香薰机工作状态
- **个性化设置**：用户偏好和使用习惯记录

### 👨‍⚕️ 医生通话模块
- **远程医疗咨询**：支持音视频通话功能
- **SIP协议集成**：稳定的通信连接
- **通话记录管理**：历史通话记录和咨询内容

### 📱 蓝牙设备管理
- **设备扫描和配对**：自动发现和连接健康设备
- **多设备支持**：同时管理多个蓝牙健康设备
- **连接状态监控**：实时设备连接状态显示
- **权限管理**：蓝牙权限的智能申请和管理

### 🔐 认证授权系统
- **华为OAuth集成**：支持华为账号登录
- **访客模式**：无需登录即可使用基础功能
- **权限分级管理**：不同用户角色的权限控制
- **数据安全**：用户数据加密存储和传输

### ⚙️ 系统设置管理
- **主题切换**：支持多种UI主题
- **个人信息管理**：用户基本信息和偏好设置
- **应用升级**：自动检测和升级功能
- **数据迁移**：从MMKV到DataStore的数据迁移

## 技术架构

### 架构设计
```
┌─────────────────────────────────────────────────────────┐
│                    HMS Application                      │
├─────────────────────────────────────────────────────────┤
│  Feature Modules                                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │   Health    │ │ Aromatherapy│ │   Doctor    │ ...  │
│  │  Monitoring │ │   Control   │ │    Call     │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
├─────────────────────────────────────────────────────────┤
│  Core Modules                                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │    Data     │ │   Network   │ │     UI      │ ...  │
│  │  Repository │ │   Service   │ │ Components  │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
├─────────────────────────────────────────────────────────┤
│  Foundation Layer                                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │   Model     │ │   Common    │ │  External   │      │
│  │ Definitions │ │  Utilities  │ │   SDKs      │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
└─────────────────────────────────────────────────────────┘
```

### 技术栈

#### 核心框架
- **Android SDK**: Target API 33, Min API 29
- **Kotlin**: 主要开发语言，支持协程
- **Jetpack Compose**: 现代化UI框架
- **Hilt**: 依赖注入框架
- **Architecture Components**: ViewModel, LiveData, Navigation

#### 网络与数据
- **Retrofit2**: HTTP客户端
- **OkHttp3**: 网络请求拦截和日志
- **Gson**: JSON序列化/反序列化
- **DataStore**: 现代化数据存储（替代MMKV）
- **RxJava2**: 响应式编程

#### UI与交互
- **Material Design 3**: UI设计规范
- **Glide**: 图片加载和缓存
- **MPAndroidChart**: 数据图表展示
- **AgentWeb**: WebView组件
- **Lottie**: 动画效果

#### 外部集成
- **华为HMS**: OAuth认证和地图服务
- **TTS SDK**: 语音合成
- **GWM SDK**: 车载平台集成
- **SIP协议**: 音视频通话

## 项目结构

```
hms/
├── app/                          # 主应用模块
│   ├── src/main/java/com/healthlink/hms/
│   │   ├── activity/            # Activity层
│   │   ├── fragment/            # Fragment层
│   │   ├── business/            # 业务逻辑层
│   │   │   ├── aromatherapy/   # 香薰功能
│   │   │   ├── doctorcall/     # 医生通话
│   │   │   └── medai/          # AI助手
│   │   └── viewmodels/         # ViewModel层
│   └── build.gradle.kts
├── core/                        # 核心模块
│   ├── common/                 # 通用工具和扩展
│   ├── data/                   # 数据层（Repository模式）
│   ├── model/                  # 数据模型定义
│   ├── network/                # 网络服务层
│   └── ui/                     # UI组件库
├── feature/                    # 功能模块
│   └── setting/               # 设置功能模块
├── build-logic/               # 构建逻辑
└── gradle/                    # Gradle配置
```

## 模块化架构

### 已实现模块

#### Core模块
- **core:model** - 数据模型和实体定义
- **core:network** - 网络服务和API接口
- **core:data** - 数据仓库和本地存储
- **core:common** - 通用工具类和扩展函数
- **core:ui** - 可复用UI组件

#### Feature模块
- **feature:setting** - 系统设置功能

### 模块依赖关系
```
app
├── feature:setting
├── core:data
├── core:network
├── core:model
└── core:ui

feature:setting
├── core:data
├── core:model
└── core:ui

core:data
├── core:network
└── core:model

core:network
└── core:model

core:ui
└── core:model
```

## 开发环境配置

### 系统要求
- **JDK**: 17+
- **Android Studio**: 最新稳定版
- **Gradle**: 8.0+
- **Android SDK**: API 33+

### 构建配置

#### 构建变体
- **debug**: 开发环境，连接测试服务器
- **preRelease**: 预发布环境
- **release**: 生产环境

#### 产品风味
- **V4**: 针对V4平台的构建
- **V35**: 针对V35平台的构建

### 快速开始

1. **克隆项目**
   ```bash
   git clone [repository-url]
   cd hms
   ```

2. **环境检查**
   ```bash
   ./gradlew --version
   # 确保JDK 17+
   ```

3. **构建项目**
   ```bash
   ./gradlew assembleDebug
   ```

4. **运行测试**
   ```bash
   ./gradlew test
   ```

## 开发指南

### 代码规范
- 遵循Kotlin官方编码规范
- 使用Hilt进行依赖注入
- Repository模式管理数据层
- MVVM架构模式
- 协程处理异步操作

### 数据存储迁移
项目正在从MMKV迁移到DataStore，详细迁移指南请参考 `MIGRATION_GUIDE.md`

### 模块开发
- 新功能应创建独立的feature模块
- 遵循单一职责原则
- 模块间通过接口通信
- 避免循环依赖

## API配置

### 环境配置
- **测试环境**: `gwm-cockpit-test.healthlinkiot.com`
- **生产环境**: `gwm-cockpit.healthlinkiot.com`
- **SIP服务**: 支持音视频通话
- **华为服务**: OAuth认证集成

## 安全与隐私

- 用户健康数据加密存储
- 网络传输使用HTTPS
- 权限最小化原则
- 支持访客模式保护隐私
- 符合健康数据保护法规

## 版本管理

- 语义化版本控制
- 自动化构建和部署
- 支持多渠道分发
- 增量更新机制

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request
5. 代码审查和合并

## 许可证

本项目采用私有许可证，仅供内部开发使用。

## 联系方式

- 项目维护者: HMS开发团队
- 技术支持: [技术支持邮箱]
- 文档更新: 请参考项目Wiki

---

*最后更新: 2025年*