# LocalDataRepository重构指导文档

## 1. 概述

### 1.1 重构背景

在HMS项目的MVVM-Repository架构改造过程中，发现`LocalDataRepository`中的方法职责不够清晰，存在功能重复和架构不一致的问题。为了提升代码的可维护性和架构的清晰度，需要将`LocalDataRepository`中的方法重新分配到更合适的Repository中。

### 1.2 重构目标

* **职责分离**：确保每个Repository专注于特定领域的数据管理

* **消除重复**：避免不同Repository间的功能重复

* **架构统一**：符合单一职责原则，提升整体架构一致性

* **维护简化**：相关功能集中管理，降低维护复杂度

### 1.3 影响范围

* `core/data`模块：Repository接口和实现类

* `feature/setting`模块：已完成MVVM-Repository改造的业务模块

* 其他可能使用`LocalDataRepository`的模块

## 2. 方法重新分配方案

### 2.1 当前LocalDataRepository方法分析

```kotlin
interface LocalDataRepository {
    // 打开/关闭通知
    suspend fun toggleNotificationSwitch(isOn: Boolean)
    // 获取通知开关状态
    suspend fun getNotificationSwitchState(): Boolean
    // 存储隐私政策
    suspend fun storePrivacyPolicy(isAgree: Boolean)
    // 获取用户ID
    suspend fun getCurrentUserId(): String?
}
```

### 2.2 方法重新分配详细方案

#### 2.2.1 通知相关方法 → SystemRepository

**迁移方法**：

* `toggleNotificationSwitch(isOn: Boolean)` → `storeNotificationOpen(isOn: Boolean)`

* `getNotificationSwitchState(): Boolean` → `getNotificationState(): Flow<Boolean>`

**理由**：

* 通知开关属于系统级设置

* SystemRepository已包含主题、语言等系统配置

* 逻辑上通知设置属于系统配置范畴

**SystemRepository新增方法**：

```kotlin
interface SystemRepository {
    // 现有方法...
    
    // 新增：通知开关
    suspend fun storeNotificationOpen(isOn: Boolean)
    fun getNotificationState(): Flow<Boolean>
}
```

#### 2.2.2 隐私政策方法 → HealthAuthorityRepository

**迁移方法**：

* `storePrivacyPolicy(isAgree: Boolean)` → 使用现有的`storePrivacyPolicyAccepted(accepted: Boolean)`

**理由**：

* 隐私政策同意状态属于权限管理范畴

* HealthAuthorityRepository已有相同功能的方法

* 避免功能重复，统一权限管理

**现有方法**：

```kotlin
interface HealthAuthorityRepository {
    // 隐私政策（已存在）
    suspend fun storePrivacyPolicyAccepted(accepted: Boolean)
    fun isPrivacyPolicyAccepted(): Flow<Boolean>
}
```

#### 2.2.3 用户ID方法 → UserRepository

**迁移方法**：

* `getCurrentUserId(): String?` → 使用现有的`getUserId(): Flow<String>`

**理由**：

* 用户ID明显属于用户相关数据

* UserRepository已有相同功能的方法

* 避免功能重复，统一用户数据管理

**现有方法**：

```kotlin
interface UserRepository {
    // 用户基本信息（已存在）
    suspend fun storeUserId(userId: String)
    fun getUserId(): Flow<String>
    suspend fun clearUserId()
}
```

## 3. 模块适配和修改指南

### 3.1 core/data模块修改

#### 3.1.1 SystemRepository接口更新

**文件路径**：`core/data/src/main/java/com/healthlink/hms/core/data/repository/SystemRepository.kt`

**修改内容**：

```kotlin
interface SystemRepository {
    // 现有方法保持不变...
    
    // 新增：通知开关
    suspend fun storeNotificationOpen(isOn: Boolean)
    fun getNotificationState(): Flow<Boolean>
}
```

#### 3.1.2 DefaultSystemRepository实现更新

**文件路径**：`core/data/src/main/java/com/healthlink/hms/core/data/repository/DefaultSystemRepository.kt`

**修改内容**：

```kotlin
@Singleton
class DefaultSystemRepository @Inject constructor(
    @SystemDataStore private val systemDataStore: DataStore<Preferences>
) : SystemRepository {
    
    // 现有实现保持不变...
    
    // 新增：通知开关实现
    override suspend fun storeNotificationOpen(isOn: Boolean) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.IS_NOTIFICATION_OPEN] = isOn
        }
    }
    
    override fun getNotificationState(): Flow<Boolean> {
        return systemDataStore.data.map { preferences ->
            preferences[DataStorePreKeysConfig.IS_NOTIFICATION_OPEN] ?: false
        }
    }
}
```

### 3.2 feature/setting模块适配

#### 3.2.1 SettingsViewModel更新

**文件路径**：`feature/setting/src/main/java/com/healthlink/hms/feature/setting/presentation/viewmodel/SettingsViewModel.kt`

**修改内容**：

```kotlin
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val settingsRepository: SettingsRepository,
    private val systemRepository: SystemRepository,  // 新增
    private val healthAuthorityRepository: HealthAuthorityRepository,  // 新增
    private val userRepository: UserRepository  // 新增
) : ViewModel() {
    
    // 通知开关状态
    val notificationState = systemRepository.getNotificationState()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false
        )
    
    // 隐私政策状态
    val privacyPolicyState = healthAuthorityRepository.isPrivacyPolicyAccepted()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false
        )
    
    // 用户ID
    val userId = userRepository.getUserId()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = ""
        )
    
    // 切换通知开关
    fun toggleNotification(isOn: Boolean) {
        viewModelScope.launch {
            try {
                systemRepository.storeNotificationOpen(isOn)
            } catch (e: Exception) {
                // 错误处理
                Log.e("SettingsViewModel", "Failed to toggle notification", e)
            }
        }
    }
    
    // 更新隐私政策同意状态
    fun updatePrivacyPolicy(accepted: Boolean) {
        viewModelScope.launch {
            try {
                healthAuthorityRepository.storePrivacyPolicyAccepted(accepted)
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "Failed to update privacy policy", e)
            }
        }
    }
}
```

#### 3.2.2 HMSPersonalActivity更新

**文件路径**：`feature/setting/src/main/java/com/healthlink/hms/feature/setting/presentation/activity/HMSPersonalActivity.kt`

**修改内容**：

```kotlin
@AndroidEntryPoint
class HMSPersonalActivity : AppCompatActivity() {
    
    private val viewModel: SettingsViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setupObservers()
        setupClickListeners()
    }
    
    private fun setupObservers() {
        // 观察通知开关状态
        lifecycleScope.launch {
            viewModel.notificationState.collect { isOn ->
                updateNotificationSwitch(isOn)
            }
        }
        
        // 观察隐私政策状态
        lifecycleScope.launch {
            viewModel.privacyPolicyState.collect { accepted ->
                updatePrivacyPolicyUI(accepted)
            }
        }
        
        // 观察用户ID
        lifecycleScope.launch {
            viewModel.userId.collect { userId ->
                updateUserIdDisplay(userId)
            }
        }
    }
    
    private fun setupClickListeners() {
        // 通知开关点击
        notificationSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.toggleNotification(isChecked)
        }
        
        // 隐私政策点击
        privacyPolicyButton.setOnClickListener {
            // 显示隐私政策对话框
            showPrivacyPolicyDialog()
        }
    }
    
    private fun showPrivacyPolicyDialog() {
        AlertDialog.Builder(this)
            .setTitle("隐私政策")
            .setMessage("请阅读并同意隐私政策...")
            .setPositiveButton("同意") { _, _ ->
                viewModel.updatePrivacyPolicy(true)
            }
            .setNegativeButton("拒绝") { _, _ ->
                viewModel.updatePrivacyPolicy(false)
            }
            .show()
    }
}
```

### 3.3 其他模块适配指南

#### 3.3.1 查找使用LocalDataRepository的模块

**搜索命令**：

```bash
# 搜索LocalDataRepository的使用
grep -r "LocalDataRepository" --include="*.kt" .

# 搜索具体方法的使用
grep -r "toggleNotificationSwitch\|getNotificationSwitchState\|storePrivacyPolicy\|getCurrentUserId" --include="*.kt" .
```

#### 3.3.2 通用适配步骤

对于每个使用`LocalDataRepository`的模块，按以下步骤进行适配：

1. **依赖注入更新**：

   ```kotlin
   // 原来
   @Inject
   lateinit var localDataRepository: LocalDataRepository

   // 更新后
   @Inject
   lateinit var systemRepository: SystemRepository
   @Inject
   lateinit var healthAuthorityRepository: HealthAuthorityRepository
   @Inject
   lateinit var userRepository: UserRepository
   ```

2. **方法调用替换**：

   ```kotlin
   // 通知开关
   // 原来：localDataRepository.toggleNotificationSwitch(true)
   // 更新：systemRepository.storeNotificationOpen(true)

   // 获取通知状态
   // 原来：localDataRepository.getNotificationSwitchState()
   // 更新：systemRepository.getNotificationState().first()

   // 隐私政策
   // 原来：localDataRepository.storePrivacyPolicy(true)
   // 更新：healthAuthorityRepository.storePrivacyPolicyAccepted(true)

   // 用户ID
   // 原来：localDataRepository.getCurrentUserId()
   // 更新：userRepository.getUserId().first()
   ```

3. **响应式编程适配**：

   ```kotlin
   // 如果需要响应式数据流
   lifecycleScope.launch {
       systemRepository.getNotificationState().collect { isOn ->
           // 处理通知状态变化
       }
   }
   ```

## 4. 分步骤实施计划

### 4.1 准备阶段（第1周）

#### 4.1.1 代码审计

* [ ] 全面搜索`LocalDataRepository`的使用情况

* [ ] 识别所有依赖模块和文件

* [ ] 评估影响范围和风险等级

* [ ] 制定详细的迁移清单

### 4.2 核心实施阶段（第2-3周）

#### 4.2.1 Repository层更新（第2周第1-2天）

* [ ] 更新`SystemRepository`接口，添加通知相关方法

* [ ] 实现`DefaultSystemRepository`中的新方法

* [ ] 验证`HealthAuthorityRepository`和`UserRepository`现有方法

* [ ] 运行单元测试确保Repository层功能正常

#### 4.2.2 依赖注入配置更新（第2周第3天）

* [ ] 更新`LocalRepositoryModule.kt`

* [ ] 确保所有Repository正确注册

* [ ] 验证依赖注入配置无循环依赖

* [ ] 编译验证无错误

#### 4.2.3 feature/setting模块适配（第2周第4-5天）

* [ ] 更新`SettingsViewModel`依赖注入

* [ ] 替换方法调用，使用新的Repository

* [ ] 更新`HMSPersonalActivity`和`DialogTransparentActivity`

* [ ] 运行模块测试确保功能正常

#### 4.2.4 其他模块逐步迁移（第3周）

* [ ] 按优先级逐个迁移其他使用模块

* [ ] 每个模块迁移后进行功能测试

* [ ] 记录迁移过程中的问题和解决方案

* [ ] 更新相关文档

### 4.3 验证和清理阶段（第4周）

#### 4.3.1 全面测试（第4周第1-2天）

* [ ] 运行所有单元测试

* [ ] 执行集成测试

* [ ] 进行端到端功能测试

* [ ] 性能测试确保无性能退化

#### 4.3.2 代码清理（第4周第3天）

* [ ] 删除`LocalDataRepository`接口和实现

* [ ] 清理相关的依赖注入配置

* [ ] 移除未使用的导入和依赖

* [ ] 代码格式化和优化

#### 4.3.3 文档更新（第4周第4-5天）

* [ ] 更新架构文档

* [ ] 更新API文档

* [ ] 编写迁移总结报告

* [ ] 更新开发指南

## 5. 依赖注入配置更新指南

### 5.1 LocalRepositoryModule.kt更新

**文件路径**：`core/data/src/main/java/com/healthlink/hms/core/data/di/LocalRepositoryModule.kt`

**修改内容**：

```kotlin
@Module
@InstallIn(SingletonComponent::class)
abstract class LocalRepositoryModule {
    
    // 移除LocalDataRepository绑定
    // @Binds
    // abstract fun bindLocalDataRepository(impl: DefaultLocalDataRepository): LocalDataRepository
    
    // 确保以下Repository绑定已启用
    @Binds
    abstract fun bindSystemRepository(impl: DefaultSystemRepository): SystemRepository
    
    @Binds
    abstract fun bindHealthAuthorityRepository(impl: DefaultHealthAuthorityRepository): HealthAuthorityRepository
    
    @Binds
    abstract fun bindUserRepository(impl: DefaultUserRepository): UserRepository
    
    // 其他Repository绑定...
}
```

### 5.2 验证依赖注入配置

**检查清单**：

* [ ] 所有Repository接口都有对应的实现类绑定

* [ ] 没有循环依赖问题

* [ ] DataStore注入配置正确

* [ ] 编译无错误

* [ ] 运行时注入正常

## 6. 测试验证策略

### 6.1 单元测试

#### 6.1.1 Repository层测试

```kotlin
@RunWith(AndroidJUnit4::class)
class DefaultSystemRepositoryTest {
    
    @Test
    fun `storeNotificationOpen should save notification state`() = runTest {
        // Given
        val testDataStore = TestDataStore()
        val repository = DefaultSystemRepository(testDataStore)
        
        // When
        repository.storeNotificationOpen(true)
        
        // Then
        val result = repository.getNotificationState().first()
        assertEquals(true, result)
    }
    
    @Test
    fun `getNotificationState should return default false`() = runTest {
        // Given
        val testDataStore = TestDataStore()
        val repository = DefaultSystemRepository(testDataStore)
        
        // When
        val result = repository.getNotificationState().first()
        
        // Then
        assertEquals(false, result)
    }
}
```

#### 6.1.2 ViewModel测试

```kotlin
@RunWith(AndroidJUnit4::class)
class SettingsViewModelTest {
    
    @Mock
    private lateinit var systemRepository: SystemRepository
    
    @Mock
    private lateinit var healthAuthorityRepository: HealthAuthorityRepository
    
    @Mock
    private lateinit var userRepository: UserRepository
    
    @Test
    fun `toggleNotification should call systemRepository`() = runTest {
        // Given
        val viewModel = SettingsViewModel(
            settingsRepository = mockSettingsRepository,
            systemRepository = systemRepository,
            healthAuthorityRepository = healthAuthorityRepository,
            userRepository = userRepository
        )
        
        // When
        viewModel.toggleNotification(true)
        
        // Then
        verify(systemRepository).storeNotificationOpen(true)
    }
}
```

### 6.2 集成测试

#### 6.2.1 端到端功能测试

```kotlin
@RunWith(AndroidJUnit4::class)
@LargeTest
class SettingsIntegrationTest {
    
    @get:Rule
    val hiltRule = HiltAndroidRule(this)
    
    @get:Rule
    val activityRule = ActivityScenarioRule(HMSPersonalActivity::class.java)
    
    @Test
    fun `notification switch should work end to end`() {
        // 点击通知开关
        onView(withId(R.id.notification_switch))
            .perform(click())
        
        // 验证开关状态
        onView(withId(R.id.notification_switch))
            .check(matches(isChecked()))
        
        // 重启Activity验证状态持久化
        activityRule.scenario.recreate()
        
        onView(withId(R.id.notification_switch))
            .check(matches(isChecked()))
    }
}
```

### 6.3 性能测试

#### 6.3.1 数据存储性能

```kotlin
@Test
fun `repository operations should complete within acceptable time`() = runTest {
    val repository = DefaultSystemRepository(testDataStore)
    
    val startTime = System.currentTimeMillis()
    
    // 执行100次存储操作
    repeat(100) {
        repository.storeNotificationOpen(it % 2 == 0)
    }
    
    val endTime = System.currentTimeMillis()
    val duration = endTime - startTime
    
    // 验证性能在可接受范围内（例如：100次操作在1秒内完成）
    assertTrue("Operations took too long: ${duration}ms", duration < 1000)
}
```

## 7. 风险控制和回滚方案

### 7.1 风险识别

#### 7.1.1 高风险项

* **数据丢失风险**：迁移过程中可能导致用户设置丢失

* **功能中断风险**：相关功能在迁移期间可能不可用

* **性能退化风险**：新的Repository实现可能影响性能

* **兼容性风险**：现有数据格式与新实现不兼容

#### 7.1.2 中风险项

* **编译错误风险**：依赖注入配置错误导致编译失败

* **测试失败风险**：现有测试用例需要更新

* **文档过时风险**：相关文档需要同步更新

### 7.2 风险缓解措施

#### 7.2.1 数据备份策略

```kotlin
// 迁移前备份现有数据
class DataMigrationHelper {
    suspend fun backupLocalDataRepositoryData(): BackupData {
        return BackupData(
            notificationState = localDataRepository.getNotificationSwitchState(),
            privacyPolicyAccepted = localDataRepository.getPrivacyPolicyState(),
            userId = localDataRepository.getCurrentUserId()
        )
    }
    
    suspend fun restoreData(backupData: BackupData) {
        systemRepository.storeNotificationOpen(backupData.notificationState)
        healthAuthorityRepository.storePrivacyPolicyAccepted(backupData.privacyPolicyAccepted)
        userRepository.storeUserId(backupData.userId ?: "")
    }
}
```

#### 7.2.2 渐进式迁移

```kotlin
// 使用Feature Flag控制迁移进度
class RepositoryMigrationManager {
    fun shouldUseNewRepository(): Boolean {
        return BuildConfig.ENABLE_NEW_REPOSITORY || 
               RemoteConfig.getBoolean("use_new_repository_implementation")
    }
}

// 在ViewModel中使用
class SettingsViewModel {
    private val migrationManager = RepositoryMigrationManager()
    
    suspend fun toggleNotification(isOn: Boolean) {
        if (migrationManager.shouldUseNewRepository()) {
            systemRepository.storeNotificationOpen(isOn)
        } else {
            localDataRepository.toggleNotificationSwitch(isOn)
        }
    }
}
```

### 7.3 回滚方案

#### 7.3.1 快速回滚步骤

1. **代码回滚**：

   ```bash
   # 回滚到迁移前的commit
   git revert <migration_commit_hash>

   # 或者切换到备份分支
   git checkout backup/before-local-data-repository-migration
   ```

2. **数据恢复**：

   ```kotlin
   // 使用备份数据恢复用户设置
   migrationHelper.restoreData(backupData)
   ```

3. **配置回滚**：

   ```kotlin
   // 在RemoteConfig中禁用新实现
   RemoteConfig.setBoolean("use_new_repository_implementation", false)
   ```

#### 7.3.2 回滚验证清单

* [ ] 所有功能恢复正常

* [ ] 用户数据完整性验证

* [ ] 性能指标恢复到迁移前水平

* [ ] 无新的崩溃或错误

* [ ] 用户反馈正常

## 8. 具体代码迁移示例

### 8.1 简单方法调用替换

#### 8.1.1 同步调用转异步调用

```kotlin
// 迁移前
class OldSettingsManager {
    fun updateNotificationSetting(isOn: Boolean) {
        localDataRepository.toggleNotificationSwitch(isOn)
    }
    
    fun getNotificationSetting(): Boolean {
        return localDataRepository.getNotificationSwitchState()
    }
}

// 迁移后
class NewSettingsManager {
    suspend fun updateNotificationSetting(isOn: Boolean) {
        systemRepository.storeNotificationOpen(isOn)
    }
    
    suspend fun getNotificationSetting(): Boolean {
        return systemRepository.getNotificationState().first()
    }
}
```

#### 8.1.2 响应式数据流使用

```kotlin
// 迁移前
class OldSettingsFragment : Fragment() {
    private fun loadSettings() {
        val isNotificationOn = localDataRepository.getNotificationSwitchState()
        updateUI(isNotificationOn)
    }
}

// 迁移后
class NewSettingsFragment : Fragment() {
    private fun observeSettings() {
        viewLifecycleOwner.lifecycleScope.launch {
            systemRepository.getNotificationState().collect { isOn ->
                updateUI(isOn)
            }
        }
    }
}
```

### 8.2 复杂业务逻辑迁移

#### 8.2.1 多Repository协调使用

```kotlin
// 迁移前
class OldUserProfileManager {
    fun initializeUserProfile() {
        val userId = localDataRepository.getCurrentUserId()
        val privacyAccepted = localDataRepository.getPrivacyPolicyState()
        val notificationOn = localDataRepository.getNotificationSwitchState()
        
        // 业务逻辑处理
        if (userId != null && privacyAccepted) {
            setupUserProfile(userId, notificationOn)
        }
    }
}

// 迁移后
class NewUserProfileManager {
    suspend fun initializeUserProfile() {
        val userId = userRepository.getUserId().first()
        val privacyAccepted = healthAuthorityRepository.isPrivacyPolicyAccepted().first()
        val notificationOn = systemRepository.getNotificationState().first()
        
        // 业务逻辑处理
        if (userId.isNotEmpty() && privacyAccepted) {
            setupUserProfile(userId, notificationOn)
        }
    }
    
    // 响应式版本
    fun observeUserProfileChanges() {
        combine(
            userRepository.getUserId(),
            healthAuthorityRepository.isPrivacyPolicyAccepted(),
            systemRepository.getNotificationState()
        ) { userId, privacyAccepted, notificationOn ->
            UserProfileState(userId, privacyAccepted, notificationOn)
        }.onEach { state ->
            if (state.userId.isNotEmpty() && state.privacyAccepted) {
                setupUserProfile(state.userId, state.notificationOn)
            }
        }.launchIn(coroutineScope)
    }
}
```

### 8.3 错误处理迁移

```kotlin
// 迁移前
class OldDataManager {
    fun saveUserSettings(settings: UserSettings) {
        try {
            localDataRepository.toggleNotificationSwitch(settings.notificationOn)
            localDataRepository.storePrivacyPolicy(settings.privacyAccepted)
        } catch (e: Exception) {
            Log.e("DataManager", "Failed to save settings", e)
        }
    }
}

// 迁移后
class NewDataManager {
    suspend fun saveUserSettings(settings: UserSettings) {
        try {
            // 并行保存以提高性能
            coroutineScope {
                launch { systemRepository.storeNotificationOpen(settings.notificationOn) }
                launch { healthAuthorityRepository.storePrivacyPolicyAccepted(settings.privacyAccepted) }
            }
        } catch (e: Exception) {
            Log.e("DataManager", "Failed to save settings", e)
            // 可以添加更细粒度的错误处理
            when (e) {
                is IOException -> handleNetworkError(e)
                is SecurityException -> handlePermissionError(e)
                else -> handleGenericError(e)
            }
        }
    }
}
```

## 9. 最佳实践建议

### 9.1 架构设计原则

#### 9.1.1 单一职责原则

* 每个Repository只负责特定领域的数据管理

* 避免在一个Repository中混合不同类型的数据操作

* 保持接口简洁，方法命名清晰

#### 9.1.2 依赖倒置原则

* 高层模块不应依赖低层模块，都应依赖抽象

* ViewModel依赖Repository接口，不依赖具体实现

* 使用依赖注入管理对象生命周期

#### 9.1.3 开闭原则

* 对扩展开放，对修改关闭

* 通过接口扩展功能，而不是修改现有实现

* 使用策略模式处理不同的数据存储需求

### 9.2 代码质量保证

#### 9.2.1 命名规范

```kotlin
// 好的命名示例
interface SystemRepository {
    suspend fun storeNotificationOpen(isOpen: Boolean)  // 动词+名词，清晰表达操作
    fun getNotificationState(): Flow<Boolean>           // get+名词+State，表明返回状态
    suspend fun clearSystemSettings()                   // 动词+范围，明确操作范围
}

// 避免的命名
interface BadRepository {
    suspend fun doSomething(flag: Boolean)              // 不明确的方法名
    fun getData(): Flow<Boolean>                        // 不明确的数据类型
    suspend fun process()                               // 不明确的操作
}
```

#### 9.2.2 错误处理策略

```kotlin
class DefaultSystemRepository {
    override suspend fun storeNotificationOpen(isOpen: Boolean) {
        try {
            systemDataStore.edit { preferences ->
                preferences[DataStorePreKeysConfig.IS_NOTIFICATION_OPEN] = isOpen
            }
        } catch (e: IOException) {
            // 记录错误但不抛出，避免崩溃
            Log.e("SystemRepository", "Failed to store notification state", e)
            // 可以考虑使用Result类型返回错误信息
        } catch (e: Exception) {
            // 处理其他未预期的错误
            Log.e("SystemRepository", "Unexpected error storing notification state", e)
            throw e  // 重新抛出未知错误
        }
    }
}
```

#### 9.2.3 测试友好设计

```kotlin
// 使用接口便于测试
class SettingsViewModel(
    private val systemRepository: SystemRepository,  // 接口类型
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO  // 可注入的调度器
) {
    fun toggleNotification(isOn: Boolean) {
        viewModelScope.launch(dispatcher) {
            systemRepository.storeNotificationOpen(isOn)
        }
    }
}

// 测试时可以轻松mock
class SettingsViewModelTest {
    @Mock
    private lateinit var mockSystemRepository: SystemRepository
    
    private val testDispatcher = UnconfinedTestDispatcher()
    
    @Test
    fun testToggleNotification() {
        val viewModel = SettingsViewModel(mockSystemRepository, testDispatcher)
        viewModel.toggleNotification(true)
        verify(mockSystemRepository).storeNotificationOpen(true)
    }
}
```

### 9.3 性能优化建议

#### 9.3.1 数据流优化

```kotlin
// 使用StateFlow缓存数据，避免重复读取
class DefaultSystemRepository {
    private val _notificationState = MutableStateFlow<Boolean?>(null)
    
    override fun getNotificationState(): Flow<Boolean> {
        return _notificationState.filterNotNull()
            .onStart {
                if (_notificationState.value == null) {
                    loadNotificationState()
                }
            }
    }
    
    private suspend fun loadNotificationState() {
        val state = systemDataStore.data.map { preferences ->
            preferences[DataStorePreKeysConfig.IS_NOTIFICATION_OPEN] ?: false
        }.first()
        _notificationState.value = state
    }
}
```

#### 9.3.2 批量操作优化

```kotlin
// 提供批量操作方法
interface SystemRepository {
    suspend fun updateSystemSettings(settings: SystemSettings)
}

class DefaultSystemRepository {
    override suspend fun updateSystemSettings(settings: SystemSettings) {
        systemDataStore.edit { preferences ->
            // 一次性更新多个设置，减少I/O操作
            preferences[DataStorePreKeysConfig.IS_NOTIFICATION_OPEN] = settings.notificationOpen
            preferences[DataStorePreKeysConfig.THEME_MODE] = settings.themeMode
            preferences[DataStorePreKeysConfig.LANGUAGE] = settings.language
        }
    }
}
```

### 9.4 维护性建议

#### 9.4.1 版本兼容性

```kotlin
// 处理数据格式变更
class DataMigration {
    suspend fun migrateFromLocalDataRepository() {
        // 检查是否需要迁移
        val needsMigration = checkMigrationNeeded()
        if (!needsMigration) return
        
        // 执行数据迁移
        val oldData = readOldFormatData()
        writeNewFormatData(oldData)
        
        // 标记迁移完成
        markMigrationComplete()
    }
}
```

#### 9.4.2 文档维护

```kotlin
/**
 * 系统设置数据仓库
 * 
 * 负责管理系统级别的配置数据，包括：
 * - 通知开关状态
 * - 主题设置
 * - 语言设置
 * - 调试模式等
 * 
 * @since 1.2.0
 * <AUTHOR> Team
 */
interface SystemRepository {
    /**
     * 存储通知开关状态
     * 
     * @param isOpen true表示开启通知，false表示关闭
     * @throws IOException 当存储操作失败时抛出
     */
    suspend fun storeNotificationOpen(isOpen: Boolean)
}
```

## 10. 总结

本文档详细说明了`LocalDataRepository`重构的完整方案，包括：

1. **明确的重构目标**：提升架构清晰度和代码可维护性
2. **详细的迁移方案**：将方法合理分配到对应的Repository
3. **完整的实施计划**：分阶段、有步骤的执行策略
4. **全面的测试策略**：确保重构后功能正常
5. **有效的风险控制**：预防和应对可能的问题
6. **实用的代码示例**：指导具体的代码修改
7. **最佳实践建议**：提升代码质量和可维护性

通过遵循本文档的指导，开发团队可以安全、高效地完成`LocalDataRepository`的重构工作，为HMS项目建立更加清晰、可维护的架构基础。

重构完成后，项目将获得以下收益：

* **更清晰的职责分离**：每个Repository专注于特定领域

* **更好的代码复用**：避免功能重复，统一数据管理

* **更强的可测试性**：接口设计便于单元测试

* **更高的可维护性**：代码结构清晰，易于理解和修改

* **更好的扩展性**：为未来功能扩展奠定基础

