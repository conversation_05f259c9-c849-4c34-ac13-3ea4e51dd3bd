# DataStore替换MMKVUtil方案

## 实施进度概览

### ✅ 已完成阶段

- **阶段1：Repository层完善** ✅ 已完成
  - 所有Repository接口已完善
  - 所有Repository实现类已启用并完善
  - 依赖注入配置已完成

- **阶段2：数据迁移实现** ✅ 已完成
  - 数据迁移管理器已实现
  - 迁移触发机制已完成
  - 应用启动时自动迁移已配置

### 🔄 待实施阶段

- **阶段3：MVVM-Repository架构实施** (第3-6周)
- **阶段4：测试和验证** (第7周)
- **阶段5：清理和优化** (第8周)

---

## 概述

本方案采用**MVVM-Repository架构模式**，完全移除`*Utils`辅助类，建立清晰的数据访问层架构。通过`core/data`模块的Repository接口和实现类直接管理DataStore操作，业务层通过ViewModel使用Repository，实现职责分离和架构统一。

## 架构设计原则

### 1. MVVM-Repository架构

* **Activity层**：负责UI交互和用户输入处理
* **ViewModel层**：负责UI逻辑和数据准备，调用Repository获取数据
* **Repository层**：负责数据操作和业务逻辑，直接操作DataStore
* **DataStore层**：负责数据持久化存储

### 2. 模块职责划分

* **`core/data`模块**：负责数据仓库抽象和实现，直接操作DataStore，提供响应式数据流
* **业务模块**：通过ViewModel使用Repository接口，采用响应式编程模式

### 3. 数据访问层次

```
Activity层 (UI交互)
    ↓
ViewModel层 (UI逻辑和数据准备)
    ↓  
Repository层 (core/data - 数据操作和业务逻辑)
    ↓
DataStore层 (数据持久化)
```

## 实施阶段

### 阶段1：Repository层完善 (第1-2周)

#### 1.1 完善现有Repository接口

**目标**：统一和完善所有Repository接口定义

**任务清单**：

* [x] 审查并完善`UserRepository`接口，确保覆盖所有用户相关操作

* [x] 完善`HealthAuthorityRepository`接口，覆盖所有健康权限操作

* [x] 完善`SystemRepository`接口，覆盖系统设置相关操作

* [x] 完善`NavigationRepository`接口，覆盖导航数据操作

* [x] 完善`SceneEngineRepository`接口，覆盖场景引擎操作

* [x] 完善`HealthDataRepository`接口，覆盖健康数据操作

**示例接口设计**：

```kotlin
interface UserRepository {
    // 基础用户信息
    suspend fun storeUserId(userId: String)
    fun getUserId(): Flow<String>
    suspend fun clearUserId()
    
    suspend fun storeUserToken(token: String)
    fun getUserToken(): Flow<String>
    suspend fun clearUserToken()
    
    // 用户详细信息
    suspend fun storeUserInfo(userInfo: UserPreferences)
    fun getUserInfo(): Flow<UserPreferences>
    
    // 访客模式判断
    fun isVisitorMode(): Flow<Boolean>
    
    // 清除所有用户数据
    suspend fun clearAllUserData()
}
```

#### 1.2 完善Repository实现类

**目标**：确保所有Repository实现类功能完整且经过测试

**任务清单**：

* [x] 完善`DefaultUserRepository`实现，确保所有方法正确实现

* [x] 完善`DefaultHealthAuthorityRepository`实现

* [x] 完善`DefaultSystemRepository`实现

* [x] 完善`DefaultNavigationRepository`实现

* [x] 完善`DefaultSceneEngineRepository`实现

* [x] 完善`DefaultHealthDataRepository`实现

* [x] 为所有Repository添加错误处理和异常管理

* [x] 添加数据验证逻辑

#### 1.3 依赖注入配置

**目标**：确保所有Repository正确注册到DI容器

**任务清单**：

* [x] 更新`LocalDataModule.kt`，启用所有Repository绑定

* [x] 确保DataStore注解限定符正确配置

* [x] 验证依赖注入链路完整性

### 阶段2：数据迁移实现 (第2周)

#### 2.1 数据迁移策略

**目标**：实现从MMKVUtil到Repository的平滑数据迁移

**任务清单**：

* [x] 创建`DataMigrationManager.kt`，负责数据迁移逻辑

* [x] 实现用户数据迁移（ID、Token、个人信息）

* [x] 实现健康权限数据迁移

* [x] 实现系统设置数据迁移

* [x] 实现导航数据迁移

* [x] 实现场景引擎数据迁移

* [x] 实现健康数据迁移

* [x] 添加迁移状态跟踪和错误处理

**迁移管理器设计**：

```kotlin
@Singleton
class DataMigrationManager @Inject constructor(
    private val userRepository: UserRepository,
    private val healthAuthorityRepository: HealthAuthorityRepository,
    private val systemRepository: SystemRepository,
    // ... 其他Repository
) {
    suspend fun migrateAllData(): MigrationResult {
        return try {
            migrateUserData()
            migrateHealthAuthorityData()
            migrateSystemData()
            // ... 其他数据迁移
            
            MigrationResult.Success
        } catch (e: Exception) {
            MigrationResult.Error(e)
        }
    }
    
    private suspend fun migrateUserData() {
        // 从MMKVUtil读取数据并写入Repository
        val userId = MMKVUtil.getUserId()
        if (userId.isNotEmpty()) {
            userRepository.storeUserId(userId)
        }
        // ... 其他用户数据迁移
    }
}
```

#### 2.2 迁移触发机制

**任务清单**：

* [x] 在应用启动时检查是否需要迁移

* [x] 实现迁移进度显示

* [x] 添加迁移失败回滚机制

* [x] 实现迁移完成标记

### 阶段3：MVVM-Repository架构实施 (第3-6周)

#### 3.1 Feature/Setting模块MVVM架构改造

**目标**：将feature/setting模块从MMKVUtil直接调用改造为MVVM-Repository架构

**核心原则**：
- 完全移除`*Utils`辅助类的使用
- Activity只负责UI交互
- ViewModel负责UI逻辑和数据准备
- Repository负责数据操作和业务逻辑

#### 3.2 MMKVUtil使用情况分析

**DefaultSettingsRepository中的使用**：
- `toggleNotification(isOn: Boolean)` - 通知开关状态存储
- `getNotificationState(): Boolean` - 通知状态获取
- `storePrivacyPolicy(isAgree: Boolean)` - 隐私政策同意状态
- `getCurrentUserId(): String?` - 当前用户ID获取

**HMSPersonalActivity中的使用**：
- `MMKVUtil.getUserId()` - 用户ID获取（埋点、默认昵称生成）
- `MMKVUtil.getUserinfo()` - 用户信息获取
- `MMKVUtil.storeUserinfoNickname()` - 用户昵称存储
- `MMKVUtil.storeUserinfoGender()` - 用户性别存储
- `MMKVUtil.clearAllExceptSome()` - 用户数据清理
- `MMKVUtil.getData(KEY_NIKE_NAME)` - 特定数据获取

#### 3.3 MVVM-Repository架构实施计划

##### 第1步：DefaultSettingsRepository替换 (第3周第1-2天)

**目标**：将Repository中的MMKVUtil调用替换为core/data模块的Repository调用

**实施步骤**：

1. **添加依赖注入**

```kotlin
@Singleton
class DefaultSettingsRepository @Inject constructor(
    private val apiService: ApiServiceKot,
    // 新增：注入core/data模块的Repository
    private val systemRepository: SystemRepository,
    private val userRepository: UserRepository
) : SettingsRepository {
    // ...
}
```

2. **替换通知相关方法为异步Repository调用**

```kotlin
// 原代码
override fun toggleNotification(isOn: Boolean) {
    MMKVUtil.storeNotificationOpen(isOn)
}

override fun getNotificationState(): Boolean {
    return MMKVUtil.getNotificationOpen()
}

// 新代码 - 异步Repository模式
override suspend fun toggleNotification(isOn: Boolean) {
    systemRepository.storeNotificationOpen(isOn)
}

override fun getNotificationState(): Flow<Boolean> {
    return systemRepository.getNotificationState()
}
```

3. **替换隐私政策方法**

```kotlin
// 原代码
override fun storePrivacyPolicy(isAgree: Boolean) {
    MMKVUtil.storePrivacyPolicy(isAgree)
}

// 新代码
override suspend fun storePrivacyPolicy(isAgree: Boolean) {
    systemRepository.storePrivacyPolicy(isAgree)
}
```

4. **替换用户ID获取方法**

```kotlin
// 原代码
override fun getCurrentUserId(): String? {
    return MMKVUtil.getUserId()
}

// 新代码
override fun getCurrentUserId(): Flow<String> {
    return userRepository.getUserId()
}
```

##### 第2步：ViewModel层实现 (第3周第3-4天)

**目标**：创建SettingsViewModel，通过Repository提供响应式数据流

**实施步骤**：

1. **创建SettingsViewModel**

```kotlin
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val settingsRepository: SettingsRepository,
    private val userRepository: UserRepository,
    private val systemRepository: SystemRepository
) : ViewModel() {
    
    // 响应式通知状态
    val notificationState: StateFlow<Boolean> = systemRepository.getNotificationState()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false
        )
    
    // 响应式用户信息
    val userInfo: StateFlow<UserPreferences> = userRepository.getUserInfo()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = UserPreferences()
        )
    
    // 响应式用户ID
    val userId: StateFlow<String> = userRepository.getUserId()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = ""
        )
    
    fun toggleNotification(isOn: Boolean) {
        viewModelScope.launch {
            systemRepository.storeNotificationOpen(isOn)
        }
    }
    
    fun updateUserInfo(userInfo: UserPreferences) {
        viewModelScope.launch {
            userRepository.storeUserInfo(userInfo)
        }
    }
    
    fun clearUserData(keepPrivacyPolicy: Boolean = true) {
        viewModelScope.launch {
            userRepository.clearAllUserData()
            if (!keepPrivacyPolicy) {
                systemRepository.clearAllSystemData()
            }
        }
    }
}
```

##### 第3步：Activity层UI响应式更新 (第3周第5天)

**目标**：在HMSPersonalActivity中观察ViewModel的响应式数据，实现UI自动更新

**实施步骤**：

1. **Activity依赖注入配置**

```kotlin
@AndroidEntryPoint
class HMSPersonalActivity : BaseVBVMActivity<FeatureSettingActivityPersonalBinding>() {
    
    private val viewModel: SettingsViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupObservers()
        setupClickListeners()
    }
}
```

2. **设置响应式数据观察**

```kotlin
private fun setupObservers() {
    // 观察用户ID变化（用于埋点和默认昵称生成）
    lifecycleScope.launch {
        viewModel.userId.collect { userId ->
            if (userId.isNotEmpty()) {
                // 埋点数据追踪
                DataTrackUtil.dtEnterPage(
                    "Health_Set_PV",
                    DataTrackUtil.userIDMap(userId)
                )
                
                // 初始化默认用户名
                initDefaultUserName(userId)
            }
        }
    }
    
    // 观察用户信息变化
    lifecycleScope.launch {
        viewModel.userInfo.collect { userInfo ->
            updateUserInfoUI(userInfo)
        }
    }
    
    // 观察通知状态变化
    lifecycleScope.launch {
        viewModel.notificationState.collect { isOn ->
            updateNotificationUI(isOn)
        }
    }
}
```

3. **UI更新方法**

```kotlin
private fun updateUserInfoUI(userInfo: UserPreferences) {
    val userId = viewModel.userId.value
    val defaultName = if (userId.length >= 6) {
        "CH${userId.takeLast(6)}"
    } else {
        "CH$userId"
    }
    
    // 更新昵称显示
    val displayName = userInfo.nickName.ifEmpty { defaultName }
    binding.settingUsername.text = displayName
    this.nickNameShow = displayName
    
    // 更新其他用户信息
    personalMap["nickName"] = displayName
    personalMap["gender"] = userInfo.gender.toString()
    this.genderShow = userInfo.gender
    
    // 如果昵称为空，设置默认昵称
    if (userInfo.nickName.isEmpty()) {
        viewModel.updateUserInfo(userInfo.copy(nickName = defaultName))
    }
}

private fun updateNotificationUI(isOn: Boolean) {
    // 更新通知开关UI状态
    // 具体实现根据UI组件而定
}
```

4. **用户操作处理**

```kotlin
private fun setupClickListeners() {
    // 退出登录
    binding.logoutButton.setOnClickListener {
        doLogout()
    }
    
    // 通知开关
    binding.notificationSwitch.setOnCheckedChangeListener { _, isChecked ->
        viewModel.toggleNotification(isChecked)
    }
}

private fun doLogout(keepPrivacyPolicy: Boolean = true) {
    viewModel.clearUserData(keepPrivacyPolicy)
    // 其他退出登录逻辑...
}
```

#### 3.4 实施时间表

**第3周**：
- 第1-2天：DefaultSettingsRepository替换
- 第3-4天：ViewModel层实现
- 第5天：Activity层UI响应式更新

**第4周**：
- 第1-2天：集成测试和调试
- 第3-4天：性能测试和优化
- 第5天：代码审查和文档更新

**第5-6周**：
- 预留时间处理发现的问题和进一步优化
- 准备生产环境部署

#### 3.5 架构优势

**MVVM-Repository架构的核心优势**：

1. **职责分离清晰**：
   - Activity专注UI交互
   - ViewModel处理UI逻辑和数据准备
   - Repository负责数据操作和业务逻辑

2. **响应式编程**：
   - 使用Flow提供响应式数据流
   - UI自动响应数据变化
   - 减少手动UI更新代码

3. **测试友好**：
   - 各层职责独立，便于单元测试
   - Repository可以轻松Mock
   - ViewModel逻辑可独立测试

4. **可维护性强**：
   - 消除Utils类的混乱
   - 统一的数据访问模式
   - 清晰的依赖关系

5. **扩展性好**：
   - 新增数据类型只需扩展Repository
   - 业务逻辑集中在Repository层
   - 支持多数据源整合

#### 3.6 依赖注入配置

**在feature/setting模块的build.gradle.kts中添加依赖**：

```kotlin
dependencies {
    // 现有依赖...
    
    // 新增：core模块依赖
    implementation(project(":core:data"))
    implementation(project(":core:model"))
    
    // Hilt依赖
    implementation(libs.hilt.android)
    kapt(libs.hilt.compiler)
    
    // ViewModel依赖
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
}
```

**在SettingsModule.kt中添加Repository绑定**：

```kotlin
@Module
@InstallIn(SingletonComponent::class)
abstract class SettingsModule {
    
    @Binds
    abstract fun bindSettingsRepository(
        defaultSettingsRepository: DefaultSettingsRepository
    ): SettingsRepository
}
```

#### 3.7 测试策略

##### Repository层单元测试

```kotlin
@Test
fun `toggleNotification should store notification state`() = runTest {
    // Given
    val isOn = true
    
    // When
    settingsRepository.toggleNotification(isOn)
    
    // Then
    verify(systemRepository).storeNotificationOpen(isOn)
}

@Test
fun `getCurrentUserId should return user id flow`() = runTest {
    // Given
    val expectedUserId = "test_user_123"
    every { userRepository.getUserId() } returns flowOf(expectedUserId)
    
    // When
    val result = settingsRepository.getCurrentUserId()
    
    // Then
    result.test {
        assertEquals(expectedUserId, awaitItem())
        awaitComplete()
    }
}
```

##### ViewModel层单元测试

```kotlin
@Test
fun `toggleNotification should update notification state`() = runTest {
    // Given
    val isOn = true
    
    // When
    viewModel.toggleNotification(isOn)
    
    // Then
    verify(systemRepository).storeNotificationOpen(isOn)
}

@Test
fun `userInfo should emit user preferences`() = runTest {
    // Given
    val testUserInfo = UserPreferences(nickName = "TestUser", gender = 1)
    every { userRepository.getUserInfo() } returns flowOf(testUserInfo)
    
    // When
    val result = viewModel.userInfo.value
    
    // Then
    assertEquals(testUserInfo, result)
}
```

##### UI层集成测试

```kotlin
@Test
fun `user info flow should work end to end`() = runTest {
    // Given
    val testUserInfo = UserPreferences(nickName = "TestUser", gender = 1)
    
    // When
    userRepository.storeUserInfo(testUserInfo)
    
    // Then
    userRepository.getUserInfo().test {
        assertEquals(testUserInfo, awaitItem())
    }
}
```

#### 3.8 风险控制

1. **渐进式替换**：按Repository -> ViewModel -> Activity的顺序逐步替换
2. **保留备份**：在替换前创建代码分支备份
3. **功能验证**：每个替换步骤后进行功能测试
4. **响应式数据流验证**：确保UI能正确响应数据变化
5. **性能监控**：对比替换前后的性能指标
6. **回滚准备**：如发现问题可快速回滚到MMKVUtil版本

### 阶段4：测试和验证 (第7周)

#### 4.1 单元测试

**任务清单**：

* [ ] 为所有Repository实现类编写单元测试

* [ ] 为SettingsViewModel编写单元测试

* [ ] 为数据迁移管理器编写单元测试

* [ ] 确保测试覆盖率达到90%以上

#### 4.2 集成测试

**任务清单**：

* [ ] 测试Repository与DataStore的集成

* [ ] 测试ViewModel与Repository的集成

* [ ] 测试数据迁移的完整流程

* [ ] 测试依赖注入的正确性

#### 4.3 UI响应式测试

**任务清单**：

* [ ] 测试UI对数据变化的响应

* [ ] 测试用户操作的数据流

* [ ] 测试Activity生命周期中的数据状态

* [ ] 测试应用升级后的数据迁移

### 阶段5：清理和优化 (第8周)

#### 5.1 代码清理

**任务清单**：

* [ ] 移除所有MMKVUtil调用

* [ ] 删除MMKVUtil.kt文件

* [ ] 移除MMKV相关依赖

* [ ] 清理未使用的导入和代码

#### 5.2 性能优化

**任务清单**：

* [ ] 优化DataStore读写性能

* [ ] 减少不必要的数据转换

* [ ] 优化Flow操作链

* [ ] 添加适当的缓存机制

#### 5.3 文档更新

**任务清单**：

* [ ] 更新API文档

* [ ] 更新架构文档

* [ ] 创建MVVM-Repository迁移指南

* [ ] 更新开发者指南

## 技术规范

### 1. 命名规范

* **Repository接口**：`{Domain}Repository`（如`UserRepository`）

* **Repository实现**：`Default{Domain}Repository`（如`DefaultUserRepository`）

* **ViewModel类**：`{Feature}ViewModel`（如`SettingsViewModel`）

* **DataStore限定符**：`@{Domain}DataStore`（如`@UserDataStore`）

### 2. 错误处理

```kotlin
// Repository层错误处理
suspend fun storeUserId(userId: String): Result<Unit> {
    return try {
        userDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.USER_ID] = userId
        }
        Result.success(Unit)
    } catch (e: Exception) {
        Log.e("UserRepository", "Failed to store user ID", e)
        Result.failure(e)
    }
}

// ViewModel层错误处理
fun updateUserInfo(userInfo: UserPreferences) {
    viewModelScope.launch {
        try {
            userRepository.storeUserInfo(userInfo)
        } catch (e: Exception) {
            Log.e("SettingsViewModel", "Failed to update user info", e)
            // 处理错误状态，如显示错误消息
        }
    }
}
```

### 3. 测试策略

```kotlin
// Repository测试示例
@Test
fun `storeUserId should save user id successfully`() = runTest {
    // Given
    val userId = "test_user_123"
    
    // When
    repository.storeUserId(userId)
    
    // Then
    repository.getUserId().test {
        assertThat(awaitItem()).isEqualTo(userId)
        awaitComplete()
    }
}
```

## 风险评估和缓解策略

### 1. 数据丢失风险

**风险**：迁移过程中可能导致数据丢失

**缓解策略**：

* 实施前完整备份MMKV数据

* 分阶段迁移，保持MMKV和DataStore并行运行

* 添加数据验证和回滚机制

### 2. 性能影响风险

**风险**：DataStore初次读取可能比MMKV慢

**缓解策略**：

* 在应用启动时预加载关键数据

* 使用适当的缓存策略

* 监控性能指标

### 3. 兼容性风险

**风险**：MVVM-Repository架构可能与现有代码不兼容

**缓解策略**：

* 渐进式替换策略，分层次实施

* 保持现有接口的向后兼容性

* 充分的测试覆盖和验证

## 成功指标

### 1. 技术指标

* [ ] 100%移除MMKVUtil调用

* [ ] 90%以上的测试覆盖率

* [ ] 零数据丢失

* [ ] 性能不低于原有实现

### 2. 架构指标

* [ ] 清晰的MVVM-Repository分层架构

* [ ] 统一的响应式数据访问模式

* [ ] 完整的依赖注入配置

* [ ] 良好的代码可维护性和可测试性

### 3. 业务指标

* [ ] 用户体验无影响

* [ ] 功能完整性保持

* [ ] 系统稳定性提升

## 总结

本实施计划采用MVVM-Repository架构模式，完全移除`*Utils`辅助类，通过清晰的分层架构和响应式编程，确保从MMKVUtil到DataStore的平滑过渡。该方案不仅解决了当前的技术债务，还建立了现代化的数据访问架构，为未来的功能扩展和维护奠定了坚实的基础。

**核心价值**：
- 职责分离清晰，提升代码可维护性
- 响应式数据流，提升用户体验
- 统一架构模式，降低开发复杂度
- 现代化技术栈，支持长期演进

## 附录

### A. 相关文档链接

* [HMS项目Core模块划分指导文档](./HMS项目Core模块划分指导文档.md)

* [Feature模块划分指导文档](./Feature模块划分指导文档.md)

* [HMS项目非模块化文件说明文档](./HMS项目非模块化文件说明文档.md)

### B. 技术参考

* [Android DataStore官方文档](https://developer.android.com/topic/libraries/architecture/datastore)

* [Repository模式最佳实践](https://developer.android.com/jetpack/guide/data-layer)

* [Kotlin协程指南](https://kotlinlang.org/docs/coroutines-guide.html)

