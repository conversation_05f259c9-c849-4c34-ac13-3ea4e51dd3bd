import org.jetbrains.kotlin.gradle.dsl.JvmTarget
plugins {
    `kotlin-dsl`
}

group = "com.healthlink.hms.buildlogic"

// Configure the build-logic plugins to target JDK 17
// This matches the JDK used to build the project, and is not related to what is running on device.
java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

kotlin {
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_17)
    }
}

tasks {
    validatePlugins {
        enableStricterValidation = true
        failOnWarning = true
    }
}

dependencies {
    compileOnly(libs.android.gradlePlugin)
    compileOnly(libs.android.tools.common)
    compileOnly(libs.compose.gradlePlugin)
    compileOnly(libs.kotlin.gradlePlugin)
    compileOnly(libs.ksp.gradlePlugin)
    compileOnly(libs.room.gradlePlugin)
}

gradlePlugin {
    plugins {
        register("androidApplication") {
            id = libs.plugins.hms.android.application.get().pluginId
            implementationClass = "com.healthlink.hms.convention.AndroidApplicationConventionPlugin"
        }
        register("androidLibrary") {
            id = libs.plugins.hms.android.library.get().pluginId
            implementationClass = "com.healthlink.hms.convention.AndroidLibraryConventionPlugin"
        }
//        register("androidLibraryCompose") {
//            id = "hms.android.library.compose"
//            implementationClass = "AndroidLibraryComposeConventionPlugin"
//        }
//        register("androidFeature") {
//            id = "hms.android.feature"
//            implementationClass = "AndroidFeatureConventionPlugin"
//        }

        register("hilt") {
            id = libs.plugins.hms.hilt.get().pluginId
            implementationClass = "com.healthlink.hms.convention.HiltConventionPlugin"
        }
    }
}

