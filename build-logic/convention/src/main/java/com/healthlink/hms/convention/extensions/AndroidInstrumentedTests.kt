///*
// * Copyright 2023 The Android Open Source Project
// *
// *   Licensed under the Apache License, Version 2.0 (the "License");
// *   you may not use this file except in compliance with the License.
// *   You may obtain a copy of the License at
// *
// *       https://www.apache.org/licenses/LICENSE-2.0
// *
// *   Unless required by applicable law or agreed to in writing, software
// *   distributed under the License is distributed on an "AS IS" BASIS,
// *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// *   See the License for the specific language governing permissions and
// *   limitations under the License.
// */
//
//package com.healthlink.hms.convention.extensions
//
//import com.android.build.api.variant.AndroidComponentsExtension
//import com.android.build.api.variant.LibraryAndroidComponentsExtension
//import org.gradle.api.Project
//
///**
// * Disable unnecessary Android instrumented tests for the [project] if there is no `androidTest` folder.
// * Otherwise, these projects would be compiled, packaged, installed and ran only to end-up with the following message:
// *
// * > Starting 0 tests on AVD
// *
// * Note: this could be improved by checking other potential sourceSets based on buildTypes and flavors.
// */
//internal fun LibraryAndroidComponentsExtension.disableUnnecessaryAndroidTests(
//    project: Project,
//) = AndroidComponentsExtension.beforeVariants {
//    it.androidTest.enable = it.androidTest.enable
//            && project.projectDir.resolve("src/androidTest").exists()
//}
