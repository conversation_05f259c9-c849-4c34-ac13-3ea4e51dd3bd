package com.healthlink.hms.convention

import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.variant.ApplicationAndroidComponentsExtension
import com.android.build.gradle.BaseExtension
import com.healthlink.hms.convention.extensions.configureKotlinAndroid
import com.healthlink.hms.convention.extensions.configurePrintApksTask
import com.healthlink.hms.convention.extensions.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.apply
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.getByType
import org.gradle.kotlin.dsl.support.kotlinCompilerOptions
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

class AndroidApplicationConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            apply(plugin = "com.android.application")
            apply(plugin = "org.jetbrains.kotlin.android")
//            apply(plugin = "nowinandroid.android.lint")
//            apply(plugin = "com.dropbox.dependency-guard")

            extensions.configure<ApplicationExtension> {
                configureKotlinAndroid(this)
//                namespace = "com.healthlink.hms"
                compileSdk = 35
                ndkVersion = "26.1.10909125"
                defaultConfig {
                    applicationId = "com.healthlink.hms"
                    minSdk = 29
                    targetSdk = 33
                    versionCode = libs.findVersion("versionCode").get().toString().toInt()
                    versionName = libs.findVersion("versionName").get().toString()
                    multiDexEnabled = true
//                    manifestPlaceholders["applicationName"] = "${versionName}"
                    ndk {
                        //abiFilters("armeabi-v7a", "arm64-v8a", "x86") // 指定支持的ABI
                    }
                }

                buildFeatures {
                    viewBinding = true
                    compose = true
                    buildConfig = true
                    dataBinding = true
                }

                @Suppress("UnstableApiUsage")
                testOptions.animationsDisabled = true
//                configureGradleManagedDevices(this)
            }
            extensions.configure<ApplicationAndroidComponentsExtension> {
                configurePrintApksTask(this)
//                configureBadgingTasks(extensions.getByType<BaseExtension>(), this)
            }
        }
    }
}
