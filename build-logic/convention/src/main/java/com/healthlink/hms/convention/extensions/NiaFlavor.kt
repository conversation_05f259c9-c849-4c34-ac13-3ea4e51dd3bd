//package com.healthlink.hms.convention.extensions
//
//import com.android.build.api.dsl.ApplicationExtension
//import com.android.build.api.dsl.ApplicationProductFlavor
//import com.android.build.api.dsl.ApplicationVariantDimension
//import com.android.build.api.dsl.CommonExtension
//import com.android.build.api.dsl.ProductFlavor
//import org.gradle.api.NamedDomainObjectContainer
//
//@Suppress("EnumEntryName")
//enum class FlavorDimension {
//    contentType
//}
//
//// The content for the app can either come from local static data which is useful for demo
//// purposes, or from a production backend server which supplies up-to-date, real content.
//// These two product flavors reflect this behaviour.
//@Suppress("EnumEntryName")
//enum class NiaFlavor(val dimension: FlavorDimension, val applicationIdSuffix: String? = null) {
//    demo(FlavorDimension.contentType, applicationIdSuffix = ".demo"),
//    prod(FlavorDimension.contentType),
//}
//
//fun configureFlavors(
//    commonExtension: CommonExtension<*, *, *, *, *, *>,
//    flavorConfigurationBlock: ProductFlavor.(flavor: NiaFlavor) -> Unit = {},
//) {
//    commonExtension.apply {
//        FlavorDimension.values().forEach { flavorDimension ->
//            CommonExtension.flavorDimensions plusAssign flavorDimension.name
//        }
//
//        CommonExtension.productFlavors {
//            NiaFlavor.values().forEach { niaFlavor ->
//                NamedDomainObjectContainer.register(niaFlavor.name) {
//                    ProductFlavor.dimension = niaFlavor.dimension.name
//                    flavorConfigurationBlock(this, niaFlavor)
//                    if (this@apply is ApplicationExtension && this is ApplicationProductFlavor) {
//                        if (niaFlavor.applicationIdSuffix != null) {
//                            ApplicationVariantDimension.applicationIdSuffix =
//                                niaFlavor.applicationIdSuffix
//                        }
//                    }
//                }
//            }
//        }
//    }
//}
