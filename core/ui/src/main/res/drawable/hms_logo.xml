<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="167.74dp"
    android:height="48dp"
    android:viewportWidth="167.74"
    android:viewportHeight="48">
  <group>
    <clip-path
        android:pathData="M1.85,44.44l37.42,0l0,2.56l-37.42,0z"/>
    <path
        android:pathData="M39.27,45.73C39.27,46.43 30.89,47 20.56,47C10.22,47 1.85,46.43 1.85,45.73C1.85,45.02 10.22,44.44 20.56,44.44C30.89,44.44 39.27,45.02 39.27,45.73"
        android:strokeWidth="1"
        android:fillColor="#F4FAFD"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <path
      android:pathData="M39.27,45.73C39.27,46.43 30.89,47 20.56,47C10.22,47 1.85,46.43 1.85,45.73C1.85,45.02 10.22,44.44 20.56,44.44C30.89,44.44 39.27,45.02 39.27,45.73"
      android:strokeWidth="1"
      android:fillColor="#F4FAFD"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M39.27,45.73C39.27,46.43 30.89,47 20.56,47C10.22,47 1.85,46.43 1.85,45.73C1.85,45.02 10.22,44.44 20.56,44.44C30.89,44.44 39.27,45.02 39.27,45.73"
      android:strokeWidth="1"
      android:fillColor="#F4F9FC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M39.27,45.73C39.27,46.43 31.08,47 20.99,47C10.88,47 2.7,46.43 2.7,45.73C2.7,45.02 10.88,44.44 20.99,44.44C31.08,44.44 39.27,45.02 39.27,45.73"
      android:strokeWidth="1"
      android:fillColor="#F4F9FC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M39.27,45.73C39.27,46.43 31.08,47 20.99,47C10.88,47 2.7,46.43 2.7,45.73C2.7,45.02 10.88,44.44 20.99,44.44C31.08,44.44 39.27,45.02 39.27,45.73"
      android:strokeWidth="1"
      android:fillColor="#F4F9FB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M38.42,45.73C38.42,46.43 30.42,47 20.56,47C10.69,47 2.7,46.43 2.7,45.73C2.7,45.02 10.69,44.44 20.56,44.44C30.42,44.44 38.42,45.02 38.42,45.73"
      android:strokeWidth="1"
      android:fillColor="#F4F8FB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M38.42,45.73C38.42,46.43 30.42,47 20.56,47C10.69,47 2.7,46.43 2.7,45.73C2.7,45.02 10.69,44.44 20.56,44.44C30.42,44.44 38.42,45.02 38.42,45.73"
      android:strokeWidth="1"
      android:fillColor="#F3F8FA"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M38.42,45.73C38.42,46.43 30.62,47 20.99,47C11.35,47 3.55,46.43 3.55,45.73C3.55,45.02 11.35,44.44 20.99,44.44C30.62,44.44 38.42,45.02 38.42,45.73"
      android:strokeWidth="1"
      android:fillColor="#F3F7FA"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M38.42,45.73C38.42,46.43 30.62,47 20.99,47C11.35,47 3.55,46.43 3.55,45.73C3.55,45.02 11.35,44.44 20.99,44.44C30.62,44.44 38.42,45.02 38.42,45.73"
      android:strokeWidth="1"
      android:fillColor="#F3F7F9"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M37.57,45.73C37.57,46.43 29.96,47 20.56,47C11.16,47 3.55,46.43 3.55,45.73C3.55,45.02 11.16,44.44 20.56,44.44C29.96,44.44 37.57,45.02 37.57,45.73"
      android:strokeWidth="1"
      android:fillColor="#F3F6F8"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M37.57,45.73C37.57,46.43 29.96,47 20.56,47C11.16,47 3.55,46.43 3.55,45.73C3.55,45.02 11.16,44.44 20.56,44.44C29.96,44.44 37.57,45.02 37.57,45.73"
      android:strokeWidth="1"
      android:fillColor="#F2F6F8"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M37.57,45.73C37.57,46.43 30.15,47 20.99,47C11.82,47 4.4,46.43 4.4,45.73C4.4,45.02 11.82,44.44 20.99,44.44C30.15,44.44 37.57,45.02 37.57,45.73"
      android:strokeWidth="1"
      android:fillColor="#F2F5F7"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M37.57,45.73C37.57,46.43 30.15,47 20.99,47C11.82,47 4.4,46.43 4.4,45.73C4.4,45.02 11.82,44.44 20.99,44.44C30.15,44.44 37.57,45.02 37.57,45.73"
      android:strokeWidth="1"
      android:fillColor="#F2F5F7"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M36.72,46.15C36.72,46.62 29.49,47 20.56,47C11.63,47 4.4,46.62 4.4,46.15C4.4,45.68 11.63,45.3 20.56,45.3C29.49,45.3 36.72,45.68 36.72,46.15"
      android:strokeWidth="1"
      android:fillColor="#F1F4F6"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M36.72,46.15C36.72,46.62 29.49,47 20.56,47C11.63,47 4.4,46.62 4.4,46.15C4.4,45.68 11.63,45.3 20.56,45.3C29.49,45.3 36.72,45.68 36.72,46.15"
      android:strokeWidth="1"
      android:fillColor="#F1F4F6"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M36.72,46.15C36.72,46.62 29.68,47 20.99,47C12.29,47 5.25,46.62 5.25,46.15C5.25,45.68 12.29,45.3 20.99,45.3C29.68,45.3 36.72,45.68 36.72,46.15"
      android:strokeWidth="1"
      android:fillColor="#F1F3F5"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M36.72,46.15C36.72,46.62 29.68,47 20.99,47C12.29,47 5.25,46.62 5.25,46.15C5.25,45.68 12.29,45.3 20.99,45.3C29.68,45.3 36.72,45.68 36.72,46.15"
      android:strokeWidth="1"
      android:fillColor="#F0F3F4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M35.87,46.15C35.87,46.62 29.02,47 20.56,47C12.1,47 5.25,46.62 5.25,46.15C5.25,45.68 12.1,45.3 20.56,45.3C29.02,45.3 35.87,45.68 35.87,46.15"
      android:strokeWidth="1"
      android:fillColor="#F0F2F4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M35.87,46.15C35.87,46.62 29.02,47 20.56,47C12.1,47 5.25,46.62 5.25,46.15C5.25,45.68 12.1,45.3 20.56,45.3C29.02,45.3 35.87,45.68 35.87,46.15"
      android:strokeWidth="1"
      android:fillColor="#F0F2F3"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M35.87,46.15C35.87,46.62 29.21,47 20.99,47C12.76,47 6.1,46.62 6.1,46.15C6.1,45.68 12.76,45.3 20.99,45.3C29.21,45.3 35.87,45.68 35.87,46.15"
      android:strokeWidth="1"
      android:fillColor="#EFF1F2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M35.87,46.15C35.87,46.62 29.21,47 20.99,47C12.76,47 6.1,46.62 6.1,46.15C6.1,45.68 12.76,45.3 20.99,45.3C29.21,45.3 35.87,45.68 35.87,46.15"
      android:strokeWidth="1"
      android:fillColor="#EFF1F2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M35.02,46.15C35.02,46.62 28.55,47 20.57,47C12.57,47 6.1,46.62 6.1,46.15C6.1,45.68 12.57,45.3 20.57,45.3C28.55,45.3 35.02,45.68 35.02,46.15"
      android:strokeWidth="1"
      android:fillColor="#EFF0F1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M35.02,46.15C35.02,46.62 28.55,47 20.57,47C12.57,47 6.1,46.62 6.1,46.15C6.1,45.68 12.57,45.3 20.57,45.3C28.55,45.3 35.02,45.68 35.02,46.15"
      android:strokeWidth="1"
      android:fillColor="#EEF0F1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M35.02,46.15C35.02,46.62 28.74,47 20.99,47C13.23,47 6.95,46.62 6.95,46.15C6.95,45.68 13.23,45.3 20.99,45.3C28.74,45.3 35.02,45.68 35.02,46.15"
      android:strokeWidth="1"
      android:fillColor="#EEEFF0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M35.02,46.15C35.02,46.62 28.74,47 20.99,47C13.23,47 6.95,46.62 6.95,46.15C6.95,45.68 13.23,45.3 20.99,45.3C28.74,45.3 35.02,45.68 35.02,46.15"
      android:strokeWidth="1"
      android:fillColor="#EDEFEF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M34.17,46.15C34.17,46.62 28.08,47 20.57,47C13.04,47 6.95,46.62 6.95,46.15C6.95,45.68 13.04,45.3 20.57,45.3C28.08,45.3 34.17,45.68 34.17,46.15"
      android:strokeWidth="1"
      android:fillColor="#EDEEEF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M34.17,46.15C34.17,46.62 28.08,47 20.57,47C13.04,47 6.95,46.62 6.95,46.15C6.95,45.68 13.04,45.3 20.57,45.3C28.08,45.3 34.17,45.68 34.17,46.15"
      android:strokeWidth="1"
      android:fillColor="#EDEDEE"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M34.17,46.15C34.17,46.62 28.08,47 20.57,47C13.04,47 6.95,46.62 6.95,46.15C6.95,45.68 13.04,45.3 20.57,45.3C28.08,45.3 34.17,45.68 34.17,46.15"
      android:strokeWidth="1"
      android:fillColor="#ECEDED"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M34.17,46.15C34.17,46.62 28.27,47 20.99,47C13.7,47 7.8,46.62 7.8,46.15C7.8,45.68 13.7,45.3 20.99,45.3C28.27,45.3 34.17,45.68 34.17,46.15"
      android:strokeWidth="1"
      android:fillColor="#ECECED"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M34.17,46.15C34.17,46.62 28.27,47 20.99,47C13.7,47 7.8,46.62 7.8,46.15C7.8,45.68 13.7,45.3 20.99,45.3C28.27,45.3 34.17,45.68 34.17,46.15"
      android:strokeWidth="1"
      android:fillColor="#EBECEC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M33.32,46.15C33.32,46.62 27.61,47 20.57,47C13.51,47 7.8,46.62 7.8,46.15C7.8,45.68 13.51,45.3 20.57,45.3C27.61,45.3 33.32,45.68 33.32,46.15"
      android:strokeWidth="1"
      android:fillColor="#EBEBEB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M33.32,46.15C33.32,46.62 27.61,47 20.57,47C13.51,47 7.8,46.62 7.8,46.15C7.8,45.68 13.51,45.3 20.57,45.3C27.61,45.3 33.32,45.68 33.32,46.15"
      android:strokeWidth="1"
      android:fillColor="#EAEAEB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M21.02,7.32C22.29,8.6 23.55,9.13 24.33,9.21C23.31,8.98 22.09,8.29 21.02,7.32M26.36,9.06C26.07,9.36 25.67,9.52 25.17,9.54C23.92,9.58 22.26,8.77 20.79,7.41C18.7,5.48 17.78,3.05 18.76,2.01C19.73,0.97 22.22,1.71 24.32,3.65C26.42,5.6 27.33,8.02 26.36,9.06M24.92,3.5C22.47,1.02 19.47,0.27 17.96,1.76C16.44,3.25 17.14,6.24 19.59,8.72C21.14,10.28 22.97,11.21 24.52,11.22C25.35,11.23 26.05,10.97 26.56,10.47C27.07,9.96 27.36,9.27 27.36,8.44L27.36,8.41C27.36,6.88 26.45,5.05 24.92,3.5M24.16,3.82C22.16,1.97 19.82,1.23 18.93,2.17C18.05,3.12 18.95,5.4 20.95,7.25C20.98,7.28 21,7.3 21.02,7.32C19.29,5.56 18.88,3.66 19.36,3.18C19.85,2.7 21.75,3.15 23.5,4.91C24.89,6.31 25.36,7.67 25.36,8.43C25.36,8.62 25.31,8.89 25.16,9.04C25.01,9.2 24.74,9.23 24.53,9.23C24.47,9.23 24.41,9.22 24.33,9.21C24.63,9.28 24.9,9.32 25.16,9.3C25.61,9.29 25.95,9.15 26.18,8.9C27.07,7.95 26.17,5.68 24.16,3.82"
      android:strokeWidth="1"
      android:fillColor="#AAAAAB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M9.08,20.49C8.98,20.34 8.9,20.17 8.83,20.01C8.89,20.17 8.97,20.33 9.08,20.49M39.42,19.47C39.33,19.37 39.24,19.27 39.14,19.17C39.15,19.29 39.18,19.41 39.18,19.54C39.18,20.3 38.77,20.95 38.21,21.21C38.42,21.52 38.53,21.84 38.51,22.16C38.31,24.42 31.64,25.61 23.32,24.87C16.53,24.27 10.72,22.52 9.18,20.61C9.15,20.57 9.11,20.54 9.08,20.49C9.76,21.56 11.21,22.52 13.42,23.37C16.22,24.43 19.96,25.2 23.98,25.53C30.91,26.11 37.01,25.22 39.12,23.32C39.73,22.78 40.07,22.16 40.12,21.46L40.12,21.28C40.12,20.65 39.88,20.05 39.42,19.47M8.97,19.54C8.95,19.84 9.07,20.16 9.32,20.48C10.84,22.34 16.6,24.06 23.33,24.66C27.26,25.01 30.99,24.93 33.84,24.44C36.59,23.96 38.22,23.12 38.3,22.14L38.3,22.07C38.3,21.81 38.2,21.55 38,21.28C37.99,21.28 37.99,21.29 37.98,21.3C37.97,21.35 37.9,21.48 37.74,21.63C36.31,22.9 31.18,23.88 24.15,23.3C15.32,22.57 10.73,19.95 10.8,19.05C10.81,18.92 10.91,18.78 11.1,18.62C10.84,18.52 10.49,18.38 10.12,18.25C9.87,18.38 9.66,18.53 9.46,18.68C9.17,18.95 9,19.24 8.97,19.54M8.78,19.53C8.8,19.16 9,18.83 9.34,18.52C9.5,18.4 9.67,18.27 9.87,18.15C9.53,18.03 9.21,17.91 8.97,17.82C8.79,18.14 8.68,18.5 8.66,18.87C8.63,19.26 8.68,19.64 8.83,20.01C8.79,19.84 8.76,19.69 8.78,19.53M23.96,16.81C30.43,17.39 35.73,18.93 37.69,20.65C38.01,20.32 38.21,19.84 38.21,19.31C38.21,18.88 38.08,18.5 37.87,18.19C35.22,16.56 30.34,15.26 24.79,14.8C20.78,14.47 16.97,14.62 14.04,15.21C12.56,15.51 11.38,15.91 10.5,16.41L12.44,17.23C15.27,16.59 19.45,16.41 23.96,16.81M12.78,17.37L13.44,17.66C15.34,17.15 18.24,16.77 22.03,16.89C18.41,16.69 15.14,16.88 12.78,17.37M23.95,17.03C27.88,17.37 31.56,18.11 34.27,19.1C35.75,19.63 36.85,20.21 37.53,20.79C37.55,20.77 37.58,20.76 37.61,20.73C36.15,19.28 31.36,17.59 24.63,17.03C23.72,16.96 22.86,16.91 22.03,16.89C22.66,16.92 23.3,16.97 23.95,17.03"
      android:strokeWidth="1"
      android:fillColor="#42B153"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M31.54,35.38C31.13,32.49 28.89,28.87 25.37,25.37C24.99,25.34 24.61,25.32 24.22,25.29C23.35,25.22 22.49,25.13 21.66,25.03C25.67,28.5 28.4,32.35 28.8,35.13C28.83,35.31 28.84,35.47 28.85,35.63C28.86,35.68 28.87,35.73 28.87,35.79C28.98,36.89 28.75,37.79 28.18,38.47C27.04,39.82 24.63,40.1 21.39,39.23C18.18,38.37 14.49,36.45 11,33.85C5.94,30.07 2.16,25.44 1.18,21.92C2.13,25.81 6.05,30.77 11.42,34.8C17.16,39.11 23.44,41.53 27.4,40.95C28.9,40.74 30.02,40.12 30.75,39.13C31.33,38.37 31.62,37.43 31.62,36.35C31.62,36.04 31.59,35.71 31.54,35.38M18.3,22.26C18.38,22.27 18.45,22.29 18.53,22.31C18.57,22.32 18.59,22.32 18.62,22.33L18.64,22.33C18.67,22.33 18.68,22.34 18.7,22.34C18.82,22.37 18.94,22.39 19.07,22.42C19.12,22.43 19.17,22.44 19.22,22.44C19.32,22.46 19.41,22.48 19.53,22.5C19.56,22.51 19.61,22.52 19.67,22.53C19.78,22.55 19.89,22.57 19.99,22.58C20.03,22.6 20.08,22.6 20.13,22.61C20.41,22.66 20.71,22.71 21.03,22.75C21.57,22.83 22.14,22.9 22.74,22.97C22.22,22.54 21.68,22.12 21.15,21.72C17.39,18.9 13.41,16.86 9.92,15.97C6.09,14.99 3.22,15.5 1.81,17.39C1.37,17.99 1.09,18.7 1,19.5C1.09,18.92 1.33,18.43 1.68,18.02C4.06,15.27 11.28,17.19 18.3,22.26M2.51,20.28C2.83,20.13 3.04,19.88 3.15,19.62C3.23,19.26 3.35,18.94 3.54,18.68C4.23,17.74 5.85,17.43 8.03,17.78C8.4,17.84 8.79,17.93 9.21,18.03C11.44,18.59 13.95,19.7 16.46,21.23C13.92,19.58 11.34,18.33 8.95,17.61C5.55,16.57 3.01,16.77 1.81,18.14C1.37,18.67 1.14,19.35 1.12,20.17C1.48,20.46 2.03,20.51 2.51,20.28M11.11,33.7C14.58,36.29 18.25,38.19 21.44,39.05C24.59,39.9 26.94,39.64 28.04,38.34C28.39,37.92 28.6,37.41 28.68,36.81C28.62,36.96 28.54,37.1 28.45,37.23C28.08,37.73 27.45,38.05 26.55,38.19C23.26,38.68 17.62,36.45 12.51,32.65C7.49,28.9 3.76,24.22 3.18,20.92C3.18,20.89 3.16,20.86 3.16,20.83C3.15,20.74 3.14,20.67 3.13,20.58C3.01,20.75 2.85,20.9 2.64,21.01C2.12,21.29 1.51,21.23 1.16,20.88C1.61,24.44 5.58,29.58 11.11,33.7"
      android:strokeWidth="1"
      android:fillColor="#00A051"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M60.11,31.8L71.1,31.8L71.1,30.19L60.11,30.19L60.11,31.8ZM64.21,37.37C64.21,38.84 63.82,40.1 63.06,41.14C62.21,42.23 61.01,43.07 59.47,43.67L57.65,42.27C59.22,41.81 60.38,41.18 61.1,40.37C61.82,39.5 62.19,38.49 62.19,37.37L62.19,35.87L58.08,35.87L58.08,34.26L73.24,34.26L73.24,35.87L68.71,35.87L68.71,41.48C68.71,41.81 68.93,41.97 69.36,41.97L71.1,41.97C71.24,41.97 71.36,41.93 71.46,41.84C71.55,41.72 71.62,41.4 71.63,40.84L71.69,40.04L73.85,40.69L73.64,41.9C73.54,42.54 73.33,42.98 72.95,43.22C72.62,43.46 72.22,43.58 71.75,43.58L68.36,43.58C67.19,43.58 66.61,43.13 66.61,42.22L66.61,35.87L64.21,35.87L64.21,37.37ZM53.55,29.11C54.66,29.85 55.86,30.86 57.09,32.13L55.39,33.23C54.06,31.93 52.9,30.94 51.92,30.28L53.55,29.11ZM54.28,36.71L51.01,36.71L51.01,35.12L56.49,35.12L56.49,43.32C56.61,43.54 56.79,43.77 57.07,44.01C57.45,44.4 58.01,44.69 58.77,44.9C59.5,45.11 60.55,45.19 61.95,45.14C66.61,45.13 70.68,45.07 74.14,44.97L73.7,46.79L60.74,46.69C59.26,46.63 58.1,46.44 57.27,46.11C56.5,45.79 55.93,45.41 55.55,44.97C55.23,45.37 54.61,45.79 53.68,46.2C52.75,46.64 51.87,46.91 51.04,47L50.33,45.46C51.74,45.19 52.71,44.86 53.23,44.48C53.77,44.04 54.13,43.48 54.28,42.82L54.28,36.71Z"
        android:strokeWidth="1"
        android:fillColor="#009944"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M92.39,44.53L95.47,44.53L95.47,41.36L92.39,41.36L92.39,44.53ZM90.45,34.73L96.39,34.73L96.39,33.48L90.45,33.48L90.45,34.73ZM90.45,32.1L96.39,32.1L96.39,30.65L90.45,30.65L90.45,32.1ZM87.03,44.53L90.33,44.53L90.33,41.36L87.03,41.36L87.03,44.53ZM90.09,37.48C89.86,38 89.45,38.6 88.85,39.3L86.89,38.54C87.54,37.93 87.96,37.46 88.16,37.12C88.38,36.7 88.48,36.16 88.48,35.49L88.48,29.11L98.36,29.11L98.36,37.45C98.36,38.54 97.67,39.09 96.28,39.09L94.17,39.09L93.6,37.55L95.56,37.55C96.12,37.55 96.39,37.33 96.39,36.88L96.39,36.11L90.38,36.11C90.35,36.64 90.25,37.09 90.09,37.48L90.09,37.48ZM81.66,44.53L84.98,44.53L84.98,41.36L81.66,41.36L81.66,44.53ZM80.44,36.29L84.32,36.29L84.32,34.41L80.44,34.41L80.44,36.29ZM80.44,32.88L84.32,32.88L84.32,30.9L80.44,30.9L80.44,32.88ZM78.49,37.84L78.49,29.35L86.28,29.35L86.28,37.84L85.29,37.84L78.49,37.84ZM79.61,39.82L97.52,39.82L97.52,44.53L99.66,44.53L99.66,46.15L77.54,46.15L77.54,44.53L79.61,44.53L79.61,39.82Z"
        android:strokeWidth="1"
        android:fillColor="#009944"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M50.33,22.3l102.91,0l0,-2.56l-102.91,0z"
        android:strokeWidth="1"
        android:fillColor="#00A051"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M88.06,17.13L90.01,17.13L90.01,5.26L88.06,5.26L88.06,17.13ZM82.48,15.73L77.9,15.73L77.9,15.29C77.9,14.69 77.91,14.15 78.15,13.74C78.25,13.6 78.37,13.47 78.5,13.36C78.83,13.12 79.34,12.98 80.14,12.98L82.36,12.98L82.36,12.98L82.46,12.98C82.47,13.09 82.47,13.22 82.47,13.35L82.48,13.35L82.48,15.73ZM84.41,12.84C84.41,10.62 82.66,8.8 80.07,8.66L80.07,8.65L79.81,8.65L79.76,8.65L79.74,8.65L79.38,8.65L76.07,8.65L76.07,10.05L76.16,10.05L79.27,10.05C80.95,10.05 81.84,10.49 82.22,11.58L79.96,11.58L79.96,11.59C77.75,11.63 76.07,13.09 76.04,14.89L76.02,14.89L76.02,17.13L84.42,17.13L84.42,12.84L84.41,12.84ZM110.71,12.84L110.71,12.84C110.71,10.52 108.63,8.65 106.39,8.65L106.36,8.65L103.58,8.65L103.58,5.28L101.63,5.28L101.63,15.73L100.91,15.73L100.91,15.73L98.57,15.73C97.23,15.73 96.17,14.61 96.17,13.24L96.17,12.36L96.17,10.05L98.9,10.05L98.9,8.65L96.17,8.65L96.17,5.26L94.21,5.26L94.21,8.65L92.54,8.65L92.54,10.05L94.21,10.05L94.21,13.66L94.22,13.66L94.22,13.66C94.22,15.58 95.95,17.13 97.81,17.13L97.84,17.13L98.29,17.13L102.76,17.13L103.58,17.13L103.58,10.05L105.57,10.05C107.83,10.05 108.77,11.15 108.77,13.35L108.77,17.13L110.71,17.13L110.71,12.84L110.71,12.84ZM66.17,11.73C66.41,10.64 67.55,9.84 68.94,9.84C70.31,9.84 71.3,10.65 71.53,11.73L66.17,11.73ZM73.69,12.32C73.69,10.22 71.55,8.53 68.91,8.53C67.4,8.53 66.06,9.09 65.18,9.96C65.1,10.03 65.02,10.11 64.96,10.19C64.94,10.22 64.91,10.26 64.87,10.29C64.86,10.31 64.84,10.35 64.81,10.38C64.49,10.8 64.27,11.28 64.19,11.79C64.14,11.99 64.12,12.2 64.09,12.4C64.07,12.93 64.12,13.44 64.23,13.88C64.69,15.77 66.75,17.19 69.26,17.19C70.9,17.19 72.37,16.56 73.3,15.6L72.2,14.75C71.55,15.44 70.61,15.9 69.48,15.9C67.6,15.9 66.22,14.69 66.13,13.15L73.69,13.15L73.69,12.33L73.69,12.32ZM59.3,8.65L52.44,8.65L52.44,5.26L50.33,5.26L50.33,17.13L52.44,17.13L52.44,10.19L59.3,10.19L59.3,17.13L61.41,17.13L61.41,5.26L59.3,5.26L59.3,8.65Z"
        android:strokeWidth="1"
        android:fillColor="#00A051"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M119.95,15.78C117.95,15.78 116.33,14.3 116.33,12.48L116.33,5.26L114.12,5.26L114.12,12.99L114.12,12.99C114.12,15.31 116.19,17.19 118.74,17.19L118.77,17.19L123.47,17.19L123.47,15.78L119.95,15.78Z"
        android:strokeWidth="1"
        android:fillColor="#656464"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M150.16,17.19l2.23,0l-4.39,-5.3l3.59,-3.19l-2.16,0l-3.7,3.21l0,-6.65l-1.85,0l0,11.93l1.85,0l0,-3.3l1.04,-0.92z"
        android:strokeWidth="1"
        android:fillColor="#656464"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M126.02,17.19l1.7,0l0,-8.52l-1.7,0z"
        android:strokeWidth="1"
        android:fillColor="#656464"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M140.48,12.87L140.48,12.87C140.48,10.55 138.35,8.67 136.06,8.67L136.04,8.67L133.1,8.67L131.13,8.67L131.13,17.19L133.1,17.19L133.1,10.07L135.23,10.07C137.53,10.07 138.5,11.17 138.5,13.38L138.5,17.19L140.48,17.19L140.48,12.87L140.48,12.87Z"
        android:strokeWidth="1"
        android:fillColor="#656464"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M127.67,7.62C127.45,7.99 126.53,7.82 125.62,7.22C124.7,6.62 124.15,5.83 124.37,5.45C124.61,5.08 125.52,5.26 126.44,5.85C127.35,6.46 127.9,7.24 127.67,7.62"
        android:strokeWidth="1"
        android:fillColor="#656464"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M161.36,41.05L159.72,41.05L159.72,39.15L161.55,39.15C161.97,39.15 162.29,39.23 162.48,39.41C162.69,39.59 162.78,39.81 162.78,40.08C162.78,40.26 162.74,40.44 162.63,40.6C162.53,40.76 162.37,40.87 162.18,40.95C161.98,41.02 161.71,41.05 161.36,41.05M162.41,41.94C162.3,41.84 162.15,41.75 161.94,41.65C162.5,41.57 162.9,41.4 163.17,41.12C163.43,40.84 163.56,40.49 163.56,40.08C163.56,39.76 163.49,39.48 163.32,39.21C163.17,38.95 162.94,38.77 162.69,38.66C162.42,38.56 162.03,38.5 161.53,38.5L158.96,38.5L158.96,44.28L159.72,44.28L159.72,41.72L160.61,41.72C160.81,41.72 160.95,41.72 161.04,41.75C161.16,41.77 161.27,41.82 161.38,41.9C161.5,41.97 161.62,42.1 161.77,42.28C161.91,42.47 162.09,42.73 162.31,43.07L163.08,44.28L164.04,44.28L163.04,42.71C162.83,42.4 162.63,42.15 162.41,41.94M161.32,45.59C159.05,45.58 157.21,43.74 157.21,41.46C157.21,39.18 159.05,37.34 161.32,37.33C163.6,37.34 165.44,39.18 165.45,41.46C165.44,43.74 163.6,45.58 161.32,45.59M161.32,36.78C158.74,36.78 156.64,38.87 156.64,41.46C156.64,44.05 158.74,46.15 161.32,46.15C163.9,46.15 166,44.05 166,41.46C166,38.87 163.9,36.78 161.32,36.78"
        android:strokeWidth="1"
        android:fillColor="#4C4948"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M106.16,36.67L102.89,36.67L102.89,35.07L108.37,35.07L108.37,43.31C108.49,43.53 108.68,43.76 108.95,44C109.33,44.39 109.89,44.69 110.65,44.89C111.38,45.1 112.43,45.18 113.83,45.14C118.5,45.12 122.56,45.06 126.02,44.96L125.58,46.79L112.62,46.69C111.15,46.63 109.99,46.44 109.15,46.11C108.38,45.79 107.81,45.41 107.43,44.96C107.11,45.36 106.49,45.79 105.56,46.2C104.63,46.64 103.75,46.91 102.92,47L102.21,45.45C103.63,45.18 104.59,44.86 105.11,44.47C105.65,44.03 106.01,43.47 106.16,42.8L106.16,36.67Z"
        android:strokeWidth="1"
        android:fillColor="#009944"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M105.51,29.11C106.62,29.88 107.8,30.92 109.01,32.24L107.34,33.37C106.02,32.03 104.88,31.01 103.91,30.32L105.51,29.11Z"
        android:strokeWidth="1"
        android:fillColor="#009944"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M143.24,38.24L147.73,38.24L147.73,37.13L143.24,37.13L143.24,38.24ZM143.24,35.63L147.73,35.63L147.73,34.66L143.24,34.66L143.24,35.63ZM136.98,40.07C137.56,40.56 138.13,41.19 138.7,41.96L136.92,42.81C136.28,42.02 135.74,41.41 135.27,41L136.98,40.07ZM139.33,42.64L140.57,44.08L137.92,45.16C136.77,45.53 135.74,45.81 134.82,46.01L133.64,44.4C134.8,44.18 135.98,43.9 137.21,43.55L139.33,42.64ZM141.15,33.25L141.15,32.06L133.63,32.06L133.63,35.63L141.15,35.63L141.15,34.66L135.92,34.66L135.92,33.25L141.15,33.25ZM152.39,44.9L151.36,46.57C149.04,46 147.2,45.29 145.81,44.46C144.97,43.94 144.11,43.19 143.24,42.25L143.24,45.41C143.24,46.47 142.52,47 141.13,47L139.12,47L138.58,45.44L140.29,45.44C140.86,45.44 141.15,45.22 141.15,44.79L141.15,39.67L135.81,39.67L135.81,38.24L141.15,38.24L141.15,37.13L133.63,37.13L133.63,39.85C133.6,41.03 133.46,42.14 133.19,43.19C132.85,44.47 132.29,45.72 131.52,46.98L129.43,46.05C130.26,44.95 130.83,43.94 131.14,42.98C131.45,41.97 131.62,40.92 131.62,39.85L131.62,30.57L140.72,30.57L140.27,29.6L142.38,29.11C142.65,29.51 142.87,30 143.04,30.57L152.16,30.57L152.16,32.06L143.24,32.06L143.24,33.25L149.81,33.25L149.81,35.63L152.39,35.63L152.39,37.13L149.81,37.13L149.81,39.67L143.29,39.67C144.05,40.63 144.79,41.44 145.51,42.08C146.82,41.36 147.82,40.74 148.49,40.19L149.9,41.39C149.15,41.98 148.22,42.59 147.08,43.19C148.43,43.95 150.21,44.52 152.39,44.9L152.39,44.9Z"
        android:strokeWidth="1"
        android:fillColor="#009944"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1,47l165,0l0,-46l-165,0z"/>
    <path
        android:pathData="M114.13,37.16L121.51,37.16L121.51,35.81L114.13,35.81L114.13,37.16ZM114.13,39.7L121.51,39.7L121.51,38.36L114.13,38.36L114.13,39.7ZM114.13,42.28L121.51,42.28L121.51,40.92L114.13,40.92L114.13,42.28ZM125.17,33.1L125.17,31.63L121.38,31.63C121.92,31.06 122.48,30.36 123.03,29.71L120.85,29.23C120.46,29.94 119.76,30.92 119.15,31.63L114.96,31.63L116.19,31.19C115.9,30.61 115.15,29.73 114.52,29.11L112.63,29.73C113.16,30.29 113.74,31.07 114.06,31.63L110.71,31.63L110.71,33.1L116.44,33.1C116.32,33.56 116.15,34.06 116,34.5L112.02,34.5L112.02,43.59L123.74,43.59L123.74,34.5L118.11,34.5C118.38,34.08 118.62,33.6 118.89,33.1L125.17,33.1Z"
        android:strokeWidth="1"
        android:fillColor="#009944"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
</vector>
