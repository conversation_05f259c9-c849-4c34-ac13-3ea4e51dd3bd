<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/fl_container"
    >

    <RelativeLayout
        android:id="@+id/privacy_agree_home"
        android:background="@drawable/dialog_bg"
        android:layout_width="960dp"
        android:layout_height="560dp"
        android:layout_gravity="center">

        <TextView
            android:id="@+id/hms_dialog_title"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_alignParentTop="true"
            android:gravity="center"
            android:text=""
            android:textColor="@color/text_color_fc_100"
            android:textSize="26sp" />

        <RelativeLayout
            android:id="@+id/lo_year_piker"
            android:layout_width="match_parent"
            android:layout_height="280dp"
            android:layout_below="@+id/hms_dialog_title"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="42dp">
            <com.shawnlin.numberpicker.NumberPicker
                android:id="@+id/body_data_piker"
                android:layout_width="wrap_content"
                android:layout_height="280dp"
                android:layout_marginStart="10dp"
                android:layout_centerInParent="true"
                app:np_dividerColor="@color/colorAccent"
                app:np_dividerType="underline"
                app:np_orientation="vertical"
                app:np_secondTextColor="@color/personal_setting_text_40"
                app:np_secondTextSize="30sp"
                app:np_selectedTextColor="@color/personal_setting_text_100"
                app:np_selectedTextSize="36sp"
                app:np_textColor="@color/personal_setting_text_30"
                app:np_textSize="22sp"
                app:np_wheelItemCount="6"
                app:np_fadingEdgeEnabled="false"
                app:np_wrapSelectorWheel="false" />
            <TextView
                android:id="@+id/body_data_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/body_data_piker"
                android:layout_marginStart="20dp"
                android:layout_marginTop="118dp"
                android:text="年"
                android:textSize="30sp"
                android:textColor="@color/personal_setting_text_100"/>
        </RelativeLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_alignParentBottom="true"
            android:layout_height="88dp">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="88dp"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/positiveButton"
                    android:layout_width="match_parent"
                    android:layout_height="88dp"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_left_bg_fill_selector"
                    android:text="确定"
                    android:textColor="@color/btn_left_text_color"
                    android:textSize="26sp" />
                <Button
                    android:id="@+id/negativeButton"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_right_bg_fill_selector"
                    android:text="取消"
                    android:textColor="@color/text_color_fc_60"
                    android:textSize="26sp" />
            </LinearLayout>
            <View
                android:layout_gravity="center_horizontal"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/dialog_divider" />
        </FrameLayout>
    </RelativeLayout>

</FrameLayout>