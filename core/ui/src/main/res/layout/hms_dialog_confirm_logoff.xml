<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fl_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/dialog_content"
        android:layout_width="640dp"
        android:layout_height="328dp"
        android:background="@drawable/dialog_bg"
        android:layout_gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            >
            <TextView
                android:id="@+id/hms_dialog_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginTop="40dp"
                android:text="注销健康账户"
                android:textStyle="bold"
                android:textColor="@color/text_color_fc_100"
                android:textSize="26sp" />

        </LinearLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:layout_marginStart="48dp"
            android:paddingTop="22dp"
            android:layout_marginEnd="48dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                >
                <TextView
                    android:id="@+id/hms_dialog_message"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:lineHeight="36dp"
                    android:text="@string/logout_app_content"
                    android:textColor="@color/text_color_fc_80"
                    android:textSize="22sp"
                    />
            </LinearLayout>

        </RelativeLayout>


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="88dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="88dp"
                android:orientation="horizontal"
                >
                <Button
                    android:id="@+id/positiveButton"
                    android:layout_width="match_parent"
                    android:layout_height="88dp"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_left_bg_fill_selector"
                    android:text="拨打电话"
                    android:textColor="@color/hms_color_primary"
                    android:textSize="26sp" />


                <Button
                    android:id="@+id/negativeButton"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_right_bg_fill_selector"
                    android:text="取消"
                    android:textColor="@color/text_color_fc_60"
                    android:textSize="26sp" />
            </LinearLayout>

            <View
                android:layout_gravity="center_horizontal"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/dialog_divider" />
        </FrameLayout>


    </LinearLayout>

</FrameLayout>