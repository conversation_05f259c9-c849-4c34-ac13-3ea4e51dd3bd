<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/fl_container"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/dialog_bg"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="640dp"
        android:layout_height="328dp">


        <TextView
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            android:id="@+id/hms_dialog_message"
            android:layout_width="match_parent"
            android:layout_height="240dp"
            android:gravity="center"
            android:text="@string/logout_app_content"
            android:textColor="@color/text_color_fc_80"
            android:textSize="22sp"
            android:lineHeight="36dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="88dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"

            android:orientation="horizontal">

            <Button
                android:id="@+id/positiveButton"
                android:layout_width="match_parent"
                android:layout_height="88dp"
                android:layout_weight="0.5"
                android:layout_marginRight="1dp"
                android:background="@drawable/dialog_btn_left_bg_fill_selector"
                android:text="拨打电话"
                android:textColor="@color/hms_color_primary"
                android:textSize="26sp"
                />
            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#1A20263B"
                />
            <Button
                android:id="@+id/negativeButton"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="0.5"
                android:layout_marginLeft="1dp"
                android:background="@drawable/dialog_btn_right_bg_fill_selector"
                android:text="取消"
                android:textColor="@color/text_color_fc_60"
                android:textSize="26sp"
                />
        </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>