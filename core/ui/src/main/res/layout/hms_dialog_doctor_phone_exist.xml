<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:id="@+id/dialog_content"
        android:background="@drawable/dialog_bg"
        android:layout_gravity="center"
        android:layout_width="640dp"
        android:layout_height="328dp">

        <TextView
            android:layout_marginTop="48dp"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            android:id="@+id/hms_dialog_message"
            android:layout_width="match_parent"
            android:layout_height="144dp"
            android:lineSpacingExtra="14dp"
            android:gravity="center"
            android:textColor="@color/text_color_fc_80"
            android:text=""
            tools:text="测试文案"
            android:textSize="22sp" />
        <LinearLayout
            android:id="@+id/ll_bottom_container"
            android:layout_width="match_parent"
            android:layout_height="88dp"
            android:layout_alignParentBottom="true"
            android:orientation="horizontal">

            <Button
                android:id="@+id/positiveButton"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/dialog_btn_single_bg_fill_selector"
                android:text="知道了"
                android:textColor="@color/btn_left_text_color"
                android:textSize="26sp" />
        </LinearLayout>

    </RelativeLayout>
</FrameLayout>