<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/dialog_bg">

    <RelativeLayout
        android:layout_width="640dp"
        android:layout_height="328dp">
        <TextView
            android:id="@+id/hms_dialog_content"
            android:layout_width="560dp"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_marginTop="100dp"
            android:gravity="center"
            android:text=""
            android:textColor="@color/text_color_fc_80"
            android:textSize="22sp" />
        <TextView
            android:id="@+id/hms_dialog_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="48dp"
            android:layout_marginTop="7dp"
            android:layout_marginRight="48dp"
            android:lineSpacingExtra="14dp"
            android:text=""
            android:textSize="22sp" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="88dp"
            android:layout_alignParentBottom="true"
            android:orientation="horizontal">

            <Button
                android:id="@+id/positiveButton"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginRight="1dp"
                android:layout_weight="0.5"
                android:background="@drawable/dialog_btn_left_bg_fill_selector"
                android:text="知道了"
                android:textColor="@color/hms_color_primary"
                android:textSize="26sp" />
        </LinearLayout>

    </RelativeLayout>
</FrameLayout>