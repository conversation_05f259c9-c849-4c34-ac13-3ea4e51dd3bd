<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.cardview.widget.CardView
            android:layout_width="1476dp"
            android:layout_height="840dp"
            app:cardBackgroundColor="#00FFFFFF"
            app:cardCornerRadius="25dp"
            app:cardElevation="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="MissingConstraints">

            <FrameLayout
                android:id="@+id/login_dialog_container"
                android:layout_width="1476dp"
                android:layout_height="840dp"
                android:orientation="vertical">

                <com.healthlink.hms.adapter.banner.CustomBanner
                    android:id="@+id/banner"
                    android:layout_width="1506dp"
                    android:layout_height="840dp"
                    android:background="@drawable/transparent"
                    app:banner_auto_loop="false"
                    app:banner_indicator_height="10dp"
                    app:banner_indicator_normal_color="#3320263B"
                    app:banner_indicator_normal_width="10dp"
                    app:banner_indicator_selected_color="#CC20263B"
                    app:banner_indicator_selected_width="10dp"
                    app:banner_indicator_space="20dp"
                    app:banner_infinite_loop="false" />

                <LinearLayout
                    android:id="@+id/noTipsContainer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginStart="100dp"
                    android:layout_marginBottom="100dp"
                    android:orientation="horizontal"
                    android:padding="20dp">

                    <ImageView
                        android:id="@+id/noTips"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:src="@drawable/shape_check_banner" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:alpha="0.6"
                        android:text="@string/tips_no_banner"
                        android:textColor="#21273D"
                        android:textSize="18sp" />

                </LinearLayout>

                <com.youth.banner.indicator.CircleIndicator
                    android:id="@+id/indicator"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"
                    android:layout_gravity="center|bottom"
                    android:layout_marginBottom="120dp" />

                <TextView
                    android:id="@+id/know"
                    android:layout_width="match_parent"
                    android:layout_height="88dp"
                    android:layout_gravity="bottom"
                    android:background="@drawable/shape_banner_bottom_bg"
                    android:gravity="center"
                    android:text="@string/tips_know"
                    android:textColor="@color/hms_color_primary"
                    android:textSize="26sp"
                    android:textStyle="bold" />

            </FrameLayout>

        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>