<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout
    android:id="@+id/fl_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"

    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_content"
        android:layout_width="640dp"
        android:layout_height="328dp"
        android:layout_gravity="center"
        android:background="@drawable/dialog_bg"
        >


        <TextView
            android:id="@+id/hms_dialog_message"
            android:layout_width="match_parent"
            android:layout_height="240dp"
            android:gravity="center"
            android:text="@string/logout_app_content"
            android:textColor="@color/text_color_fc_80"
            android:textSize="22sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="88dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="88dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/positiveButton"
                    android:layout_width="match_parent"
                    android:layout_height="88dp"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_left_bg_fill_selector"
                    android:text="小憩模式"
                    android:textColor="@color/hms_color_primary"
                    android:textSize="26sp"
                    />
                <Button
                    android:id="@+id/negativeButton"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_right_bg_fill_selector"
                    android:text="知道了"
                    android:textColor="@color/text_color_fc_60"
                    android:textSize="26sp"
                    />
            </LinearLayout>

            <View
                android:layout_width="1dp"
                android:layout_gravity="center_horizontal"
                android:layout_height="match_parent"
                android:background="@color/dialog_divider"
                />

        </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>