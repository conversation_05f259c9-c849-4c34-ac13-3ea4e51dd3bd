<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/privacy_agree_home"
        android:layout_width="960dp"
        android:layout_height="560dp"
        android:layout_gravity="center"
        android:background="@drawable/dialog_bg"
        android:visibility="visible">

        <LinearLayout
            android:id="@+id/lo_dialog_title"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_alignParentTop="true"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/hms_dialog_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:gravity="center"
                android:lineHeight="42dp"
                android:text="@string/user_agreement_privacy_statement"
                android:textColor="@color/text_color_fc_100"
                android:textSize="26sp" />
        </LinearLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="344dp"
            android:layout_below="@+id/lo_dialog_title"
            android:visibility="visible">

            <TextView
                android:id="@+id/hms_dialog_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="48dp"
                android:layout_marginTop="7dp"
                android:layout_marginRight="48dp"
                android:lineSpacingExtra="14dp"
                android:textColor="@color/text_color_fc_80"
                android:text=""
                android:textSize="22sp" />
        </ScrollView>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="88dp"
            android:layout_alignParentBottom="true">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="88dp"
                android:layout_alignParentBottom="true"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/positiveButton"
                    android:layout_width="match_parent"
                    android:layout_height="88dp"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_left_bg_fill_selector"
                    android:text="同意"
                    android:textColor="@color/hms_color_primary"
                    android:textSize="26sp" />
                <Button
                    android:id="@+id/negativeButton"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_right_bg_fill_selector"
                    android:text="取消"
                    android:textColor="@color/text_color_fc_60"
                    android:textSize="26sp" />
            </LinearLayout>
            <View
                android:layout_gravity="center_horizontal"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/dialog_divider" />
        </FrameLayout>

    </RelativeLayout>

</FrameLayout>