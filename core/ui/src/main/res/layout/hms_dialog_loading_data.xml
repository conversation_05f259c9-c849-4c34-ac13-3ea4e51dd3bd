<cn.enjoytoday.shadow.ShadowLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/shadowLayout"
    android:gravity="center"
    app:shadowRadius="24dp"
    app:shadowColor="#26000000"
    app:bgColor="@color/loading_data_bg"
    android:background="@drawable/bg_doctor_call_dial"
    app:xOffset="0dp"
    app:yOffset="0dp"
    app:blurRadius="24dp"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >
    <LinearLayout
        android:layout_gravity="center"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@drawable/loading_data_bg">

        <ImageView
            android:id="@+id/iv_loading_amin"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:gravity="center"
            android:src="@drawable/loading_80x80"
            />
        <TextView
            android:id="@+id/tv_loading_text"
            android:layout_marginTop="32dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="加载中..."
            android:textSize="26sp"
            android:textColor="@color/text_color_fc_80"
            />
    </LinearLayout>
</cn.enjoytoday.shadow.ShadowLayout>