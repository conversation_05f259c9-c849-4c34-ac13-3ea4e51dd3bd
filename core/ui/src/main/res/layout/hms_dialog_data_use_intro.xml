<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fl_container"
    android:layout_width="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/content_container"
        android:layout_width="1280dp"
        android:layout_height="640dp"
        android:layout_gravity="center"
        android:background="@drawable/dialog_bg">
        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="match_parent"
            android:layout_height="96dp"
            android:orientation="vertical"
            android:background="@drawable/dialog_title_radius"
            android:layout_alignParentTop="true"
            >
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/data_use_intro"
                android:textSize="30sp"
                android:textColor="@color/text_color_fc_100"
                android:layout_gravity="center"/>
        </LinearLayout>
        <FrameLayout
            android:layout_below="@+id/ll_title"
            android:id="@+id/lo_data_usage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            >
            <LinearLayout
                android:id="@+id/lo_loading"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone"/>

            <com.hieupt.android.standalonescrollbar.view.ScrollView2
                android:id="@+id/data_usage_container_scroll"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="68dp"
                android:layout_marginEnd="68dp">

                <LinearLayout
                    android:id="@+id/data_usage_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal" />
            </com.hieupt.android.standalonescrollbar.view.ScrollView2>
        </FrameLayout>

        <com.hieupt.android.standalonescrollbar.StandaloneScrollBar
            android:layout_alignRight="@+id/lo_data_usage"
            android:layout_alignTop="@+id/lo_data_usage"
            android:layout_marginTop="40dp"
            app:scrollbarAlwaysShow="true"
            app:scrollbarThumbLength="64dp"
            app:scrollbarThumbDrawable="@drawable/scrollbar_ver_thumb"
            android:id="@+id/scrollbar"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="16dp"
            android:layout_width="4dp"
            android:layout_marginBottom="5dp"
            android:layout_height="match_parent"
            />
    </RelativeLayout>
</FrameLayout>