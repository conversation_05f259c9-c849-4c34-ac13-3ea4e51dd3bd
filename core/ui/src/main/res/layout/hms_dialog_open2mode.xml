<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout
    android:id="@+id/fl_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_content"
        android:background="@drawable/dialog_bg"
        android:layout_gravity="center"
        android:layout_width="960dp"
        android:layout_height="560dp">
        <LinearLayout
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:id="@+id/icon_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginTop="80dp"
            >

            <ImageView
                android:id="@+id/icon_left"
                android:layout_width="140dp"
                android:layout_height="140dp"
                android:src="@drawable/icon_wheel_heating"
                />

            <ImageView
                android:layout_marginStart="10dp"
                android:id="@+id/icon_right"
                android:layout_width="140dp"
                android:layout_height="140dp"
                android:src="@drawable/icon_seat_heating"
                />

        </LinearLayout>

        <TextView
            android:id="@+id/mode_title"
            android:layout_marginTop="40dp"
            app:layout_constraintTop_toBottomOf="@+id/icon_container"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="是否开启温暖模式"
            android:textSize="30sp"
            android:textStyle="bold"
            android:gravity="center"
            android:textColor="@color/text_color_333"
            />

        <TextView
            android:layout_marginTop="30dp"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            android:id="@+id/mode_tips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="开启温暖模式将会打开方向盘加热和主驾座椅加热"
            android:textColor="@color/text_color_666"
            android:textSize="22sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/mode_title"/>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="88dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">
            <LinearLayout
                android:id="@+id/area_btn"
                android:layout_width="match_parent"
                android:layout_height="88dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/positiveButton"
                    android:layout_width="match_parent"
                    android:layout_height="88dp"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_left_bg_fill_selector"
                    android:text="拨打电话"
                    android:textColor="@color/card_status_normal_bg_color"
                    android:textSize="26sp"
                    />

                <Button
                    android:id="@+id/negativeButton"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="0.5"
                    android:background="@drawable/dialog_btn_right_bg_fill_selector"
                    android:text="取消"
                    android:textColor="@color/text_color_999"
                    android:textSize="26sp"
                    />
            </LinearLayout>

            <View
                android:layout_gravity="center_horizontal"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/dialog_divider"
                />
        </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>