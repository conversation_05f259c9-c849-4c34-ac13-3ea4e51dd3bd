<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_gravity="center"
    android:gravity="center"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:id="@+id/loadingView_container"
    android:background="@color/view_color_bg">

    <ImageView
        android:id="@+id/iv_loading_amin"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:gravity="center"
        android:src="@drawable/loading_80x80"
        />
    <TextView
        android:layout_marginTop="30dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="加载中..."
        android:textSize="30sp"
        android:textColor="@color/text_color_fc_100"
        />

</LinearLayout>