<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout
    android:id="@+id/fl_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >

    <RelativeLayout
        android:id="@+id/dialog_content"
        android:layout_width="640dp"
        android:layout_height="560dp"
        android:layout_gravity="center"
        android:background="@drawable/dialog_bg">

        <TextView
            android:id="@+id/hms_dialog_title"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:gravity="center"
            android:text="健康建议"
            android:textColor="@color/text_color_fc_100"
            android:textSize="26sp"
            android:layout_alignParentTop="true"/>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="350dp"
            android:layout_marginLeft="48dp"
            android:layout_marginRight="16dp"
            android:layout_below="@+id/hms_dialog_title">

            <TextView
                android:id="@+id/hms_dialog_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginRight="32dp"
                android:lineHeight="36dp"
                android:textColor="@color/text_color_fc_80"
                android:text=""
                android:textSize="22sp" />
        </ScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="88dp"
            android:layout_alignParentBottom="true"
            android:background="@drawable/bg_dialog_w_960_button"
            android:orientation="horizontal">
            <Button
                android:id="@+id/positiveButton"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="0.5"
                android:layout_marginRight="1dp"
                android:background="#00000000"
                android:text="知道了"
                android:textColor="@color/hms_color_primary"
                android:textSize="26sp"
                />
        </LinearLayout>

    </RelativeLayout>

</FrameLayout>