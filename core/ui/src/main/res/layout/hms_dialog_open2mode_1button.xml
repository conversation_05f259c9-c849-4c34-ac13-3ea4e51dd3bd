<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout
    android:id="@+id/fl_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_content"
        android:background="@drawable/dialog_bg"
        android:layout_gravity="center"
        android:layout_width="960dp"
        android:layout_height="560dp">
        <LinearLayout
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:id="@+id/icon_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginTop="114dp"
            >

            <ImageView
                android:id="@+id/icon_left"
                android:layout_width="96dp"
                android:layout_height="96dp"
                android:src="@drawable/icon_emergency"
                />

            <ImageView
                android:layout_marginStart="60dp"
                android:id="@+id/icon_right"
                android:layout_width="96dp"
                android:layout_height="96dp"
                android:src="@drawable/icon_seat_heating"
                />

        </LinearLayout>

        <TextView
            android:id="@+id/mode_title"
            android:layout_marginTop="32dp"
            app:layout_constraintTop_toBottomOf="@+id/icon_container"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="呼叫紧急救援"
            android:textSize="30sp"
            android:gravity="center"
            android:textColor="@color/text_color_fc_100"
            />

        <TextView
            android:layout_marginTop="8dp"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            android:id="@+id/mode_tips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="在顶灯面板，长按SOS紧急救援按钮可以出发紧急救援呼叫服务"
            android:textColor="@color/text_color_fc_80"
            android:textSize="22sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/mode_title"/>

        <LinearLayout
            android:id="@+id/area_btn"
            android:layout_width="match_parent"
            android:layout_height="88dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"

            android:orientation="horizontal">

            <Button
                android:id="@+id/positiveButton"
                android:layout_width="match_parent"
                android:layout_height="88dp"
                android:layout_weight="0.5"
                android:layout_marginRight="1dp"
                android:background="@drawable/dialog_btn_single_bg_fill_selector"
                android:text="知道了"
                android:textColor="@color/btn_left_text_color"
                android:textSize="26sp"
                />
        </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>