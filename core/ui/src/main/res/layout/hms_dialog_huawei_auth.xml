<!-- res/layout/custom_dialog_view.xml -->
<FrameLayout
    android:id="@+id/fl_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_content"
        android:background="@drawable/dialog_bg"
        android:layout_gravity="center"
        android:layout_width="640dp"
        android:layout_height="328dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="144dp"
            android:layout_marginStart="48dp"
            android:layout_marginTop="48dp"
            android:layout_marginEnd="48dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_centerInParent="true"
                >

                <TextView
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:id="@+id/hms_dialog_message"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineHeight="36dp"
                    android:lineSpacingExtra="7dp"
                    android:text="@string/noti_open_data_auth"
                    android:textSize="22sp"
                    android:gravity="start|center"
                    android:textColor="@color/text_color_fc_100"
                    />
            </LinearLayout>
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/area_btn"
            android:layout_width="match_parent"
            android:layout_height="88dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"

            android:orientation="horizontal">

            <Button
                android:id="@+id/positiveButton"
                android:layout_width="match_parent"
                android:layout_height="88dp"
                android:layout_weight="0.5"
                android:layout_marginRight="1dp"
                android:background="@drawable/dialog_btn_single_bg_fill_selector"
                android:text="知道了"
                android:textColor="@color/btn_left_text_color"
                android:textSize="26sp"
                />
        </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>