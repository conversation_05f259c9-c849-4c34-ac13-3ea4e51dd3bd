<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/view_color_bg"
    >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        >
        <ImageView
            android:layout_width="240dp"
            android:layout_height="240dp"
            android:src="@drawable/img_wifi_error1"
            />
        <TextView
            android:layout_marginTop="24dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:lineHeight="36dp"
            android:text="网络异常，请点击下方按钮重新加载"
            android:textSize="22sp"
            android:textColor="@color/text_color_fc_80"
            />
            <TextView
                android:id="@+id/btn_no_net_refresh"
                android:layout_width="280dp"
                android:layout_height="64dp"
                android:layout_marginTop="40dp"
                android:gravity="center"
                android:background="@drawable/bg_connect_error_btn"
                android:text="刷新重试"
                android:textSize="22sp"
                android:textColor="@color/connect_error_btn_text"
                />
    </LinearLayout>
</FrameLayout>