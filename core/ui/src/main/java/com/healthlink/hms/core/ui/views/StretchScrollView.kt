package com.healthlink.hms.core.ui.views

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.TranslateAnimation
import androidx.core.widget.NestedScrollView

/**
 * @Author: 付仁秀
 * @Description：
 */
open class StretchScrollView(context: Context?, attrs: AttributeSet?) : NestedScrollView(
    context!!, attrs
) {
    private var innerView: View? = null
    private var mLastY = 0f // 上次手势事件的y坐标
    private val normal = Rect()    // 记录子View的正常位置

    override fun onFinishInflate() {
        initView()
        super.onFinishInflate()
    }
    private fun initView() {
        // 去除原本ScrollView滚动到边界时的效果
        overScrollMode = OVER_SCROLL_NEVER
        if (getChildAt(0) != null) {
            innerView = getChildAt(0)
        }
    }

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        when (ev.action) {
            MotionEvent.ACTION_UP ->
                if (!normal.isEmpty) {
                    planAnimation()
                    normal.setEmpty()
                    mLastY = 0f
                }
            MotionEvent.ACTION_MOVE -> {
                val currentY = ev.y
                val distanceY = (mLastY - currentY).toInt()
                // 处理Y轴的滚动事件，当滚动到最上或者最下时需要移动布局
                // 手指刚触及屏幕时，也会触发此事件，此时mLastY的值还是0，会立即触发一个比较大的移动。这里过滤掉这种情况
                if (isNeedTranslate && mLastY != 0f) {
                    if (normal.isEmpty) {
                        // 保存正常的布局位置
                        normal[innerView!!.left, innerView!!.top, innerView!!.right] =
                            innerView!!.bottom
                    }
                    // 移动布局， 使distance/4 防止平移过快
                    innerView!!.layout(
                        innerView!!.left, innerView!!.top - distanceY / 4,
                        innerView!!.right, innerView!!.bottom - distanceY / 4
                    )
                }
                mLastY = currentY
            }
        }
        return super.onTouchEvent(ev)
    }

    fun planAnimation() {
        // 开启移动动画
        val animation = TranslateAnimation(0f, 0f, innerView!!.top.toFloat(), normal.top.toFloat())
        animation.duration = 200
        innerView!!.startAnimation(animation)
        // 补间动画并不会真正修改innerView的位置，这里需要设置使得innerView回到正常的布局位置
        innerView!!.layout(normal.left, normal.top, normal.right, normal.bottom)
    }

    val isNeedTranslate: Boolean
        get() {
            val offset = innerView!!.measuredHeight - height
            val scrollY = scrollY
            // 顶部或者底部
            return scrollY == 0 || scrollY == offset
        }
}