plugins {
    alias(libs.plugins.hms.android.library)
//    alias(libs.plugins.hilt)
//    alias(libs.plugins.ksp)
    alias(libs.plugins.hms.hilt)
}

android {
    namespace = "com.healthlink.hms.core.data"
//    compileSdk = 34
//
//    defaultConfig {
//        minSdk = 28
//
//        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
//        consumerProguardFiles("consumer-rules.pro")
//    }
//
//    buildTypes {
//        release {
//            isMinifyEnabled = false
//            proguardFiles(
//                getDefaultProguardFile("proguard-android-optimize.txt"),
//                "proguard-rules.pro"
//            )
//        }
//    }
//    compileOptions {
//        sourceCompatibility = JavaVersion.VERSION_17
//        targetCompatibility = JavaVersion.VERSION_17
//    }
//    kotlinOptions {
//        jvmTarget = "17"
//    }
}

dependencies {
    // :core:model
    implementation(project(":core:model"))
    // :core:network
    implementation(project(":core:network"))
    // :core:common
    implementation(project(":core:common"))
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    // hilt
    implementation(libs.hilt.android)
    ksp(libs.hilt.android.compiler)

    // local datastore
    implementation("androidx.datastore:datastore-preferences:1.1.7")
    // implementation("androidx.datastore:datastore-preferences-core:1.1.7")

    

    // Unit Testing
    testImplementation(libs.junit)
    testImplementation("org.junit.jupiter:junit-jupiter:5.10.1")
    testImplementation("org.junit.jupiter:junit-jupiter-engine:5.10.1")
    testImplementation("org.junit.vintage:junit-vintage-engine:5.10.1")
    
    // Mockito for mocking
    testImplementation("org.mockito:mockito-core:5.7.0")
    testImplementation("org.mockito:mockito-inline:5.2.0")
    testImplementation("org.mockito.kotlin:mockito-kotlin:5.2.1")
    
    // Coroutines Testing
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3")
    
    // Turbine for Flow testing
    testImplementation("app.cash.turbine:turbine:1.0.0")
    
    // DataStore Testing
    testImplementation("androidx.datastore:datastore-preferences-core:1.1.7")
    
    // AssertJ for fluent assertions
    testImplementation("org.assertj:assertj-core:3.24.2")
    
    // Hilt Testing
    testImplementation("com.google.dagger:hilt-android-testing:2.48.1")
    kspTest("com.google.dagger:hilt-android-compiler:2.48.1")
    
    // Android Testing
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation("com.google.dagger:hilt-android-testing:2.48.1")
    kspAndroidTest("com.google.dagger:hilt-android-compiler:2.48.1")
}