package com.healthlink.hms.core.data.di

import com.healthlink.hms.core.data.repository.DefaultInitRepository
import com.healthlink.hms.core.data.repository.InitRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class DataModule {
    @Binds
    internal abstract fun bindsInitRepository(initRepository: DefaultInitRepository): InitRepository
}