package com.healthlink.hms.core.data.repository

import com.healthlink.hms.core.model.store.SceneEnginePreferences
import kotlinx.coroutines.flow.Flow

/**
 * 场景引擎相关数据Repository接口
 * Created by imaginedays on 2024/12/19
 */
interface SceneEngineRepository {
    
    // 场景引擎开关
    suspend fun storeSceneEngineEnabled(enabled: Boolean)
    fun isSceneEngineEnabled(): Flow<Boolean>
    
    // 当前场景ID
    suspend fun storeCurrentSceneId(sceneId: String)
    fun getCurrentSceneId(): Flow<String>
    
    // 场景配置
    suspend fun storeSceneConfig(config: String)
    fun getSceneConfig(): Flow<String>
    
    // 场景数据同步时间
    suspend fun storeSceneDataSyncTime(timestamp: Long)
    fun getSceneDataSyncTime(): Flow<Long>
    
    // 场景推荐开关
    suspend fun storeSceneRecommendationEnabled(enabled: Boolean)
    fun isSceneRecommendationEnabled(): Flow<Boolean>
    
    // 自动场景切换
    suspend fun storeAutoSceneSwitchEnabled(enabled: Boolean)
    fun isAutoSceneSwitchEnabled(): Flow<Boolean>
    
    // 场景学习模式
    suspend fun storeSceneLearningEnabled(enabled: Boolean)
    fun isSceneLearningEnabled(): Flow<Boolean>
    
    // 场景数据收集
    suspend fun storeSceneDataCollectionEnabled(enabled: Boolean)
    fun isSceneDataCollectionEnabled(): Flow<Boolean>
    
    // 获取所有场景引擎设置
    fun getSceneEnginePreferences(): Flow<SceneEnginePreferences>
    
    // 批量更新场景引擎设置
    suspend fun updateSceneEnginePreferences(preferences: SceneEnginePreferences)
    
    // 重置场景引擎设置
    suspend fun resetSceneEngineSettings()
}