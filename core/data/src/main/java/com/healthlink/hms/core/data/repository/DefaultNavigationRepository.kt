package com.healthlink.hms.core.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import com.healthlink.hms.core.data.datastore.DataStorePreKeysConfig
import com.healthlink.hms.core.data.datastore.NavigationDataStore
import com.healthlink.hms.core.model.store.NavigationPreferences
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 导航相关数据Repository实现
 * Created by imaginedays on 2024/12/19
 */
@Singleton
class DefaultNavigationRepository @Inject constructor(
    @NavigationDataStore private val navigationDataStore: DataStore<Preferences>
) : NavigationRepository {

    override suspend fun storeCurrentBottomNavIndex(index: Int) {
        navigationDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.CURRENT_BOTTOM_NAV_INDEX] = index
        }
    }

    override fun getCurrentBottomNavIndex(): Flow<Int> {
        return navigationDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.CURRENT_BOTTOM_NAV_INDEX] ?: 0
            }
    }

    override suspend fun storeLastVisitedPage(page: String) {
        navigationDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.LAST_VISITED_PAGE] = page
        }
    }

    override fun getLastVisitedPage(): Flow<String> {
        return navigationDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.LAST_VISITED_PAGE] ?: ""
            }
    }

    override suspend fun storeNavigationHistory(history: String) {
        navigationDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.NAVIGATION_HISTORY] = history
        }
    }

    override fun getNavigationHistory(): Flow<String> {
        return navigationDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.NAVIGATION_HISTORY] ?: ""
            }
    }

    override suspend fun storeShowGuide(show: Boolean) {
        navigationDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.SHOW_GUIDE] = show
        }
    }

    override fun shouldShowGuide(): Flow<Boolean> {
        return navigationDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.SHOW_GUIDE] ?: true
            }
    }

    override suspend fun storeGuideCompleted(completed: Boolean) {
        navigationDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.GUIDE_COMPLETED] = completed
        }
    }

    override fun isGuideCompleted(): Flow<Boolean> {
        return navigationDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.GUIDE_COMPLETED] ?: false
            }
    }

    override suspend fun storeDeepLinkHandled(handled: Boolean) {
        navigationDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.DEEP_LINK_HANDLED] = handled
        }
    }

    override fun isDeepLinkHandled(): Flow<Boolean> {
        return navigationDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.DEEP_LINK_HANDLED] ?: false
            }
    }

    override suspend fun storeLaunchTarget(target: String) {
        navigationDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.LAUNCH_TARGET] = target
        }
    }

    override fun getLaunchTarget(): Flow<String> {
        return navigationDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.LAUNCH_TARGET] ?: ""
            }
    }

    override fun getNavigationPreferences(): Flow<NavigationPreferences> {
        return navigationDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                NavigationPreferences(
                    currentBottomNavIndex = preferences[DataStorePreKeysConfig.CURRENT_BOTTOM_NAV_INDEX] ?: 0,
                    lastVisitedPage = preferences[DataStorePreKeysConfig.LAST_VISITED_PAGE] ?: "",
                    navigationHistory = preferences[DataStorePreKeysConfig.NAVIGATION_HISTORY] ?: "",
                    showGuide = preferences[DataStorePreKeysConfig.SHOW_GUIDE] ?: true,
                    guideCompleted = preferences[DataStorePreKeysConfig.GUIDE_COMPLETED] ?: false,
                    deepLinkHandled = preferences[DataStorePreKeysConfig.DEEP_LINK_HANDLED] ?: false,
                    launchTarget = preferences[DataStorePreKeysConfig.LAUNCH_TARGET] ?: ""
                )
            }
    }

    override suspend fun updateNavigationPreferences(preferences: NavigationPreferences) {
        navigationDataStore.edit { dataStorePreferences ->
            dataStorePreferences[DataStorePreKeysConfig.CURRENT_BOTTOM_NAV_INDEX] = preferences.currentBottomNavIndex
            dataStorePreferences[DataStorePreKeysConfig.LAST_VISITED_PAGE] = preferences.lastVisitedPage
            dataStorePreferences[DataStorePreKeysConfig.NAVIGATION_HISTORY] = preferences.navigationHistory
            dataStorePreferences[DataStorePreKeysConfig.SHOW_GUIDE] = preferences.showGuide
            dataStorePreferences[DataStorePreKeysConfig.GUIDE_COMPLETED] = preferences.guideCompleted
            dataStorePreferences[DataStorePreKeysConfig.DEEP_LINK_HANDLED] = preferences.deepLinkHandled
            dataStorePreferences[DataStorePreKeysConfig.LAUNCH_TARGET] = preferences.launchTarget
        }
    }

    override suspend fun resetNavigationSettings() {
        navigationDataStore.edit { preferences ->
            preferences.clear()
        }
    }
}