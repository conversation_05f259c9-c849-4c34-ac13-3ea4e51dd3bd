package com.healthlink.hms.core.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import com.healthlink.hms.core.data.datastore.DataStorePreKeysConfig
import com.healthlink.hms.core.data.datastore.HealthDataDataStore
import com.healthlink.hms.core.model.store.HealthDataPreferences
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 健康数据相关Repository实现
 * Created by imaginedays on 2024/12/19
 */
@Singleton
class DefaultHealthDataRepository @Inject constructor(
    @HealthDataDataStore private val healthDataDataStore: DataStore<Preferences>
) : HealthDataRepository {

    override suspend fun storeDataSyncEnabled(enabled: Boolean) {
        healthDataDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.DATA_SYNC_ENABLED] = enabled
        }
    }

    override fun isDataSyncEnabled(): Flow<Boolean> {
        return healthDataDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.DATA_SYNC_ENABLED] ?: true
            }
    }

    override suspend fun storeLastSyncTime(timestamp: Long) {
        healthDataDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.LAST_SYNC_TIME] = timestamp
        }
    }

    override fun getLastSyncTime(): Flow<Long> {
        return healthDataDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.LAST_SYNC_TIME] ?: 0L
            }
    }

    override suspend fun storeSyncInterval(intervalMinutes: Int) {
        healthDataDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.SYNC_INTERVAL] = intervalMinutes
        }
    }

    override fun getSyncInterval(): Flow<Int> {
        return healthDataDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.SYNC_INTERVAL] ?: 30
            }
    }

    override suspend fun storeWifiOnlySyncEnabled(enabled: Boolean) {
        healthDataDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.WIFI_ONLY_SYNC_ENABLED] = enabled
        }
    }

    override fun isWifiOnlySyncEnabled(): Flow<Boolean> {
        return healthDataDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.WIFI_ONLY_SYNC_ENABLED] ?: false
            }
    }

    override suspend fun storeAutoBackupEnabled(enabled: Boolean) {
        healthDataDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.AUTO_BACKUP_ENABLED] = enabled
        }
    }

    override fun isAutoBackupEnabled(): Flow<Boolean> {
        return healthDataDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.AUTO_BACKUP_ENABLED] ?: true
            }
    }

    override suspend fun storeDataCompressionEnabled(enabled: Boolean) {
        healthDataDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.DATA_COMPRESSION_ENABLED] = enabled
        }
    }

    override fun isDataCompressionEnabled(): Flow<Boolean> {
        return healthDataDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.DATA_COMPRESSION_ENABLED] ?: true
            }
    }

    override suspend fun storeDataEncryptionEnabled(enabled: Boolean) {
        healthDataDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.DATA_ENCRYPTION_ENABLED] = enabled
        }
    }

    override fun isDataEncryptionEnabled(): Flow<Boolean> {
        return healthDataDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.DATA_ENCRYPTION_ENABLED] ?: false
            }
    }

    override suspend fun storeDataRetentionDays(days: Int) {
        healthDataDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.DATA_RETENTION_DAYS] = days
        }
    }

    override fun getDataRetentionDays(): Flow<Int> {
        return healthDataDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.DATA_RETENTION_DAYS] ?: 365
            }
    }

    override fun getHealthDataPreferences(): Flow<HealthDataPreferences> {
        return healthDataDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                HealthDataPreferences(
                    phoneDoctorNumber = preferences[DataStorePreKeysConfig.PHONE_DOCTOR_NUMBER] ?: "",
                    bindDoctorService = preferences[DataStorePreKeysConfig.BIND_DOCTOR_SERVICE] ?: false,
                    isPhoneDoctorMember = preferences[DataStorePreKeysConfig.IS_PHONE_DOCTOR_MEMBER] ?: false,
                    healthReportScoreChangeIsUp = preferences[DataStorePreKeysConfig.HEALTH_REPORT_SCORE_CHANGE_IS_UP] ?: false,
                    healthTips = preferences[DataStorePreKeysConfig.HEALTH_TIPS] ?: "",
                    healthReportNotify = preferences[DataStorePreKeysConfig.HEALTH_REPORT_NOTIFY] ?: false,
                    vinCode = preferences[DataStorePreKeysConfig.VIN_CODE] ?: "",
                    dataSyncEnabled = preferences[DataStorePreKeysConfig.DATA_SYNC_ENABLED] ?: true,
                    lastSyncTime = preferences[DataStorePreKeysConfig.LAST_SYNC_TIME] ?: 0L,
                    syncInterval = preferences[DataStorePreKeysConfig.SYNC_INTERVAL] ?: 30,
                    wifiOnlySyncEnabled = preferences[DataStorePreKeysConfig.WIFI_ONLY_SYNC_ENABLED] ?: false,
                    autoBackupEnabled = preferences[DataStorePreKeysConfig.AUTO_BACKUP_ENABLED] ?: true,
                    dataCompressionEnabled = preferences[DataStorePreKeysConfig.DATA_COMPRESSION_ENABLED] ?: true,
                    dataEncryptionEnabled = preferences[DataStorePreKeysConfig.DATA_ENCRYPTION_ENABLED] ?: false,
                    dataRetentionDays = preferences[DataStorePreKeysConfig.DATA_RETENTION_DAYS] ?: 365
                )
            }
    }

    override suspend fun updateHealthDataPreferences(preferences: HealthDataPreferences) {
        healthDataDataStore.edit { dataStorePreferences ->
            dataStorePreferences[DataStorePreKeysConfig.PHONE_DOCTOR_NUMBER] = preferences.phoneDoctorNumber
            dataStorePreferences[DataStorePreKeysConfig.BIND_DOCTOR_SERVICE] = preferences.bindDoctorService
            dataStorePreferences[DataStorePreKeysConfig.IS_PHONE_DOCTOR_MEMBER] = preferences.isPhoneDoctorMember
            dataStorePreferences[DataStorePreKeysConfig.HEALTH_REPORT_SCORE_CHANGE_IS_UP] = preferences.healthReportScoreChangeIsUp
            dataStorePreferences[DataStorePreKeysConfig.HEALTH_TIPS] = preferences.healthTips
            dataStorePreferences[DataStorePreKeysConfig.HEALTH_REPORT_NOTIFY] = preferences.healthReportNotify
            dataStorePreferences[DataStorePreKeysConfig.VIN_CODE] = preferences.vinCode
            dataStorePreferences[DataStorePreKeysConfig.DATA_SYNC_ENABLED] = preferences.dataSyncEnabled
            dataStorePreferences[DataStorePreKeysConfig.LAST_SYNC_TIME] = preferences.lastSyncTime
            dataStorePreferences[DataStorePreKeysConfig.SYNC_INTERVAL] = preferences.syncInterval
            dataStorePreferences[DataStorePreKeysConfig.WIFI_ONLY_SYNC_ENABLED] = preferences.wifiOnlySyncEnabled
            dataStorePreferences[DataStorePreKeysConfig.AUTO_BACKUP_ENABLED] = preferences.autoBackupEnabled
            dataStorePreferences[DataStorePreKeysConfig.DATA_COMPRESSION_ENABLED] = preferences.dataCompressionEnabled
            dataStorePreferences[DataStorePreKeysConfig.DATA_ENCRYPTION_ENABLED] = preferences.dataEncryptionEnabled
            dataStorePreferences[DataStorePreKeysConfig.DATA_RETENTION_DAYS] = preferences.dataRetentionDays
        }
    }

    override suspend fun resetHealthDataSettings() {
        healthDataDataStore.edit { preferences ->
            preferences.clear()
        }
    }
}