package com.healthlink.hms.core.data.repository

import com.healthlink.hms.core.model.store.NavigationPreferences
import kotlinx.coroutines.flow.Flow

/**
 * 导航相关数据Repository接口
 * Created by imaginedays on 2024/12/19
 */
interface NavigationRepository {
    
    // 当前选中的底部导航索引
    suspend fun storeCurrentBottomNavIndex(index: Int)
    fun getCurrentBottomNavIndex(): Flow<Int>
    
    // 上次访问的页面
    suspend fun storeLastVisitedPage(page: String)
    fun getLastVisitedPage(): Flow<String>
    
    // 导航历史记录
    suspend fun storeNavigationHistory(history: String)
    fun getNavigationHistory(): Flow<String>
    
    // 是否显示引导页
    suspend fun storeShowGuide(show: Boolean)
    fun shouldShowGuide(): Flow<Boolean>
    
    // 引导页完成状态
    suspend fun storeGuideCompleted(completed: Boolean)
    fun isGuideCompleted(): Flow<Boolean>
    
    // 深度链接处理
    suspend fun storeDeepLinkHandled(handled: Boolean)
    fun isDeepLinkHandled(): Flow<Boolean>
    
    // 启动页跳转目标
    suspend fun storeLaunchTarget(target: String)
    fun getLaunchTarget(): Flow<String>
    
    // 获取所有导航设置
    fun getNavigationPreferences(): Flow<NavigationPreferences>
    
    // 批量更新导航设置
    suspend fun updateNavigationPreferences(preferences: NavigationPreferences)
    
    // 重置导航设置
    suspend fun resetNavigationSettings()
}