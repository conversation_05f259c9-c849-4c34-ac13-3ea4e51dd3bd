package com.healthlink.hms.core.data.di

import com.healthlink.hms.core.data.repository.DefaultHealthAuthorityRepository
import com.healthlink.hms.core.data.repository.DefaultHealthDataRepository
import com.healthlink.hms.core.data.repository.DefaultNavigationRepository
import com.healthlink.hms.core.data.repository.DefaultSceneEngineRepository
import com.healthlink.hms.core.data.repository.DefaultSystemRepository
import com.healthlink.hms.core.data.repository.DefaultUserRepository
import com.healthlink.hms.core.data.repository.HealthAuthorityRepository
import com.healthlink.hms.core.data.repository.HealthDataRepository
import com.healthlink.hms.core.data.repository.NavigationRepository
import com.healthlink.hms.core.data.repository.SceneEngineRepository
import com.healthlink.hms.core.data.repository.SystemRepository
import com.healthlink.hms.core.data.repository.UserRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Repository绑定模块
 * 将Repository接口与其实现类进行依赖注入绑定
 * Created by imaginedays on 2024/12/19
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class LocalDataModule {
    /**
     * 绑定用户Repository
     */
    @Binds
    @Singleton
    abstract fun bindUserRepository(
        defaultUserRepository: DefaultUserRepository
    ): UserRepository
    
    /**
     * 绑定健康权限Repository
     */
    @Binds
    @Singleton
    abstract fun bindHealthAuthorityRepository(
        defaultHealthAuthorityRepository: DefaultHealthAuthorityRepository
    ): HealthAuthorityRepository

    /**
     * 绑定系统设置Repository
     */
    @Binds
    @Singleton
    abstract fun bindSystemRepository(
        defaultSystemRepository: DefaultSystemRepository
    ): SystemRepository

    /**
     * 绑定导航Repository
     */
    @Binds
    @Singleton
    abstract fun bindNavigationRepository(
        defaultNavigationRepository: DefaultNavigationRepository
    ): NavigationRepository

    /**
     * 绑定场景引擎Repository
     */
    @Binds
    @Singleton
    abstract fun bindSceneEngineRepository(
        defaultSceneEngineRepository: DefaultSceneEngineRepository
    ): SceneEngineRepository

    /**
     * 绑定健康数据Repository
     */
    @Binds
    @Singleton
    abstract fun bindHealthDataRepository(
        defaultHealthDataRepository: DefaultHealthDataRepository
    ): HealthDataRepository
}