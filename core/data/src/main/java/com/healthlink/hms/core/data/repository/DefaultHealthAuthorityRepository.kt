package com.healthlink.hms.core.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import com.healthlink.hms.core.data.datastore.DataStorePreKeysConfig
import com.healthlink.hms.core.data.datastore.HealthAuthorityDataStore
import com.healthlink.hms.core.model.store.HealthAuthorityPreferences
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 健康权限相关数据Repository实现
 * Created by imaginedays on 2024/12/19
 */
@Singleton
class DefaultHealthAuthorityRepository @Inject constructor(
    @HealthAuthorityDataStore private val healthAuthorityDataStore: DataStore<Preferences>
) : HealthAuthorityRepository {

    override suspend fun storePrivacyPolicyAccepted(accepted: Boolean) {
        healthAuthorityDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.PRIVACY_POLICY_ACCEPTED] = accepted
        }
    }

    override fun isPrivacyPolicyAccepted(): Flow<Boolean> {
        return healthAuthorityDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.PRIVACY_POLICY_ACCEPTED] ?: false
            }
    }

    override suspend fun storeHeartRatePermission(granted: Boolean) {
        healthAuthorityDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.HEART_RATE_PERMISSION] = granted
        }
    }

    override fun isHeartRatePermissionGranted(): Flow<Boolean> {
        return healthAuthorityDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.HEART_RATE_PERMISSION] ?: false
            }
    }

    override suspend fun storeSleepPermission(granted: Boolean) {
        healthAuthorityDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.SLEEP_PERMISSION] = granted
        }
    }

    override fun isSleepPermissionGranted(): Flow<Boolean> {
        return healthAuthorityDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.SLEEP_PERMISSION] ?: false
            }
    }

    override suspend fun storeExercisePermission(granted: Boolean) {
        healthAuthorityDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.EXERCISE_PERMISSION] = granted
        }
    }

    override fun isExercisePermissionGranted(): Flow<Boolean> {
        return healthAuthorityDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.EXERCISE_PERMISSION] ?: false
            }
    }

    override suspend fun storeHealthDataPermission(granted: Boolean) {
        healthAuthorityDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.HEALTH_DATA_PERMISSION] = granted
        }
    }

    override fun isHealthDataPermissionGranted(): Flow<Boolean> {
        return healthAuthorityDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.HEALTH_DATA_PERMISSION] ?: false
            }
    }

    override suspend fun storeLocationPermission(granted: Boolean) {
        healthAuthorityDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.LOCATION_PERMISSION] = granted
        }
    }

    override fun isLocationPermissionGranted(): Flow<Boolean> {
        return healthAuthorityDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.LOCATION_PERMISSION] ?: false
            }
    }

    override suspend fun storeNotificationPermission(granted: Boolean) {
        healthAuthorityDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.NOTIFICATION_PERMISSION] = granted
        }
    }

    override fun isNotificationPermissionGranted(): Flow<Boolean> {
        return healthAuthorityDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.NOTIFICATION_PERMISSION] ?: false
            }
    }

    override fun getHealthAuthorityPreferences(): Flow<HealthAuthorityPreferences> {
        return healthAuthorityDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                HealthAuthorityPreferences(
                    privacyPolicyAccepted = preferences[DataStorePreKeysConfig.PRIVACY_POLICY_ACCEPTED] ?: false,
                    heartRatePermission = preferences[DataStorePreKeysConfig.HEART_RATE_PERMISSION] ?: false,
                    sleepPermission = preferences[DataStorePreKeysConfig.SLEEP_PERMISSION] ?: false,
                    exercisePermission = preferences[DataStorePreKeysConfig.EXERCISE_PERMISSION] ?: false,
                    healthDataPermission = preferences[DataStorePreKeysConfig.HEALTH_DATA_PERMISSION] ?: false,
                    locationPermission = preferences[DataStorePreKeysConfig.LOCATION_PERMISSION] ?: false,
                    notificationPermission = preferences[DataStorePreKeysConfig.NOTIFICATION_PERMISSION] ?: false
                )
            }
    }

    override suspend fun updateHealthAuthorityPreferences(preferences: HealthAuthorityPreferences) {
        healthAuthorityDataStore.edit { dataStorePreferences ->
            dataStorePreferences[DataStorePreKeysConfig.PRIVACY_POLICY_ACCEPTED] = preferences.privacyPolicyAccepted
            dataStorePreferences[DataStorePreKeysConfig.HEART_RATE_PERMISSION] = preferences.heartRatePermission
            dataStorePreferences[DataStorePreKeysConfig.SLEEP_PERMISSION] = preferences.sleepPermission
            dataStorePreferences[DataStorePreKeysConfig.EXERCISE_PERMISSION] = preferences.exercisePermission
            dataStorePreferences[DataStorePreKeysConfig.HEALTH_DATA_PERMISSION] = preferences.healthDataPermission
            dataStorePreferences[DataStorePreKeysConfig.LOCATION_PERMISSION] = preferences.locationPermission
            dataStorePreferences[DataStorePreKeysConfig.NOTIFICATION_PERMISSION] = preferences.notificationPermission
        }
    }

    override suspend fun clearAllPermissions() {
        healthAuthorityDataStore.edit { preferences ->
            preferences.clear()
        }
    }
}