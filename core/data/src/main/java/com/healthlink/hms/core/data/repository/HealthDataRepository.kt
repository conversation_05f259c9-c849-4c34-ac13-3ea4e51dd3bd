package com.healthlink.hms.core.data.repository

import com.healthlink.hms.core.model.store.HealthDataPreferences
import kotlinx.coroutines.flow.Flow

/**
 * 健康数据相关Repository接口
 * Created by imaginedays on 2024/12/19
 */
interface HealthDataRepository {
    
    // 数据同步开关
    suspend fun storeDataSyncEnabled(enabled: Boolean)
    fun isDataSyncEnabled(): Flow<Boolean>
    
    // 最后同步时间
    suspend fun storeLastSyncTime(timestamp: Long)
    fun getLastSyncTime(): Flow<Long>
    
    // 同步间隔（分钟）
    suspend fun storeSyncInterval(intervalMinutes: Int)
    fun getSyncInterval(): Flow<Int>
    
    // WiFi同步开关
    suspend fun storeWifiOnlySyncEnabled(enabled: Boolean)
    fun isWifiOnlySyncEnabled(): Flow<Boolean>
    
    // 自动备份开关
    suspend fun storeAutoBackupEnabled(enabled: Boolean)
    fun isAutoBackupEnabled(): Flow<Boolean>
    
    // 数据压缩开关
    suspend fun storeDataCompressionEnabled(enabled: Boolean)
    fun isDataCompressionEnabled(): Flow<Boolean>
    
    // 数据加密开关
    suspend fun storeDataEncryptionEnabled(enabled: Boolean)
    fun isDataEncryptionEnabled(): Flow<Boolean>
    
    // 数据保留天数
    suspend fun storeDataRetentionDays(days: Int)
    fun getDataRetentionDays(): Flow<Int>
    
    // 获取所有健康数据设置
    fun getHealthDataPreferences(): Flow<HealthDataPreferences>
    
    // 批量更新健康数据设置
    suspend fun updateHealthDataPreferences(preferences: HealthDataPreferences)
    
    // 重置健康数据设置
    suspend fun resetHealthDataSettings()
}