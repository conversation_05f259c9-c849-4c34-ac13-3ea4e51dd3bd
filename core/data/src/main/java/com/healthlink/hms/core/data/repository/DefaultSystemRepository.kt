package com.healthlink.hms.core.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import com.healthlink.hms.core.data.datastore.SystemDataStore
import com.healthlink.hms.core.data.datastore.DataStorePreKeysConfig
import com.healthlink.hms.core.model.store.SystemPreferences
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 系统设置相关数据Repository实现
 * Created by imaginedays on 2024/12/19
 */
@Singleton
class DefaultSystemRepository @Inject constructor(
    @SystemDataStore private val systemDataStore: DataStore<Preferences>
) : SystemRepository {

    override suspend fun storeAppVersion(version: String) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.APP_VERSION] = version
        }
    }

    override fun getAppVersion(): Flow<String> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.APP_VERSION] ?: ""
            }
    }

    override suspend fun storeVersionCode(versionCode: Int) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.VERSION_CODE] = versionCode
        }
    }

    override fun getVersionCode(): Flow<Int> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.VERSION_CODE] ?: 0
            }
    }

    override suspend fun storeFirstLaunch(isFirst: Boolean) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.FIRST_LAUNCH] = isFirst
        }
    }

    override fun isFirstLaunch(): Flow<Boolean> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.FIRST_LAUNCH] ?: true
            }
    }

    override suspend fun storeFirstLaunchAfterBoot(isFirst: Boolean) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.FIRST_LAUNCH_AFTER_BOOT] = isFirst
        }
    }

    override fun isFirstLaunchAfterBoot(): Flow<Boolean> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.FIRST_LAUNCH_AFTER_BOOT] ?: true
            }
    }

    override suspend fun storeDebugMode(enabled: Boolean) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.DEBUG_MODE] = enabled
        }
    }

    override fun isDebugModeEnabled(): Flow<Boolean> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.DEBUG_MODE] ?: false
            }
    }

    override suspend fun storeLogLevel(level: Int) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.LOG_LEVEL] = level
        }
    }

    override fun getLogLevel(): Flow<Int> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.LOG_LEVEL] ?: 0
            }
    }

    override suspend fun storeBaseUrl(url: String) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.BASE_URL] = url
        }
    }

    override fun getBaseUrl(): Flow<String> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.BASE_URL] ?: ""
            }
    }

    override suspend fun storeApiTimeout(timeout: Long) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.API_TIMEOUT] = timeout
        }
    }

    override fun getApiTimeout(): Flow<Long> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.API_TIMEOUT] ?: 30000L
            }
    }

    override suspend fun storeCacheSize(size: Long) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.CACHE_SIZE] = size
        }
    }

    override fun getCacheSize(): Flow<Long> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.CACHE_SIZE] ?: 50 * 1024 * 1024L // 50MB
            }
    }

    override suspend fun storeCacheEnabled(enabled: Boolean) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.CACHE_ENABLED] = enabled
        }
    }

    override fun isCacheEnabled(): Flow<Boolean> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.CACHE_ENABLED] ?: true
            }
    }

    override suspend fun storeThemeMode(mode: Int) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.THEME_MODE] = mode
        }
    }

    override fun getThemeMode(): Flow<Int> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.THEME_MODE] ?: 0 // 默认跟随系统
            }
    }

    override suspend fun storeLanguage(language: String) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.LANGUAGE] = language
        }
    }

    override fun getLanguage(): Flow<String> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.LANGUAGE] ?: "zh"
            }
    }

    override fun getSystemPreferences(): Flow<SystemPreferences> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                SystemPreferences(
                    appVersion = preferences[DataStorePreKeysConfig.APP_VERSION] ?: "",
                    versionCode = preferences[DataStorePreKeysConfig.VERSION_CODE] ?: 0,
                    firstLaunch = preferences[DataStorePreKeysConfig.FIRST_LAUNCH] ?: true,
                    firstLaunchAfterBoot = preferences[DataStorePreKeysConfig.FIRST_LAUNCH_AFTER_BOOT] ?: true,
                    debugMode = preferences[DataStorePreKeysConfig.DEBUG_MODE] ?: false,
                    logLevel = preferences[DataStorePreKeysConfig.LOG_LEVEL] ?: 0,
                    baseUrl = preferences[DataStorePreKeysConfig.BASE_URL] ?: "",
                    apiTimeout = preferences[DataStorePreKeysConfig.API_TIMEOUT] ?: 30000L,
                    cacheSize = preferences[DataStorePreKeysConfig.CACHE_SIZE] ?: 50 * 1024 * 1024L,
                    cacheEnabled = preferences[DataStorePreKeysConfig.CACHE_ENABLED] ?: true,
                    themeMode = preferences[DataStorePreKeysConfig.THEME_MODE] ?: 0,
                    language = preferences[DataStorePreKeysConfig.LANGUAGE] ?: "zh",
                    isNotificationOpen = preferences[DataStorePreKeysConfig.IS_NOTIFICATION_OPEN] ?: false
                )
            }
    }

    override suspend fun updateSystemPreferences(preferences: SystemPreferences) {
        systemDataStore.edit { dataStorePreferences ->
            dataStorePreferences[DataStorePreKeysConfig.APP_VERSION] = preferences.appVersion
            dataStorePreferences[DataStorePreKeysConfig.VERSION_CODE] = preferences.versionCode
            dataStorePreferences[DataStorePreKeysConfig.FIRST_LAUNCH] = preferences.firstLaunch
            dataStorePreferences[DataStorePreKeysConfig.FIRST_LAUNCH_AFTER_BOOT] = preferences.firstLaunchAfterBoot
            dataStorePreferences[DataStorePreKeysConfig.DEBUG_MODE] = preferences.debugMode
            dataStorePreferences[DataStorePreKeysConfig.LOG_LEVEL] = preferences.logLevel
            dataStorePreferences[DataStorePreKeysConfig.BASE_URL] = preferences.baseUrl
            dataStorePreferences[DataStorePreKeysConfig.API_TIMEOUT] = preferences.apiTimeout
            dataStorePreferences[DataStorePreKeysConfig.CACHE_SIZE] = preferences.cacheSize
            dataStorePreferences[DataStorePreKeysConfig.CACHE_ENABLED] = preferences.cacheEnabled
            dataStorePreferences[DataStorePreKeysConfig.THEME_MODE] = preferences.themeMode
            dataStorePreferences[DataStorePreKeysConfig.LANGUAGE] = preferences.language
            dataStorePreferences[DataStorePreKeysConfig.IS_NOTIFICATION_OPEN] = preferences.isNotificationOpen
        }
    }

    override suspend fun storeNotificationOpen(isOn: Boolean) {
        systemDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.IS_NOTIFICATION_OPEN] = isOn
        }
    }

    override fun getNotificationState(): Flow<Boolean> {
        return systemDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.IS_NOTIFICATION_OPEN] ?: false
            }
    }

    override suspend fun resetSystemSettings() {
        systemDataStore.edit { preferences ->
            preferences.clear()
        }
    }
}