package com.healthlink.hms.core.data.repository

import com.healthlink.hms.core.model.store.HealthAuthorityPreferences
import kotlinx.coroutines.flow.Flow

/**
 * 健康权限相关数据Repository接口
 * Created by imaginedays on 2024/12/19
 */
interface HealthAuthorityRepository {
    
    // 隐私政策
    suspend fun storePrivacyPolicyAccepted(accepted: Boolean)
    fun isPrivacyPolicyAccepted(): Flow<Boolean>
    
    // 心率权限
    suspend fun storeHeartRatePermission(granted: Boolean)
    fun isHeartRatePermissionGranted(): Flow<Boolean>
    
    // 睡眠权限
    suspend fun storeSleepPermission(granted: Boolean)
    fun isSleepPermissionGranted(): Flow<Boolean>
    
    // 运动权限
    suspend fun storeExercisePermission(granted: Boolean)
    fun isExercisePermissionGranted(): Flow<Boolean>
    
    // 健康数据权限
    suspend fun storeHealthDataPermission(granted: Boolean)
    fun isHealthDataPermissionGranted(): Flow<Boolean>
    
    // 位置权限
    suspend fun storeLocationPermission(granted: Boolean)
    fun isLocationPermissionGranted(): Flow<Boolean>
    
    // 通知权限
    suspend fun storeNotificationPermission(granted: Boolean)
    fun isNotificationPermissionGranted(): Flow<Boolean>
    
    // 获取所有健康权限信息
    fun getHealthAuthorityPreferences(): Flow<HealthAuthorityPreferences>
    
    // 批量更新健康权限
    suspend fun updateHealthAuthorityPreferences(preferences: HealthAuthorityPreferences)
    
    // 清除所有权限数据
    suspend fun clearAllPermissions()
}