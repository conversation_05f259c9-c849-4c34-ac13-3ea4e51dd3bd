package com.healthlink.hms.core.data.datastore

import androidx.datastore.preferences.core.*

object DataStorePreKeysConfig {

    // 用户相关
    val USER_ID = stringPreferencesKey("user_id")
    val USER_TOKEN = stringPreferencesKey("user_token")
    val USER_NICKNAME = stringPreferencesKey("user_nickname")
    val USER_BIRTH_YEAR = intPreferencesKey("user_birth_year")
    val USER_BIRTH_MONTH = intPreferencesKey("user_birth_month")
    val USER_HEIGHT = intPreferencesKey("user_height")
    val USER_WEIGHT = intPreferencesKey("user_weight")
    val USER_GENDER = intPreferencesKey("user_gender")
    val USER_PRIVACY_AGREEMENT = booleanPreferencesKey("user_privacy_agreement") // 用户是否同意隐私政策,默认为no


    // 健康权限相关
    val HEART_RATE_AUTHORITY = booleanPreferencesKey("heart_rate_authority")
    val SLEEP_AUTHORITY = booleanPreferencesKey("sleep_authority")
    val STRESS_AUTHORITY = booleanPreferencesKey("stress_authority")
    val BLOOD_OXYGEN_AUTHORITY = booleanPreferencesKey("blood_oxygen_authority")
    val BLOOD_PRESSURE_AUTHORITY = booleanPreferencesKey("blood_pressure_authority")
    val BODY_TEMPERATURE_AUTHORITY = booleanPreferencesKey("body_temperature_authority")
    val LOCATION_PERMISSION = booleanPreferencesKey("location_permission")
    val NOTIFICATION_PERMISSION = booleanPreferencesKey("notification_permission")
    val EXERCISE_PERMISSION = booleanPreferencesKey("exercise_permission")
    val HEALTH_DATA_PERMISSION = booleanPreferencesKey("health_data_permission")
    val HEART_RATE_PERMISSION = booleanPreferencesKey("heart_rate_permission")
    val SLEEP_PERMISSION = booleanPreferencesKey("sleep_permission")
    val PRIVACY_POLICY_ACCEPTED = booleanPreferencesKey("privacy_policy_accepted")

    // 系统设置相关
    val IS_NOTIFICATION_OPEN = booleanPreferencesKey("is_notification_open")
    val IS_OPEN_WARN_MODE = booleanPreferencesKey("is_open_warn_mode")
    val LAST_THEME_MODE = intPreferencesKey("last_theme_mode")
    val PRIVACY_POLICY_AGREED = booleanPreferencesKey("privacy_policy_agreed")
    val LAST_POWER_MODE = stringPreferencesKey("last_power_mode")
    val BANNER_TIPS = stringPreferencesKey("banner_tips")
    val ALREADY_BANNER_TIPS = stringPreferencesKey("already_banner_tips")
    val LAST_BANNER_POSITION = intPreferencesKey("last_banner_position")
    
    // 应用系统设置
    val APP_VERSION = stringPreferencesKey("app_version")
    val VERSION_CODE = intPreferencesKey("version_code")
    val FIRST_LAUNCH = booleanPreferencesKey("first_launch")
    val FIRST_LAUNCH_AFTER_BOOT = booleanPreferencesKey("first_launch_after_boot")
    val DEBUG_MODE = booleanPreferencesKey("debug_mode")
    val LOG_LEVEL = intPreferencesKey("log_level")
    val BASE_URL = stringPreferencesKey("base_url")
    val API_TIMEOUT = longPreferencesKey("api_timeout")
    val CACHE_SIZE = longPreferencesKey("cache_size")
    val CACHE_ENABLED = booleanPreferencesKey("cache_enabled")
    val THEME_MODE = intPreferencesKey("theme_mode")
    val LANGUAGE = stringPreferencesKey("language")

    // 导航相关
    val DESTINATION_ELEVATION = stringPreferencesKey("destination_elevation")
    val CUR_LOCATION_ELEVATION = stringPreferencesKey("cur_location_elevation")
    val NAVIGATION_GUIDE_STATUS = booleanPreferencesKey("navigation_guide_status")
    val NAVIGATION_GUIDE_STATUS_TIMESTAMP = longPreferencesKey("navigation_guide_status_timestamp")
    val NAVIGATION_INFO = stringPreferencesKey("navigation_info")
    val LONGITUDE = doublePreferencesKey("longitude")
    val LATITUDE = doublePreferencesKey("latitude")
    val DEEP_LINK_HANDLED = booleanPreferencesKey("deep_link_handled")
    val LAUNCH_TARGET = stringPreferencesKey("launch_target")
    val SHOW_GUIDE = booleanPreferencesKey("show_guide")
    val GUIDE_COMPLETED = booleanPreferencesKey("guide_completed")
    val LAST_VISITED_PAGE = stringPreferencesKey("last_visited_page")
    val NAVIGATION_HISTORY = stringPreferencesKey("navigation_history")
    val CURRENT_BOTTOM_NAV_INDEX = intPreferencesKey("current_bottom_nav_index")

    // 场景引擎相关
    val IS_FIRST_LAUNCH_APP_AFTER_BOOT = booleanPreferencesKey("is_first_launch_app_after_boot")
    val SCENE_REMAIN_RUN_COUNT = stringPreferencesKey("scene_remain_run_count")
    val LAST_P_TIME = stringPreferencesKey("last_p_time")
    val WELCOME_TIME = stringPreferencesKey("welcome_time")
    val LAST_GEAR_STATUS_INFO = stringPreferencesKey("last_gear_status_info")
    val POWER_MODE_2_START_TIME = stringPreferencesKey("power_mode_2_start_time")
    val LAST_DOCTOR_CALL_TIME = stringPreferencesKey("last_doctor_call_time")
    val HEALTH_REPORT_SCORE_CHANGE_IS_UP = booleanPreferencesKey("health_report_score_change_is_up")
    
    // 场景引擎核心设置
    val SCENE_ENGINE_ENABLED = booleanPreferencesKey("scene_engine_enabled")
    val CURRENT_SCENE_ID = stringPreferencesKey("current_scene_id")
    val SCENE_CONFIG = stringPreferencesKey("scene_config")
    val SCENE_DATA_SYNC_TIME = longPreferencesKey("scene_data_sync_time")
    
    // 场景推荐和自动切换
    val SCENE_RECOMMENDATION_ENABLED = booleanPreferencesKey("scene_recommendation_enabled")
    val AUTO_SCENE_SWITCH_ENABLED = booleanPreferencesKey("auto_scene_switch_enabled")
    val SCENE_LEARNING_ENABLED = booleanPreferencesKey("scene_learning_enabled")
    val SCENE_DATA_COLLECTION_ENABLED = booleanPreferencesKey("scene_data_collection_enabled")


    // 健康数据相关
    val PHONE_DOCTOR_NUMBER = stringPreferencesKey("phone_doctor_number")
    val BIND_DOCTOR_SERVICE = booleanPreferencesKey("bind_doctor_service")
    val IS_PHONE_DOCTOR_MEMBER = booleanPreferencesKey("is_phone_doctor_member")
    val HEALTH_TIPS = stringPreferencesKey("health_tips")
    val HEALTH_REPORT_NOTIFY = booleanPreferencesKey("health_report_notify")
    val VIN_CODE = stringPreferencesKey("vin_code")
    
    // 健康数据同步设置
    val DATA_SYNC_ENABLED = booleanPreferencesKey("data_sync_enabled")
    val LAST_SYNC_TIME = longPreferencesKey("last_sync_time")
    val SYNC_INTERVAL = intPreferencesKey("sync_interval")
    val WIFI_ONLY_SYNC_ENABLED = booleanPreferencesKey("wifi_only_sync_enabled")
    val AUTO_BACKUP_ENABLED = booleanPreferencesKey("auto_backup_enabled")
    val DATA_COMPRESSION_ENABLED = booleanPreferencesKey("data_compression_enabled")
    val DATA_ENCRYPTION_ENABLED = booleanPreferencesKey("data_encryption_enabled")
    val DATA_RETENTION_DAYS = intPreferencesKey("data_retention_days")
}