package com.healthlink.hms.core.data.datastore

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * 分层DataStore配置模块
 * 按功能模块划分不同的DataStore实例，支持按需加载
 * Created by imaginedays on 2024/12/19
 */

// 用户相关DataStore
private val Context.userDataStore: DataStore<Preferences> by preferencesDataStore(name = "hms_user_preferences")

// 健康权限DataStore
private val Context.healthAuthorityDataStore: DataStore<Preferences> by preferencesDataStore(name = "hms_health_authority")

// 系统设置DataStore
private val Context.systemDataStore: DataStore<Preferences> by preferencesDataStore(name = "hms_system_preferences")

// 导航相关DataStore
private val Context.navigationDataStore: DataStore<Preferences> by preferencesDataStore(name = "hms_navigation_preferences")

// 场景引擎DataStore
private val Context.sceneEngineDataStore: DataStore<Preferences> by preferencesDataStore(name = "hms_scene_engine")

// 健康数据DataStore
private val Context.healthDataStore: DataStore<Preferences> by preferencesDataStore(name = "hms_health_data")

// 定义限定符注解
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class UserDataStore

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class HealthAuthorityDataStore

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class SystemDataStore

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class NavigationDataStore

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class SceneEngineDataStore

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class HealthDataDataStore

@Module
@InstallIn(SingletonComponent::class)
object DataStoreModule {
    
    @Provides
    @Singleton
    @UserDataStore
    fun provideUserDataStore(@ApplicationContext context: Context): DataStore<Preferences> =
        context.userDataStore

    @Provides
    @Singleton
    @HealthAuthorityDataStore
    fun provideHealthAuthorityDataStore(@ApplicationContext context: Context): DataStore<Preferences> =
        context.healthAuthorityDataStore

    @Provides
    @Singleton
    @SystemDataStore
    fun provideSystemDataStore(@ApplicationContext context: Context): DataStore<Preferences> =
        context.systemDataStore

    @Provides
    @Singleton
    @NavigationDataStore
    fun provideNavigationDataStore(@ApplicationContext context: Context): DataStore<Preferences> =
        context.navigationDataStore

    @Provides
    @Singleton
    @SceneEngineDataStore
    fun provideSceneEngineDataStore(@ApplicationContext context: Context): DataStore<Preferences> =
        context.sceneEngineDataStore

    @Provides
    @Singleton
    @HealthDataDataStore
    fun provideHealthDataStore(@ApplicationContext context: Context): DataStore<Preferences> =
        context.healthDataStore
}