package com.healthlink.hms.core.data.repository

import com.healthlink.hms.core.model.store.SystemPreferences
import kotlinx.coroutines.flow.Flow

/**
 * 系统设置相关数据Repository接口
 * Created by imaginedays on 2024/12/19
 */
interface SystemRepository {
    
    // 应用版本信息
    suspend fun storeAppVersion(version: String)
    fun getAppVersion(): Flow<String>
    
    suspend fun storeVersionCode(versionCode: Int)
    fun getVersionCode(): Flow<Int>
    
    // 首次启动标识
    suspend fun storeFirstLaunch(isFirst: Boolean)
    fun isFirstLaunch(): Flow<Boolean>
    
    // 开机首次启动标识
    suspend fun storeFirstLaunchAfterBoot(isFirst: Boolean)
    fun isFirstLaunchAfterBoot(): Flow<Boolean>
    
    // 调试模式
    suspend fun storeDebugMode(enabled: Boolean)
    fun isDebugModeEnabled(): Flow<Boolean>
    
    // 日志级别
    suspend fun storeLogLevel(level: Int)
    fun getLogLevel(): Flow<Int>
    
    // 网络配置
    suspend fun storeBaseUrl(url: String)
    fun getBaseUrl(): Flow<String>
    
    suspend fun storeApiTimeout(timeout: Long)
    fun getApiTimeout(): Flow<Long>
    
    // 缓存配置
    suspend fun storeCacheSize(size: Long)
    fun getCacheSize(): Flow<Long>
    
    suspend fun storeCacheEnabled(enabled: Boolean)
    fun isCacheEnabled(): Flow<Boolean>
    
    // 主题设置
    suspend fun storeThemeMode(mode: Int)
    fun getThemeMode(): Flow<Int>
    
    // 语言设置
    suspend fun storeLanguage(language: String)
    fun getLanguage(): Flow<String>
    
    // 获取所有系统设置
    fun getSystemPreferences(): Flow<SystemPreferences>
    
    // 批量更新系统设置
    suspend fun updateSystemPreferences(preferences: SystemPreferences)
    
    // 通知开关
    suspend fun storeNotificationOpen(isOn: Boolean)
    fun getNotificationState(): Flow<Boolean>
    
    // 重置系统设置
    suspend fun resetSystemSettings()
}