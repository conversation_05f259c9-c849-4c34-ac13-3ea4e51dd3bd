package com.healthlink.hms.core.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import com.healthlink.hms.core.data.datastore.DataStorePreKeysConfig
import com.healthlink.hms.core.data.datastore.SceneEngineDataStore
import com.healthlink.hms.core.model.store.SceneEnginePreferences
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 场景引擎相关数据Repository实现
 * Created by imaginedays on 2024/12/19
 */
@Singleton
class DefaultSceneEngineRepository @Inject constructor(
    @SceneEngineDataStore private val sceneEngineDataStore: DataStore<Preferences>
) : SceneEngineRepository {

    override suspend fun storeSceneEngineEnabled(enabled: Boolean) {
        sceneEngineDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.SCENE_ENGINE_ENABLED] = enabled
        }
    }

    override fun isSceneEngineEnabled(): Flow<Boolean> {
        return sceneEngineDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.SCENE_ENGINE_ENABLED] ?: false
            }
    }

    override suspend fun storeCurrentSceneId(sceneId: String) {
        sceneEngineDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.CURRENT_SCENE_ID] = sceneId
        }
    }

    override fun getCurrentSceneId(): Flow<String> {
        return sceneEngineDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.CURRENT_SCENE_ID] ?: ""
            }
    }

    override suspend fun storeSceneConfig(config: String) {
        sceneEngineDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.SCENE_CONFIG] = config
        }
    }

    override fun getSceneConfig(): Flow<String> {
        return sceneEngineDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.SCENE_CONFIG] ?: ""
            }
    }

    override suspend fun storeSceneDataSyncTime(timestamp: Long) {
        sceneEngineDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.SCENE_DATA_SYNC_TIME] = timestamp
        }
    }

    override fun getSceneDataSyncTime(): Flow<Long> {
        return sceneEngineDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.SCENE_DATA_SYNC_TIME] ?: 0L
            }
    }

    override suspend fun storeSceneRecommendationEnabled(enabled: Boolean) {
        sceneEngineDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.SCENE_RECOMMENDATION_ENABLED] = enabled
        }
    }

    override fun isSceneRecommendationEnabled(): Flow<Boolean> {
        return sceneEngineDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.SCENE_RECOMMENDATION_ENABLED] ?: true
            }
    }

    override suspend fun storeAutoSceneSwitchEnabled(enabled: Boolean) {
        sceneEngineDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.AUTO_SCENE_SWITCH_ENABLED] = enabled
        }
    }

    override fun isAutoSceneSwitchEnabled(): Flow<Boolean> {
        return sceneEngineDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.AUTO_SCENE_SWITCH_ENABLED] ?: false
            }
    }

    override suspend fun storeSceneLearningEnabled(enabled: Boolean) {
        sceneEngineDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.SCENE_LEARNING_ENABLED] = enabled
        }
    }

    override fun isSceneLearningEnabled(): Flow<Boolean> {
        return sceneEngineDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.SCENE_LEARNING_ENABLED] ?: true
            }
    }

    override suspend fun storeSceneDataCollectionEnabled(enabled: Boolean) {
        sceneEngineDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.SCENE_DATA_COLLECTION_ENABLED] = enabled
        }
    }

    override fun isSceneDataCollectionEnabled(): Flow<Boolean> {
        return sceneEngineDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.SCENE_DATA_COLLECTION_ENABLED] ?: true
            }
    }

    override fun getSceneEnginePreferences(): Flow<SceneEnginePreferences> {
        return sceneEngineDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                SceneEnginePreferences(
                    sceneEngineEnabled = preferences[DataStorePreKeysConfig.SCENE_ENGINE_ENABLED] ?: false,
                    currentSceneId = preferences[DataStorePreKeysConfig.CURRENT_SCENE_ID] ?: "",
                    sceneConfig = preferences[DataStorePreKeysConfig.SCENE_CONFIG] ?: "",
                    sceneDataSyncTime = preferences[DataStorePreKeysConfig.SCENE_DATA_SYNC_TIME] ?: 0L,
                    sceneRecommendationEnabled = preferences[DataStorePreKeysConfig.SCENE_RECOMMENDATION_ENABLED] ?: true,
                    autoSceneSwitchEnabled = preferences[DataStorePreKeysConfig.AUTO_SCENE_SWITCH_ENABLED] ?: false,
                    sceneLearningEnabled = preferences[DataStorePreKeysConfig.SCENE_LEARNING_ENABLED] ?: true,
                    sceneDataCollectionEnabled = preferences[DataStorePreKeysConfig.SCENE_DATA_COLLECTION_ENABLED] ?: true
                )
            }
    }

    override suspend fun updateSceneEnginePreferences(preferences: SceneEnginePreferences) {
        sceneEngineDataStore.edit { dataStorePreferences ->
            dataStorePreferences[DataStorePreKeysConfig.SCENE_ENGINE_ENABLED] = preferences.sceneEngineEnabled
            dataStorePreferences[DataStorePreKeysConfig.CURRENT_SCENE_ID] = preferences.currentSceneId
            dataStorePreferences[DataStorePreKeysConfig.SCENE_CONFIG] = preferences.sceneConfig
            dataStorePreferences[DataStorePreKeysConfig.SCENE_DATA_SYNC_TIME] = preferences.sceneDataSyncTime
            dataStorePreferences[DataStorePreKeysConfig.SCENE_RECOMMENDATION_ENABLED] = preferences.sceneRecommendationEnabled
            dataStorePreferences[DataStorePreKeysConfig.AUTO_SCENE_SWITCH_ENABLED] = preferences.autoSceneSwitchEnabled
            dataStorePreferences[DataStorePreKeysConfig.SCENE_LEARNING_ENABLED] = preferences.sceneLearningEnabled
            dataStorePreferences[DataStorePreKeysConfig.SCENE_DATA_COLLECTION_ENABLED] = preferences.sceneDataCollectionEnabled
        }
    }

    override suspend fun resetSceneEngineSettings() {
        sceneEngineDataStore.edit { preferences ->
            preferences.clear()
        }
    }
}