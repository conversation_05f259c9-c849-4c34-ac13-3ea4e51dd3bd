package com.healthlink.hms.core.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import com.healthlink.hms.core.data.datastore.DataStorePreKeysConfig
import com.healthlink.hms.core.data.datastore.UserDataStore
import com.healthlink.hms.core.model.store.UserPreferences
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户相关数据Repository实现
 * Created by imaginedays on 2024/12/19
 */
@Singleton
class DefaultUserRepository @Inject constructor(
    @UserDataStore private val userDataStore: DataStore<Preferences>
) : UserRepository {
    
    override suspend fun storeUserId(userId: String) {
        userDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.USER_ID] = userId
        }
    }
    
    override fun getUserId(): Flow<String> {
        return userDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.USER_ID] ?: ""
            }
    }
    
    override suspend fun clearUserId() {
        userDataStore.edit { preferences ->
            preferences.remove(DataStorePreKeysConfig.USER_ID)
        }
    }
    
    override suspend fun storeUserToken(token: String) {
        userDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.USER_TOKEN] = token
        }
    }
    
    override fun getUserToken(): Flow<String> {
        return userDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.USER_TOKEN] ?: ""
            }
    }
    
    override suspend fun clearUserToken() {
        userDataStore.edit { preferences ->
            preferences.remove(DataStorePreKeysConfig.USER_TOKEN)
        }
    }
    
    override suspend fun storeUserInfo(userInfo: UserPreferences) {
        userDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.USER_ID] = userInfo.userId
            preferences[DataStorePreKeysConfig.USER_TOKEN] = userInfo.userToken
            preferences[DataStorePreKeysConfig.USER_NICKNAME] = userInfo.nickname
            preferences[DataStorePreKeysConfig.USER_BIRTH_YEAR] = userInfo.birthYear
            preferences[DataStorePreKeysConfig.USER_BIRTH_MONTH] = userInfo.birthMonth
            preferences[DataStorePreKeysConfig.USER_HEIGHT] = userInfo.height
            preferences[DataStorePreKeysConfig.USER_WEIGHT] = userInfo.weight
            preferences[DataStorePreKeysConfig.USER_GENDER] = userInfo.gender
        }
    }
    
    override fun getUserInfo(): Flow<UserPreferences> {
        return userDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                UserPreferences(
                    userId = preferences[DataStorePreKeysConfig.USER_ID] ?: "",
                    userToken = preferences[DataStorePreKeysConfig.USER_TOKEN] ?: "",
                    nickname = preferences[DataStorePreKeysConfig.USER_NICKNAME] ?: "",
                    birthYear = preferences[DataStorePreKeysConfig.USER_BIRTH_YEAR] ?: 0,
                    birthMonth = preferences[DataStorePreKeysConfig.USER_BIRTH_MONTH] ?: 0,
                    height = preferences[DataStorePreKeysConfig.USER_HEIGHT] ?: 0,
                    weight = preferences[DataStorePreKeysConfig.USER_WEIGHT] ?: 0,
                    gender = preferences[DataStorePreKeysConfig.USER_GENDER] ?: 0
                )
            }
    }
    
    override suspend fun storeNickname(nickname: String) {
        userDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.USER_NICKNAME] = nickname
        }
    }
    
    override fun getNickname(): Flow<String> {
        return userDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.USER_NICKNAME] ?: ""
            }
    }
    
    override suspend fun storeBirthYear(year: Int) {
        userDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.USER_BIRTH_YEAR] = year
        }
    }
    
    override fun getBirthYear(): Flow<Int> {
        return userDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.USER_BIRTH_YEAR] ?: 0
            }
    }
    
    override suspend fun storeBirthMonth(month: Int) {
        userDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.USER_BIRTH_MONTH] = month
        }
    }
    
    override fun getBirthMonth(): Flow<Int> {
        return userDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.USER_BIRTH_MONTH] ?: 0
            }
    }
    
    override suspend fun storeHeight(height: Int) {
        userDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.USER_HEIGHT] = height
        }
    }
    
    override fun getHeight(): Flow<Int> {
        return userDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.USER_HEIGHT] ?: 0
            }
    }
    
    override suspend fun storeWeight(weight: Int) {
        userDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.USER_WEIGHT] = weight
        }
    }
    
    override fun getWeight(): Flow<Int> {
        return userDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.USER_WEIGHT] ?: 0
            }
    }
    
    override suspend fun storeGender(gender: Int) {
        userDataStore.edit { preferences ->
            preferences[DataStorePreKeysConfig.USER_GENDER] = gender
        }
    }
    
    override fun getGender(): Flow<Int> {
        return userDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                preferences[DataStorePreKeysConfig.USER_GENDER] ?: 0
            }
    }
    
    override fun isVisitorMode(): Flow<Boolean> {
        return combine(
            getUserId(),
            getUserToken()
        ) { userId, token ->
            userId.isEmpty() || token.isEmpty()
        }
    }
    
    override suspend fun clearAllUserData() {
        userDataStore.edit { preferences ->
            preferences.clear()
        }
    }
}