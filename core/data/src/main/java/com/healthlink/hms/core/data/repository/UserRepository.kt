package com.healthlink.hms.core.data.repository

import com.healthlink.hms.core.model.store.UserPreferences
import kotlinx.coroutines.flow.Flow

/**
 * 用户相关数据Repository接口
 * Created by imaginedays on 2024/12/19
 */
interface UserRepository {
    
    // 用户基本信息
    suspend fun storeUserId(userId: String)
    fun getUserId(): Flow<String>
    suspend fun clearUserId()
    
    suspend fun storeUserToken(token: String)
    fun getUserToken(): Flow<String>
    suspend fun clearUserToken()
    
    // 用户详细信息
    suspend fun storeUserInfo(userInfo: UserPreferences)
    fun getUserInfo(): Flow<UserPreferences>
    
    suspend fun storeNickname(nickname: String)
    fun getNickname(): Flow<String>
    
    suspend fun storeBirthYear(year: Int)
    fun getBirthYear(): Flow<Int>
    
    suspend fun storeBirthMonth(month: Int)
    fun getBirthMonth(): Flow<Int>
    
    suspend fun storeHeight(height: Int)
    fun getHeight(): Flow<Int>
    
    suspend fun storeWeight(weight: Int)
    fun getWeight(): Flow<Int>
    
    suspend fun storeGender(gender: Int)
    fun getGender(): Flow<Int>
    
    // 访客模式判断
    fun isVisitorMode(): Flow<Boolean>
    
    // 清除所有用户数据
    suspend fun clearAllUserData()
}