package com.healthlink.hms.core.data.repository

import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.init.InitInfoDTO
import com.healthlink.hms.core.network.api.ApiServiceKot
import com.healthlink.hms.core.network.di.IoDispatcher
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DefaultInitRepository @Inject constructor(
    private val apiService: ApiServiceKot,
    @IoDispatcher private val dispatcher: CoroutineDispatcher) : InitRepository {

   override fun getInitInfo(param: Map<String, String>?): Flow<Result<BaseResponse<InitInfoDTO>>> = flow {
        try {
            // 发起网络请求
            val response = apiService.getInitInfo(param)
            // 检查业务逻辑错误
            if (response.isSuccess()) {
                emit(Result.success(response))
            } else {
                emit(Result.failure(Exception(response.msg)))
            }
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }.flowOn(dispatcher)
}