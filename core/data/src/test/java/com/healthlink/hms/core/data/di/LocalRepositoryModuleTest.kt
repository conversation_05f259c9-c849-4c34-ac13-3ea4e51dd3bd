package com.healthlink.hms.core.data.di

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

/**
 * LocalDataModule 单元测试
 * 测试Repository绑定模块的正确性
 */
class LocalRepositoryModuleTest {

    private val defaultLocalDataRepository: DefaultLocalDataRepository = org.mockito.kotlin.mock()

    @Test
    fun `LocalRepositoryModule should be instantiable`() {
        // Given & When
        val module = object : LocalDataModule() {
            override fun bindLocalDataRepository(
                defaultLocalDataRepository: DefaultLocalDataRepository
            ): LocalDataRepository {
                return defaultLocalDataRepository
            }
        }

        // Then
        assertThat(module).isNotNull()
    }

    @Test
    fun `bindLocalDataRepository should return the same instance`() {
        // Given
        val module = object : LocalDataModule() {
            override fun bindLocalDataRepository(
                defaultLocalDataRepository: DefaultLocalDataRepository
            ): LocalDataRepository {
                return defaultLocalDataRepository
            }
        }

        // When
        val result = module.bindLocalDataRepository(defaultLocalDataRepository)

        // Then
        assertThat(result).isSameAs(defaultLocalDataRepository)
        assertThat(result).isInstanceOf(LocalDataRepository::class.java)
    }

    @Test
    fun `module should support multiple repository bindings`() {
        // Given
        val module = object : LocalDataModule() {
            override fun bindLocalDataRepository(
                defaultLocalDataRepository: DefaultLocalDataRepository
            ): LocalDataRepository {
                return defaultLocalDataRepository
            }
        }

        // When
        val result1 = module.bindLocalDataRepository(defaultLocalDataRepository)
        val result2 = module.bindLocalDataRepository(defaultLocalDataRepository)

        // Then
        assertThat(result1).isSameAs(result2)
        assertThat(result1).isInstanceOf(LocalDataRepository::class.java)
        assertThat(result2).isInstanceOf(LocalDataRepository::class.java)
    }
}