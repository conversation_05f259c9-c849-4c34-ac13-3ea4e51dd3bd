package com.healthlink.hms.core.data.di

import com.healthlink.hms.core.data.repository.DefaultInitRepository
import com.healthlink.hms.core.data.repository.InitRepository
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

/**
 * DataModule 单元测试
 * 测试DI模块的绑定是否正确
 */
class DataModuleTest {

    @Test
    fun `DataModule should be instantiable`() {
        // Given & When
        val dataModule = object : DataModule() {
            override fun bindsInitRepository(initRepository: DefaultInitRepository): InitRepository {
                return initRepository
            }
        }

        // Then
        assertThat(dataModule).isNotNull()
    }

    @Test
    fun `bindsInitRepository should return the same instance`() {
        // Given
        val defaultInitRepository = org.mockito.Mockito.mock(DefaultInitRepository::class.java)
        val dataModule = object : DataModule() {
            override fun bindsInitRepository(initRepository: DefaultInitRepository): InitRepository {
                return initRepository
            }
        }

        // When
        val result = dataModule.bindsInitRepository(defaultInitRepository)

        // Then
        assertThat(result).isSameAs(defaultInitRepository)
        assertThat(result).isInstanceOf(InitRepository::class.java)
        assertThat(result).isInstanceOf(DefaultInitRepository::class.java)
    }
}