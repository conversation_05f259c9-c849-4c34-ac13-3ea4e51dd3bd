package com.healthlink.hms.core.data.datastore

import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.doublePreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

/**
 * DataStorePreKeysConfig 单元测试
 * 测试DataStore配置键的正确性
 */
class DataStorePreKeysConfigTest {

    @Test
    fun `user related keys should have correct names and types`() {
        // 用户相关键
        assertThat(DataStorePreKeysConfig.USER_ID.name).isEqualTo("user_id")
        assertThat(DataStorePreKeysConfig.USER_ID).isInstanceOf(stringPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.USER_TOKEN.name).isEqualTo("user_token")
        assertThat(DataStorePreKeysConfig.USER_TOKEN).isInstanceOf(stringPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.USER_NICKNAME.name).isEqualTo("user_nickname")
        assertThat(DataStorePreKeysConfig.USER_NICKNAME).isInstanceOf(stringPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.USER_BIRTH_YEAR.name).isEqualTo("user_birth_year")
        assertThat(DataStorePreKeysConfig.USER_BIRTH_YEAR).isInstanceOf(intPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.USER_BIRTH_MONTH.name).isEqualTo("user_birth_month")
        assertThat(DataStorePreKeysConfig.USER_BIRTH_MONTH).isInstanceOf(intPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.USER_HEIGHT.name).isEqualTo("user_height")
        assertThat(DataStorePreKeysConfig.USER_HEIGHT).isInstanceOf(intPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.USER_WEIGHT.name).isEqualTo("user_weight")
        assertThat(DataStorePreKeysConfig.USER_WEIGHT).isInstanceOf(intPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.USER_GENDER.name).isEqualTo("user_gender")
        assertThat(DataStorePreKeysConfig.USER_GENDER).isInstanceOf(intPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.USER_PRIVACY_AGREEMENT.name).isEqualTo("user_privacy_agreement")
        assertThat(DataStorePreKeysConfig.USER_PRIVACY_AGREEMENT).isInstanceOf(booleanPreferencesKey("").javaClass)
    }

    @Test
    fun `health authority keys should have correct names and types`() {
        // 健康权限相关键
        assertThat(DataStorePreKeysConfig.HEART_RATE_AUTHORITY.name).isEqualTo("heart_rate_authority")
        assertThat(DataStorePreKeysConfig.HEART_RATE_AUTHORITY).isInstanceOf(booleanPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.SLEEP_AUTHORITY.name).isEqualTo("sleep_authority")
        assertThat(DataStorePreKeysConfig.SLEEP_AUTHORITY).isInstanceOf(booleanPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.STRESS_AUTHORITY.name).isEqualTo("stress_authority")
        assertThat(DataStorePreKeysConfig.STRESS_AUTHORITY).isInstanceOf(booleanPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.BLOOD_OXYGEN_AUTHORITY.name).isEqualTo("blood_oxygen_authority")
        assertThat(DataStorePreKeysConfig.BLOOD_OXYGEN_AUTHORITY).isInstanceOf(booleanPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.BLOOD_PRESSURE_AUTHORITY.name).isEqualTo("blood_pressure_authority")
        assertThat(DataStorePreKeysConfig.BLOOD_PRESSURE_AUTHORITY).isInstanceOf(booleanPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.BODY_TEMPERATURE_AUTHORITY.name).isEqualTo("body_temperature_authority")
        assertThat(DataStorePreKeysConfig.BODY_TEMPERATURE_AUTHORITY).isInstanceOf(booleanPreferencesKey("").javaClass)
    }

    @Test
    fun `system settings keys should have correct names and types`() {
        // 系统设置相关键
        assertThat(DataStorePreKeysConfig.IS_NOTIFICATION_OPEN.name).isEqualTo("is_notification_open")
        assertThat(DataStorePreKeysConfig.IS_NOTIFICATION_OPEN).isInstanceOf(booleanPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.IS_OPEN_WARN_MODE.name).isEqualTo("is_open_warn_mode")
        assertThat(DataStorePreKeysConfig.IS_OPEN_WARN_MODE).isInstanceOf(booleanPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.LAST_THEME_MODE.name).isEqualTo("last_theme_mode")
        assertThat(DataStorePreKeysConfig.LAST_THEME_MODE).isInstanceOf(intPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.PRIVACY_POLICY_AGREED.name).isEqualTo("privacy_policy_agreed")
        assertThat(DataStorePreKeysConfig.PRIVACY_POLICY_AGREED).isInstanceOf(booleanPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.LAST_POWER_MODE.name).isEqualTo("last_power_mode")
        assertThat(DataStorePreKeysConfig.LAST_POWER_MODE).isInstanceOf(stringPreferencesKey("").javaClass)
    }

    @Test
    fun `navigation keys should have correct names and types`() {
        // 导航相关键
        assertThat(DataStorePreKeysConfig.DESTINATION_ELEVATION.name).isEqualTo("destination_elevation")
        assertThat(DataStorePreKeysConfig.DESTINATION_ELEVATION).isInstanceOf(stringPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.CUR_LOCATION_ELEVATION.name).isEqualTo("cur_location_elevation")
        assertThat(DataStorePreKeysConfig.CUR_LOCATION_ELEVATION).isInstanceOf(stringPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.NAVIGATION_GUIDE_STATUS.name).isEqualTo("navigation_guide_status")
        assertThat(DataStorePreKeysConfig.NAVIGATION_GUIDE_STATUS).isInstanceOf(booleanPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.NAVIGATION_GUIDE_STATUS_TIMESTAMP.name).isEqualTo("navigation_guide_status_timestamp")
        assertThat(DataStorePreKeysConfig.NAVIGATION_GUIDE_STATUS_TIMESTAMP).isInstanceOf(longPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.LONGITUDE.name).isEqualTo("longitude")
        assertThat(DataStorePreKeysConfig.LONGITUDE).isInstanceOf(doublePreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.LATITUDE.name).isEqualTo("latitude")
        assertThat(DataStorePreKeysConfig.LATITUDE).isInstanceOf(doublePreferencesKey("").javaClass)
    }

    @Test
    fun `scene engine keys should have correct names and types`() {
        // 场景引擎相关键
        assertThat(DataStorePreKeysConfig.IS_FIRST_LAUNCH_APP_AFTER_BOOT.name).isEqualTo("is_first_launch_app_after_boot")
        assertThat(DataStorePreKeysConfig.IS_FIRST_LAUNCH_APP_AFTER_BOOT).isInstanceOf(booleanPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.SCENE_REMAIN_RUN_COUNT.name).isEqualTo("scene_remain_run_count")
        assertThat(DataStorePreKeysConfig.SCENE_REMAIN_RUN_COUNT).isInstanceOf(stringPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.LAST_P_TIME.name).isEqualTo("last_p_time")
        assertThat(DataStorePreKeysConfig.LAST_P_TIME).isInstanceOf(stringPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.WELCOME_TIME.name).isEqualTo("welcome_time")
        assertThat(DataStorePreKeysConfig.WELCOME_TIME).isInstanceOf(stringPreferencesKey("").javaClass)
    }

    @Test
    fun `health data keys should have correct names and types`() {
        // 健康数据相关键
        assertThat(DataStorePreKeysConfig.PHONE_DOCTOR_NUMBER.name).isEqualTo("phone_doctor_number")
        assertThat(DataStorePreKeysConfig.PHONE_DOCTOR_NUMBER).isInstanceOf(stringPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.BIND_DOCTOR_SERVICE.name).isEqualTo("bind_doctor_service")
        assertThat(DataStorePreKeysConfig.BIND_DOCTOR_SERVICE).isInstanceOf(booleanPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.IS_PHONE_DOCTOR_MEMBER.name).isEqualTo("is_phone_doctor_member")
        assertThat(DataStorePreKeysConfig.IS_PHONE_DOCTOR_MEMBER).isInstanceOf(booleanPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.HEALTH_TIPS.name).isEqualTo("health_tips")
        assertThat(DataStorePreKeysConfig.HEALTH_TIPS).isInstanceOf(stringPreferencesKey("").javaClass)
        
        assertThat(DataStorePreKeysConfig.VIN_CODE.name).isEqualTo("vin_code")
        assertThat(DataStorePreKeysConfig.VIN_CODE).isInstanceOf(stringPreferencesKey("").javaClass)
    }

    @Test
    fun `all keys should have unique names`() {
        // 收集所有键的名称
        val keyNames = setOf(
            DataStorePreKeysConfig.USER_ID.name,
            DataStorePreKeysConfig.USER_TOKEN.name,
            DataStorePreKeysConfig.USER_NICKNAME.name,
            DataStorePreKeysConfig.USER_BIRTH_YEAR.name,
            DataStorePreKeysConfig.USER_BIRTH_MONTH.name,
            DataStorePreKeysConfig.USER_HEIGHT.name,
            DataStorePreKeysConfig.USER_WEIGHT.name,
            DataStorePreKeysConfig.USER_GENDER.name,
            DataStorePreKeysConfig.USER_PRIVACY_AGREEMENT.name,
            DataStorePreKeysConfig.HEART_RATE_AUTHORITY.name,
            DataStorePreKeysConfig.SLEEP_AUTHORITY.name,
            DataStorePreKeysConfig.STRESS_AUTHORITY.name,
            DataStorePreKeysConfig.BLOOD_OXYGEN_AUTHORITY.name,
            DataStorePreKeysConfig.BLOOD_PRESSURE_AUTHORITY.name,
            DataStorePreKeysConfig.BODY_TEMPERATURE_AUTHORITY.name,
            DataStorePreKeysConfig.IS_NOTIFICATION_OPEN.name,
            DataStorePreKeysConfig.IS_OPEN_WARN_MODE.name,
            DataStorePreKeysConfig.LAST_THEME_MODE.name,
            DataStorePreKeysConfig.PRIVACY_POLICY_AGREED.name,
            DataStorePreKeysConfig.LAST_POWER_MODE.name,
            DataStorePreKeysConfig.DESTINATION_ELEVATION.name,
            DataStorePreKeysConfig.CUR_LOCATION_ELEVATION.name,
            DataStorePreKeysConfig.NAVIGATION_GUIDE_STATUS.name,
            DataStorePreKeysConfig.NAVIGATION_GUIDE_STATUS_TIMESTAMP.name,
            DataStorePreKeysConfig.LONGITUDE.name,
            DataStorePreKeysConfig.LATITUDE.name,
            DataStorePreKeysConfig.IS_FIRST_LAUNCH_APP_AFTER_BOOT.name,
            DataStorePreKeysConfig.SCENE_REMAIN_RUN_COUNT.name,
            DataStorePreKeysConfig.LAST_P_TIME.name,
            DataStorePreKeysConfig.WELCOME_TIME.name,
            DataStorePreKeysConfig.PHONE_DOCTOR_NUMBER.name,
            DataStorePreKeysConfig.BIND_DOCTOR_SERVICE.name,
            DataStorePreKeysConfig.IS_PHONE_DOCTOR_MEMBER.name,
            DataStorePreKeysConfig.HEALTH_TIPS.name,
            DataStorePreKeysConfig.VIN_CODE.name
        )
        
        // 验证所有键名都是唯一的
        assertThat(keyNames).hasSize(35) // 根据实际键的数量调整
    }
}