package com.healthlink.hms.core.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.emptyPreferences
import app.cash.turbine.test
import com.healthlink.hms.core.data.datastore.DataStorePreKeysConfig
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.whenever
import java.io.IOException
import java.net.SocketTimeoutException

/**
 * Flow操作和错误处理综合测试
 * 测试Repository中Flow操作的各种场景
 */
class FlowOperationsTest {

    private val userDataStore: DataStore<Preferences> = org.mockito.kotlin.mock()
    private val healthDataDataStore: DataStore<Preferences> = org.mockito.kotlin.mock()
    private val preferences: Preferences = org.mockito.kotlin.mock()

    private lateinit var userRepository: DefaultUserRepository
    private lateinit var healthDataRepository: DefaultHealthDataRepository

    @BeforeEach
    fun setup() {
        userRepository = DefaultUserRepository(userDataStore)
        healthDataRepository = DefaultHealthDataRepository(healthDataDataStore)
    }

    @Test
    fun `Flow should emit multiple values correctly`() = runTest {
        // Given
        val userId1 = "user1"
        val userId2 = "user2"
        val preferences1 = mockPreferences(DataStorePreKeysConfig.USER_ID, userId1)
        val preferences2 = mockPreferences(DataStorePreKeysConfig.USER_ID, userId2)
        
        whenever(userDataStore.data).thenReturn(flowOf(preferences1, preferences2))

        // When & Then
        userRepository.getUserId().test {
            assertThat(awaitItem()).isEqualTo(userId1)
            assertThat(awaitItem()).isEqualTo(userId2)
            awaitComplete()
        }
    }

    @Test
    fun `Flow should handle IOException gracefully`() = runTest {
        // Given
        val ioException = IOException("DataStore read error")
        whenever(userDataStore.data).thenReturn(flow { throw ioException })

        // When & Then
        userRepository.getUserId().test {
            assertThat(awaitItem()).isEmpty() // default value
            awaitComplete()
        }
    }

    @Test
    fun `Flow should propagate non-IOException`() = runTest {
        // Given
        val runtimeException = RuntimeException("Unexpected error")
        whenever(userDataStore.data).thenReturn(flow { throw runtimeException })

        // When & Then
        userRepository.getUserId().test {
            awaitError()
        }
    }

    @Test
    fun `Flow should handle network timeout in repository`() = runTest {
        // Given
        val timeoutException = SocketTimeoutException("Network timeout")
        whenever(userDataStore.data).thenReturn(flow { throw timeoutException })

        // When & Then
        userRepository.getUserId().test {
            awaitError()
        }
    }

    @Test
    fun `Flow should emit empty preferences correctly`() = runTest {
        // Given
        whenever(userDataStore.data).thenReturn(flowOf(emptyPreferences()))

        // When & Then
        userRepository.getUserId().test {
            assertThat(awaitItem()).isEmpty() // default value for missing key
            awaitComplete()
        }
    }

    @Test
    fun `Flow should handle rapid emissions`() = runTest {
        // Given
        val values = (1..10).map { "user$it" }
        val preferencesFlow = flow {
            values.forEach { userId ->
                emit(mockPreferences(DataStorePreKeysConfig.USER_ID, userId))
            }
        }
        whenever(userDataStore.data).thenReturn(preferencesFlow)

        // When & Then
        userRepository.getUserId().test {
            values.forEach { expectedUserId ->
                assertThat(awaitItem()).isEqualTo(expectedUserId)
            }
            awaitComplete()
        }
    }

    @Test
    fun `Flow should handle boolean preferences correctly`() = runTest {
        // Given
        whenever(preferences[DataStorePreKeysConfig.DATA_SYNC_ENABLED]).thenReturn(true)
        whenever(healthDataDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        healthDataRepository.isDataSyncEnabled().test {
            assertThat(awaitItem()).isTrue()
            awaitComplete()
        }
    }

    @Test
    fun `Flow should handle integer preferences correctly`() = runTest {
        // Given
        val syncInterval = 120
        whenever(preferences[DataStorePreKeysConfig.SYNC_INTERVAL]).thenReturn(syncInterval)
        whenever(healthDataDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        healthDataRepository.getSyncInterval().test {
            assertThat(awaitItem()).isEqualTo(syncInterval)
            awaitComplete()
        }
    }

    @Test
    fun `Flow should handle long preferences correctly`() = runTest {
        // Given
        val timestamp = System.currentTimeMillis()
        whenever(preferences[DataStorePreKeysConfig.SYNC_INTERVAL]).thenReturn(60)
        whenever(healthDataDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        healthDataRepository.getSyncInterval().test {
            assertThat(awaitItem()).isEqualTo(60)
            awaitComplete()
        }
    }

    @Test
    fun `Flow should handle null values with defaults`() = runTest {
        // Given
        whenever(preferences[DataStorePreKeysConfig.USER_ID]).thenReturn(null)
        whenever(preferences[DataStorePreKeysConfig.USER_TOKEN]).thenReturn(null)
        whenever(userDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        userRepository.isVisitorMode().test {
            assertThat(awaitItem()).isTrue() // both userId and token are empty
            awaitComplete()
        }
    }

    @Test
    fun `Flow should handle combine operations correctly`() = runTest {
        // Given
        whenever(preferences[DataStorePreKeysConfig.USER_ID]).thenReturn("test_user")
        whenever(preferences[DataStorePreKeysConfig.USER_TOKEN]).thenReturn("test_token")
        whenever(userDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        userRepository.isVisitorMode().test {
            assertThat(awaitItem()).isFalse() // both userId and token exist
            awaitComplete()
        }
    }

    private fun mockPreferences(key: Preferences.Key<String>, value: String): Preferences {
        val mockPrefs = org.mockito.kotlin.mock<Preferences>()
        whenever(mockPrefs[key]).thenReturn(value)
        return mockPrefs
    }
}