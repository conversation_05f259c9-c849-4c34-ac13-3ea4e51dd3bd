package com.healthlink.hms.core.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import app.cash.turbine.test
import com.healthlink.hms.core.data.datastore.DataStorePreKeysConfig
import com.healthlink.hms.core.model.store.HealthDataPreferences
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.whenever
import java.io.IOException

/**
 * DefaultHealthDataRepository 单元测试
 * 测试健康数据Repository的所有功能
 */
class DefaultHealthDataRepositoryTest {

    private val healthDataDataStore: DataStore<Preferences> = org.mockito.kotlin.mock()
    private val preferences: Preferences = org.mockito.kotlin.mock()

    private lateinit var repository: DefaultHealthDataRepository

    @BeforeEach
    fun setup() {
        repository = DefaultHealthDataRepository(healthDataDataStore)
    }

    @Test
    fun `storeDataSyncEnabled should store boolean value`() = runTest {
        // Given
        val enabled = true

        // When
        repository.storeDataSyncEnabled(enabled)

        // Then - test passes if no exception is thrown
    }

    @Test
    fun `isDataSyncEnabled should return stored value`() = runTest {
        // Given
        whenever(preferences[DataStorePreKeysConfig.DATA_SYNC_ENABLED]).thenReturn(true)
        whenever(healthDataDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.isDataSyncEnabled().test {
            assertThat(awaitItem()).isTrue()
            awaitComplete()
        }
    }

    @Test
    fun `isDataSyncEnabled should return default value when key not found`() = runTest {
        // Given
        whenever(preferences[DataStorePreKeysConfig.DATA_SYNC_ENABLED]).thenReturn(null)
        whenever(healthDataDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.isDataSyncEnabled().test {
            assertThat(awaitItem()).isTrue() // default value
            awaitComplete()
        }
    }

    @Test
    fun `isDataSyncEnabled should handle IOException gracefully`() = runTest {
        // Given
        whenever(healthDataDataStore.data).thenReturn(flowOf(emptyPreferences()))

        // When & Then
        repository.isDataSyncEnabled().test {
            assertThat(awaitItem()).isTrue() // default value
            awaitComplete()
        }
    }

    @Test
    fun `storeLastSyncTime should store timestamp`() = runTest {
        // Given
        val timestamp = 1234567890L

        // When
        repository.storeLastSyncTime(timestamp)

        // Then - test passes if no exception is thrown
    }

    @Test
    fun `getLastSyncTime should return stored timestamp`() = runTest {
        // Given
        val timestamp = 1234567890L
        whenever(preferences[DataStorePreKeysConfig.LAST_SYNC_TIME]).thenReturn(timestamp)
        whenever(healthDataDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.getLastSyncTime().test {
            assertThat(awaitItem()).isEqualTo(timestamp)
            awaitComplete()
        }
    }

    @Test
    fun `getHealthDataPreferences should return complete preferences object`() = runTest {
        // Given
        whenever(preferences[DataStorePreKeysConfig.DATA_SYNC_ENABLED]).thenReturn(false)
        whenever(preferences[DataStorePreKeysConfig.LAST_SYNC_TIME]).thenReturn(1234567890L)
        whenever(preferences[DataStorePreKeysConfig.SYNC_INTERVAL]).thenReturn(60)
        whenever(preferences[DataStorePreKeysConfig.WIFI_ONLY_SYNC_ENABLED]).thenReturn(true)
        whenever(preferences[DataStorePreKeysConfig.AUTO_BACKUP_ENABLED]).thenReturn(false)
        whenever(preferences[DataStorePreKeysConfig.DATA_COMPRESSION_ENABLED]).thenReturn(false)
        whenever(preferences[DataStorePreKeysConfig.DATA_ENCRYPTION_ENABLED]).thenReturn(false)
        whenever(preferences[DataStorePreKeysConfig.DATA_RETENTION_DAYS]).thenReturn(180)
        whenever(healthDataDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.getHealthDataPreferences().test {
            val result = awaitItem()
            assertThat(result.dataSyncEnabled).isFalse()
            assertThat(result.lastSyncTime).isEqualTo(1234567890L)
            assertThat(result.syncInterval).isEqualTo(60)
            assertThat(result.wifiOnlySyncEnabled).isTrue()
            assertThat(result.autoBackupEnabled).isFalse()
            assertThat(result.dataCompressionEnabled).isFalse()
            assertThat(result.dataEncryptionEnabled).isFalse()
            assertThat(result.dataRetentionDays).isEqualTo(180)
            awaitComplete()
        }
    }

    @Test
    fun `updateHealthDataPreferences should store all preferences`() = runTest {
        // Given
        val healthDataPreferences = HealthDataPreferences(
            dataSyncEnabled = false,
            lastSyncTime = 1234567890L,
            syncInterval = 60,
            wifiOnlySyncEnabled = true,
            autoBackupEnabled = false,
            dataCompressionEnabled = false,
            dataEncryptionEnabled = false,
            dataRetentionDays = 180
        )

        // When
        repository.updateHealthDataPreferences(healthDataPreferences)

        // Then - test passes if no exception is thrown
    }

    @Test
    fun `resetHealthDataSettings should clear all preferences`() = runTest {
        // When
        repository.resetHealthDataSettings()

        // Then - test passes if no exception is thrown
    }
}