package com.healthlink.hms.core.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.emptyPreferences
import app.cash.turbine.test
import com.healthlink.hms.core.data.datastore.DataStorePreKeysConfig
import com.healthlink.hms.core.model.store.UserPreferences
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.whenever

/**
 * DefaultUserRepository 单元测试
 * 测试用户数据Repository的所有功能
 */
class DefaultUserRepositoryTest {

    private val userDataStore: DataStore<Preferences> = org.mockito.kotlin.mock()
    private val preferences: Preferences = org.mockito.kotlin.mock()

    private lateinit var repository: DefaultUserRepository

    @BeforeEach
    fun setup() {
        repository = DefaultUserRepository(userDataStore)
    }

    @Test
    fun `storeUserId should store user id`() = runTest {
        // Given
        val userId = "test_user_123"

        // When
        repository.storeUserId(userId)

        // Then - test passes if no exception is thrown
    }

    @Test
    fun `getUserId should return stored user id`() = runTest {
        // Given
        val userId = "test_user_123"
        whenever(preferences[DataStorePreKeysConfig.USER_ID]).thenReturn(userId)
        whenever(userDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.getUserId().test {
            assertThat(awaitItem()).isEqualTo(userId)
            awaitComplete()
        }
    }

    @Test
    fun `getUserId should return empty string when not found`() = runTest {
        // Given
        whenever(preferences[DataStorePreKeysConfig.USER_ID]).thenReturn(null)
        whenever(userDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.getUserId().test {
            assertThat(awaitItem()).isEmpty()
            awaitComplete()
        }
    }

    @Test
    fun `clearUserId should remove user id`() = runTest {
        // When
        repository.clearUserId()

        // Then - test passes if no exception is thrown
    }

    @Test
    fun `storeUserToken should store token`() = runTest {
        // Given
        val token = "test_token_abc123"

        // When
        repository.storeUserToken(token)

        // Then - test passes if no exception is thrown
    }

    @Test
    fun `getUserToken should return stored token`() = runTest {
        // Given
        val token = "test_token_abc123"
        whenever(preferences[DataStorePreKeysConfig.USER_TOKEN]).thenReturn(token)
        whenever(userDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.getUserToken().test {
            assertThat(awaitItem()).isEqualTo(token)
            awaitComplete()
        }
    }

    @Test
    fun `clearUserToken should remove token`() = runTest {
        // When
        repository.clearUserToken()

        // Then - test passes if no exception is thrown
    }

    @Test
    fun `storeUserInfo should store complete user information`() = runTest {
        // Given
        val userInfo = UserPreferences(
            userId = "test_user_123",
            userToken = "test_token_abc123",
            nickname = "TestUser",
            birthYear = 1990,
            birthMonth = 5,
            height = 175,
            weight = 70,
            gender = 1
        )

        // When
        repository.storeUserInfo(userInfo)

        // Then - test passes if no exception is thrown
    }

    @Test
    fun `getUserInfo should return complete user information`() = runTest {
        // Given
        whenever(preferences[DataStorePreKeysConfig.USER_ID]).thenReturn("test_user_123")
        whenever(preferences[DataStorePreKeysConfig.USER_TOKEN]).thenReturn("test_token_abc123")
        whenever(preferences[DataStorePreKeysConfig.USER_NICKNAME]).thenReturn("TestUser")
        whenever(preferences[DataStorePreKeysConfig.USER_BIRTH_YEAR]).thenReturn(1990)
        whenever(preferences[DataStorePreKeysConfig.USER_BIRTH_MONTH]).thenReturn(5)
        whenever(preferences[DataStorePreKeysConfig.USER_HEIGHT]).thenReturn(175)
        whenever(preferences[DataStorePreKeysConfig.USER_WEIGHT]).thenReturn(70)
        whenever(preferences[DataStorePreKeysConfig.USER_GENDER]).thenReturn(1)
        whenever(userDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.getUserInfo().test {
            val result = awaitItem()
            assertThat(result.userId).isEqualTo("test_user_123")
            assertThat(result.userToken).isEqualTo("test_token_abc123")
            assertThat(result.nickname).isEqualTo("TestUser")
            assertThat(result.birthYear).isEqualTo(1990)
            assertThat(result.birthMonth).isEqualTo(5)
            assertThat(result.height).isEqualTo(175)
            assertThat(result.weight).isEqualTo(70)
            assertThat(result.gender).isEqualTo(1)
            awaitComplete()
        }
    }

    @Test
    fun `getUserInfo should return default values when not found`() = runTest {
        // Given
        whenever(preferences[DataStorePreKeysConfig.USER_ID]).thenReturn(null)
        whenever(preferences[DataStorePreKeysConfig.USER_TOKEN]).thenReturn(null)
        whenever(preferences[DataStorePreKeysConfig.USER_NICKNAME]).thenReturn(null)
        whenever(preferences[DataStorePreKeysConfig.USER_BIRTH_YEAR]).thenReturn(null)
        whenever(preferences[DataStorePreKeysConfig.USER_BIRTH_MONTH]).thenReturn(null)
        whenever(preferences[DataStorePreKeysConfig.USER_HEIGHT]).thenReturn(null)
        whenever(preferences[DataStorePreKeysConfig.USER_WEIGHT]).thenReturn(null)
        whenever(preferences[DataStorePreKeysConfig.USER_GENDER]).thenReturn(null)
        whenever(userDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.getUserInfo().test {
            val result = awaitItem()
            assertThat(result.userId).isEmpty()
            assertThat(result.userToken).isEmpty()
            assertThat(result.nickname).isEmpty()
            assertThat(result.birthYear).isZero()
            assertThat(result.birthMonth).isZero()
            assertThat(result.height).isZero()
            assertThat(result.weight).isZero()
            assertThat(result.gender).isZero()
            awaitComplete()
        }
    }

    @Test
    fun `isVisitorMode should return true when userId is empty`() = runTest {
        // Given
        whenever(preferences[DataStorePreKeysConfig.USER_ID]).thenReturn("")
        whenever(preferences[DataStorePreKeysConfig.USER_TOKEN]).thenReturn("test_token")
        whenever(userDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.isVisitorMode().test {
            assertThat(awaitItem()).isTrue()
            awaitComplete()
        }
    }

    @Test
    fun `isVisitorMode should return true when token is empty`() = runTest {
        // Given
        whenever(preferences[DataStorePreKeysConfig.USER_ID]).thenReturn("test_user")
        whenever(preferences[DataStorePreKeysConfig.USER_TOKEN]).thenReturn("")
        whenever(userDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.isVisitorMode().test {
            assertThat(awaitItem()).isTrue()
            awaitComplete()
        }
    }

    @Test
    fun `isVisitorMode should return false when both userId and token exist`() = runTest {
        // Given
        whenever(preferences[DataStorePreKeysConfig.USER_ID]).thenReturn("test_user")
        whenever(preferences[DataStorePreKeysConfig.USER_TOKEN]).thenReturn("test_token")
        whenever(userDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.isVisitorMode().test {
            assertThat(awaitItem()).isFalse()
            awaitComplete()
        }
    }

    @Test
    fun `clearAllUserData should clear all preferences`() = runTest {
        // When
        repository.clearAllUserData()

        // Then - test passes if no exception is thrown
    }

    @Test
    fun `storeNickname should store nickname`() = runTest {
        // Given
        val nickname = "TestNickname"

        // When
        repository.storeNickname(nickname)

        // Then - test passes if no exception is thrown
    }

    @Test
    fun `getNickname should return stored nickname`() = runTest {
        // Given
        val nickname = "TestNickname"
        whenever(preferences[DataStorePreKeysConfig.USER_NICKNAME]).thenReturn(nickname)
        whenever(userDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.getNickname().test {
            assertThat(awaitItem()).isEqualTo(nickname)
            awaitComplete()
        }
    }

    @Test
    fun `storeHeight should store height value`() = runTest {
        // Given
        val height = 180

        // When
        repository.storeHeight(height)

        // Then - test passes if no exception is thrown
    }

    @Test
    fun `getHeight should return stored height`() = runTest {
        // Given
        val height = 180
        whenever(preferences[DataStorePreKeysConfig.USER_HEIGHT]).thenReturn(height)
        whenever(userDataStore.data).thenReturn(flowOf(preferences))

        // When & Then
        repository.getHeight().test {
            assertThat(awaitItem()).isEqualTo(height)
            awaitComplete()
        }
    }
}