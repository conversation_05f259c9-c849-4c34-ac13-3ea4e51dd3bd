package com.healthlink.hms.core.data.repository

import app.cash.turbine.test
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.init.InitInfoDTO
import com.healthlink.hms.core.network.api.ApiServiceKot
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.whenever

/**
 * DefaultInitRepository 单元测试
 * 测试初始化Repository的网络请求功能
 */
class DefaultInitRepositoryTest {

    private val apiService: ApiServiceKot = org.mockito.kotlin.mock()

    private lateinit var testDispatcher: CoroutineDispatcher
    private lateinit var repository: DefaultInitRepository

    @BeforeEach
    fun setup() {
        testDispatcher = StandardTestDispatcher()
        repository = DefaultInitRepository(apiService, testDispatcher)
    }

    @Test
    fun `getInitInfo should return success result when api call succeeds`() = runTest {
        // Given
        val param = mapOf("key" to "value")
        val initInfoDTO = InitInfoDTO(
            userId = "test_user_123",
            membershipId = "test_membership_456",
            isPhoneDoctorMember = true
        )
        val successResponse = BaseResponse(
            code = "200",
            msg = "Success",
            data = initInfoDTO
        )
        whenever(apiService.getInitInfo(param)).thenReturn(successResponse)

        // When & Then
        repository.getInitInfo(param).test {
            val result = awaitItem()
            assertThat(result.isSuccess).isTrue()
            assertThat(result.getOrNull()).isEqualTo(successResponse)
            awaitComplete()
        }
    }

    @Test
    fun `getInitInfo should return failure result when api response indicates error`() = runTest {
        // Given
        val param = mapOf("key" to "value")
        val errorResponse = BaseResponse<InitInfoDTO>(
            code = "400",
            msg = "Error occurred",
            data = null
        )
        whenever(apiService.getInitInfo(param)).thenReturn(errorResponse)

        // When & Then
        repository.getInitInfo(param).test {
            val result = awaitItem()
            assertThat(result.isFailure).isTrue()
            assertThat(result.exceptionOrNull()?.message).isEqualTo("Error occurred")
            awaitComplete()
        }
    }

    @Test
    fun `getInitInfo should return failure result when api call throws exception`() = runTest {
        // Given
        val param = mapOf("key" to "value")
        val exception = RuntimeException("Network error")
        whenever(apiService.getInitInfo(param)).thenThrow(exception)

        // When & Then
        repository.getInitInfo(param).test {
            val result = awaitItem()
            assertThat(result.isFailure).isTrue()
            assertThat(result.exceptionOrNull()).isEqualTo(exception)
            awaitComplete()
        }
    }

    @Test
    fun `getInitInfo should handle null param`() = runTest {
        // Given
        val initInfoDTO = InitInfoDTO(
            userId = "test_user_123",
            membershipId = "test_membership_456",
            isPhoneDoctorMember = true
        )
        val successResponse = BaseResponse(
            code = "200",
            msg = "Success",
            data = initInfoDTO
        )
        whenever(apiService.getInitInfo(null)).thenReturn(successResponse)

        // When & Then
        repository.getInitInfo(null).test {
            val result = awaitItem()
            assertThat(result.isSuccess).isTrue()
            assertThat(result.getOrNull()).isEqualTo(successResponse)
            awaitComplete()
        }
    }

    @Test
    fun `getInitInfo should handle empty param map`() = runTest {
        // Given
        val emptyParam = emptyMap<String, String>()
        val initInfoDTO = InitInfoDTO(
            userId = "test_user_123",
            membershipId = "test_membership_456",
            isPhoneDoctorMember = true
        )
        val successResponse = BaseResponse(
            code = "200",
            msg = "Success",
            data = initInfoDTO
        )
        whenever(apiService.getInitInfo(emptyParam)).thenReturn(successResponse)

        // When & Then
        repository.getInitInfo(emptyParam).test {
            val result = awaitItem()
            assertThat(result.isSuccess).isTrue()
            assertThat(result.getOrNull()).isEqualTo(successResponse)
            awaitComplete()
        }
    }
}