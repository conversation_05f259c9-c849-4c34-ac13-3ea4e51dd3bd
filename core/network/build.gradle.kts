plugins {
    alias(libs.plugins.hms.android.library)
//    alias(libs.plugins.hilt)
//    alias(libs.plugins.ksp)
    alias(libs.plugins.hms.hilt)
}

android {
    namespace = "com.healthlink.hms.core.network"
//    compileSdk = 34

    buildFeatures {
        buildConfig = true
    }

    defaultConfig {
//        minSdk = 28
        val versionName = libs.versions.versionName.get()
        val versionCode = libs.versions.versionCode.get().toInt()
        buildConfigField("String", "VERSION_NAME", "\"${versionName}\"")
        buildConfigField("String", "VERSION_CODE", "\"${versionCode}\"")
        // TODO: 环境配置
        val apiUrlDebug = project.property("API_BASE_URL_DEBUG") as String
        buildConfigField("String", "BASE_URL", "${apiUrlDebug}")
//        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

//    buildTypes {
//        release {
//            isMinifyEnabled = false
//            proguardFiles(
//                getDefaultProguardFile("proguard-android-optimize.txt"),
//                "proguard-rules.pro"
//            )
//        }
//    }
//    compileOptions {
//        sourceCompatibility = JavaVersion.VERSION_17
//        targetCompatibility = JavaVersion.VERSION_17
//    }
//    kotlinOptions {
//        jvmTarget = "17"
//    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    // 数据模型
    implementation(project(":core:model"))
    // 通用模块
    implementation(project(":core:common"))
    // 网络
    implementation(libs.okhttp)
    // retrofit
    implementation(libs.retrofit)
    implementation(libs.retrofit2.converter.gson)
    implementation(libs.retrofit2.kotlin.coroutines.adapter)

    //日志拦截器
    implementation("com.squareup.okhttp3:logging-interceptor:4.9.0")
    implementation("com.squareup.retrofit2:adapter-rxjava2:2.4.0")

    // hilt
    implementation(libs.hilt.android)
    ksp(libs.hilt.android.compiler)

    // rxjava
    implementation("io.reactivex.rxjava2:rxandroid:2.1.1")
    implementation("io.reactivex.rxjava2:rxjava:2.2.12")

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}