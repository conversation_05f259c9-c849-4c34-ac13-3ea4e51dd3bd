package com.healthlink.hms.core.network.api;

import com.healthlink.hms.core.model.BaseResponse;
import com.healthlink.hms.core.model.JourneySignalAddParam;
import com.healthlink.hms.core.model.bean.BaseDTO;
import com.healthlink.hms.core.model.dto.BloodPressureResponseDTO;
import com.healthlink.hms.core.model.dto.ChartDataDTO;
import com.healthlink.hms.core.model.dto.HWAuthStatusDTO;
import com.healthlink.hms.core.model.dto.HealthBloodpressureSummaryDTO;
import com.healthlink.hms.core.model.dto.HealthHistoryDataStatusDTO;
import com.healthlink.hms.core.model.dto.HealthReportDTO;
import com.healthlink.hms.core.model.dto.HealthSpO2SummaryDTO;
import com.healthlink.hms.core.model.dto.HealthSummarizeDTO;
import com.healthlink.hms.core.model.dto.HealthTempSummaryDTO;
import com.healthlink.hms.core.model.dto.HealthTipsDTO;
import com.healthlink.hms.core.model.dto.HolidayDTO;
import com.healthlink.hms.core.model.dto.LiveHealthStatusDTO;
import com.healthlink.hms.core.model.dto.Pressure1HourSummaryDTO;
import com.healthlink.hms.core.model.dto.SleepDayResponseDTO;
import com.healthlink.hms.core.model.dto.SleepMonthResponseDTO;
import com.healthlink.hms.core.model.dto.SleepWeekResponseDTO;
import com.healthlink.hms.core.model.dto.SleepYearResponseDTO;
import com.healthlink.hms.core.model.dto.SpO2ItemResponseDTO;
import com.healthlink.hms.core.model.dto.TempItemResponseDTO;
import com.healthlink.hms.core.model.dto.UpgradeVersionDTO;
import com.healthlink.hms.core.model.dto.UserInfoDTO;
import com.healthlink.hms.core.model.dto.charts.HealthSleepSummeryDTO;
import com.healthlink.hms.core.model.dto.charts.HealthSummeryDTO;
import com.healthlink.hms.core.model.dto.charts.heartrate.HeartRateStatDTO;
import com.healthlink.hms.core.model.dto.charts.pressure.PressureDetailRespDTO;
import com.healthlink.hms.core.model.dto.charts.pressure.PressureSummaryDTO;
import com.healthlink.hms.core.model.dto.init.CallDoctorPhoneDTO;
import com.healthlink.hms.core.model.dto.init.DoctorServiceDTO;
import com.healthlink.hms.core.model.dto.init.InitInfoDTO;
import com.healthlink.hms.core.model.dto.init.VehicleCapacityDTO;
import com.healthlink.hms.core.network.BuildConfig;
import com.healthlink.hms.core.network.model.HealthInfoDTO;
import com.healthlink.hms.core.network.model.HealthInfoRequestParam;

import java.util.Map;

import io.reactivex.Observable;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;

public interface ApiService {
    String API_URL = BuildConfig.BASE_URL;
    String GERNERAL_URL = "/app-api/gwm-cockpit";

    String URL_API_NA = API_URL + GERNERAL_URL;

    String URL_API_HEALTH_DATA = "/healthdata/detail";

    String URL_API_HEALTH_DETAIL  = API_URL + GERNERAL_URL + URL_API_HEALTH_DATA;
    // 应用初始化接口
    @POST(URL_API_NA + "/init/info")
    Observable<BaseResponse<InitInfoDTO>> getInitInfo(@Body Map param);

    // 车机服务功能列表 /app-api/gwm-cockpit/init/capacity
    @POST(URL_API_NA + "/init/capacity")
    Observable<BaseResponse<VehicleCapacityDTO>> getVehicleServiceList(@Body Map param);

    // 获取服务信息（电话医生服务号码，紧急救援服务号码）
    @GET(URL_API_NA + "/service/info")
    Observable<BaseResponse<DoctorServiceDTO>> getDoctorService();

    // 模拟登录华为 华为授权登录界面
    @GET("/login/hms/auth")
    Observable<BaseResponse<BaseDTO>> huaweiauthInterface();

    // 车机App首页数据
    // 健康数据心率、血氧
    @POST(URL_API_NA + "/index/info")
    Observable<BaseResponse<HealthInfoDTO>> newHealthInfo(@Body HealthInfoRequestParam param);

    // 图表数据
    @POST(URL_API_NA + "/index/info/detail")
    Observable<BaseResponse<ChartDataDTO>> newHealthInfoDetail(@Body HealthInfoRequestParam param);

    // 健康tips
    @GET(URL_API_NA + "/index/tips")
    Observable<BaseResponse<HealthTipsDTO>> getHealthTips();

    // 健康tips分页获取
    @GET(URL_API_NA + "/index/tips/page/{vin}")
    Observable<BaseResponse<HealthTipsDTO>> getHealthTipsWithVin(@Path("vin") String vin);

    //region 车机App健康详情数据
    // 实时体征参数综合分析
    @POST(URL_API_HEALTH_DETAIL + "/result")
    Observable<BaseResponse<LiveHealthStatusDTO>> getLiveHealthStatus(@Body Map param);

    // 心率
    @POST(URL_API_HEALTH_DETAIL + "/heartrate")
    Observable<BaseResponse<HeartRateStatDTO>> getHeartrateDetail(@Body Map param);
    //睡眠-日
    @POST(URL_API_HEALTH_DETAIL + "/sleep")
    Observable<BaseResponse<SleepDayResponseDTO>> getSleepDayDetail(@Body Map param);

    @POST(URL_API_HEALTH_DETAIL + "/sleep")
    Observable<BaseResponse<SleepWeekResponseDTO>> getSleepWeekDetail(@Body Map param);

    @POST(URL_API_HEALTH_DETAIL + "/sleep")
    Observable<BaseResponse<SleepMonthResponseDTO>> getSleepMonthDetail(@Body Map param);

    @POST(URL_API_HEALTH_DETAIL + "/sleep")
    Observable<BaseResponse<SleepYearResponseDTO>> getSleepYearDetail(@Body Map param);
    // 心率日建议
    @POST(URL_API_HEALTH_DETAIL + "/heartrate/advice")
    Observable<BaseResponse<HealthSummeryDTO>> getHealthSummery(@Body Map param);

    // 血氧
    @POST(URL_API_HEALTH_DETAIL + "/spo2")
    Observable<BaseResponse<HealthInfoDTO>> getSpo2Detail(@Body HealthInfoRequestParam param);

    // 睡眠
    @POST(URL_API_HEALTH_DETAIL + "/sleep")
    Observable<BaseResponse<HealthInfoDTO>> getSleepDetail(@Body HealthInfoRequestParam param);
    // 睡眠健康建议
    @POST(URL_API_HEALTH_DETAIL + "/sleep/advice")
    Observable<BaseResponse<HealthSleepSummeryDTO>> getHealthSleepSummery(@Body Map param);

    // 压力详情接口
    @POST(URL_API_HEALTH_DETAIL + "/stress")
    Observable<BaseResponse<PressureDetailRespDTO>> getPressureDetail(@Body Map param);

    @POST(URL_API_HEALTH_DETAIL + "/stress/advice")
    Observable<BaseResponse<PressureSummaryDTO>> getPressureSummary(@Body Map param);

    @POST(URL_API_HEALTH_DETAIL + "/spo2")
    Observable<BaseResponse<SpO2ItemResponseDTO>> getSpO2DayDetail(@Body Map param);

    @POST(URL_API_HEALTH_DETAIL + "/spo2/advice")
    Observable<BaseResponse<HealthSpO2SummaryDTO>> getSpO2Summary(@Body Map param);

    @POST(URL_API_HEALTH_DETAIL + "/temperature")
    Observable<BaseResponse<TempItemResponseDTO>> getTempDetail(@Body Map param);

    @POST(URL_API_HEALTH_DETAIL + "/temperature/advice")
    Observable<BaseResponse<HealthTempSummaryDTO>> getTempSummary(@Body Map param);

    @POST(URL_API_HEALTH_DETAIL + "/bloodpressure")
    Observable<BaseResponse<BloodPressureResponseDTO>> getBloodPressureDetail(@Body Map param);

    @POST(URL_API_HEALTH_DETAIL + "/bloodpressure/advice")
    Observable<BaseResponse<HealthBloodpressureSummaryDTO>> getBloodpressureSummary(@Body Map param);

    @GET(URL_API_HEALTH_DETAIL + "/historydata/status/{vin}")
    Observable<BaseResponse<HealthHistoryDataStatusDTO>> getDataReadyStatus(@Path("vin") String vin);

    //版本信息
    @GET(URL_API_NA + "/service/version/{versionId}")
    Observable<BaseResponse<UpgradeVersionDTO>> getVersionStatus(@Path("versionId") String versionId);

    // 当前日期是不是节假日
    @GET(URL_API_NA + "/service/holiday")
    Observable<BaseResponse<HolidayDTO>> todayIsHoliday();

    // 最近一小时压力状态统计
    // /app-api/gwm-cockpit/healthdata/detail/stress/count/{userId}
    @GET(URL_API_HEALTH_DETAIL + "/temperature/advice")
    Observable<BaseResponse<Pressure1HourSummaryDTO>> pressure1HourSummary(@Path("userId") String userId);

    //用户注销
    @POST(URL_API_NA + "/user/delete/{userId}")
    Observable<BaseResponse<Boolean>> deleteUser(@Path("userId") String userId);

    //关联账户解绑
    @POST(URL_API_NA + "/user/unbind/{userId}")
    Observable<BaseResponse<Boolean>> unbindAccount(@Path("userId") String userId);

    //获取用户数据
    @GET(URL_API_NA + "/user/info/{userId}")
    Observable<BaseResponse<UserInfoDTO>> userInfo(@Path("userId") String userId);

    /**
     * 获取用户华为运动健康服务状态
     */
    @GET(URL_API_NA + "/hms/profile/status/{userId}")
    Observable<BaseResponse<HWAuthStatusDTO>> getAuthStatus(@Path("userId") String userId);

    //保存用户数据
    @POST(URL_API_NA + "/user/info")
    Observable<BaseResponse<Boolean>> saveUserInfo(@Body Map param);

    //上报行程信号数据
    @POST(URL_API_NA + "/vehicle/signal/add")
    Observable<BaseResponse<Boolean>> JourneySignalAdd(@Body JourneySignalAddParam param);
    //获取健康报告页面信息
    @POST(URL_API_HEALTH_DETAIL + "/report")
    Observable<BaseResponse<HealthReportDTO>> getHealthReportInfo(@Body Map param);

    //获取健康总结信息
    @POST(URL_API_HEALTH_DETAIL + "/result")
    Observable<BaseResponse<HealthSummarizeDTO>> getHealthSummarizeInfo(@Body Map param);

    // 数据使用说明
    @GET(URL_API_NA + "/config/data/instructions")
    Observable<BaseResponse<String>> dataUsageIntro();

    // 电话医生-车辆备案更新手机号 /app-api/gwm-cockpit/phone-doctor/membership/update
    @POST(URL_API_NA + "/phone-doctor/membership/update")
    Observable<BaseResponse<Boolean>> updateDoctorServicePhone(@Body Map param);

    // 电话医生-车辆备案 /app-api/gwm-cockpit/phone-doctor/membership/add
    @POST(URL_API_NA + "/phone-doctor/membership/add")
    Observable<BaseResponse<CallDoctorPhoneDTO>> queryDoctorServicePhone(@Body Map param);

    //endregion
}
