package com.healthlink.hms.core.network.rx;

import android.app.Activity;
import android.util.Log;

//import com.healthlink.hms.R;
//import com.healthlink.hms.activity.MainActivity;
//import com.healthlink.hms.application.HmsApplication;
import com.healthlink.hms.core.common.utils.MMKVUtil;
import com.healthlink.hms.core.model.BaseResponse;
import com.healthlink.hms.core.model.BusinessRespCode;
//import com.healthlink.hms.widget.HMSWidgetManager;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

public abstract class BaseObserver<T> implements Observer<T> {

    private Disposable disposable;
    //开始
    @Override
    public void onSubscribe(Disposable disposable) {
        this.disposable = disposable;
    }

    //继续
    @Override
    public void onNext(T t) {
        // 全局拦截
        if(t!=null){
            // TODO: judge privacy model
            if(true/**!HmsApplication.Companion.isPrivacyMode()*/) {
                if (t instanceof BaseResponse<?>) {
                    BaseResponse baseResponse = (BaseResponse) t;

                    Log.d("BaseObserver", "response code = " + baseResponse.getCode());
                    // 如果响应码为2，提示已经退出登录，提示重新登录
                    if (baseResponse != null && BusinessRespCode.LOGIN_EXPIRE.equals(baseResponse.getCode())) {
                        // 如果在首页，通知loading消失
                        setMainActivityForPeddingLogout();
                        // 显示对话框 // TODO: showGlobalDialog
//                        HmsApplication.Companion.showGlobalDialog("", HmsApplication.appContext.getString(R.string.noti_login_invalid), true);

                        // 停止网络请求处理链路
                        if (disposable != null) {
                            disposable.dispose();
                        }
                        return;
                    }
                    // 如果关闭华为运动健康服务
                    if (baseResponse != null && BusinessRespCode.HUAWEI_APP_CLOSE_SERVICE.equals(baseResponse.getCode())) {

                        // 如果在首页，通知loading消失
                        setMainActivityForPeddingLogout();
                        // 显示对话框 // TODO: showGlobalDialog
//                        HmsApplication.Companion.showGlobalDialog("", HmsApplication.appContext.getString(R.string.noti_open_data_auth), false);

                        // 停止网络请求处理链路
                        if (disposable != null) {
                            disposable.dispose();
                        }
                        return;
                    }
                    // 如果取消了授权
                    else if (baseResponse != null && BusinessRespCode.USER_LOGIN_TIME_OUT_OR_CANCEL_AUTH.equals(baseResponse.getCode())) {
                        // 如果在首页，通知loading消失
                        setMainActivityForPeddingLogout();
                        // TODO: showGlobalDialog
//                        HmsApplication.Companion.showGlobalDialog("", HmsApplication.appContext.getString(R.string.noti_huawei_oauth_changed_or_timeout), true);
                        // 停止网络请求处理链路
                        if (disposable != null) {
                            disposable.dispose();
                        }
                        return;
                    }
                }
            } else {
                // 停止网络请求处理链路
                Log.i("BaseObserver", "stop network request");
                if (disposable != null) {
                    disposable.dispose();
                }
                return;
            }
        }
        onSuccess(t);
    }

    /**
     * 如果是主页，刷新到无效状态
     */
    private static void setMainActivityForPeddingLogout() {
        MMKVUtil.INSTANCE.clearAllExceptSome(true);
        // 如果在首页，则处理首页 TODO: update widget
//        Activity currentActivity = HmsApplication.Companion.getCurrentActivity();
//        if(currentActivity!=null && currentActivity instanceof MainActivity){
//            MainActivity mainActivity = (MainActivity) currentActivity;
//            mainActivity.closeLoadingRefreshData();
//            mainActivity.showVisitorMode();
//        }
//        // 更新桌面卡片
//        HMSWidgetManager.INSTANCE.updateHmsWidget(HmsApplication.appContext);
    }

    //异常
    @Override
    public void onError(Throwable e) {
        onFailure(e);
    }

    //完成
    @Override
    public void onComplete() {

    }

    //成功
    public abstract void onSuccess(T t);

    //失败
    public abstract void onFailure(Throwable e);
}
