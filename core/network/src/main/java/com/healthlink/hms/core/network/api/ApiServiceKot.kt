package com.healthlink.hms.core.network.api

import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.AltitudeDatas
import com.healthlink.hms.core.model.dto.GPSDTO
import com.healthlink.hms.core.model.dto.HealthReportDTO
import com.healthlink.hms.core.model.dto.HolidayDTO
import com.healthlink.hms.core.model.dto.LiveHealthStatusDTO
import com.healthlink.hms.core.model.dto.Pressure1HourSummaryDTO
import com.healthlink.hms.core.model.dto.UserInfoDTO
import com.healthlink.hms.core.model.dto.init.InitInfoDTO
import com.healthlink.hms.core.network.model.HealthInfoDTO
import com.healthlink.hms.core.network.model.HealthInfoRequestParam
import io.reactivex.Observable
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

/**
 * Created by imaginedays on 2024/7/19
 *
 *
 */
interface ApiServiceKot {
    companion object {
        const val GERNERAL_URL: String =  "/app-api/gwm-cockpit"
        const val URL_API_HEALTH_DATA: String = "/healthdata/detail"
        const val URL_API_HEALTH_DETAIL: String =  GERNERAL_URL + URL_API_HEALTH_DATA
    }

    // 应用初始化接口
    @POST("$GERNERAL_URL/init/info")
    suspend fun getInitInfo(@Body param: Map<String, String>?): BaseResponse<InitInfoDTO>

    // 实时体征参数综合分析
    @POST("$URL_API_HEALTH_DETAIL/result")
    suspend fun getLiveHealthStatus(@Body param: Map<String,String>?): BaseResponse<LiveHealthStatusDTO>

    // 当前日期是不是节假日
    @GET("$GERNERAL_URL/service/holiday")
    suspend fun todayIsHoliday(): BaseResponse<HolidayDTO>

    // 根据经纬度获取海拔 gps
    @GET("$GERNERAL_URL/service/elevation/{gps}")
    suspend fun getElevationByGps(@Path("gps") gps: String?): BaseResponse<GPSDTO>

    // 最近一小时压力状态统计
    @GET("$URL_API_HEALTH_DETAIL/stress/count/{userId}")
    suspend fun pressure1HourSummary(@Path("userId") userId: String?): BaseResponse<Pressure1HourSummaryDTO>

    // 最近4小时未活动
    @GET("$URL_API_HEALTH_DETAIL/activity/{userId}")
    suspend fun activity4HourSummary(@Path("userId") userId: String?): BaseResponse<*>

    // 健康数据心率、血氧
    @POST("${GERNERAL_URL}/index/info")
    suspend fun newHealthInfo(@Body param: HealthInfoRequestParam): BaseResponse<HealthInfoDTO?>


    //获取健康报告页面信息
    @POST("$URL_API_HEALTH_DETAIL/report")
    suspend fun getHealthReportInfo(@Body param: Map<String, String>): BaseResponse<HealthReportDTO>

    // 获取地理位置海拔等级 /app-api/gwm-cockpit/service/areaHighAltitudeJson
    @GET("$GERNERAL_URL/service/areaHighAltitudeJson")
    suspend fun getAreaHighAltitudeJson(): BaseResponse<AltitudeDatas>

    /**
     * Settings
     */
    //用户注销
    @POST("$GERNERAL_URL/user/delete/{userId}")
    suspend fun deleteUser(@Path("userId") userId: String): BaseResponse<Boolean>

    //关联账户解绑
    @POST("$GERNERAL_URL/user/unbind/{userId}")
    suspend fun unbindAccount(@Path("userId") userId: String): BaseResponse<Boolean>

    //获取用户数据
    @GET("$GERNERAL_URL/user/info/{userId}")
    suspend fun getUserInfo(@Path("userId") userId: String): BaseResponse<UserInfoDTO>

    // 保存用户个人信息
    @POST("${GERNERAL_URL}/user/info")
//    @JvmSuppressWildcards
    suspend fun saveUserInfo(@Body param: Map<String,Any>): BaseResponse<Boolean>

    // 数据使用说明
    @GET("${GERNERAL_URL}/config/data/instructions")
    suspend fun dataUsageIntro(): BaseResponse<String>
}