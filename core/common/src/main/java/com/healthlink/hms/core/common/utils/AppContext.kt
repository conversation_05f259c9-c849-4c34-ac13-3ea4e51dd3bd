package com.healthlink.hms.core.common.utils

import android.app.Activity
import android.content.Context
import com.healthlink.hms.core.common.BuildConfig
import java.util.LinkedList

object AppContext : BaseContext() {
    private lateinit var contextProvider: IContextProvider
    private val activityList = LinkedList<Activity>()
    private var foregroundCount = 0
    private var backgroundTime = 0L

    // 常量定义
    const val CLIENT_NAME = "android"
    const val CHANNEL_NAME = "INSTALL_CHANNEL"

    // 可配置项
    var mock = false
    var channel = "official"
    var appVersion = BuildConfig.VERSION_NAME
    var isNeedGestures = false
    var pushToken: String? = null

    // 锁对象
    private val loginInfoLock = Any()
    private val userInfoLock = Any()
    private val historyLock = Any()

    fun initialize(provider: IContextProvider) {
        contextProvider = provider
        // 调用父类的初始化方法
        init(contextProvider.getContext())
    }

    fun getContext(): Context = contextProvider.getContext()

}