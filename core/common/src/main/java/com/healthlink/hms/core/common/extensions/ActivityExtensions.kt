package com.healthlink.hms.core.common.extensions

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.Color
import android.graphics.Rect
import android.os.Build
import android.util.Log
import android.view.View
import android.view.ViewTreeObserver
import android.view.Window
import android.view.WindowInsets
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.healthlink.hms.core.common.R


/**
 * Created by imaginedays on 2024/7/18
 * Activity 扩展
 */

fun AppCompatActivity.setUpSystemBar(){
    val window = window
    // Build.VERSION.SDK_INT >= 30 windowInsetsControllerCompat 才有值否则为空
    val windowInsetsControllerCompat = WindowCompat.getInsetsController(window,window.decorView)// ViewCompat.getWindowInsetsController(window.decorView)
    val resources = resources
    val lightStatusBar = resources.getBoolean(R.bool.lightStatusBar)
    val lightNavigationBar = resources.getBoolean(R.bool.lightNavigationBar)

    //设置状态栏图标深浅色
    windowInsetsControllerCompat?.isAppearanceLightStatusBars = lightStatusBar

    //设置Dock栏图标深浅色
    windowInsetsControllerCompat?.isAppearanceLightNavigationBars = lightNavigationBar

    //设置状态栏背景色
    window.statusBarColor = resources.getColor(R.color.statusBarColor, null)

    //设置Dock栏背景色
    window.navigationBarColor = resources.getColor(R.color.navigationBarColor, null)
    translucentStatusBar(this)
}

fun translucentStatusBar(activity: Activity) {
    val window = activity.window
    window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
    window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            or View.SYSTEM_UI_FLAG_LAYOUT_STABLE)
    window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
    window.statusBarColor = Color.TRANSPARENT

//    val uiModeManager  = activity.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
//    //设置状态栏文字颜色
//    setStatusBarTextColor(window, uiModeManager.nightMode == UiModeManager.MODE_NIGHT_YES)
}

fun setStatusBarTextColor(window: Window, light: Boolean) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        var systemUiVisibility = window.decorView.systemUiVisibility
        systemUiVisibility = if (light) {
            //黑色文字
            systemUiVisibility and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
        } else {
            //白色文字
            systemUiVisibility or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
        }
        window.decorView.systemUiVisibility = systemUiVisibility
    }
}

/**
 * 针对华为登录和个人信息页两个界面的statusBar和navigationBar设置
 */
fun AppCompatActivity.setUpStatusBar() {
    window?.apply {
        val resources = context.resources
        val lightStatusBar = resources.getBoolean(R.bool.lightStatusBar)
        val lightNavigationBar = resources.getBoolean(R.bool.lightNavigationBar)

        WindowCompat.getInsetsController(this,this.decorView).apply {
            //设置状态栏图标深浅色
            isAppearanceLightStatusBars = false
            //设置Dock栏图标深浅色
            isAppearanceLightNavigationBars = lightNavigationBar
        }

//            //设置状态栏背景色
        statusBarColor = ContextCompat.getColor(context, R.color.statusBarColor)
//
//            //设置Dock栏背景色
        navigationBarColor = ContextCompat.getColor(context, R.color.navigationBarColor)

        // 确保没有设置半透明状态栏标志
        clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        // 允许绘制系统栏背景
        addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        // 设置状态栏颜色为透明
        statusBarColor = Color.TRANSPARENT
    }
}

@SuppressLint("NewApi")
fun AppCompatActivity.observeKeyboardChange(onChange : (isShowing: Boolean) -> Unit, keyboardThreshold: Int = 200) {
    val rootView = window.decorView
    val r = Rect()
    var lastHeight = 0

    val listener = ViewTreeObserver.OnGlobalLayoutListener {
        val insets = rootView.rootWindowInsets
        val imeHeight = insets?.isVisible(WindowInsets.Type.ime())?.let {
            if (it) insets.getInsets(WindowInsets.Type.ime()).bottom else 0
        } ?: 0

        val currentHeight = rootView.height - imeHeight

        if (lastHeight == 0) {
            lastHeight = currentHeight
        } else {
            val diff = lastHeight - currentHeight
            if (diff > keyboardThreshold) {
                onChange(true)
                lastHeight = currentHeight
            } else if (diff < -keyboardThreshold) {
                onChange(false)
                lastHeight = currentHeight
            }
        }
    }

    // 添加监听器
    rootView.viewTreeObserver.addOnGlobalLayoutListener(listener)

    // 在 Activity 销毁时移除监听器
    this.lifecycle.addObserver(object : DefaultLifecycleObserver {
        override fun onDestroy(owner: LifecycleOwner) {
            super.onDestroy(owner)
            rootView.viewTreeObserver.removeOnGlobalLayoutListener(listener)
        }
    })
}

