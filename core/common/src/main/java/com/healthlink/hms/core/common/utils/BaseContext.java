package com.healthlink.hms.core.common.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.Environment;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.WindowManager;

import java.lang.ref.WeakReference;
import java.lang.reflect.Field;
import java.util.concurrent.Callable;

import kotlin.jvm.JvmStatic;

public abstract class BaseContext {

    /**
     * 全局静态context
     */
    public static Context sAppContext = null;

    /**
     * 网络连接管理
     */
    public static ConnectivityManager sConnectManager = null;

    /**
     * 是否合法环境
     */
    public static boolean sIsValidEnvironment = true;

    /**
     * SDCard status
     */
    public static boolean sIsValidSDCard = true;

    /**
     * 本地加密
     */
    public static boolean sUseNativeCrypto = true;

    /**
     * 移动设备的唯一标识
     */
    private static String IMEI = null;
    /**
     * MAC地址，用于模拟IMEI
     */
    private static String MAC = null;
    /**
     * SIM卡唯一标识
     */
    private static String IMSI = null;
    /**
     * 手机号码
     */
    private static String PHONE_NUMBER = null;

    /**
     * 屏幕高幕
     */
    public static int sScreenHeight;
    /**
     * 屏幕宽
     */
    public static int sScreenWidth;
    /**
     * 屏幕密度dpi
     */
    public static int sScreenDpi;
    /**
     * 设备唯一码
     */
    public static String deviceId;

    public static String uuid = DeviceUtil.generateUUID();
    /**
     * 设备品牌
     */
    public static String deviceBrand = Build.BRAND;

    /**
     * 设备型号
     */
    public static String deviceModel = Build.MODEL;

    /**
     * 设备名称
     */
    public static String deviceName = deviceBrand + "_" + deviceModel;

    private static final String COFFEE_OS_VERSION = "ro.vendor.gwm.coffee_os_version";  // 咖啡机版本
    /**
     * CoffeeOS版本
     */
    public static String coffeeOSVersion = SystemPropertyUtils.INSTANCE.getSystemPropertyInt(COFFEE_OS_VERSION, 310) + "";
    /**
     * 设备SimSerialNumber,从V2.1.0才开始有
     */
    public static String simSerialNumber = "";

    /**
     * 接收其他context的结果的原始context，当前只保证主线程处理
     */
    private static WeakReference<Context> mHostContext = null;
    /**
     * context的结果处理接口
     */
    private static Runnable mContextResultRunnable = null;

    /**
     * 初始化方法
     *
     * @param context
     */
    public static void init(Context context) {
        if (sAppContext == null) {
            sAppContext = context;
        }
        initScreenParam();
        initDeviceInfo();
        deviceId = DeviceUtil.readDeviceId(context);
    }

    public static ConnectivityManager getConnectManager() {
        if (sConnectManager == null) {
            sConnectManager = (ConnectivityManager) sAppContext.getSystemService(Context.CONNECTIVITY_SERVICE);
        }
        return sConnectManager;
    }

    /**
     * 检测SD是否存在
     *
     * @return
     */
    public static boolean checkSDCard() {

        String storageState = Environment.getExternalStorageState();
        BaseContext.sIsValidSDCard = Environment.MEDIA_MOUNTED.equals(storageState);

        return BaseContext.sIsValidSDCard;
    }

    /**
     * 检测网络是否畅通
     *
     * @return true:畅通，false:不畅通
     */
    public static boolean checkNetWork() {
        return isNetworkAvailable();
    }

    /**
     * 初始化设备信息
     *
     * @return
     */
    private static void initDeviceInfo() {
        if (TextUtils.isEmpty(PHONE_NUMBER)) {
            PHONE_NUMBER = DeviceUtil.getPhoneNumber(sAppContext);
        }
        if (TextUtils.isEmpty(IMEI)) {
            IMEI = DeviceUtil.getIMEI(sAppContext);
        }
        if (TextUtils.isEmpty(IMSI)) {
            IMSI = DeviceUtil.getIMSI(sAppContext);
        }
        if (TextUtils.isEmpty(MAC)) {
            MAC = DeviceUtil.getMAC(sAppContext);
        }
    }

    /**
     * 初始化手机屏幕参数
     */
    private static void initScreenParam() {
        if (sScreenHeight == 0 || sScreenWidth == 0 || sScreenDpi == 0) {
            WindowManager wm = (WindowManager) sAppContext.getSystemService(Context.WINDOW_SERVICE);
            if (wm == null) {
                return;
            }

            DisplayMetrics metric = new DisplayMetrics();
            wm.getDefaultDisplay().getMetrics(metric);
            int width = metric.widthPixels; // 屏幕宽度（像素）
            int height = metric.heightPixels; // 屏幕高度（像素）
            int densityDpi = metric.densityDpi; // 屏幕密度DPI（120 / 160 / 240）

            sScreenHeight = height;
            sScreenWidth = width;
            sScreenDpi = densityDpi;
        }
    }

    /**
     * 获取手机IMEI
     *
     * @return 设备唯一标识
     */
    public static String getIMEI() {
        if (TextUtils.isEmpty(IMEI)) {
            IMEI = DeviceUtil.getIMEI(sAppContext);
        }
        return !TextUtils.isEmpty(IMEI) ? IMEI : (!TextUtils.isEmpty(MAC) ? MAC
                : "0000000000000000");
    }

    /**
     * 获取手机MAC
     *
     * @return 设备唯一标识
     */
    public static String getMAC() {
        if (TextUtils.isEmpty(MAC)) {
            MAC = DeviceUtil.getMAC(sAppContext);
        }
        return !TextUtils.isEmpty(MAC) ? MAC : "0000000000000000";
    }

    /**
     * 获取手机IMSI
     *
     * @return sim卡唯一标识
     */
    public static String getIMSI() {
        if (TextUtils.isEmpty(IMSI)) {
            IMSI = DeviceUtil.getIMSI(sAppContext);
        }
        return IMSI != null ? IMSI : "";
    }

    /**
     * 获取系统版本
     *
     * @return
     */
    public static String getOSVersion() {
        return Build.VERSION.RELEASE;
    }

    private static boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = getConnectManager();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Network network = getConnectManager().getActiveNetwork();
            if (network == null) {
                return false;
            }
            NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(network);
            return capabilities != null && capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                    && capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
        } else {
            @SuppressWarnings("deprecation")
            NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
            return networkInfo != null && networkInfo.isConnected();
        }
    }

    /**
     * app环境下执行装载类（改进systemClassLoader装载和appClassLoader一致）
     * 部分装载系统内部使用了systemClassLoader（例如，Serializable的装载）
     * <p>
     * ClassLoader.loadClass 只适用于无关联的类定义，有层次关系的定义慎用
     *
     * @param classCaller
     * @return 装载的类
     * @throws Exception
     */
    public static <T> T callClass(Callable<T> classCaller) throws Exception {

        if (classCaller == null) {
            throw new IllegalArgumentException("classCaller must not be null.");
        }

        ClassLoader sysClassLoader = BaseContext.class.getClassLoader();
        ClassLoader appClassLoader = sAppContext.getClassLoader();
        if (sysClassLoader == appClassLoader) {
            return classCaller.call();
        }

        // systemClassLoader模拟appClassLoader，调用装载类后，撤回现有分类器

        T classObj = null;
        // sysClassLoader = system + boot
        // appClassLoader = app + system + boot
        synchronized (sysClassLoader) {
            Field parentField = null;
            ClassLoader bootClassLoader = sysClassLoader.getParent();
            ClassLoader diffClassLoader = null;
            try {
                // 修改systemclassloader链式关系
                parentField = ClassLoader.class.getDeclaredField("parent");
                parentField.setAccessible(true);
                if (parentField.getType() != ClassLoader.class) {
                    parentField = null;
                } else {
                    // 查找共同的parent
                    diffClassLoader = appClassLoader;
                    while (diffClassLoader != null
                            && !sysClassLoader.equals(diffClassLoader
                            .getParent())) {
                        diffClassLoader = diffClassLoader.getParent();
                    }
                    if (diffClassLoader != null) {
                        // appClassLoader = app + boot
                        // sysClassLoader = system + appClassLoader
                        parentField.set(diffClassLoader, bootClassLoader);
                        parentField.set(sysClassLoader, appClassLoader);
                    }
                }
            } catch (Exception e) {
                // e.printStackTrace();
                parentField = null;
            }

            try {
                classObj = classCaller.call();
            } finally {
                if (parentField != null) {
                    // 还原systemclassloader链式关系
                    try {
                        if (diffClassLoader != null) {
                            parentField.set(sysClassLoader, bootClassLoader);
                            parentField.set(diffClassLoader, sysClassLoader);
                        }

                        parentField.setAccessible(false);
                    } catch (Exception e) {
                        // e.printStackTrace();
                    }
                }
            }
        }

        return classObj;
    }

    /**
     * 为指定的classloader分配APP的ClassLoader
     *
     * @param src
     * @return 可能返回自身或者appclassloader
     */
    public static ClassLoader allocateAppClassLoader(ClassLoader src) {
        ClassLoader appClassLoader = sAppContext.getClassLoader();

        if (src != null) {
            // 遍历查找是否包含appclassloader
            ClassLoader cl = src;
            while (cl != null) {
                if (cl.equals(appClassLoader)) {
                    return src;
                }
                cl = cl.getParent();
            }
        }

        return appClassLoader;
    }

    /**
     * 指定的Context等待处理返回结果，通常用于非startActivityForResult处理的异常情况，
     *
     * @param source
     */
    public static void startContextForResult(Context source) {
        mHostContext = null;
        mContextResultRunnable = null;

        if (source != null) {
            mHostContext = new WeakReference<Context>(source);
        }
    }

    /**
     * Context对应的结果处理方式
     *
     * @param result
     */
    public static void setContextResult(Runnable result) {
        if (mHostContext != null && mHostContext.get() != null) {
            mContextResultRunnable = result;
        }
    }

    /**
     * 处理context对应的结果，被等待就结果的context调用
     *
     * @param context
     */
    public static void onContextResult(Context context) {
        if (mHostContext != null) {
            if (mHostContext.get() == context && mContextResultRunnable != null) {
                mContextResultRunnable.run();
            }
            mHostContext.clear();
            mHostContext = null;
        }

        mContextResultRunnable = null;
    }


    /**
     * 获取网络环境
     *
     * @return 2G, 3G, 4G, wifi
     */
    public static String getNetType() {
        ConnectivityManager connectManager = getConnectManager();
        NetworkInfo info = connectManager.getActiveNetworkInfo();
        if (info == null) {
            return NetInfo.UNKNOWN;
        }
        if (info.getType() == ConnectivityManager.TYPE_WIFI) {
            // wifi网络
            return NetInfo.CONNECT_WIFI;
        } else if (info.getType() == ConnectivityManager.TYPE_MOBILE) {
            // 手机网络
            int subType = info.getSubtype();
            if (subType == TelephonyManager.NETWORK_TYPE_CDMA || subType == TelephonyManager.NETWORK_TYPE_EDGE || subType == TelephonyManager.NETWORK_TYPE_GPRS) {
                // 2G网络
                return NetInfo.CONNECT_2G;
            } else if (subType == TelephonyManager.NETWORK_TYPE_HSDPA || subType == TelephonyManager.NETWORK_TYPE_EVDO_0 || subType == TelephonyManager.NETWORK_TYPE_EVDO_A || subType == TelephonyManager.NETWORK_TYPE_EVDO_B || subType == TelephonyManager.NETWORK_TYPE_UMTS) {
                // 3G网络
                return NetInfo.CONNECT_3G;
            } else if (subType == TelephonyManager.NETWORK_TYPE_LTE) {
                // 4G网络，LTE是3g到4g的过渡，是3.9G的全球标准
                return NetInfo.CONNECT_4G;
            } else {
                return NetInfo.UNKNOWN;
            }
        } else {
            return NetInfo.UNKNOWN;
        }
    }

    public static boolean isWifi() {
        return NetInfo.CONNECT_WIFI.equals(getNetType());
    }
}
