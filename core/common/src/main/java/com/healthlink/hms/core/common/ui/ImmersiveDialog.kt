package com.healthlink.hms.core.common.ui

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.content.res.Configuration
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat
import android.view.Window
import com.healthlink.hms.core.common.R

/**
 * Created by imaginedays on 2024/8/11
 * 沉浸式状态栏
 */
class ImmersiveDialog(context: Context, style: Int) : HmsDialog(context,style) {
    init {
        window?.apply {
//            setFlags(
//                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
//                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
//            )
            val windowInsetsControllerCompat = WindowCompat.getInsetsController(this,this.decorView)// ViewCompat.getWindowInsetsController(window.decorView)
            val resources = context.resources
            val lightStatusBar = resources.getBoolean(R.bool.lightStatusBar)
            val lightNavigationBar = resources.getBoolean(R.bool.lightNavigationBar)

            //设置状态栏图标深浅色
            windowInsetsControllerCompat?.isAppearanceLightStatusBars = lightStatusBar

            //设置Dock栏图标深浅色
            windowInsetsControllerCompat?.isAppearanceLightNavigationBars = lightNavigationBar

//            //设置状态栏背景色
//            it.statusBarColor = ContextCompat.getColor(context, R.color.statusBarColor)//resources.getColor(R.color.statusBarColor, resources.newTheme())
//
//            //设置Dock栏背景色
//            it.navigationBarColor = ContextCompat.getColor(context, R.color.navigationBarColor)//resources.getColor(R.color.navigationBarColor, resources.newTheme())

            clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE)
            addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            statusBarColor = Color.TRANSPARENT
        }

    }

}