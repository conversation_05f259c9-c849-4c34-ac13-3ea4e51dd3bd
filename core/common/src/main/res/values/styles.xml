<resources>
    <style name="MyDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@color/dialog_mask_color</item>
        <!--        <item name="android:windowIsTranslucent">true</item>-->
        <!--        &lt;!&ndash; 内容区域在状态栏和导航栏后面显示 &ndash;&gt;-->
        <!--        <item name="android:fitsSystemWindows">false</item>-->
    </style>

</resources>