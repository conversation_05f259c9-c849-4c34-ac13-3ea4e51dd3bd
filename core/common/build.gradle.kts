import org.gradle.kotlin.dsl.project

plugins {
    alias(libs.plugins.hms.android.library)
    alias(libs.plugins.hms.hilt)
}

android {
    namespace = "com.healthlink.hms.core.common"
//    compileSdk = 34

    buildFeatures {
        buildConfig = true
        dataBinding = true
    }

    defaultConfig {
//        minSdk = 28
        val versionName = libs.versions.versionName.get()
        buildConfigField("String", "VERSION_NAME", "\"${versionName}\"")
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

//    buildTypes {
//        release {
//            isMinifyEnabled = false
//            proguardFiles(
//                getDefaultProguardFile("proguard-android-optimize.txt"),
//                "proguard-rules.pro"
//            )
//        }
//    }
//    compileOptions {
//        sourceCompatibility = JavaVersion.VERSION_17
//        targetCompatibility = JavaVersion.VERSION_17
//    }
//    kotlinOptions {
//        jvmTarget = "17"
//    }
}

dependencies {
    implementation(project(":core:ui"))
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    // Hilt依赖注入
    implementation(libs.hilt.android)
    ksp(libs.hilt.android.compiler)
    // 添加必要的依赖以支持BaseVBVMActivity
    implementation(libs.androidx.lifecycle.runtime)
    // local datastore
    implementation("androidx.datastore:datastore-preferences:1.1.7")
    // implementation("androidx.datastore:datastore-preferences-core:1.1.7")

    // :core:model
    implementation(project(":core:model"))
    // third party
    // 阴影
    api(libs.shadowview)
    // GSON
    api(libs.gson)
    // 腾讯MMKV本地存储
    api(libs.mmkv.static)
    // 界面适配
    api(libs.androidautosize)
    //agentWeb  webView
    api(libs.agentweb.core)
    // 滚动条
    api(libs.android.standalone.scroll.bar)

    // 通用工具类
    api(libs.utilcodex)

    // gwm
    //引入埋点sdk
    api(files("libs/GWMDataTrackSDK-2.5.10.aar"))
    // UI
    api(files("libs/libVrWidget-2.0.8.jar"))
    api(files("libs/libWidgetCux-1.1.5-SOP-20240816.031318-1.aar"))

    // 判断小憩模式是否可用
    compileOnly(files("libs/gwmhmiframework-0.4.0.jar"))
    // SIP电话
    compileOnly(files("libs/reduce-release-sit-20250422v1.aar"))

    //NumberPicker修改版
    api(files("libs/NumberPicker-2.4.13.aar"))

    // unpeek livedata数据回流
    api(libs.unpeek.livedata)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}