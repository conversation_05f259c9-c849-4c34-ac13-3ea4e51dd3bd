package com.healthlink.hms.core.model.store

/**
 * 用户相关偏好设置数据模型
 * Created by imaginedays on 2024/12/19
 */
data class UserPreferences(
    val userId: String = "",
    val userToken: String = "",
    val nickname: String = "",
    val birthYear: Int = 0,
    val birthMonth: Int = 0,
    val height: Int = 0,
    val weight: Int = 0,
    val gender: Int = 0
)

/**
 * 健康权限设置数据模型
 */
data class HealthAuthorityPreferences(
    val heartRateAuthority: Boolean = false,
    val sleepAuthority: Boolean = false,
    val stressAuthority: Boolean = false,
    val bloodOxygenAuthority: Boolean = false,
    val bloodPressureAuthority: Boolean = false,
    val bodyTemperatureAuthority: Boolean = false,
    val locationPermission: Boolean = false,
    val notificationPermission: Boolean = false,
    val exercisePermission: Boolean = false,
    val healthDataPermission: Boolean = false,
    val heartRatePermission: Boolean = false,
    val sleepPermission: Boolean = false,
    val privacyPolicyAccepted: Boolean = false
)

/**
 * 系统设置数据模型
 */
data class SystemPreferences(
    // 应用基本信息
    val appVersion: String = "",
    val versionCode: Int = 0,
    val firstLaunch: Boolean = true,
    val firstLaunchAfterBoot: Boolean = true,
    
    // 调试和日志设置
    val debugMode: Boolean = false,
    val logLevel: Int = 0,
    
    // 网络和缓存设置
    val baseUrl: String = "",
    val apiTimeout: Long = 30000L,
    val cacheSize: Long = 50 * 1024 * 1024L, // 50MB
    val cacheEnabled: Boolean = true,
    
    // UI和主题设置
    val themeMode: Int = 0,
    val language: String = "zh",
    
    // 原有系统设置
    val isNotificationOpen: Boolean = true,
    val isOpenWarnMode: Boolean = false,
    val lastThemeMode: Int = 0,
    val privacyPolicyAgreed: Boolean = false,
    val lastPowerMode: String = "",
    val bannerTips: String = "",
    val alreadyBannerTips: String = "",
    val lastBannerPosition: Int = 0
)

/**
 * 导航相关设置数据模型
 */
data class NavigationPreferences(
    val destinationElevation: String = "",
    val curLocationElevation: String = "",
    val navigationGuideStatus: Boolean = false,
    val navigationGuideStatusTimestamp: Long = 0L,
    val navigationInfo: String = "",
    val longitude: Double = 0.0,
    val latitude: Double = 0.0,
    val deepLinkHandled: Boolean = false,
    val launchTarget: String = "",
    val showGuide: Boolean = false,
    val guideCompleted: Boolean = false,
    val lastVisitedPage: String = "",
    val navigationHistory: String = "",
    val currentBottomNavIndex: Int = 0
)

/**
 * 场景引擎相关设置数据模型
 */
data class SceneEnginePreferences(
    // 场景引擎核心设置
    val sceneEngineEnabled: Boolean = false,
    val currentSceneId: String = "",
    val sceneConfig: String = "",
    val sceneDataSyncTime: Long = 0L,
    
    // 场景推荐和自动切换
    val sceneRecommendationEnabled: Boolean = true,
    val autoSceneSwitchEnabled: Boolean = false,
    val sceneLearningEnabled: Boolean = true,
    val sceneDataCollectionEnabled: Boolean = true,
    
    // 原有场景引擎设置
    val isFirstLaunchAppAfterBoot: Boolean = false,
    val sceneRemainRunCount: String = "",
    val lastPTime: String = "",
    val welcomeTime: String = "",
    val lastGearStatusInfo: String = "",
    val powerMode2StartTime: String = "",
    val lastDoctorCallTime: String = ""
)

/**
 * 健康数据相关设置数据模型
 */
data class HealthDataPreferences(
    val phoneDoctorNumber: String = "",
    val bindDoctorService: Boolean = false,
    val isPhoneDoctorMember: Boolean = false,
    val healthReportScoreChangeIsUp: Boolean = false,
    val healthTips: String = "",
    val healthReportNotify: Boolean = false,
    val vinCode: String = "",
    // 数据同步相关设置
    val dataSyncEnabled: Boolean = true,
    val lastSyncTime: Long = 0L,
    val syncInterval: Int = 30,
    val wifiOnlySyncEnabled: Boolean = false,
    val autoBackupEnabled: Boolean = true,
    val dataCompressionEnabled: Boolean = true,
    val dataEncryptionEnabled: Boolean = false,
    val dataRetentionDays: Int = 365
)