package com.healthlink.hms.core.model.dto

/**
 * 历史状态数据传输对象
 * Created for health-report module
 */
data class HistoryStatusDTO(
    /**
     * 用户ID
     */
    val userId: String? = null,
    
    /**
     * 历史记录ID
     */
    val historyId: String? = null,
    
    /**
     * 状态类型
     */
    val statusType: String? = null,
    
    /**
     * 状态值
     */
    val statusValue: String? = null,
    
    /**
     * 状态描述
     */
    val statusDescription: String? = null,
    
    /**
     * 记录时间
     */
    val recordTime: String? = null,
    
    /**
     * 创建时间
     */
    val createTime: String? = null,
    
    /**
     * 更新时间
     */
    val updateTime: String? = null,
    
    /**
     * 数据来源
     */
    val dataSource: String? = null,
    
    /**
     * 扩展数据
     */
    val extData: Map<String, Any>? = null
)