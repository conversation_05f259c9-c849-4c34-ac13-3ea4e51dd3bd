package com.healthlink.hms.core.model.dto

/**
 * 健康报告数据传输对象
 * Created for health-report module
 */
data class HealthReportDTO(
    /**
     * 报告ID
     */
    val reportId: String? = null,
    
    /**
     * 用户ID
     */
    val userId: String? = null,
    
    /**
     * 时间范围代码 (day/week/month/year)
     */
    val timeCode: String? = null,
    
    /**
     * 车辆识别码
     */
    val vin: String? = null,
    
    /**
     * 健康评分
     */
    val healthScore: Int? = null,
    
    /**
     * 健康等级
     */
    val healthLevel: String? = null,
    
    /**
     * 风险等级
     */
    val riskLevel: String? = null,
    
    /**
     * 健康建议
     */
    val healthAdvice: String? = null,
    
    /**
     * 风险描述
     */
    val riskDescription: String? = null,
    
    /**
     * 报告生成时间
     */
    val createTime: String? = null,
    
    /**
     * 数据开始时间
     */
    val startTime: String? = null,
    
    /**
     * 数据结束时间
     */
    val endTime: String? = null,
    
    /**
     * 心率数据
     */
    val heartRateData: Any? = null,
    
    /**
     * 血氧数据
     */
    val bloodOxygenData: Any? = null,
    
    /**
     * 血压数据
     */
    val bloodPressureData: Any? = null,
    
    /**
     * 体温数据
     */
    val temperatureData: Any? = null,
    
    /**
     * 睡眠数据
     */
    val sleepData: Any? = null,
    
    /**
     * 压力数据
     */
    val pressureData: Any? = null
)