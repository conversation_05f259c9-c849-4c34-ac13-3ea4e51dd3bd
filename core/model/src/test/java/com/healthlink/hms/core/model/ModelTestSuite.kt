package com.healthlink.hms.core.model

import com.healthlink.hms.core.model.dto.BloodPressureResponseDTOTest
import com.healthlink.hms.core.model.dto.HealthSummarizeDTOTest
import com.healthlink.hms.core.model.dto.UserInfoDTOTest
import org.junit.runner.RunWith
import org.junit.runners.Suite

/**
 * Core Model模块的测试套件
 * 包含所有Model相关的单元测试
 */
@RunWith(Suite::class)
@Suite.SuiteClasses(
    // 基础类测试
    BaseResponseTest::class,
    BaseResponseCallbackTest::class,
    
    // DTO类测试
    HealthSummarizeDTOTest::class,
    BloodPressureResponseDTOTest::class,
    UserInfoDTOTest::class,
    
    // 序列化测试
    SerializationTest::class
)
class ModelTestSuite {
    // 测试套件类，用于组织和运行所有Model模块的测试
    // 可以通过运行这个类来执行所有相关的单元测试
}