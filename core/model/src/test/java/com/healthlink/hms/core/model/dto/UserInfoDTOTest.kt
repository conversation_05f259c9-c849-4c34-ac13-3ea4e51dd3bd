package com.healthlink.hms.core.model.dto

import org.junit.Assert.*
import org.junit.Test

/**
 * UserInfoDTO的单元测试
 * 测试用户信息数据传输对象的各种功能
 */
class UserInfoDTOTest {

    @Test
    fun `test constructor with all parameters`() {
        val dto = UserInfoDTO(
            userId = "user123",
            nickName = "张三",
            gender = 1,
            birthYear = 1990,
            birthMonth = 5,
            height = 175,
            weight = 70.5f
        )
        
        assertEquals("user123", dto.userId)
        assertEquals("张三", dto.nickName)
        assertEquals(1, dto.gender)
        assertEquals(1990, dto.birthYear)
        assertEquals(5, dto.birthMonth)
        assertEquals(175, dto.height)
        assertEquals(70.5f, dto.weight!!, 0.01f)
    }

    @Test
    fun `test constructor with null parameters`() {
        val dto = UserInfoDTO(
            userId = null,
            nickName = null,
            gender = null,
            birthYear = null,
            birthMonth = null,
            height = null,
            weight = null
        )
        
        assertNull(dto.userId)
        assertNull(dto.nickName)
        assertNull(dto.gender)
        assertNull(dto.birthYear)
        assertNull(dto.birthMonth)
        assertNull(dto.height)
        assertNull(dto.weight)
    }

    @Test
    fun `test constructor with mixed null and non-null parameters`() {
        val dto = UserInfoDTO(
            userId = "user456",
            nickName = null,
            gender = 0,
            birthYear = null,
            birthMonth = 12,
            height = null,
            weight = 65.0f
        )
        
        assertEquals("user456", dto.userId)
        assertNull(dto.nickName)
        assertEquals(0, dto.gender)
        assertNull(dto.birthYear)
        assertEquals(12, dto.birthMonth)
        assertNull(dto.height)
        assertEquals(65.0f, dto.weight!!, 0.01f)
    }

    @Test
    fun `test gender values`() {
        val maleUser = UserInfoDTO(
            userId = "male_user",
            nickName = "男性用户",
            gender = 1,
            birthYear = 1985,
            birthMonth = 3,
            height = 180,
            weight = 75.0f
        )
        
        val femaleUser = UserInfoDTO(
            userId = "female_user",
            nickName = "女性用户",
            gender = 0,
            birthYear = 1992,
            birthMonth = 8,
            height = 165,
            weight = 55.5f
        )
        
        assertEquals(1, maleUser.gender)
        assertEquals(0, femaleUser.gender)
    }

    @Test
    fun `test birth month boundary values`() {
        val januaryUser = UserInfoDTO(
            userId = "jan_user",
            nickName = "一月用户",
            gender = 1,
            birthYear = 1990,
            birthMonth = 1,
            height = 170,
            weight = 65.0f
        )
        
        val decemberUser = UserInfoDTO(
            userId = "dec_user",
            nickName = "十二月用户",
            gender = 0,
            birthYear = 1995,
            birthMonth = 12,
            height = 160,
            weight = 50.0f
        )
        
        assertEquals(1, januaryUser.birthMonth)
        assertEquals(12, decemberUser.birthMonth)
    }

    @Test
    fun `test weight precision`() {
        val dto = UserInfoDTO(
            userId = "precision_user",
            nickName = "精度测试用户",
            gender = 1,
            birthYear = 1988,
            birthMonth = 6,
            height = 175,
            weight = 72.345f
        )
        
        assertEquals(72.345f, dto.weight!!, 0.001f)
    }

    @Test
    fun `test data class equality`() {
        val dto1 = UserInfoDTO(
            userId = "user123",
            nickName = "张三",
            gender = 1,
            birthYear = 1990,
            birthMonth = 5,
            height = 175,
            weight = 70.5f
        )
        
        val dto2 = UserInfoDTO(
            userId = "user123",
            nickName = "张三",
            gender = 1,
            birthYear = 1990,
            birthMonth = 5,
            height = 175,
            weight = 70.5f
        )
        
        val dto3 = UserInfoDTO(
            userId = "user456",
            nickName = "张三",
            gender = 1,
            birthYear = 1990,
            birthMonth = 5,
            height = 175,
            weight = 70.5f
        )
        
        assertEquals(dto1, dto2)
        assertNotEquals(dto1, dto3)
    }

    @Test
    fun `test data class hashCode`() {
        val dto1 = UserInfoDTO(
            userId = "user123",
            nickName = "张三",
            gender = 1,
            birthYear = 1990,
            birthMonth = 5,
            height = 175,
            weight = 70.5f
        )
        
        val dto2 = UserInfoDTO(
            userId = "user123",
            nickName = "张三",
            gender = 1,
            birthYear = 1990,
            birthMonth = 5,
            height = 175,
            weight = 70.5f
        )
        
        assertEquals(dto1.hashCode(), dto2.hashCode())
    }

    @Test
    fun `test data class copy`() {
        val original = UserInfoDTO(
            userId = "user123",
            nickName = "张三",
            gender = 1,
            birthYear = 1990,
            birthMonth = 5,
            height = 175,
            weight = 70.5f
        )
        
        val copied = original.copy()
        assertEquals(original, copied)
        
        val modifiedCopy = original.copy(nickName = "李四", weight = 75.0f)
        assertEquals("user123", modifiedCopy.userId)
        assertEquals("李四", modifiedCopy.nickName)
        assertEquals(1, modifiedCopy.gender)
        assertEquals(1990, modifiedCopy.birthYear)
        assertEquals(5, modifiedCopy.birthMonth)
        assertEquals(175, modifiedCopy.height)
        assertEquals(75.0f, modifiedCopy.weight!!, 0.01f)
        assertNotEquals(original, modifiedCopy)
    }

    @Test
    fun `test data class destructuring`() {
        val dto = UserInfoDTO(
            userId = "user789",
            nickName = "王五",
            gender = 0,
            birthYear = 1995,
            birthMonth = 9,
            height = 160,
            weight = 55.0f
        )
        
        val (userId, nickName, gender, birthYear, birthMonth, height, weight) = dto
        
        assertEquals("user789", userId)
        assertEquals("王五", nickName)
        assertEquals(0, gender)
        assertEquals(1995, birthYear)
        assertEquals(9, birthMonth)
        assertEquals(160, height)
        assertEquals(55.0f, weight!!, 0.01f)
    }

    @Test
    fun `test toString contains all fields`() {
        val dto = UserInfoDTO(
            userId = "user123",
            nickName = "张三",
            gender = 1,
            birthYear = 1990,
            birthMonth = 5,
            height = 175,
            weight = 70.5f
        )
        
        val toString = dto.toString()
        assertTrue(toString.contains("userId=user123"))
        assertTrue(toString.contains("nickName=张三"))
        assertTrue(toString.contains("gender=1"))
        assertTrue(toString.contains("birthYear=1990"))
        assertTrue(toString.contains("birthMonth=5"))
        assertTrue(toString.contains("height=175"))
        assertTrue(toString.contains("weight=70.5"))
    }

    @Test
    fun `test with empty strings`() {
        val dto = UserInfoDTO(
            userId = "",
            nickName = "",
            gender = 1,
            birthYear = 1990,
            birthMonth = 5,
            height = 175,
            weight = 70.5f
        )
        
        assertEquals("", dto.userId)
        assertEquals("", dto.nickName)
    }

    @Test
    fun `test extreme values`() {
        val dto = UserInfoDTO(
            userId = "extreme_user",
            nickName = "极值测试",
            gender = 1,
            birthYear = 1900,
            birthMonth = 1,
            height = 300,
            weight = 999.99f
        )
        
        assertEquals("extreme_user", dto.userId)
        assertEquals("极值测试", dto.nickName)
        assertEquals(1900, dto.birthYear)
        assertEquals(300, dto.height)
        assertEquals(999.99f, dto.weight!!, 0.01f)
    }
}