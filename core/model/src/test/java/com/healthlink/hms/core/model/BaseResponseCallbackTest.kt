package com.healthlink.hms.core.model

import org.junit.Assert.*
import org.junit.Test
import org.mockito.Mockito.*

/**
 * BaseResponseCallback的单元测试
 * 测试回调接口的功能和使用场景
 */
class BaseResponseCallbackTest {

    @Test
    fun `test callback interface methods are called correctly`() {
        // 创建mock回调
        @Suppress("UNCHECKED_CAST")
        val callback = mock(BaseResponseCallback::class.java) as BaseResponseCallback<String>
        
        // 创建成功响应
        val successResponse = BaseResponse(
            code = BusinessRespCode.ACCESS_SUCCESS,
            msg = "success",
            data = "test data"
        )
        
        // 创建失败响应
        val failedResponse = BaseResponse<String>(
            code = "3",
            msg = "failed",
            data = null
        )
        
        // 调用成功回调
        callback.onSuccess(successResponse)
        verify(callback).onSuccess(successResponse)
        
        // 调用失败回调
        callback.onFailed(failedResponse)
        verify(callback).onFailed(failedResponse)
    }

    @Test
    fun `test concrete callback implementation`() {
        var successCalled = false
        var failedCalled = false
        var receivedSuccessResponse: BaseResponse<String>? = null
        var receivedFailedResponse: BaseResponse<String>? = null
        
        // 创建具体的回调实现
        val callback = object : BaseResponseCallback<String> {
            override fun onSuccess(response: BaseResponse<String>) {
                successCalled = true
                receivedSuccessResponse = response
            }
            
            override fun onFailed(response: BaseResponse<String>) {
                failedCalled = true
                receivedFailedResponse = response
            }
        }
        
        // 测试成功回调
        val successResponse = BaseResponse(
            code = BusinessRespCode.ACCESS_SUCCESS,
            msg = "success",
            data = "test data"
        )
        
        callback.onSuccess(successResponse)
        
        assertTrue(successCalled)
        assertFalse(failedCalled)
        assertEquals(successResponse, receivedSuccessResponse)
        assertNull(receivedFailedResponse)
        
        // 重置状态
        successCalled = false
        failedCalled = false
        receivedSuccessResponse = null
        receivedFailedResponse = null
        
        // 测试失败回调
        val failedResponse = BaseResponse<String>(
            code = "3",
            msg = "failed",
            data = null
        )
        
        callback.onFailed(failedResponse)
        
        assertFalse(successCalled)
        assertTrue(failedCalled)
        assertNull(receivedSuccessResponse)
        assertEquals(failedResponse, receivedFailedResponse)
    }

    @Test
    fun `test callback with different data types`() {
        // 测试String类型回调
        val stringCallback = object : BaseResponseCallback<String> {
            override fun onSuccess(response: BaseResponse<String>) {
                assertEquals("test string", response.data)
            }
            override fun onFailed(response: BaseResponse<String>) {}
        }
        
        stringCallback.onSuccess(BaseResponse(code = "0", data = "test string"))
        
        // 测试Int类型回调
        val intCallback = object : BaseResponseCallback<Int> {
            override fun onSuccess(response: BaseResponse<Int>) {
                assertEquals(123, response.data)
            }
            override fun onFailed(response: BaseResponse<Int>) {}
        }
        
        intCallback.onSuccess(BaseResponse(code = "0", data = 123))
        
        // 测试List类型回调
        val listCallback = object : BaseResponseCallback<List<String>> {
            override fun onSuccess(response: BaseResponse<List<String>>) {
                assertEquals(listOf("item1", "item2"), response.data)
            }
            override fun onFailed(response: BaseResponse<List<String>>) {}
        }
        
        listCallback.onSuccess(BaseResponse(code = "0", data = listOf("item1", "item2")))
    }

    @Test
    fun `test callback with custom data class`() {
        data class TestData(val name: String, val value: Int)
        
        var receivedData: TestData? = null
        
        val callback = object : BaseResponseCallback<TestData> {
            override fun onSuccess(response: BaseResponse<TestData>) {
                receivedData = response.data
            }
            override fun onFailed(response: BaseResponse<TestData>) {}
        }
        
        val testData = TestData("test", 456)
        callback.onSuccess(BaseResponse(code = "0", data = testData))
        
        assertEquals(testData, receivedData)
    }

    @Test
    fun `test callback error handling`() {
        var exceptionThrown = false
        
        val callback = object : BaseResponseCallback<String> {
            override fun onSuccess(response: BaseResponse<String>) {
                // 模拟在回调中抛出异常
                throw RuntimeException("Test exception")
            }
            override fun onFailed(response: BaseResponse<String>) {}
        }
        
        try {
            callback.onSuccess(BaseResponse(code = "0", data = "test"))
        } catch (e: RuntimeException) {
            exceptionThrown = true
            assertEquals("Test exception", e.message)
        }
        
        assertTrue(exceptionThrown)
    }

    @Test
    fun `test callback with null data`() {
        var successCallbackExecuted = false
        var failedCallbackExecuted = false
        
        val callback = object : BaseResponseCallback<String> {
            override fun onSuccess(response: BaseResponse<String>) {
                successCallbackExecuted = true
                assertNull(response.data)
            }
            override fun onFailed(response: BaseResponse<String>) {
                failedCallbackExecuted = true
                assertNull(response.data)
            }
        }
        
        // 测试成功响应但数据为null
        callback.onSuccess(BaseResponse(code = "0", data = null))
        assertTrue(successCallbackExecuted)
        
        // 测试失败响应数据为null
        callback.onFailed(BaseResponse(code = "3", data = null))
        assertTrue(failedCallbackExecuted)
    }
}