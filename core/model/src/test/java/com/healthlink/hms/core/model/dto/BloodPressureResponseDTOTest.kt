package com.healthlink.hms.core.model.dto

import org.junit.Assert.*
import org.junit.Test

/**
 * BloodPressureResponseDTO的单元测试
 * 测试血压响应数据传输对象的各种功能
 */
class BloodPressureResponseDTOTest {

    @Test
    fun `test constructor with all parameters`() {
        val nodeList = arrayListOf(
            BloodPressureItemDTO(
                startTime = "2024-01-01 00:00:00",
                endTime = "2024-01-01 23:59:59",
                systolicPressureAvg = 120.0f,
                systolicPressureMax = 130.0f,
                systolicPressureLast = 125.0f,
                diastolicPressureAvg = 80.0f,
                diastolicPressureMax = 85.0f,
                diastolicPressureMin = 75.0f,
                diastolicPressureLast = 82.0f,
                sphygmusAvg = 72.0f,
                sphygmusMax = 80.0f,
                sphygmusMin = 65.0f,
                sphygmusLast = 75.0f,
                createTime = "2024-01-01 12:00:00"
            )
        )
        
        val dto = BloodPressureResponseDTO(
            startTime = "2024-01-01 00:00:00",
            endTime = "2024-01-01 23:59:59",
            fetchTime = "2024-01-02 08:00:00",
            nodeList = nodeList
        )
        
        assertEquals("2024-01-01 00:00:00", dto.startTime)
        assertEquals("2024-01-01 23:59:59", dto.endTime)
        assertEquals("2024-01-02 08:00:00", dto.fetchTime)
        assertEquals(nodeList, dto.nodeList)
        assertEquals(1, dto.nodeList.size)
    }

    @Test
    fun `test constructor with empty node list`() {
        val dto = BloodPressureResponseDTO(
            startTime = "2024-01-01 00:00:00",
            endTime = "2024-01-01 23:59:59",
            fetchTime = "2024-01-02 08:00:00",
            nodeList = arrayListOf()
        )
        
        assertEquals("2024-01-01 00:00:00", dto.startTime)
        assertEquals("2024-01-01 23:59:59", dto.endTime)
        assertEquals("2024-01-02 08:00:00", dto.fetchTime)
        assertTrue(dto.nodeList.isEmpty())
    }

    @Test
    fun `test constructor with multiple nodes`() {
        val nodeList = arrayListOf(
            BloodPressureItemDTO(
                startTime = "2024-01-01 00:00:00",
                endTime = "2024-01-01 11:59:59",
                systolicPressureAvg = 120.0f,
                systolicPressureMax = 130.0f,
                systolicPressureLast = 125.0f,
                diastolicPressureAvg = 80.0f,
                diastolicPressureMax = 85.0f,
                diastolicPressureMin = 75.0f,
                diastolicPressureLast = 82.0f,
                sphygmusAvg = 72.0f,
                sphygmusMax = 80.0f,
                sphygmusMin = 65.0f,
                sphygmusLast = 75.0f,
                createTime = "2024-01-01 06:00:00"
            ),
            BloodPressureItemDTO(
                startTime = "2024-01-01 12:00:00",
                endTime = "2024-01-01 23:59:59",
                systolicPressureAvg = 115.0f,
                systolicPressureMax = 125.0f,
                systolicPressureLast = 120.0f,
                diastolicPressureAvg = 75.0f,
                diastolicPressureMax = 80.0f,
                diastolicPressureMin = 70.0f,
                diastolicPressureLast = 78.0f,
                sphygmusAvg = 68.0f,
                sphygmusMax = 75.0f,
                sphygmusMin = 60.0f,
                sphygmusLast = 70.0f,
                createTime = "2024-01-01 18:00:00"
            )
        )
        
        val dto = BloodPressureResponseDTO(
            startTime = "2024-01-01 00:00:00",
            endTime = "2024-01-01 23:59:59",
            fetchTime = "2024-01-02 08:00:00",
            nodeList = nodeList
        )
        
        assertEquals(2, dto.nodeList.size)
        assertEquals(120.0f, dto.nodeList[0].systolicPressureAvg)
        assertEquals(115.0f, dto.nodeList[1].systolicPressureAvg)
    }

    @Test
    fun `test data class equality`() {
        val nodeList1 = arrayListOf(
            BloodPressureItemDTO(
                startTime = "2024-01-01 00:00:00",
                endTime = "2024-01-01 23:59:59",
                systolicPressureAvg = 120.0f,
                systolicPressureMax = 130.0f,
                systolicPressureLast = 125.0f,
                diastolicPressureAvg = 80.0f,
                diastolicPressureMax = 85.0f,
                diastolicPressureMin = 75.0f,
                diastolicPressureLast = 82.0f,
                sphygmusAvg = 72.0f,
                sphygmusMax = 80.0f,
                sphygmusMin = 65.0f,
                sphygmusLast = 75.0f,
                createTime = "2024-01-01 12:00:00"
            )
        )
        
        val nodeList2 = arrayListOf(
            BloodPressureItemDTO(
                startTime = "2024-01-01 00:00:00",
                endTime = "2024-01-01 23:59:59",
                systolicPressureAvg = 120.0f,
                systolicPressureMax = 130.0f,
                systolicPressureLast = 125.0f,
                diastolicPressureAvg = 80.0f,
                diastolicPressureMax = 85.0f,
                diastolicPressureMin = 75.0f,
                diastolicPressureLast = 82.0f,
                sphygmusAvg = 72.0f,
                sphygmusMax = 80.0f,
                sphygmusMin = 65.0f,
                sphygmusLast = 75.0f,
                createTime = "2024-01-01 12:00:00"
            )
        )
        
        val dto1 = BloodPressureResponseDTO(
            startTime = "2024-01-01 00:00:00",
            endTime = "2024-01-01 23:59:59",
            fetchTime = "2024-01-02 08:00:00",
            nodeList = nodeList1
        )
        
        val dto2 = BloodPressureResponseDTO(
            startTime = "2024-01-01 00:00:00",
            endTime = "2024-01-01 23:59:59",
            fetchTime = "2024-01-02 08:00:00",
            nodeList = nodeList2
        )
        
        val dto3 = BloodPressureResponseDTO(
            startTime = "2024-01-02 00:00:00",
            endTime = "2024-01-01 23:59:59",
            fetchTime = "2024-01-02 08:00:00",
            nodeList = nodeList1
        )
        
        assertEquals(dto1, dto2)
        assertNotEquals(dto1, dto3)
    }

    @Test
    fun `test data class copy`() {
        val nodeList = arrayListOf(
            BloodPressureItemDTO(
                startTime = "2024-01-01 00:00:00",
                endTime = "2024-01-01 23:59:59",
                systolicPressureAvg = 120.0f,
                systolicPressureMax = 130.0f,
                systolicPressureLast = 125.0f,
                diastolicPressureAvg = 80.0f,
                diastolicPressureMax = 85.0f,
                diastolicPressureMin = 75.0f,
                diastolicPressureLast = 82.0f,
                sphygmusAvg = 72.0f,
                sphygmusMax = 80.0f,
                sphygmusMin = 65.0f,
                sphygmusLast = 75.0f,
                createTime = "2024-01-01 12:00:00"
            )
        )
        
        val original = BloodPressureResponseDTO(
            startTime = "2024-01-01 00:00:00",
            endTime = "2024-01-01 23:59:59",
            fetchTime = "2024-01-02 08:00:00",
            nodeList = nodeList
        )
        
        val copied = original.copy()
        assertEquals(original, copied)
        
        val modifiedCopy = original.copy(startTime = "2024-01-02 00:00:00")
        assertEquals("2024-01-02 00:00:00", modifiedCopy.startTime)
        assertEquals("2024-01-01 23:59:59", modifiedCopy.endTime)
        assertEquals("2024-01-02 08:00:00", modifiedCopy.fetchTime)
        assertEquals(nodeList, modifiedCopy.nodeList)
        assertNotEquals(original, modifiedCopy)
    }

    @Test
    fun `test data class destructuring`() {
        val nodeList = arrayListOf<BloodPressureItemDTO>()
        
        val dto = BloodPressureResponseDTO(
            startTime = "2024-01-01 00:00:00",
            endTime = "2024-01-01 23:59:59",
            fetchTime = "2024-01-02 08:00:00",
            nodeList = nodeList
        )
        
        val (startTime, endTime, fetchTime, nodes) = dto
        
        assertEquals("2024-01-01 00:00:00", startTime)
        assertEquals("2024-01-01 23:59:59", endTime)
        assertEquals("2024-01-02 08:00:00", fetchTime)
        assertEquals(nodeList, nodes)
    }

    @Test
    fun `test toString contains all fields`() {
        val nodeList = arrayListOf<BloodPressureItemDTO>()
        
        val dto = BloodPressureResponseDTO(
            startTime = "2024-01-01 00:00:00",
            endTime = "2024-01-01 23:59:59",
            fetchTime = "2024-01-02 08:00:00",
            nodeList = nodeList
        )
        
        val toString = dto.toString()
        assertTrue(toString.contains("startTime=2024-01-01 00:00:00"))
        assertTrue(toString.contains("endTime=2024-01-01 23:59:59"))
        assertTrue(toString.contains("fetchTime=2024-01-02 08:00:00"))
        assertTrue(toString.contains("nodeList"))
    }
}