package com.healthlink.hms.core.model

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.healthlink.hms.core.model.dto.*
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * 数据序列化/反序列化测试
 * 测试DTO类与JSON之间的转换功能
 */
class SerializationTest {

    private lateinit var gson: Gson

    @Before
    fun setUp() {
        gson = Gson()
    }

    @Test
    fun `test BaseResponse serialization and deserialization`() {
        val originalResponse = BaseResponse(
            code = "0",
            msg = "success",
            data = "test data"
        )
        
        // 序列化
        val json = gson.toJson(originalResponse)
        assertNotNull(json)
        assertTrue(json.contains("\"code\":\"0\""))
        assertTrue(json.contains("\"msg\":\"success\""))
        assertTrue(json.contains("\"data\":\"test data\""))
        
        // 反序列化
        val deserializedResponse = gson.fromJson(json, BaseResponse::class.java)
        assertEquals(originalResponse.code, deserializedResponse.code)
        assertEquals(originalResponse.msg, deserializedResponse.msg)
        assertEquals(originalResponse.data, deserializedResponse.data)
    }

    @Test
    fun `test HealthSummarizeDTO serialization and deserialization`() {
        val originalDto = HealthSummarizeDTO(
            riskLevel = "low",
            healthResult = "healthy",
            score = "85"
        )
        
        // 序列化
        val json = gson.toJson(originalDto)
        assertNotNull(json)
        assertTrue(json.contains("\"riskLevel\":\"low\""))
        assertTrue(json.contains("\"healthResult\":\"healthy\""))
        assertTrue(json.contains("\"score\":\"85\""))
        
        // 反序列化
        val deserializedDto = gson.fromJson(json, HealthSummarizeDTO::class.java)
        assertEquals(originalDto, deserializedDto)
    }

    @Test
    fun `test UserInfoDTO serialization and deserialization`() {
        val originalDto = UserInfoDTO(
            userId = "user123",
            nickName = "张三",
            gender = 1,
            birthYear = 1990,
            birthMonth = 5,
            height = 175,
            weight = 70.5f
        )
        
        // 序列化
        val json = gson.toJson(originalDto)
        assertNotNull(json)
        assertTrue(json.contains("\"userId\":\"user123\""))
        assertTrue(json.contains("\"nickName\":\"张三\""))
        assertTrue(json.contains("\"gender\":1"))
        assertTrue(json.contains("\"weight\":70.5"))
        
        // 反序列化
        val deserializedDto = gson.fromJson(json, UserInfoDTO::class.java)
        assertEquals(originalDto, deserializedDto)
    }

    @Test
    fun `test BloodPressureResponseDTO serialization and deserialization`() {
        val nodeList = arrayListOf(
            BloodPressureItemDTO(
                startTime = "2024-01-01 00:00:00",
                endTime = "2024-01-01 23:59:59",
                systolicPressureAvg = 120.0f,
                systolicPressureMax = 130.0f,
                systolicPressureLast = 125.0f,
                diastolicPressureAvg = 80.0f,
                diastolicPressureMax = 85.0f,
                diastolicPressureMin = 75.0f,
                diastolicPressureLast = 82.0f,
                sphygmusAvg = 72.0f,
                sphygmusMax = 80.0f,
                sphygmusMin = 65.0f,
                sphygmusLast = 75.0f,
                createTime = "2024-01-01 12:00:00"
            )
        )
        
        val originalDto = BloodPressureResponseDTO(
            startTime = "2024-01-01 00:00:00",
            endTime = "2024-01-01 23:59:59",
            fetchTime = "2024-01-02 08:00:00",
            nodeList = nodeList
        )
        
        // 序列化
        val json = gson.toJson(originalDto)
        assertNotNull(json)
        assertTrue(json.contains("\"startTime\":\"2024-01-01 00:00:00\""))
        assertTrue(json.contains("\"nodeList\":"))
        assertTrue(json.contains("\"systolicPressureAvg\":120.0"))
        
        // 反序列化
        val deserializedDto = gson.fromJson(json, BloodPressureResponseDTO::class.java)
        assertEquals(originalDto, deserializedDto)
        assertEquals(1, deserializedDto.nodeList.size)
        assertEquals(120.0f, deserializedDto.nodeList[0].systolicPressureAvg!!, 0.01f)
    }

    @Test
    fun `test serialization with null values`() {
        val dtoWithNulls = HealthSummarizeDTO(
            riskLevel = null,
            healthResult = "healthy",
            score = null
        )
        
        // 序列化
        val json = gson.toJson(dtoWithNulls)
        assertNotNull(json)
        assertTrue(json.contains("\"healthResult\":\"healthy\""))
        
        // 反序列化
        val deserializedDto = gson.fromJson(json, HealthSummarizeDTO::class.java)
        assertEquals(dtoWithNulls, deserializedDto)
        assertNull(deserializedDto.riskLevel)
        assertEquals("healthy", deserializedDto.healthResult)
        assertNull(deserializedDto.score)
    }

    @Test
    fun `test deserialization from incomplete JSON`() {
        val incompleteJson = """
            {
                "riskLevel": "medium",
                "score": "75"
            }
        """.trimIndent()
        
        val deserializedDto = gson.fromJson(incompleteJson, HealthSummarizeDTO::class.java)
        assertEquals("medium", deserializedDto.riskLevel)
        assertNull(deserializedDto.healthResult)
        assertEquals("75", deserializedDto.score)
    }

    @Test
    fun `test deserialization from JSON with extra fields`() {
        val jsonWithExtraFields = """
            {
                "riskLevel": "high",
                "healthResult": "warning",
                "score": "45",
                "extraField1": "ignored",
                "extraField2": 123
            }
        """.trimIndent()
        
        val deserializedDto = gson.fromJson(jsonWithExtraFields, HealthSummarizeDTO::class.java)
        assertEquals("high", deserializedDto.riskLevel)
        assertEquals("warning", deserializedDto.healthResult)
        assertEquals("45", deserializedDto.score)
    }

    @Test
    fun `test BaseResponse with generic DTO serialization`() {
        val healthDto = HealthSummarizeDTO(
            riskLevel = "low",
            healthResult = "excellent",
            score = "95"
        )
        
        val response = BaseResponse(
            code = "0",
            msg = "success",
            data = healthDto
        )
        
        // 序列化
        val json = gson.toJson(response)
        assertNotNull(json)
        assertTrue(json.contains("\"code\":\"0\""))
        assertTrue(json.contains("\"riskLevel\":\"low\""))
        
        // 注意：由于泛型类型擦除，直接反序列化BaseResponse<HealthSummarizeDTO>比较复杂
        // 这里我们测试JSON结构的正确性
        assertTrue(json.contains("\"data\":"))
        assertTrue(json.contains("\"healthResult\":\"excellent\""))
    }

    @Test
    fun `test empty collections serialization`() {
        val emptyResponse = BloodPressureResponseDTO(
            startTime = "2024-01-01 00:00:00",
            endTime = "2024-01-01 23:59:59",
            fetchTime = "2024-01-02 08:00:00",
            nodeList = arrayListOf()
        )
        
        // 序列化
        val json = gson.toJson(emptyResponse)
        assertNotNull(json)
        assertTrue(json.contains("\"nodeList\":[]"))
        
        // 反序列化
        val deserializedDto = gson.fromJson(json, BloodPressureResponseDTO::class.java)
        assertEquals(emptyResponse, deserializedDto)
        assertTrue(deserializedDto.nodeList.isEmpty())
    }

    @Test(expected = JsonSyntaxException::class)
    fun `test deserialization with invalid JSON`() {
        val invalidJson = "{ invalid json structure"
        gson.fromJson(invalidJson, HealthSummarizeDTO::class.java)
    }

    @Test
    fun `test special characters in JSON`() {
        val dtoWithSpecialChars = UserInfoDTO(
            userId = "user@123",
            nickName = "张三\"李四",
            gender = 1,
            birthYear = 1990,
            birthMonth = 5,
            height = 175,
            weight = 70.5f
        )
        
        // 序列化
        val json = gson.toJson(dtoWithSpecialChars)
        assertNotNull(json)
        
        // 反序列化
        val deserializedDto = gson.fromJson(json, UserInfoDTO::class.java)
        assertEquals(dtoWithSpecialChars, deserializedDto)
        assertEquals("张三\"李四", deserializedDto.nickName)
    }

    @Test
    fun `test large numbers serialization`() {
        val dtoWithLargeNumbers = UserInfoDTO(
            userId = "large_user",
            nickName = "大数值测试",
            gender = 1,
            birthYear = 2024,
            birthMonth = 12,
            height = 999999,
            weight = Float.MAX_VALUE
        )
        
        // 序列化
        val json = gson.toJson(dtoWithLargeNumbers)
        assertNotNull(json)
        
        // 反序列化
        val deserializedDto = gson.fromJson(json, UserInfoDTO::class.java)
        assertEquals(dtoWithLargeNumbers.height, deserializedDto.height)
        assertEquals(dtoWithLargeNumbers.weight!!, deserializedDto.weight!!, 0.01f)
    }
}