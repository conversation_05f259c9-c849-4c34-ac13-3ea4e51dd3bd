package com.healthlink.hms.core.model.dto

import org.junit.Assert.*
import org.junit.Test

/**
 * HealthSummarizeDTO的单元测试
 * 测试健康摘要数据传输对象的各种功能
 */
class HealthSummarizeDTOTest {

    @Test
    fun `test constructor with all parameters`() {
        val dto = HealthSummarizeDTO(
            riskLevel = "low",
            healthResult = "healthy",
            score = "85"
        )
        
        assertEquals("low", dto.riskLevel)
        assertEquals("healthy", dto.healthResult)
        assertEquals("85", dto.score)
    }

    @Test
    fun `test constructor with null parameters`() {
        val dto = HealthSummarizeDTO(
            riskLevel = null,
            healthResult = null,
            score = null
        )
        
        assertNull(dto.riskLevel)
        assertNull(dto.healthResult)
        assertNull(dto.score)
    }

    @Test
    fun `test constructor with mixed null and non-null parameters`() {
        val dto = HealthSummarizeDTO(
            riskLevel = "medium",
            healthResult = null,
            score = "75"
        )
        
        assertEquals("medium", dto.riskLevel)
        assertNull(dto.healthResult)
        assertEquals("75", dto.score)
    }

    @Test
    fun `test data class equality`() {
        val dto1 = HealthSummarizeDTO(
            riskLevel = "low",
            healthResult = "healthy",
            score = "85"
        )
        
        val dto2 = HealthSummarizeDTO(
            riskLevel = "low",
            healthResult = "healthy",
            score = "85"
        )
        
        val dto3 = HealthSummarizeDTO(
            riskLevel = "high",
            healthResult = "healthy",
            score = "85"
        )
        
        assertEquals(dto1, dto2)
        assertNotEquals(dto1, dto3)
    }

    @Test
    fun `test data class hashCode`() {
        val dto1 = HealthSummarizeDTO(
            riskLevel = "low",
            healthResult = "healthy",
            score = "85"
        )
        
        val dto2 = HealthSummarizeDTO(
            riskLevel = "low",
            healthResult = "healthy",
            score = "85"
        )
        
        assertEquals(dto1.hashCode(), dto2.hashCode())
    }

    @Test
    fun `test data class toString`() {
        val dto = HealthSummarizeDTO(
            riskLevel = "low",
            healthResult = "healthy",
            score = "85"
        )
        
        val toString = dto.toString()
        assertTrue(toString.contains("riskLevel=low"))
        assertTrue(toString.contains("healthResult=healthy"))
        assertTrue(toString.contains("score=85"))
    }

    @Test
    fun `test data class copy`() {
        val original = HealthSummarizeDTO(
            riskLevel = "low",
            healthResult = "healthy",
            score = "85"
        )
        
        val copied = original.copy()
        assertEquals(original, copied)
        
        val modifiedCopy = original.copy(riskLevel = "high")
        assertEquals("high", modifiedCopy.riskLevel)
        assertEquals("healthy", modifiedCopy.healthResult)
        assertEquals("85", modifiedCopy.score)
        assertNotEquals(original, modifiedCopy)
    }

    @Test
    fun `test data class destructuring`() {
        val dto = HealthSummarizeDTO(
            riskLevel = "medium",
            healthResult = "warning",
            score = "60"
        )
        
        val (riskLevel, healthResult, score) = dto
        
        assertEquals("medium", riskLevel)
        assertEquals("warning", healthResult)
        assertEquals("60", score)
    }

    @Test
    fun `test with empty strings`() {
        val dto = HealthSummarizeDTO(
            riskLevel = "",
            healthResult = "",
            score = ""
        )
        
        assertEquals("", dto.riskLevel)
        assertEquals("", dto.healthResult)
        assertEquals("", dto.score)
    }

    @Test
    fun `test with special characters`() {
        val dto = HealthSummarizeDTO(
            riskLevel = "high-risk",
            healthResult = "需要注意",
            score = "85.5%"
        )
        
        assertEquals("high-risk", dto.riskLevel)
        assertEquals("需要注意", dto.healthResult)
        assertEquals("85.5%", dto.score)
    }
}