package com.healthlink.hms.core.model

import org.junit.Assert.*
import org.junit.Test

/**
 * BaseResponse的单元测试
 * 测试基础响应类的各种功能和边界条件
 */
class BaseResponseTest {

    @Test
    fun `test default constructor creates response with failed status`() {
        val response = BaseResponse<String>()
        
        assertEquals("3", response.code)
        assertEquals("数据获取失败！请重试~", response.msg)
        assertNull(response.data)
        assertFalse(response.isSuccess())
    }

    @Test
    fun `test constructor with all parameters`() {
        val testData = "test data"
        val response = BaseResponse(
            code = "0",
            msg = "success",
            data = testData
        )
        
        assertEquals("0", response.code)
        assertEquals("success", response.msg)
        assertEquals(testData, response.data)
        assertTrue(response.isSuccess())
    }

    @Test
    fun `test isSuccess returns true for success code`() {
        val response = BaseResponse<String>(code = BusinessRespCode.ACCESS_SUCCESS)
        assertTrue(response.isSuccess())
    }

    @Test
    fun `test isSuccess returns false for failed codes`() {
        val failedCodes = listOf(
            BusinessRespCode.DATA_ACCESS_NO_AUTH,
            BusinessRespCode.HUAWEI_APP_CLOSE_SERVICE,
            BusinessRespCode.LOGIN_EXPIRE,
            BusinessRespCode.USER_LOGIN_TIME_OUT_OR_CANCEL_AUTH,
            BusinessRespCode.ACCESS_FAILED_NO_INTERNET,
            "3", // default failed code
            "unknown_code"
        )
        
        failedCodes.forEach { code ->
            val response = BaseResponse<String>(code = code)
            assertFalse("Code $code should return false for isSuccess()", response.isSuccess())
        }
    }

    @Test
    fun `test response with null code`() {
        val response = BaseResponse<String>(code = null)
        assertFalse(response.isSuccess())
    }

    @Test
    fun `test response with different data types`() {
        // Test with String data
        val stringResponse = BaseResponse(code = "0", data = "test string")
        assertEquals("test string", stringResponse.data)
        
        // Test with Int data
        val intResponse = BaseResponse(code = "0", data = 123)
        assertEquals(123, intResponse.data)
        
        // Test with List data
        val listData = listOf("item1", "item2")
        val listResponse = BaseResponse(code = "0", data = listData)
        assertEquals(listData, listResponse.data)
        
        // Test with custom object
        val customObject = TestDataClass("test", 456)
        val objectResponse = BaseResponse(code = "0", data = customObject)
        assertEquals(customObject, objectResponse.data)
    }

    @Test
    fun `test response equality`() {
        val response1 = BaseResponse(code = "0", msg = "success", data = "test")
        val response2 = BaseResponse(code = "0", msg = "success", data = "test")
        val response3 = BaseResponse(code = "1", msg = "success", data = "test")
        
        assertEquals(response1, response2)
        assertNotEquals(response1, response3)
    }

    @Test
    fun `test response toString`() {
        val response = BaseResponse(code = "0", msg = "success", data = "test")
        val toString = response.toString()
        
        assertTrue(toString.contains("code=0"))
        assertTrue(toString.contains("msg=success"))
        assertTrue(toString.contains("data=test"))
    }

    @Test
    fun `test response hashCode`() {
        val response1 = BaseResponse(code = "0", msg = "success", data = "test")
        val response2 = BaseResponse(code = "0", msg = "success", data = "test")
        
        assertEquals(response1.hashCode(), response2.hashCode())
    }

    @Test
    fun `test business response codes constants`() {
        assertEquals("5", BusinessRespCode.DATA_ACCESS_NO_AUTH)
        assertEquals("4", BusinessRespCode.HUAWEI_APP_CLOSE_SERVICE)
        assertEquals("2", BusinessRespCode.LOGIN_EXPIRE)
        assertEquals("7", BusinessRespCode.USER_LOGIN_TIME_OUT_OR_CANCEL_AUTH)
        assertEquals("0", BusinessRespCode.ACCESS_SUCCESS)
        assertEquals("666", BusinessRespCode.ACCESS_FAILED_NO_INTERNET)
    }

    // Test data class for testing generic types
    private data class TestDataClass(
        val name: String,
        val value: Int
    )
}