// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.compose) apply false
    alias(libs.plugins.hilt) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.jvm) apply false
    alias(libs.plugins.ksp) apply false
}

buildscript {
    repositories {
        jcenter()
        google()
        mavenCentral()
    }
}

//ext {
//    set("appVersionName", libs.versions.versionName.get())
//    set("appVersionCode",libs.versions.versionCode.get().toInt())
//}



