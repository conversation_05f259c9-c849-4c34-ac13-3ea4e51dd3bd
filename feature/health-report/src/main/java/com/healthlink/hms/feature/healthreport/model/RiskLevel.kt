package com.healthlink.hms.feature.healthreport.model

/**
 * 健康风险等级数据类
 */
data class RiskLevel(
    /**
     * 风险等级类型
     */
    val level: RiskType,
    
    /**
     * 风险因素列表
     */
    val factors: List<String>,
    
    /**
     * 健康建议列表
     */
    val recommendations: List<String>
) {
    /**
     * 获取风险等级描述
     */
    fun getLevelDescription(): String {
        return when (level) {
            RiskType.LOW -> "低风险"
            RiskType.MEDIUM -> "中等风险"
            RiskType.HIGH -> "高风险"
            RiskType.CRITICAL -> "严重风险"
        }
    }
    
    /**
     * 获取风险等级颜色资源ID（可根据需要调整）
     */
    fun getLevelColor(): String {
        return when (level) {
            RiskType.LOW -> "#4CAF50"      // 绿色
            RiskType.MEDIUM -> "#FF9800"   // 橙色
            RiskType.HIGH -> "#F44336"     // 红色
            RiskType.CRITICAL -> "#9C27B0" // 紫色
        }
    }
}