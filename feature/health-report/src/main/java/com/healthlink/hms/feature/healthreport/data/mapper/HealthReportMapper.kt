package com.healthlink.hms.feature.healthreport.data.mapper

import com.healthlink.hms.core.model.dto.HealthReportDTO
import com.healthlink.hms.core.model.dto.HistoryStatusDTO
import com.healthlink.hms.feature.healthreport.model.HealthReportData
import com.healthlink.hms.feature.healthreport.model.HealthScore
import com.healthlink.hms.feature.healthreport.model.ReportTimeRange
import com.healthlink.hms.feature.healthreport.model.RiskLevel
import com.healthlink.hms.feature.healthreport.model.RiskType
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 健康报告数据映射器
 * 负责将网络层DTO转换为Domain层模型
 */
@Singleton
class HealthReportMapper @Inject constructor() {
    
    /**
     * 将HealthReportDTO转换为HealthReportData
     */
    fun mapToHealthReportData(
        dto: HealthReportDTO,
        timeRange: ReportTimeRange
    ): HealthReportData {
        return HealthReportData(
            healthScore = mapToHealthScore(dto),
            riskLevel = mapToRiskLevel(dto),
            timeRange = timeRange,
            healthPart = "", // 使用空字符串，实际数据在具体字段中
            tripPart = "", // 使用空字符串
            summary = dto.riskDescription ?: "",
            advice = dto.healthAdvice ?: "",
            contrast = "" // 使用空字符串
        )
    }
    
    /**
     * 将DTO中的健康评分数据转换为HealthScore
     */
    private fun mapToHealthScore(dto: HealthReportDTO): HealthScore {
        val totalScore = dto.healthScore ?: 0
        
        // 根据总分确定健康等级
        val healthLevel = when {
            totalScore >= 90 -> com.healthlink.hms.feature.healthreport.model.HealthLevel.EXCELLENT
            totalScore >= 80 -> com.healthlink.hms.feature.healthreport.model.HealthLevel.GOOD
            totalScore >= 70 -> com.healthlink.hms.feature.healthreport.model.HealthLevel.NORMAL
            totalScore >= 60 -> com.healthlink.hms.feature.healthreport.model.HealthLevel.WARNING
            else -> com.healthlink.hms.feature.healthreport.model.HealthLevel.DANGER
        }
        
        // 生成健康描述
        val description = when (healthLevel) {
            com.healthlink.hms.feature.healthreport.model.HealthLevel.EXCELLENT -> "您的健康状况非常优秀，请继续保持！"
            com.healthlink.hms.feature.healthreport.model.HealthLevel.GOOD -> "您的健康状况良好，建议继续保持健康的生活方式。"
            com.healthlink.hms.feature.healthreport.model.HealthLevel.NORMAL -> "您的健康状况正常，建议适当关注健康指标。"
            com.healthlink.hms.feature.healthreport.model.HealthLevel.WARNING -> "您的健康状况需要注意，建议及时调整生活方式。"
            com.healthlink.hms.feature.healthreport.model.HealthLevel.DANGER -> "您的健康状况存在风险，建议尽快咨询医生。"
        }
        
        return HealthScore(
            score = totalScore.toString(),
            level = healthLevel,
            description = description
        )
    }
    
    /**
     * 将DTO中的风险数据转换为RiskLevel
     */
    private fun mapToRiskLevel(dto: HealthReportDTO): RiskLevel {
        val riskType = when (dto.riskLevel?.lowercase()) {
            "high" -> RiskType.HIGH
            "medium" -> RiskType.MEDIUM
            "low" -> RiskType.LOW
            "critical" -> RiskType.CRITICAL
            else -> RiskType.LOW
        }
        
        return RiskLevel(
            level = riskType,
            factors = emptyList(), // 暂时使用空列表
            recommendations = listOf(dto.healthAdvice ?: "保持健康的生活方式")
        )
    }
    
    /**
     * 从健康部分数据中提取特定指标的评分
     */
    private fun extractScoreFromHealthPart(healthPart: String?, indicator: String): Int {
        if (healthPart.isNullOrEmpty()) return 0
        
        // 这里可以根据实际的数据格式来解析
        // 假设healthPart是JSON格式或者特定格式的字符串
        return try {
            // 简单的解析逻辑，实际项目中可能需要更复杂的JSON解析
            val regex = "\"$indicator\"\\s*:\\s*(\\d+)".toRegex()
            val matchResult = regex.find(healthPart)
            matchResult?.groupValues?.get(1)?.toIntOrNull() ?: 0
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 解析风险因素
     */
    private fun parseRiskFactors(riskFactors: String?): List<String> {
        if (riskFactors.isNullOrEmpty()) return emptyList()
        
        return try {
            // 假设风险因素以逗号分隔
            riskFactors.split(",").map { it.trim() }.filter { it.isNotEmpty() }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 解析健康建议
     */
    private fun parseRecommendations(recommendations: String?): List<String> {
        if (recommendations.isNullOrEmpty()) return emptyList()
        
        return try {
            // 假设建议以逗号分隔
            recommendations.split(",").map { it.trim() }.filter { it.isNotEmpty() }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 将HistoryStatusDTO转换为布尔值，表示数据是否准备就绪
     */
    fun mapToDataReadyStatus(dto: HistoryStatusDTO): Boolean {
        return when (dto.statusType?.lowercase()) {
            "ready", "completed", "success" -> true
            else -> false
        }
    }
    
    /**
     * 创建时间范围对象
     */
    fun createTimeRange(timeCode: String): ReportTimeRange {
        return when (timeCode.lowercase()) {
            "day" -> ReportTimeRange(
                timeCode = "day",
                displayName = "日报",
                startTime = "",
                endTime = ""
            )
            "week" -> ReportTimeRange(
                timeCode = "week",
                displayName = "周报",
                startTime = "",
                endTime = ""
            )
            "month" -> ReportTimeRange(
                timeCode = "month",
                displayName = "月报",
                startTime = "",
                endTime = ""
            )
            "year" -> ReportTimeRange(
                timeCode = "year",
                displayName = "年报",
                startTime = "",
                endTime = ""
            )
            else -> ReportTimeRange(
                timeCode = "day",
                displayName = "日报",
                startTime = "",
                endTime = ""
            )
        }
    }
}