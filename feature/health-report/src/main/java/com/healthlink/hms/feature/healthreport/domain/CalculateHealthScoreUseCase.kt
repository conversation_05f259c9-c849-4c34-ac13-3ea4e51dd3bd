package com.healthlink.hms.feature.healthreport.domain

import com.healthlink.hms.core.model.dto.HealthReportDTO
import com.healthlink.hms.feature.healthreport.model.HealthScore
import com.healthlink.hms.feature.healthreport.model.HealthLevel
import kotlin.math.max
import kotlin.math.min
import javax.inject.Inject
import javax.inject.Singleton/**
 * 计算健康评分用例
 * 负责根据各项健康指标计算综合健康评分
 */
@Singleton
class CalculateHealthScoreUseCase @Inject constructor() {
    
    companion object {
        // 各项指标的权重
        private const val HEART_RATE_WEIGHT = 0.20f
        private const val SLEEP_WEIGHT = 0.25f
        private const val SPO2_WEIGHT = 0.15f
        private const val PRESSURE_WEIGHT = 0.15f
        private const val TEMPERATURE_WEIGHT = 0.10f
        private const val BLOOD_PRESSURE_WEIGHT = 0.15f
        
        // 评分范围
        private const val MIN_SCORE = 0
        private const val MAX_SCORE = 100
    }
    
    /**
     * 计算健康评分
     * @param healthReportDTO 健康报告DTO
     * @return 健康评分
     */
    operator fun invoke(healthReportDTO: HealthReportDTO): HealthScore {
        // 计算各项指标评分
        val heartRateScore = calculateHeartRateScore(healthReportDTO)
        val sleepScore = calculateSleepScore(healthReportDTO)
        val spo2Score = calculateSpO2Score(healthReportDTO)
        val pressureScore = calculatePressureScore(healthReportDTO)
        val temperatureScore = calculateTemperatureScore(healthReportDTO)
        val bloodPressureScore = calculateBloodPressureScore(healthReportDTO)
        
        // 计算加权总分
        val totalScore = calculateWeightedTotalScore(
            heartRateScore,
            sleepScore,
            spo2Score,
            pressureScore,
            temperatureScore,
            bloodPressureScore
        )
        
        // 根据总分确定健康等级
        val healthLevel = when {
            totalScore >= 90 -> HealthLevel.EXCELLENT
            totalScore >= 80 -> HealthLevel.GOOD
            totalScore >= 70 -> HealthLevel.NORMAL
            totalScore >= 60 -> HealthLevel.WARNING
            else -> HealthLevel.DANGER
        }
        
        // 生成健康描述
        val description = when (healthLevel) {
            HealthLevel.EXCELLENT -> "您的健康状况非常优秀，请继续保持！"
            HealthLevel.GOOD -> "您的健康状况良好，建议继续保持健康的生活方式。"
            HealthLevel.NORMAL -> "您的健康状况正常，建议适当关注健康指标。"
            HealthLevel.WARNING -> "您的健康状况需要注意，建议及时调整生活方式。"
            HealthLevel.DANGER -> "您的健康状况存在风险，建议尽快咨询医生。"
        }
        
        return HealthScore(
            score = totalScore.toString(),
            level = healthLevel,
            description = description
        )
    }
    
    /**
     * 计算心率评分
     */
    private fun calculateHeartRateScore(dto: HealthReportDTO): Int {
        // 从heartRateData中解析心率数据或使用默认逻辑
        val heartRateData = parseDataFromAny(dto.heartRateData)
        
        return when {
            heartRateData.isEmpty() -> 85 // 默认评分
            else -> {
                // 根据心率范围计算评分
                // 正常心率范围：60-100 bpm
                val avgHeartRate = heartRateData.average()
                when {
                    avgHeartRate in 60.0..100.0 -> 95
                    avgHeartRate in 50.0..60.0 || avgHeartRate in 100.0..120.0 -> 80
                    avgHeartRate in 40.0..50.0 || avgHeartRate in 120.0..140.0 -> 65
                    else -> 50
                }
            }
        }
    }
    
    /**
     * 计算睡眠评分
     */
    private fun calculateSleepScore(dto: HealthReportDTO): Int {
        val sleepData = parseDataFromAny(dto.sleepData)
        
        return when {
            sleepData.isEmpty() -> 80 // 默认评分
            else -> {
                // 根据睡眠时长计算评分
                // 理想睡眠时长：7-9小时
                val avgSleepHours = sleepData.average()
                when {
                    avgSleepHours in 7.0..9.0 -> 95
                    avgSleepHours in 6.0..7.0 || avgSleepHours in 9.0..10.0 -> 85
                    avgSleepHours in 5.0..6.0 || avgSleepHours in 10.0..11.0 -> 70
                    else -> 55
                }
            }
        }
    }
    
    /**
     * 计算血氧评分
     */
    private fun calculateSpO2Score(dto: HealthReportDTO): Int {
        val spo2Data = parseDataFromAny(dto.bloodOxygenData)
        
        return when {
            spo2Data.isEmpty() -> 90 // 默认评分
            else -> {
                // 正常血氧饱和度：95%-100%
                val avgSpO2 = spo2Data.average()
                when {
                    avgSpO2 >= 95.0 -> 95
                    avgSpO2 >= 90.0 -> 80
                    avgSpO2 >= 85.0 -> 65
                    else -> 50
                }
            }
        }
    }
    
    /**
     * 计算压力评分
     */
    private fun calculatePressureScore(dto: HealthReportDTO): Int {
        val pressureData = parseDataFromAny(dto.pressureData)
        
        return when {
            pressureData.isEmpty() -> 75 // 默认评分
            else -> {
                // 压力指数越低越好
                val avgPressure = pressureData.average()
                when {
                    avgPressure <= 30.0 -> 95
                    avgPressure <= 50.0 -> 85
                    avgPressure <= 70.0 -> 70
                    else -> 55
                }
            }
        }
    }
    
    /**
     * 计算体温评分
     */
    private fun calculateTemperatureScore(dto: HealthReportDTO): Int {
        val tempData = parseDataFromAny(dto.temperatureData)
        
        return when {
            tempData.isEmpty() -> 88 // 默认评分
            else -> {
                // 正常体温范围：36.1-37.2°C
                val avgTemp = tempData.average()
                when {
                    avgTemp in 36.1..37.2 -> 95
                    avgTemp in 35.5..36.1 || avgTemp in 37.2..37.8 -> 80
                    avgTemp in 35.0..35.5 || avgTemp in 37.8..38.5 -> 65
                    else -> 50
                }
            }
        }
    }
    
    /**
     * 计算血压评分
     */
    private fun calculateBloodPressureScore(dto: HealthReportDTO): Int {
        val bpData = parseDataFromAny(dto.bloodPressureData)
        
        return when {
            bpData.isEmpty() -> 82 // 默认评分
            else -> {
                // 理想血压：收缩压 < 120, 舒张压 < 80
                // 假设数据格式为 [收缩压, 舒张压] 的交替排列
                val systolic = bpData.filterIndexed { index, _ -> index % 2 == 0 }.average()
                val diastolic = bpData.filterIndexed { index, _ -> index % 2 == 1 }.average()
                
                when {
                    systolic < 120 && diastolic < 80 -> 95
                    systolic < 130 && diastolic < 85 -> 85
                    systolic < 140 && diastolic < 90 -> 70
                    else -> 55
                }
            }
        }
    }
    
    /**
     * 计算加权总分
     */
    private fun calculateWeightedTotalScore(
        heartRateScore: Int,
        sleepScore: Int,
        spo2Score: Int,
        pressureScore: Int,
        temperatureScore: Int,
        bloodPressureScore: Int
    ): Int {
        val weightedSum = heartRateScore * HEART_RATE_WEIGHT +
                sleepScore * SLEEP_WEIGHT +
                spo2Score * SPO2_WEIGHT +
                pressureScore * PRESSURE_WEIGHT +
                temperatureScore * TEMPERATURE_WEIGHT +
                bloodPressureScore * BLOOD_PRESSURE_WEIGHT
        
        return max(MIN_SCORE, min(MAX_SCORE, weightedSum.toInt()))
    }
    
    /**
     * 从Any类型数据中解析数值列表
     */
    private fun parseDataFromAny(data: Any?): List<Double> {
        if (data == null) return emptyList()
        
        return try {
            when (data) {
                is List<*> -> {
                    data.mapNotNull { 
                        when (it) {
                            is Number -> it.toDouble()
                            is String -> it.toDoubleOrNull()
                            else -> null
                        }
                    }
                }
                is String -> {
                    // 尝试解析JSON数组格式的字符串
                    val cleanData = data.trim().removePrefix("[").removeSuffix("]")
                    if (cleanData.isNotEmpty()) {
                        cleanData.split(",")
                            .mapNotNull { it.trim().toDoubleOrNull() }
                    } else {
                        emptyList()
                    }
                }
                is Number -> listOf(data.toDouble())
                else -> emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
}