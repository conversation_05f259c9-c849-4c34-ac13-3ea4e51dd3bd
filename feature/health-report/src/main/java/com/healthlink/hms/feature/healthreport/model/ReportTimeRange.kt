package com.healthlink.hms.feature.healthreport.model

/**
 * 报告时间范围数据模型
 * @param timeCode 时间代码
 * @param displayName 显示名称
 * @param startTime 开始时间
 * @param endTime 结束时间
 */
data class ReportTimeRange(
    val timeCode: String,
    val displayName: String,
    val startTime: String = "",
    val endTime: String = ""
)

/**
 * 时间范围类型枚举
 */
enum class TimeRangeType(val code: String, val displayName: String) {
    DAY("day", "日报"),
    WEEK("week", "周报"),
    MONTH("month", "月报"),
    YEAR("year", "年报")
}