package com.healthlink.hms.feature.healthreport.domain.repository

import com.healthlink.hms.core.model.dto.HealthReportDTO
import com.healthlink.hms.core.model.dto.HistoryStatusDTO
import com.healthlink.hms.feature.healthreport.data.remote.dto.HealthReportRequestDTO
import kotlinx.coroutines.flow.Flow

/**
 * 健康报告数据仓库接口
 * 定义健康报告相关的数据访问操作
 */
interface HealthReportRepository {
    
    /**
     * 获取健康报告数据
     * @param userId 用户ID
     * @param timeCode 时间代码
     * @param forceRefresh 是否强制刷新
     * @return 健康报告数据流
     */
    suspend fun getHealthReportData(
        userId: String,
        timeCode: String,
        forceRefresh: Boolean = false
    ): Flow<Result<HealthReportDTO>>
    
    /**
     * 获取历史状态数据
     * @param userId 用户ID
     * @param historyId 历史记录ID
     * @return 历史状态数据流
     */
    suspend fun getHistoryStatusData(
        userId: String,
        historyId: String
    ): Flow<Result<List<HistoryStatusDTO>>>
    
    /**
     * 提交健康报告请求
     * @param request 健康报告请求
     * @return 提交结果
     */
    suspend fun submitHealthReportRequest(
        request: HealthReportRequestDTO
    ): Result<Boolean>
    
    /**
     * 检查数据是否准备就绪
     * @param userId 用户ID
     * @param timeCode 时间代码
     * @return 数据准备状态
     */
    suspend fun checkDataReady(
        userId: String,
        timeCode: String
    ): Result<Boolean>
    
    /**
     * 清除缓存数据
     * @param userId 用户ID
     */
    suspend fun clearCache(userId: String)
    
    /**
     * 检查是否有缓存数据
     * @param userId 用户ID
     * @param timeCode 时间代码
     * @return 是否有缓存
     */
    suspend fun hasCachedData(userId: String, timeCode: String): Boolean
}