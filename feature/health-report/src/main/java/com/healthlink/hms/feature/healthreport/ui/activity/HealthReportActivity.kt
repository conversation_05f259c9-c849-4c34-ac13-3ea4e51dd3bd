package com.healthlink.hms.feature.healthreport.ui.activity

import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import com.healthlink.hms.core.common.utils.DataTrackUtil
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.feature.healthreport.R
import com.healthlink.hms.feature.healthreport.databinding.ActivityHealthReportBinding
import com.healthlink.hms.feature.healthreport.ui.fragment.ReportDayFragment
import com.healthlink.hms.feature.healthreport.viewmodel.HealthReportViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 健康报告Activity
 * 迁移自原HealthReportActivity，使用新的模块化架构
 */
@AndroidEntryPoint
class HealthReportActivity : AppCompatActivity() {
    
    private val viewModel: HealthReportViewModel by viewModels()
    private lateinit var binding: ActivityHealthReportBinding
    
    private val userId: String by lazy {
        MMKVUtil.getUserId() ?: ""
    }
    
    private val fragmentList = mutableListOf<ReportDayFragment>()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_health_report)
        binding.viewModel = viewModel
        binding.lifecycleOwner = this
        
        initFragments()
        setupObservers()
        
        // 数据埋点
        DataTrackUtil.dtEnterPage("Health_Healthreports_PV", DataTrackUtil.userIDMap(userId))
    }
    
    override fun onDestroy() {
        super.onDestroy()
        DataTrackUtil.dtExitPage("Health_Healthreports_Close", DataTrackUtil.userIDMap(userId))
    }
    
    /**
     * 初始化Fragment
     */
    private fun initFragments() {
        fragmentList.clear()
        fragmentList.add(ReportDayFragment.newInstance("day", userId, this))
        fragmentList.add(ReportDayFragment.newInstance("week", userId, this))
        fragmentList.add(ReportDayFragment.newInstance("month", userId, this))
        fragmentList.add(ReportDayFragment.newInstance("year", userId, this))
        
        // 设置ViewPager适配器
        setupViewPager()
    }
    
    /**
     * 设置ViewPager
     */
    private fun setupViewPager() {
        // 创建ViewPager2适配器
        val adapter = object : androidx.viewpager2.adapter.FragmentStateAdapter(this) {
            override fun createFragment(position: Int): androidx.fragment.app.Fragment {
                return fragmentList[position]
            }

            override fun getItemCount(): Int {
                return fragmentList.size
            }
        }
        
        binding.viewPager?.adapter = adapter
        binding.viewPager?.isUserInputEnabled = true
        
        // 设置TabLayout与ViewPager的关联
        setupTabLayout()
    }
    
    /**
     * 设置观察者
     */
    private fun setupObservers() {
        lifecycleScope.launch {
            viewModel.uiState.collect { uiState ->
                if (uiState.isLoading) {
                    showLoading()
                } else {
                    hideLoading()
                }
                
                uiState.errorMessage?.let { errorMessage ->
                    showError(errorMessage)
                }
            }
        }
    }
    
    /**
     * 设置TabLayout
     */
    private fun setupTabLayout() {
        val timeName = arrayOf("日", "周", "月", "年")
        
        binding.tabLayout?.let { tabLayout ->
            binding.viewPager?.let { viewPager ->
                val mediator = com.google.android.material.tabs.TabLayoutMediator(
                    tabLayout, viewPager
                ) { tab, position ->
                    tab.text = timeName[position]
                    
                    // 创建自定义TextView
                    val textView = android.widget.TextView(this).apply {
                        layoutParams = android.view.ViewGroup.LayoutParams(
                            android.view.ViewGroup.LayoutParams.WRAP_CONTENT,
                            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                        text = timeName[position]
                        setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, 26f)
                        gravity = android.view.Gravity.CENTER
                    }
                    tab.customView = textView
                }
                mediator.attach()
            }
        }
    }

    /**
     * 显示加载状态
     */
    private fun showLoading() {
        // 实现加载状态显示逻辑
        // 可以显示进度条或加载动画
    }

    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        // 实现隐藏加载状态逻辑
        // 隐藏进度条或加载动画
    }

    /**
     * 显示错误信息
     */
    private fun showError(message: String) {
        // 实现错误信息显示逻辑
        // 可以使用Toast或Snackbar显示错误信息
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 返回主页面
     */
    fun backToMain() {
        DataTrackUtil.dtClick(
            "Health_Healthreports_Return_Click",
            DataTrackUtil.userIDMap(userId)
        )
        finish()
    }
    
    /**
     * 获取卡片授权状态
     */
    fun getCardAuthStatus(): Boolean {
        return true
    }
    
    /**
     * 设置Tab可见性
     */
    fun setTabVisibilityforNetErrorOrSettingView(visibility: Int) {
        binding.tabLayout?.visibility = visibility
    }
}