package com.healthlink.hms.feature.healthreport.data.remote

import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.HealthReportDTO
import com.healthlink.hms.core.model.dto.HistoryStatusDTO
import com.healthlink.hms.feature.healthreport.data.remote.dto.HealthReportRequestDTO
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * 健康报告网络API接口
 */
interface HealthReportApi {
    
    /**
     * 获取健康报告数据
     * @param userId 用户ID
     * @param timeCode 时间代码（day/week/month/year）
     * @param vin 车辆识别码
     * @return 健康报告数据响应
     */
    @GET("health/report")
    suspend fun getHealthReport(
        @Query("userId") userId: String,
        @Query("timeCode") timeCode: String,
        @Query("vin") vin: String
    ): BaseResponse<HealthReportDTO>
    
    /**
     * 获取历史状态数据
     * @param userId 用户ID
     * @return 历史状态数据响应
     */
    @GET("health/history/status")
    suspend fun getHistoryStatus(
        @Query("userId") userId: String
    ): BaseResponse<HistoryStatusDTO>
    
    /**
     * 提交健康报告请求
     * @param request 健康报告请求参数
     * @return 提交结果响应
     */
    @POST("health/report/request")
    suspend fun submitHealthReportRequest(
        @Body request: HealthReportRequestDTO
    ): BaseResponse<String>
    
    /**
     * 检查数据是否准备就绪
     * @param userId 用户ID
     * @param timeCode 时间代码
     * @return 数据准备状态响应
     */
    @GET("health/report/ready")
    suspend fun checkDataReady(
        @Query("userId") userId: String,
        @Query("timeCode") timeCode: String
    ): BaseResponse<Boolean>
    
    /**
     * 获取健康报告生成进度
     * @param userId 用户ID
     * @param timeCode 时间代码
     * @return 生成进度响应（0-100）
     */
    @GET("health/report/progress")
    suspend fun getReportProgress(
        @Query("userId") userId: String,
        @Query("timeCode") timeCode: String
    ): BaseResponse<Int>
}