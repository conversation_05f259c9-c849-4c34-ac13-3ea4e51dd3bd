package com.healthlink.hms.feature.healthreport.data.repository

import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.HealthReportDTO
import com.healthlink.hms.core.model.dto.HistoryStatusDTO
import com.healthlink.hms.feature.healthreport.data.local.HealthReportCache
import com.healthlink.hms.feature.healthreport.data.mapper.HealthReportMapper
import com.healthlink.hms.feature.healthreport.data.remote.HealthReportApi
import com.healthlink.hms.feature.healthreport.data.remote.dto.HealthReportRequestDTO
import com.healthlink.hms.feature.healthreport.domain.repository.HealthReportRepository
import com.healthlink.hms.feature.healthreport.model.HealthReportData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.catch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 健康报告数据仓库默认实现
 */
@Singleton
class DefaultHealthReportRepository @Inject constructor(
    private val api: HealthReportApi,
    private val cache: HealthReportCache,
    private val mapper: HealthReportMapper
) : HealthReportRepository {
    
    override suspend fun getHealthReportData(
        userId: String,
        timeCode: String,
        forceRefresh: Boolean
    ): Flow<Result<HealthReportDTO>> = flow {
        try {
            // 如果不强制刷新，首先尝试从缓存获取
            if (!forceRefresh) {
                val cachedData = cache.getHealthReportData(timeCode)
                if (cachedData != null && !cache.isCacheExpired(timeCode)) {
                    emit(Result.success(convertToDTO(cachedData)))
                    return@flow
                }
            }
            
            // 缓存无效或不存在，从网络获取
            val response = api.getHealthReport(userId, timeCode, "")
            
            // 处理网络响应
            if (response.isSuccess() && response.data != null) {
                // 缓存网络数据
                val healthReportData = mapper.mapToHealthReportData(
                    response.data!!,
                    mapper.createTimeRange(timeCode)
                )
                cache.saveHealthReportData(timeCode, healthReportData)
                
                emit(Result.success(response.data!!))
            } else {
                emit(Result.failure(Exception(response.msg ?: "获取健康报告失败")))
            }
        } catch (e: Exception) {
            // 网络请求失败，尝试返回缓存数据
            val cachedData = cache.getHealthReportData(timeCode)
            if (cachedData != null) {
                emit(Result.success(convertToDTO(cachedData)))
            } else {
                emit(Result.failure(e))
            }
        }
    }
    
    override suspend fun getHistoryStatusData(
        userId: String,
        historyId: String
    ): Flow<Result<List<HistoryStatusDTO>>> = flow {
        try {
            val response = api.getHistoryStatus(userId)
            if (response.isSuccess() && response.data != null) {
                // 将单个HistoryStatusDTO包装成List
                emit(Result.success(listOf(response.data!!)))
            } else {
                emit(Result.failure(Exception(response.msg ?: "获取历史状态失败")))
            }
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }
    
    override suspend fun submitHealthReportRequest(
        request: HealthReportRequestDTO
    ): Result<Boolean> {
        return try {
            val response = api.submitHealthReportRequest(request)
            if (response.isSuccess()) {
                Result.success(true)
            } else {
                Result.failure(Exception(response.msg ?: "提交请求失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun checkDataReady(
        userId: String,
        timeCode: String
    ): Result<Boolean> {
        return try {
            val response = api.checkDataReady(userId, timeCode)
            if (response.isSuccess() && response.data != null) {
                Result.success(response.data ?: false)
            } else {
                Result.success(false)
            }
        } catch (e: Exception) {
            Result.success(false)
        }
    }
    
    override suspend fun clearCache(userId: String) {
        cache.clearAllData()
    }
    
    override suspend fun hasCachedData(userId: String, timeCode: String): Boolean {
        val cachedData = cache.getHealthReportData(timeCode)
        return cachedData != null && !cache.isCacheExpired(timeCode)
    }
    

    
    /**
     * 将HealthReportData转换为HealthReportDTO（用于缓存数据的响应）
     */
    private fun convertToDTO(data: HealthReportData): HealthReportDTO {
        return HealthReportDTO(
            userId = "", // 可以从其他地方获取
            timeCode = data.timeRange.timeCode,
            healthScore = data.healthScore.score.toIntOrNull(),
            healthLevel = data.healthScore.level.name.lowercase(),
            riskLevel = data.riskLevel.level.name.lowercase(),
            healthAdvice = data.advice,
            riskDescription = data.summary,
            createTime = System.currentTimeMillis().toString(),
            startTime = data.timeRange.startTime,
            endTime = data.timeRange.endTime
        )
    }
}