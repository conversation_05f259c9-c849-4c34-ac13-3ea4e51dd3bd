package com.healthlink.hms.feature.healthreport.domain

import com.healthlink.hms.feature.healthreport.data.mapper.HealthReportMapper
import com.healthlink.hms.feature.healthreport.domain.repository.HealthReportRepository
import com.healthlink.hms.feature.healthreport.model.HealthReportData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.catch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 生成健康报告用例
 * 负责协调健康报告的生成流程，包括数据获取、处理和缓存
 */
@Singleton
class GenerateHealthReportUseCase @Inject constructor(
    private val repository: HealthReportRepository,
    private val mapper: HealthReportMapper
) {
    
    /**
     * 生成健康报告
     * @param userId 用户ID
     * @param timeCode 时间代码（day/week/month/year）
     * @param vin 车辆识别码
     * @return 健康报告数据流
     */
    suspend operator fun invoke(
        userId: String,
        timeCode: String,
        vin: String
    ): Flow<Result<HealthReportData>> {
        return repository.getHealthReportData(userId, timeCode, false)
            .map { result ->
                result.fold(
                    onSuccess = { healthReportDTO ->
                        val timeRange = mapper.createTimeRange(timeCode)
                        val healthReportData = mapper.mapToHealthReportData(healthReportDTO, timeRange)
                        Result.success(healthReportData)
                    },
                    onFailure = { exception ->
                        Result.failure(exception)
                    }
                )
            }
            .catch { exception ->
                emit(Result.failure(exception))
            }
    }
    
    /**
     * 获取缓存的健康报告
     * @param timeCode 时间代码
     * @return 缓存的健康报告数据，如果缓存不存在或已过期则返回null
     */
    suspend fun getCachedReport(timeCode: String): HealthReportData? {
        return try {
            // 这里应该通过其他方式检查缓存，暂时返回null
            // 实际实现需要根据具体的缓存策略来调整
            null
        } catch (e: Exception) {
            // 缓存读取失败，返回null
            null
        }
    }
    
    /**
     * 刷新健康报告数据
     * 清除缓存并重新获取数据
     * @param userId 用户ID
     * @param timeCode 时间代码
     * @param vin 车辆识别码
     * @return 刷新后的健康报告数据流
     */
    suspend fun refreshReport(
        userId: String,
        timeCode: String,
        vin: String
    ): Flow<Result<HealthReportData>> {
        // 清除用户的缓存数据
        repository.clearCache(userId)
        // 重新获取数据
        return invoke(userId, timeCode, vin)
    }
    
    /**
     * 预加载健康报告数据
     * 在后台预先获取数据并缓存，提升用户体验
     * @param userId 用户ID
     * @param timeCodes 需要预加载的时间代码列表
     * @param vin 车辆识别码
     */
    suspend fun preloadReports(
        userId: String,
        timeCodes: List<String>,
        vin: String
    ) {
        timeCodes.forEach { timeCode ->
            try {
                // 检查缓存是否存在且有效
                val cachedData = getCachedReport(timeCode)
                if (cachedData == null) {
                    // 缓存不存在，预加载数据
                    invoke(userId, timeCode, vin).collect { result ->
                        // 数据会自动缓存到Repository中
                    }
                }
            } catch (e: Exception) {
                // 预加载失败不影响主流程
                e.printStackTrace()
            }
        }
    }
    
    /**
     * 检查是否有可用的缓存数据
     * @param timeCode 时间代码
     * @return 是否有有效的缓存数据
     */
    suspend fun hasCachedData(timeCode: String): Boolean {
        return getCachedReport(timeCode) != null
    }
    
    /**
     * 获取所有时间维度的缓存状态
     * @return 各时间维度的缓存状态映射
     */
    suspend fun getCacheStatus(): Map<String, Boolean> {
        val timeCodes = listOf("day", "week", "month", "year")
        return timeCodes.associateWith { timeCode ->
            hasCachedData(timeCode)
        }
    }
}