package com.healthlink.hms.feature.healthreport.di

import com.healthlink.hms.feature.healthreport.data.local.HealthReportCache
import com.healthlink.hms.feature.healthreport.data.mapper.HealthReportMapper
import com.healthlink.hms.feature.healthreport.data.remote.HealthReportApi
import com.healthlink.hms.feature.healthreport.data.repository.DefaultHealthReportRepository
import com.healthlink.hms.feature.healthreport.domain.repository.HealthReportRepository
import com.google.gson.Gson
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton

/**
 * 健康报告模块的Hilt依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class HealthReportModule {
    
    /**
     * 绑定健康报告数据仓库实现
     */
    @Binds
    @Singleton
    abstract fun bindHealthReportRepository(
        defaultHealthReportRepository: DefaultHealthReportRepository
    ): HealthReportRepository
    
    companion object {
        
        /**
         * 提供健康报告API接口
         */
        @Provides
        @Singleton
        fun provideHealthReportApi(retrofit: Retrofit): HealthReportApi {
            return retrofit.create(HealthReportApi::class.java)
        }
        
        /**
         * 提供Gson实例
         */
        @Provides
        @Singleton
        fun provideGson(): Gson {
            return Gson()
        }
    }
}