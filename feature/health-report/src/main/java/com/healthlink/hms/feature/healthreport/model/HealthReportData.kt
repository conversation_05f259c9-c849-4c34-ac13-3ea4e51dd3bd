package com.healthlink.hms.feature.healthreport.model

/**
 * 健康报告数据模型
 * @param healthScore 健康评分
 * @param riskLevel 风险等级
 * @param timeRange 时间范围
 * @param healthPart 健康部分数据
 * @param tripPart 行程部分数据
 * @param summary 健康总结
 * @param advice 健康建议
 * @param contrast 同期对比
 */
data class HealthReportData(
    val healthScore: HealthScore = HealthScore(),
    val riskLevel: RiskLevel = RiskLevel(
        level = RiskType.LOW,
        factors = emptyList(),
        recommendations = emptyList()
    ),
    val timeRange: ReportTimeRange,
    val healthPart: String = "",
    val tripPart: String = "",
    val summary: String = "",
    val advice: String = "",
    val contrast: String = ""
)

/**
 * 健康报告状态
 */
data class HealthReportStatus(
    val isDataReady: Boolean = false,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val lastUpdateTime: Long = 0L
)