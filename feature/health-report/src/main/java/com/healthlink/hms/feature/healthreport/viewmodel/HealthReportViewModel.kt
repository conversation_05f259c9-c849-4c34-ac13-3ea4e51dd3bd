package com.healthlink.hms.feature.healthreport.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.healthlink.hms.core.model.dto.HistoryStatusDTO
import com.healthlink.hms.feature.healthreport.domain.GenerateHealthReportUseCase
import com.healthlink.hms.feature.healthreport.domain.usecase.GetHealthHistoryUseCase
import com.healthlink.hms.feature.healthreport.model.HealthReportData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 健康报告ViewModel
 * 使用现代化的状态管理，采用StateFlow替代LiveData
 */
@HiltViewModel
class HealthReportViewModel @Inject constructor(
    private val generateHealthReportUseCase: GenerateHealthReportUseCase,
    private val getHealthHistoryUseCase: GetHealthHistoryUseCase
) : ViewModel() {
    
    // UI状态封装
    data class UiState(
        val isLoading: Boolean = false,
        val healthReportData: HealthReportData? = null,
        val historyStatusData: HistoryStatusDTO? = null,
        val errorMessage: String? = null,
        val isDataReady: Boolean = false,
        val lastUpdateTime: Long = 0L
    )
    
    // 主要UI状态
    private val _uiState = MutableStateFlow(UiState())
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()
    
    // 当前选中的时间代码
    private val _currentTimeCode = MutableStateFlow("day")
    val currentTimeCode: StateFlow<String> = _currentTimeCode.asStateFlow()
    
    // 缓存状态
    private val _cacheStatus = MutableStateFlow<Map<String, Boolean>>(emptyMap())
    val cacheStatus: StateFlow<Map<String, Boolean>> = _cacheStatus.asStateFlow()
    
    /**
     * 获取健康报告数据
     * @param userId 用户ID
     * @param timeCode 时间代码
     * @param vin 车辆识别码
     */
    fun getHealthReportData(userId: String, timeCode: String, vin: String) {
        viewModelScope.launch {
            // 更新当前时间代码
            _currentTimeCode.value = timeCode
            
            // 先尝试获取缓存数据
            val cachedData = generateHealthReportUseCase.getCachedReport(timeCode)
            if (cachedData != null) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    healthReportData = cachedData,
                    isDataReady = true,
                    lastUpdateTime = System.currentTimeMillis(),
                    errorMessage = null
                )
                return@launch
            }
            
            // 设置加载状态
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                errorMessage = null
            )
            
            // 获取新数据
            generateHealthReportUseCase(userId, timeCode, vin)
                .collect { result ->
                    result.fold(
                        onSuccess = { data ->
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                healthReportData = data,
                                isDataReady = true,
                                lastUpdateTime = System.currentTimeMillis(),
                                errorMessage = null
                            )
                        },
                        onFailure = { exception ->
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                isDataReady = false,
                                errorMessage = exception.message ?: "获取健康报告数据失败"
                            )
                        }
                    )
                }
        }
    }
    
    /**
     * 获取历史状态数据
     * @param userId 用户ID
     */
    fun getHistoryStatusData(userId: String) {
        viewModelScope.launch {
            getHealthHistoryUseCase(userId)
                .collect { result ->
                    result.fold(
                        onSuccess = { data ->
                            _uiState.value = _uiState.value.copy(
                                historyStatusData = data,
                                errorMessage = null
                            )
                        },
                        onFailure = { exception ->
                            _uiState.value = _uiState.value.copy(
                                errorMessage = exception.message ?: "获取历史状态数据失败"
                            )
                        }
                    )
                }
        }
    }
    
    /**
     * 刷新健康报告数据
     */
    fun refreshHealthReportData(userId: String, timeCode: String, vin: String) {
        viewModelScope.launch {
            // 设置加载状态
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                errorMessage = null
            )
            
            // 使用UseCase的刷新方法
            generateHealthReportUseCase.refreshReport(userId, timeCode, vin)
                .collect { result ->
                    result.fold(
                        onSuccess = { data ->
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                healthReportData = data,
                                isDataReady = true,
                                lastUpdateTime = System.currentTimeMillis(),
                                errorMessage = null
                            )
                        },
                        onFailure = { exception ->
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                errorMessage = exception.message ?: "刷新健康报告数据失败"
                            )
                        }
                    )
                }
        }
    }
    
    /**
     * 切换时间维度
     * @param timeCode 新的时间代码
     * @param userId 用户ID
     * @param vin 车辆识别码
     */
    fun switchTimeCode(timeCode: String, userId: String, vin: String) {
        if (_currentTimeCode.value != timeCode) {
            getHealthReportData(userId, timeCode, vin)
        }
    }
    
    /**
     * 预加载所有时间维度的数据
     * @param userId 用户ID
     * @param vin 车辆识别码
     */
    fun preloadAllReports(userId: String, vin: String) {
        viewModelScope.launch {
            val timeCodes = listOf("day", "week", "month", "year")
            generateHealthReportUseCase.preloadReports(userId, timeCodes, vin)
            
            // 更新缓存状态
            updateCacheStatus()
        }
    }
    
    /**
     * 更新缓存状态
     */
    fun updateCacheStatus() {
        viewModelScope.launch {
            val cacheStatus = generateHealthReportUseCase.getCacheStatus()
            _cacheStatus.value = cacheStatus
        }
    }
    
    /**
     * 清除错误信息
     */
    fun clearErrorMessage() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    /**
     * 获取当前健康报告数据
     */
    fun getCurrentHealthReportData(): HealthReportData? {
        return _uiState.value.healthReportData
    }
    
    /**
     * 检查数据是否准备就绪
     */
    fun isDataReady(): Boolean {
        return _uiState.value.isDataReady
    }
    
    /**
     * 检查是否正在加载
     */
    fun isLoading(): Boolean {
        return _uiState.value.isLoading
    }
    
    /**
     * 获取错误信息
     */
    fun getErrorMessage(): String? {
        return _uiState.value.errorMessage
    }
    
    /**
     * 检查指定时间代码是否有缓存
     */
    fun hasCachedData(timeCode: String): Boolean {
        return _cacheStatus.value[timeCode] == true
    }
}