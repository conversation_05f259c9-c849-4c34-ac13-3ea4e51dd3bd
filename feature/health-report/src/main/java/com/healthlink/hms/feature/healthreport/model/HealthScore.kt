package com.healthlink.hms.feature.healthreport.model

/**
 * 健康评分数据模型
 * @param score 健康评分值
 * @param level 健康等级
 * @param description 健康描述
 */
data class HealthScore(
    val score: String = "0",
    val level: HealthLevel = HealthLevel.NORMAL,
    val description: String = ""
)

/**
 * 健康等级枚举
 */
enum class HealthLevel {
    EXCELLENT,  // 优秀
    GOOD,       // 良好
    NORMAL,     // 正常
    WARNING,    // 警告
    DANGER      // 危险
}