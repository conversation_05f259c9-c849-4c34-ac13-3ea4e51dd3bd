package com.healthlink.hms.feature.healthreport.data.remote.dto

/**
 * 健康报告请求数据传输对象
 */
data class HealthReportRequestDTO(
    /**
     * 用户ID
     */
    val userId: String,
    
    /**
     * 时间代码（day/week/month/year）
     */
    val timeCode: String,
    
    /**
     * 车辆识别码
     */
    val vin: String,
    
    /**
     * 请求时间戳
     */
    val requestTime: Long = System.currentTimeMillis(),
    
    /**
     * 客户端版本
     */
    val clientVersion: String? = null,
    
    /**
     * 设备信息
     */
    val deviceInfo: String? = null,
    
    /**
     * 扩展参数
     */
    val extParams: Map<String, String>? = null
)