package com.healthlink.hms.feature.healthreport.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.healthlink.hms.core.common.utils.DataTrackUtil
import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.feature.healthreport.R
import com.healthlink.hms.feature.healthreport.databinding.FragmentReportDayBinding
import com.healthlink.hms.feature.healthreport.model.HealthReportData
import com.healthlink.hms.feature.healthreport.ui.activity.HealthReportActivity
import com.healthlink.hms.feature.healthreport.ui.charts.StatisticsView
import com.healthlink.hms.feature.healthreport.viewmodel.HealthReportViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

/**
 * 健康报告日报Fragment
 * 迁移自原ReportDayFragment，使用新的模块化架构
 */
@AndroidEntryPoint
class ReportDayFragment : Fragment() {
    
    private val viewModel: HealthReportViewModel by viewModels()
    private var _binding: FragmentReportDayBinding? = null
    private val binding get() = _binding!!
    
    private var timeCode: String? = null
    private var userId: String = ""
    private var isDataReady = false
    private var isAnimationPlayed = false
    
    private lateinit var chartView: StatisticsView
    private var chartDataList: MutableList<StatisticsView.StatisticsItem> = arrayListOf()
    
    companion object {
        private const val ARG_TIME_CODE = "ARG_TIME_CODE"
        private const val ARG_USER_ID = "ARG_USER_ID"
        private var _fragmentInteractWithAC: WeakReference<HealthReportActivity>? = null
        
        fun newInstance(
            timeCode: String,
            userId: String,
            activity: HealthReportActivity
        ): ReportDayFragment {
            val fragment = ReportDayFragment()
            val args = Bundle()
            args.putString(ARG_TIME_CODE, timeCode)
            args.putString(ARG_USER_ID, userId)
            fragment.arguments = args
            _fragmentInteractWithAC = WeakReference(activity)
            return fragment
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            timeCode = it.getString(ARG_TIME_CODE)
            userId = it.getString(ARG_USER_ID) ?: ""
        }
        isAnimationPlayed = false
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DataBindingUtil.inflate(inflater, R.layout.fragment_report_day, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()
        setupObservers()
        loadData()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    
    /**
     * 初始化UI
     */
    private fun initUI() {
        // 初始化统计图表
        chartView = binding.statisticsView
        chartView.setOnPopWindowListener(object : StatisticsView.PopWindowListener {
            override fun onHeartRateClick(
                clickable: Boolean,
                offsetX: Float,
                offsetY: Float,
                directionRight: Boolean
            ) {
                if (clickable) {
                    // 处理心率点击事件
                    DataTrackUtil.dtClick("Health_Healthreports_HeartRate_Click")
                }
            }
            
            override fun onSleepClick(
                clickable: Boolean,
                offsetX: Float,
                offsetY: Float,
                directionRight: Boolean
            ) {
                if (clickable) {
                    // 处理睡眠点击事件
                    DataTrackUtil.dtClick("Health_Healthreports_Sleep_Click")
                }
            }
            
            override fun onSpo2Click(
                clickable: Boolean,
                offsetX: Float,
                offsetY: Float,
                directionRight: Boolean
            ) {
                if (clickable) {
                    // 处理血氧点击事件
                    DataTrackUtil.dtClick("Health_Healthreports_SpO2_Click")
                }
            }
            
            override fun onPressureClick(
                clickable: Boolean,
                offsetX: Float,
                offsetY: Float,
                directionRight: Boolean
            ) {
                if (clickable) {
                    // 处理压力点击事件
                    DataTrackUtil.dtClick("Health_Healthreports_Pressure_Click")
                }
            }
            
            override fun onTempertureClick(
                clickable: Boolean,
                offsetX: Float,
                offsetY: Float,
                directionRight: Boolean
            ) {
                if (clickable) {
                    // 处理体温点击事件
                    DataTrackUtil.dtClick("Health_Healthreports_Temperature_Click")
                }
            }
            
            override fun onBloodPressurClick(
                clickable: Boolean,
                offsetX: Float,
                offsetY: Float,
                directionRight: Boolean
            ) {
                if (clickable) {
                    // 处理血压点击事件
                    DataTrackUtil.dtClick("Health_Healthreports_BloodPressure_Click")
                }
            }
        })
    }
    
    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察UI状态变化
        lifecycleScope.launch {
            viewModel.uiState.collect { uiState ->
                // 处理加载状态
                if (uiState.isLoading) {
                    showLoading()
                } else {
                    hideLoading()
                }
                
                // 处理健康报告数据
                uiState.healthReportData?.let { data ->
                    updateUI(data)
                }
                
                // 处理数据准备状态
                if (uiState.isDataReady) {
                    isDataReady = true
                    binding.reportScroll.visibility = View.VISIBLE
                    _fragmentInteractWithAC?.get()?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
                }
                
                // 处理错误信息
                uiState.errorMessage?.let { errorMessage ->
                    showNoDataView(errorMessage)
                }
                
                // 处理历史状态数据
                uiState.historyStatusData?.let { historyStatus ->
                    // 这里需要根据实际的HistoryStatusDTO结构来处理
                    // 假设有一个status字段
                    checkDataReadyStatus("ready") // 临时处理，需要根据实际数据结构调整
                }
            }
        }
    }
    
    /**
     * 加载数据
     */
    private fun loadData() {
        timeCode?.let { code ->
            val vin = MMKVUtil.getVinCode() ?: ""
            
            // 先获取历史状态数据
            viewModel.getHistoryStatusData(userId)
            
            // 根据数据埋点
            when (code) {
                "day" -> DataTrackUtil.dtClick("Health_Healthreports_Daytab_Click")
                "week" -> DataTrackUtil.dtClick("Health_Healthreports_Weektab_Click")
                "month" -> DataTrackUtil.dtClick("Health_Healthreports_Mouthtab_Click")
                "year" -> DataTrackUtil.dtClick("Health_Healthreports_Yeartab_Click")
            }
        }
    }
    
    /**
     * 检查数据准备状态
     */
    private fun checkDataReadyStatus(status: String) {
        // 简化状态检查逻辑
        val isReady = status == "ready" || status == "2"
        
        if (isReady) {
            getDataReady()
        } else {
            showNoDataView("健康报告分析中，请稍等……")
        }
    }
    
    /**
     * 数据准备就绪
     */
    private fun getDataReady() {
        isDataReady = true
        timeCode?.let { code ->
            val vin = MMKVUtil.getVinCode() ?: ""
            viewModel.getHealthReportData(userId, code, vin)
        }
    }
    
    /**
     * 更新UI
     */
    private fun updateUI(data: HealthReportData) {
        // 更新健康评分
        binding.healthScore.text = data.healthScore.score
        binding.healthDescription.text = data.healthScore.description
        
        // 更新健康总结
        binding.healthSummary.text = data.summary
        
        // 更新健康建议
        binding.healthAdvice.text = data.advice
        
        // 更新统计图表
        updateChartData(data)
    }
    
    /**
     * 更新图表数据
     */
    private fun updateChartData(data: HealthReportData) {
        // 这里需要根据实际的健康数据来构建图表数据
        // 示例数据，实际需要根据data中的健康指标来构建
        chartDataList.clear()
        chartDataList.add(StatisticsView.StatisticsItem("心率", true, 80, 15, 5))
        chartDataList.add(StatisticsView.StatisticsItem("睡眠", true, 75, 20, 5))
        chartDataList.add(StatisticsView.StatisticsItem("血氧", true, 85, 10, 5))
        chartDataList.add(StatisticsView.StatisticsItem("压力", true, 70, 25, 5))
        chartDataList.add(StatisticsView.StatisticsItem("体温", true, 90, 8, 2))
        chartDataList.add(StatisticsView.StatisticsItem("血压", true, 78, 18, 4))
        
        chartView.setValue(ArrayList(chartDataList))
    }
    
    /**
     * 显示加载状态
     */
    private fun showLoading() {
        binding.loadingProgress.visibility = View.VISIBLE
        binding.reportScroll.visibility = View.GONE
    }
    
    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        binding.loadingProgress.visibility = View.GONE
    }
    
    /**
     * 显示无数据视图
     */
    private fun showNoDataView(message: String) {
        binding.noDataLayout.visibility = View.VISIBLE
        binding.reportScroll.visibility = View.GONE
        _fragmentInteractWithAC?.get()?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
    }
    
    override fun onResume() {
        super.onResume()
        if (isDataReady) {
            timeCode?.let { code ->
                val vin = MMKVUtil.getVinCode() ?: ""
                viewModel.getHealthReportData(userId, code, vin)
            }
        }
    }
}