package com.healthlink.hms.feature.healthreport.data.local

import com.healthlink.hms.core.common.utils.MMKVUtil
import com.healthlink.hms.feature.healthreport.model.HealthReportData
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject
import javax.inject.Singleton
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 健康报告本地缓存管理器
 */
@Singleton
class HealthReportCache @Inject constructor(
    private val gson: Gson
) {
    
    private val mutex = Mutex()
    private val memoryCache = mutableMapOf<String, CacheEntry>()
    
    companion object {
        private const val CACHE_PREFIX = "health_report_"
        private const val CACHE_TIME_PREFIX = "health_report_time_"
        private const val CACHE_EXPIRY_DURATION = 30 * 60 * 1000L // 30分钟
    }
    
    /**
     * 缓存条目
     */
    private data class CacheEntry(
        val data: HealthReportData,
        val timestamp: Long
    )
    
    /**
     * 保存健康报告数据到缓存
     */
    suspend fun saveHealthReportData(timeCode: String, data: HealthReportData) {
        mutex.withLock {
            val timestamp = System.currentTimeMillis()
            val cacheEntry = CacheEntry(data, timestamp)
            
            // 内存缓存
            memoryCache[timeCode] = cacheEntry
            
            // 持久化缓存
            try {
                val dataJson = gson.toJson(data)
                MMKVUtil.putString(CACHE_PREFIX + timeCode, dataJson)
                MMKVUtil.putLong(CACHE_TIME_PREFIX + timeCode, timestamp)
            } catch (e: Exception) {
                // 序列化失败，只保留内存缓存
                e.printStackTrace()
            }
        }
    }
    
    /**
     * 从缓存获取健康报告数据
     */
    suspend fun getHealthReportData(timeCode: String): HealthReportData? {
        return mutex.withLock {
            // 先检查内存缓存
            val memoryCacheEntry = memoryCache[timeCode]
            if (memoryCacheEntry != null && !isExpired(memoryCacheEntry.timestamp)) {
                return@withLock memoryCacheEntry.data
            }
            
            // 检查持久化缓存
            try {
                val dataJson = MMKVUtil.getString(CACHE_PREFIX + timeCode)
                val timestamp = MMKVUtil.getLong(CACHE_TIME_PREFIX + timeCode, 0L)
                
                if (dataJson != null && timestamp > 0 && !isExpired(timestamp)) {
                    val type = object : TypeToken<HealthReportData>() {}.type
                    val data = gson.fromJson<HealthReportData>(dataJson, type)
                    
                    // 更新内存缓存
                    memoryCache[timeCode] = CacheEntry(data, timestamp)
                    return@withLock data
                }
            } catch (e: Exception) {
                // 反序列化失败
                e.printStackTrace()
                // 清理损坏的缓存
                clearHealthReportData(timeCode)
            }
            
            null
        }
    }
    
    /**
     * 检查缓存是否过期
     */
    suspend fun isCacheExpired(timeCode: String): Boolean {
        return mutex.withLock {
            // 检查内存缓存
            val memoryCacheEntry = memoryCache[timeCode]
            if (memoryCacheEntry != null) {
                return@withLock isExpired(memoryCacheEntry.timestamp)
            }
            
            // 检查持久化缓存
            val timestamp = MMKVUtil.getLong(CACHE_TIME_PREFIX + timeCode, 0L)
            return@withLock timestamp == 0L || isExpired(timestamp)
        }
    }
    
    /**
     * 清理指定时间代码的缓存数据
     */
    suspend fun clearHealthReportData(timeCode: String) {
        mutex.withLock {
            // 清理内存缓存
            memoryCache.remove(timeCode)
            
            // 清理持久化缓存
            MMKVUtil.remove(CACHE_PREFIX + timeCode)
            MMKVUtil.remove(CACHE_TIME_PREFIX + timeCode)
        }
    }
    
    /**
     * 清理所有缓存数据
     */
    suspend fun clearAllData() {
        mutex.withLock {
            // 清理内存缓存
            memoryCache.clear()
            
            // 清理持久化缓存
            val allKeys = MMKVUtil.getAllKeys()
            allKeys?.forEach { key ->
                if (key.startsWith(CACHE_PREFIX) || key.startsWith(CACHE_TIME_PREFIX)) {
                    MMKVUtil.remove(key)
                }
            }
        }
    }
    
    /**
     * 获取缓存大小（条目数量）
     */
    suspend fun getCacheSize(): Int {
        return mutex.withLock {
            memoryCache.size
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    suspend fun getCacheStats(): CacheStats {
        return mutex.withLock {
            val totalEntries = memoryCache.size
            val expiredEntries = memoryCache.values.count { isExpired(it.timestamp) }
            val validEntries = totalEntries - expiredEntries
            
            CacheStats(
                totalEntries = totalEntries,
                validEntries = validEntries,
                expiredEntries = expiredEntries
            )
        }
    }
    
    /**
     * 清理过期的缓存条目
     */
    suspend fun cleanupExpiredEntries() {
        mutex.withLock {
            val currentTime = System.currentTimeMillis()
            val expiredKeys = memoryCache.filter { (_, entry) ->
                isExpired(entry.timestamp)
            }.keys
            
            expiredKeys.forEach { key ->
                memoryCache.remove(key)
                MMKVUtil.remove(CACHE_PREFIX + key)
                MMKVUtil.remove(CACHE_TIME_PREFIX + key)
            }
        }
    }
    
    /**
     * 检查时间戳是否过期
     */
    private fun isExpired(timestamp: Long): Boolean {
        return System.currentTimeMillis() - timestamp > CACHE_EXPIRY_DURATION
    }
    
    /**
     * 缓存统计信息
     */
    data class CacheStats(
        val totalEntries: Int,
        val validEntries: Int,
        val expiredEntries: Int
    )
}