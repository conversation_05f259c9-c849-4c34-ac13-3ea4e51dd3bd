package com.healthlink.hms.feature.healthreport.domain.usecase

import com.healthlink.hms.core.model.dto.HistoryStatusDTO
import com.healthlink.hms.feature.healthreport.data.mapper.HealthReportMapper
import com.healthlink.hms.feature.healthreport.domain.repository.HealthReportRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.catch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 获取健康历史数据用例
 * 负责获取和处理用户的健康历史状态数据
 */
@Singleton
class GetHealthHistoryUseCase @Inject constructor(
    private val repository: HealthReportRepository,
    private val mapper: HealthReportMapper
) {
    
    /**
     * 获取健康历史状态数据
     * @param userId 用户ID
     * @return 历史状态数据流
     */
    suspend operator fun invoke(userId: String): Flow<Result<HistoryStatusDTO>> {
        return repository.getHistoryStatusData(userId, "")
            .map { result ->
                result.fold(
                    onSuccess = { historyList ->
                        // 取第一个历史状态数据，如果列表为空则返回失败
                        if (historyList.isNotEmpty()) {
                            Result.success(historyList.first())
                        } else {
                            Result.failure(Exception("历史状态数据为空"))
                        }
                    },
                    onFailure = { exception ->
                        Result.failure(exception)
                    }
                )
            }
            .catch { exception ->
                emit(Result.failure(exception))
            }
    }
    
    /**
     * 检查数据是否准备就绪
     * @param userId 用户ID
     * @return 数据准备状态
     */
    suspend fun checkDataReady(userId: String): Flow<Result<Boolean>> {
        return repository.getHistoryStatusData(userId, "")
            .map { result ->
                result.fold(
                    onSuccess = { historyList ->
                        if (historyList.isNotEmpty()) {
                            val isReady = mapper.mapToDataReadyStatus(historyList.first())
                            Result.success(isReady)
                        } else {
                            Result.success(false) // 默认为未准备就绪
                        }
                    },
                    onFailure = { _ ->
                        Result.success(false) // 默认为未准备就绪
                    }
                )
            }
            .catch { exception ->
                emit(Result.success(false)) // 异常时默认为未准备就绪
            }
    }
    
    /**
     * 获取历史状态描述
     * @param userId 用户ID
     * @return 状态描述字符串
     */
    suspend fun getStatusDescription(userId: String): Flow<Result<String>> {
        return repository.getHistoryStatusData(userId, "")
            .map { result ->
                result.fold(
                    onSuccess = { historyList ->
                        if (historyList.isNotEmpty()) {
                            val description = historyList.first().statusDescription ?: "状态正常"
                            Result.success(description)
                        } else {
                            Result.success("状态未知")
                        }
                    },
                    onFailure = { _ ->
                        Result.success("状态未知")
                    }
                )
            }
            .catch { exception ->
                emit(Result.success("状态获取失败"))
            }
    }
    
    /**
     * 获取最后更新时间
     * @param userId 用户ID
     * @return 最后更新时间戳
     */
    suspend fun getLastUpdateTime(userId: String): Flow<Result<String>> {
        return repository.getHistoryStatusData(userId, "")
            .map { result ->
                result.fold(
                    onSuccess = { historyList ->
                        if (historyList.isNotEmpty()) {
                            val updateTime = historyList.first().updateTime ?: "未知"
                            Result.success(updateTime)
                        } else {
                            Result.success("未知")
                        }
                    },
                    onFailure = { _ ->
                        Result.success("未知")
                    }
                )
            }
            .catch { exception ->
                emit(Result.success("未知"))
            }
    }
}