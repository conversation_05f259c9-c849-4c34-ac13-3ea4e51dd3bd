<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.healthlink.hms.feature.healthreport.viewmodel.HealthReportViewModel" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 主要内容滚动视图 -->
        <ScrollView
            android:id="@+id/reportScroll"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- 健康评分卡片 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="健康评分"
                            android:textColor="@android:color/black"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/healthScore"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:textColor="?attr/colorPrimary"
                            android:textSize="32sp"
                            android:textStyle="bold"
                            tools:text="85" />

                        <TextView
                            android:id="@+id/healthDescription"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:textColor="@android:color/black"
                            android:textSize="14sp"
                            tools:text="您的健康状况良好，建议继续关注健康指标变化。" />

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- 统计图表卡片 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="健康指标统计"
                            android:textColor="@android:color/black"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <com.healthlink.hms.feature.healthreport.ui.charts.StatisticsView
                            android:id="@+id/statisticsView"
                            android:layout_width="match_parent"
                            android:layout_height="200dp"
                            android:layout_marginTop="16dp" />

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- 健康总结卡片 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="健康总结"
                            android:textColor="@android:color/black"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/healthSummary"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:textColor="@android:color/black"
                            android:textSize="14sp"
                            tools:text="本周您的整体健康状况良好，各项指标基本正常。" />

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- 健康建议卡片 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="健康建议"
                            android:textColor="@android:color/black"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/healthAdvice"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:textColor="@android:color/black"
                            android:textSize="14sp"
                            tools:text="建议您保持规律作息，适量运动，均衡饮食。" />

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

            </LinearLayout>
        </ScrollView>

        <!-- 加载状态 -->
        <ProgressBar
            android:id="@+id/loadingProgress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="visible" />

        <!-- 无数据状态 -->
        <LinearLayout
            android:id="@+id/noDataLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="暂无数据"
                android:textColor="@android:color/darker_gray"
                android:textSize="16sp" />

        </LinearLayout>

    </FrameLayout>
</layout>