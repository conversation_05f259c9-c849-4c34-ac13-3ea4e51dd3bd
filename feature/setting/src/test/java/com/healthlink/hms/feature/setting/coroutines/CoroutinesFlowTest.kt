package com.healthlink.hms.feature.setting.coroutines

import app.cash.turbine.test
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.feature.setting.utils.MainDispatcherRule
import com.healthlink.hms.feature.setting.utils.TestUtils
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.time.Duration.Companion.seconds

/**
 * 协程和 Flow 测试的专门测试用例
 * 展示各种高级测试技巧和最佳实践
 */
@ExperimentalCoroutinesApi
class CoroutinesFlowTest {
    
    private val testDispatcher = StandardTestDispatcher()
    private val testScope = TestScope(testDispatcher)
    @get:Rule
    private val mainDispatcherRule = MainDispatcherRule(testDispatcher)
    
    // ========== Flow 基础测试 ==========
    
    @Test
    fun `test simple flow emission`() = runTest {
        // Given
        val testFlow = flowOf(1, 2, 3, 4, 5)
        
        // When & Then
        testFlow.test {
            assertEquals(1, awaitItem())
            assertEquals(2, awaitItem())
            assertEquals(3, awaitItem())
            assertEquals(4, awaitItem())
            assertEquals(5, awaitItem())
            awaitComplete()
        }
    }
    
    @Test
    fun `test flow with transformation`() = runTest {
        // Given
        val sourceFlow = flowOf(1, 2, 3)
        val transformedFlow = sourceFlow.map { it * 2 }
        
        // When & Then
        transformedFlow.test {
            assertEquals(2, awaitItem())
            assertEquals(4, awaitItem())
            assertEquals(6, awaitItem())
            awaitComplete()
        }
    }
    
    @Test
    fun `test flow with delay using virtual time`() = runTest {
        // Given
        val delayedFlow = flow {
            emit("start")
            delay(1000) // 1 second delay
            emit("middle")
            delay(2000) // 2 seconds delay
            emit("end")
        }
        
        // When & Then
        delayedFlow.test {
            assertEquals("start", awaitItem())
            
            // 推进虚拟时间
            testScheduler.advanceTimeBy(1000)
            assertEquals("middle", awaitItem())
            
            testScheduler.advanceTimeBy(2000)
            assertEquals("end", awaitItem())
            
            awaitComplete()
        }
    }
    
    // ========== 错误处理测试 ==========
    
    @Test
    fun `test flow error handling`() = runTest {
        // Given
        val errorFlow = flow<String> {
            emit("success")
            throw RuntimeException("Test error")
        }
        
        // When & Then
        errorFlow.test {
            assertEquals("success", awaitItem())
            val error = awaitError()
            assertTrue(error is RuntimeException)
            assertEquals("Test error", error.message)
        }
    }
    
    @Test
    fun `test flow error recovery with catch`() = runTest {
        // Given
        val errorFlow = flow<String> {
            emit("success")
            throw RuntimeException("Test error")
        }.catch { emit("recovered") }
        
        // When & Then
        errorFlow.test {
            assertEquals("success", awaitItem())
            assertEquals("recovered", awaitItem())
            awaitComplete()
        }
    }
    
    // ========== BaseResponse Flow 测试 ==========
    
    @Test
    fun `test BaseResponse success flow`() = runTest {
        // Given
        val successResponse = TestUtils.createSuccessResponse("test data")
        val responseFlow = flowOf(successResponse)
        
        // When & Then
        responseFlow.test {
            val response = awaitItem()
            assertEquals("0", response.code)
            assertEquals("success", response.msg)
            assertEquals("test data", response.data)
            awaitComplete()
        }
    }
    
    @Test
    fun `test BaseResponse error flow`() = runTest {
        // Given
        val errorResponse = TestUtils.createErrorResponse<String>("500", "Server error")
        val responseFlow = flowOf(errorResponse)
        
        // When & Then
        responseFlow.test {
            val response = awaitItem()
            assertEquals("500", response.code)
            assertEquals("Server error", response.msg)
            assertEquals(null, response.data)
            awaitComplete()
        }
    }
    
    // ========== 复杂 Flow 操作测试 ==========
    
    @Test
    fun `test flow with multiple operations`() = runTest {
        // Given
        val complexFlow = flow {
            emit(1)
            delay(100)
            emit(2)
            delay(100)
            emit(3)
        }
            .map { it * 10 }
            .onEach { delay(50) }
            .flowOn(testDispatcher)
        
        // When & Then
        complexFlow.test {
            assertEquals(10, awaitItem())
            
            testScheduler.advanceTimeBy(150) // 100 + 50
            assertEquals(20, awaitItem())
            
            testScheduler.advanceTimeBy(150) // 100 + 50
            assertEquals(30, awaitItem())
            
            awaitComplete()
        }
    }
    
    @Test
    fun `test flow cancellation`() = runTest {
        // Given
        val infiniteFlow = flow {
            var count = 0
            while (true) {
                emit(count++)
                delay(100)
            }
        }
        
        // When & Then
        infiniteFlow.test {
            assertEquals(0, awaitItem())
            
            testScheduler.advanceTimeBy(100)
            assertEquals(1, awaitItem())
            
            testScheduler.advanceTimeBy(100)
            assertEquals(2, awaitItem())
            
            // 取消 Flow
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    // ========== 并发测试 ==========
    
    @Test
    fun `test concurrent flows`() = runTest {
        // Given
        val flow1 = flow {
            delay(100)
            emit("flow1")
        }
        
        val flow2 = flow {
            delay(200)
            emit("flow2")
        }
        
        // When & Then
        flow1.test {
            testScheduler.advanceTimeBy(100)
            assertEquals("flow1", awaitItem())
            awaitComplete()
        }
        
        flow2.test {
            testScheduler.advanceTimeBy(200)
            assertEquals("flow2", awaitItem())
            awaitComplete()
        }
    }
    
    // ========== 实际业务场景测试 ==========
    
    @Test
    fun `test repository-like flow with network simulation`() = runTest {
        // Given - 模拟网络请求
        val networkFlow = flow {
            delay(1000) // 模拟网络延迟
            emit(TestUtils.createSuccessResponse("network data"))
        }
        
        // When & Then
        networkFlow.test {
            // 验证没有立即发射
            expectNoEvents()
            
            // 推进时间模拟网络请求完成
            testScheduler.advanceTimeBy(1000)
            
            val response = awaitItem()
            assertEquals("0", response.code)
            assertEquals("network data", response.data)
            awaitComplete()
        }
    }
    
    @Test
    fun `test flow timeout scenario`() = runTest {
        // Given
        val timeoutFlow = flow {
            delay(5000) // 5 seconds delay
            emit("too late")
        }
        
        // When & Then
        timeoutFlow.test(timeout = 3.seconds) {
            // 这个测试会在 3 秒后超时，因为 flow 需要 5 秒才能发射
            expectNoEvents()
            
            // 推进时间但不足以完成 flow
            testScheduler.advanceTimeBy(3000)
            expectNoEvents()
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    // ========== StateFlow 和 SharedFlow 测试模式 ==========
    
    @Test
    fun `test state flow behavior`() = runTest {
        // Given
        val stateFlow = kotlinx.coroutines.flow.MutableStateFlow("initial")
        
        // When & Then
        stateFlow.test {
            // StateFlow 会立即发射当前值
            assertEquals("initial", awaitItem())
            
            // 更新状态
            stateFlow.value = "updated"
            assertEquals("updated", awaitItem())
            
            // 设置相同值不会触发新的发射
            stateFlow.value = "updated"
            expectNoEvents()
            
            cancelAndIgnoreRemainingEvents()
        }
    }
}