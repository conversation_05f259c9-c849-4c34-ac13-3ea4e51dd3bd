package com.healthlink.hms.feature.setting.viewmodels

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import app.cash.turbine.test
import com.healthlink.hms.core.data.repository.SystemRepository
import com.healthlink.hms.core.data.repository.UserRepository
import com.healthlink.hms.core.model.dto.UserInfoDTO
import com.healthlink.hms.feature.setting.di.SettingsRepository
import com.healthlink.hms.feature.setting.utils.MainDispatcherRule
import com.healthlink.hms.feature.setting.utils.TestUtils
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * SettingsViewModel 单元测试
 * 测试所有业务逻辑和状态管理
 */
@ExperimentalCoroutinesApi
@RunWith(MockitoJUnitRunner::class)
class SettingsViewModelTest {
    
    @Mock
    private lateinit var settingsRepository: SettingsRepository
    
    @Mock
    private lateinit var systemRepository: SystemRepository
    
    @Mock
    private lateinit var userRepository: UserRepository
    
    private lateinit var viewModel: SettingsViewModel
    
    @get:Rule
    private val mainDispatcherRule = MainDispatcherRule()
    
    @get:Rule
    private val instantTaskExecutorRule = InstantTaskExecutorRule()
    
    @Before
    fun setup() {
        viewModel = SettingsViewModel(settingsRepository, systemRepository, userRepository)
    }
    
    // ========== 用户注销测试 ==========
    
    @Test
    fun `deleteUser should emit success event when repository returns success`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val successFlow = TestUtils.createSuccessFlow(true)
        whenever(settingsRepository.deleteUser(userId)).thenReturn(successFlow)
        
        // When
        viewModel.deleteUser(userId)
        
        // Then
        viewModel.uiEvent.test {
            val event1 = awaitItem()
            assertTrue(event1 is SettingsUiEvent.DeleteUserSuccess)
            
            val event2 = awaitItem()
            assertTrue(event2 is SettingsUiEvent.ShowToast)
            assertEquals("注销账户成功", (event2 as SettingsUiEvent.ShowToast).message)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    @Test
    fun `deleteUser should emit error toast when repository returns failure`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val errorFlow = TestUtils.createErrorFlow<Boolean>("1", "删除失败")
        whenever(settingsRepository.deleteUser(userId)).thenReturn(errorFlow)
        
        // When
        viewModel.deleteUser(userId)
        
        // Then
        viewModel.uiEvent.test {
            val event = awaitItem()
            assertTrue(event is SettingsUiEvent.ShowToast)
            assertEquals("注销账户失败", (event as SettingsUiEvent.ShowToast).message)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    @Test
    fun `deleteUser should handle exception and emit error toast`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        whenever(settingsRepository.deleteUser(userId)).thenThrow(RuntimeException("Network error"))
        
        // When
        viewModel.deleteUser(userId)
        
        // Then
        viewModel.uiEvent.test {
            val event = awaitItem()
            assertTrue(event is SettingsUiEvent.ShowToast)
            assertTrue((event as SettingsUiEvent.ShowToast).message.contains("注销失败"))
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    // ========== 解绑账户测试 ==========
    
    @Test
    fun `unbindAccount should emit success event when repository returns success`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val successFlow = TestUtils.createSuccessFlow(true)
        whenever(settingsRepository.unbindAccount(userId)).thenReturn(successFlow)
        
        // When
        viewModel.unbindAccount(userId)
        
        // Then
        viewModel.uiEvent.test {
            val event1 = awaitItem()
            assertTrue(event1 is SettingsUiEvent.UnbindAccountSuccess)
            
            val event2 = awaitItem()
            assertTrue(event2 is SettingsUiEvent.ShowToast)
            assertEquals("解绑成功", (event2 as SettingsUiEvent.ShowToast).message)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    @Test
    fun `unbindAccount should emit error toast when repository returns failure`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val errorFlow = TestUtils.createErrorFlow<Boolean>("1", "解绑失败")
        whenever(settingsRepository.unbindAccount(userId)).thenReturn(errorFlow)
        
        // When
        viewModel.unbindAccount(userId)
        
        // Then
        viewModel.uiEvent.test {
            val event = awaitItem()
            assertTrue(event is SettingsUiEvent.ShowToast)
            assertEquals("解绑失败", (event as SettingsUiEvent.ShowToast).message)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    // ========== 获取用户信息测试 ==========
    
    @Test
    fun `getUserInfo should update UI state when repository returns success`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val userInfo = TestUtils.createTestUserInfo()
        val successFlow = TestUtils.createSuccessFlow(userInfo)
        whenever(settingsRepository.getUserInfo(userId)).thenReturn(successFlow)
        
        // When
        viewModel.getUserInfo(userId)
        
        // Then
        viewModel.uiState.test {
            val initialState = awaitItem()
            assertNull(initialState.userInfo)
            
            val updatedState = awaitItem()
            assertEquals(userInfo, updatedState.userInfo)
            assertFalse(updatedState.isLoading)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    @Test
    fun `getUserInfo should emit error toast when repository returns failure`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val errorFlow = TestUtils.createErrorFlow<UserInfoDTO>("1", "获取失败")
        whenever(settingsRepository.getUserInfo(userId)).thenReturn(errorFlow)
        
        // When
        viewModel.getUserInfo(userId)
        
        // Then
        viewModel.uiEvent.test {
            val event = awaitItem()
            assertTrue(event is SettingsUiEvent.ShowToast)
            assertEquals("获取用户信息失败", (event as SettingsUiEvent.ShowToast).message)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    // ========== 保存用户信息测试 ==========
    
    @Test
    fun `saveUserInfo should emit success event when repository returns success`() = runTest {
        // Given
        val requestParam = TestUtils.createTestSaveUserParams()
        val successFlow = TestUtils.createSuccessFlow(true)
        whenever(settingsRepository.saveUserInfo(requestParam)).thenReturn(successFlow)
        
        // When
        viewModel.saveUserInfo(requestParam)
        
        // Then
        viewModel.uiEvent.test {
            val event = awaitItem()
            assertTrue(event is SettingsUiEvent.SaveUserInfoSuccess)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    @Test
    fun `saveUserInfo should emit failed event when repository returns failure`() = runTest {
        // Given
        val requestParam = TestUtils.createTestSaveUserParams()
        val errorFlow = TestUtils.createErrorFlow<Boolean>("1", "保存失败")
        whenever(settingsRepository.saveUserInfo(requestParam)).thenReturn(errorFlow)
        
        // When
        viewModel.saveUserInfo(requestParam)
        
        // Then
        viewModel.uiEvent.test {
            val event = awaitItem()
            assertTrue(event is SettingsUiEvent.SaveUserInfoFailed)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    // ========== 本地通知状态测试 ==========
    
    @Test
    fun `notificationState should reflect repository state`() = runTest {
        // Given
        val expectedState = true
        whenever(systemRepository.getNotificationState()).thenReturn(kotlinx.coroutines.flow.flowOf(expectedState))
        
        // When & Then
        viewModel.notificationState.test {
            val state = awaitItem()
            assertEquals(expectedState, state)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    @Test
    fun `updateNotificationState should call repository and update UI state`() = runTest {
        // Given
        val newState = true
        
        // When
        viewModel.updateNotificationState(newState)
        
        // Then
        verify(systemRepository).storeNotificationOpen(newState)
        
        viewModel.uiState.test {
            val initialState = awaitItem()
            assertFalse(initialState.isNotificationEnabled)
            
            val updatedState = awaitItem()
            assertEquals(newState, updatedState.isNotificationEnabled)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    

    
    // ========== 获取当前用户ID测试 ==========
    
    @Test
    fun `userId should reflect repository state`() = runTest {
        // Given
        val expectedUserId = TestUtils.TEST_USER_ID
        whenever(userRepository.getUserId()).thenReturn(kotlinx.coroutines.flow.flowOf(expectedUserId))
        
        // When & Then
        viewModel.userId.test {
            val userId = awaitItem()
            assertEquals(expectedUserId, userId)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    // ========== 加载状态测试 ==========
    
    @Test
    fun `loading state should be managed correctly during operations`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val successFlow = TestUtils.createSuccessFlow(true)
        whenever(settingsRepository.deleteUser(userId)).thenReturn(successFlow)
        
        // When
        viewModel.deleteUser(userId)
        
        // Then
        viewModel.isLoading.test {
            val initialLoading = awaitItem()
            assertFalse(initialLoading)
            
            val loadingState = awaitItem()
            assertTrue(loadingState)
            
            val finalLoading = awaitItem()
            assertFalse(finalLoading)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
    
    // ========== 错误处理测试 ==========
    
    @Test
    fun `clearError should clear error state`() = runTest {
        // Given - 先设置一个错误状态
        val userId = TestUtils.TEST_USER_ID
        whenever(settingsRepository.deleteUser(userId)).thenThrow(RuntimeException("Test error"))
        viewModel.deleteUser(userId)
        
        // When
        viewModel.clearError()
        
        // Then
        viewModel.uiState.test {
            val state = awaitItem()
            assertNull(state.error)
            
            cancelAndIgnoreRemainingEvents()
        }
    }
}