package com.healthlink.hms.feature.setting.di

import app.cash.turbine.test
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.UserInfoDTO
import com.healthlink.hms.core.network.api.ApiServiceKot
import com.healthlink.hms.feature.setting.utils.MainDispatcherRule
import com.healthlink.hms.feature.setting.utils.TestUtils
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * DefaultSettingsRepository 单元测试
 * 测试所有 API 调用和本地存储功能
 */
@ExperimentalCoroutinesApi
@RunWith(MockitoJUnitRunner::class)
class DefaultSettingsRepositoryTest {
    
    @Mock
    private lateinit var apiService: ApiServiceKot
    
    private lateinit var repository: DefaultSettingsRepository
    
    @get:Rule
    private val mainDispatcherRule = MainDispatcherRule()
    
    @Before
    fun setup() {
        repository = DefaultSettingsRepository(apiService)
    }
    
    // ========== API 调用测试 ==========
    
    @Test
    fun `deleteUser should return success response when API call succeeds`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val successResponse = TestUtils.createSuccessResponse(true)
        whenever(apiService.deleteUser(userId)).thenReturn(successResponse)
        
        // When & Then
        repository.deleteUser(userId).test {
            val result = awaitItem()
            assertEquals("0", result.code)
            assertEquals(true, result.data)
            awaitComplete()
        }
    }
    
    @Test
    fun `deleteUser should return error response when API call fails`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        whenever(apiService.deleteUser(userId)).thenThrow(RuntimeException("Network error"))
        
        // When & Then
        repository.deleteUser(userId).test {
            val result = awaitItem()
            // 验证返回了默认的 BaseResponse
            assertNotNull(result)
            awaitComplete()
        }
    }
    
    @Test
    fun `unbindAccount should return success response when API call succeeds`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val successResponse = TestUtils.createSuccessResponse(true)
        whenever(apiService.unbindAccount(userId)).thenReturn(successResponse)
        
        // When & Then
        repository.unbindAccount(userId).test {
            val result = awaitItem()
            assertEquals("0", result.code)
            assertEquals(true, result.data)
            awaitComplete()
        }
    }
    
    @Test
    fun `unbindAccount should return error response when API call fails`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        whenever(apiService.unbindAccount(userId)).thenThrow(RuntimeException("Network error"))
        
        // When & Then
        repository.unbindAccount(userId).test {
            val result = awaitItem()
            assertNotNull(result)
            awaitComplete()
        }
    }
    
    @Test
    fun `getUserInfo should return success response with user data when API call succeeds`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val userInfo = TestUtils.createTestUserInfo()
        val successResponse = TestUtils.createSuccessResponse(userInfo)
        whenever(apiService.getUserInfo(userId)).thenReturn(successResponse)
        
        // When & Then
        repository.getUserInfo(userId).test {
            val result = awaitItem()
            assertEquals("0", result.code)
            assertEquals(userInfo, result.data)
            assertEquals(userId, result.data?.userId)
            awaitComplete()
        }
    }
    
    @Test
    fun `getUserInfo should return error response when API call fails`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        whenever(apiService.getUserInfo(userId)).thenThrow(RuntimeException("Network error"))
        
        // When & Then
        repository.getUserInfo(userId).test {
            val result = awaitItem()
            assertNotNull(result)
            awaitComplete()
        }
    }
    
    @Test
    fun `saveUserInfo should return success response when API call succeeds`() = runTest {
        // Given
        val requestParam = TestUtils.createTestSaveUserParams()
        val successResponse = TestUtils.createSuccessResponse(true)
        whenever(apiService.saveUserInfo(requestParam)).thenReturn(successResponse)
        
        // When & Then
        repository.saveUserInfo(requestParam).test {
            val result = awaitItem()
            assertEquals("0", result.code)
            assertEquals(true, result.data)
            awaitComplete()
        }
    }
    
    @Test
    fun `saveUserInfo should return error response when API call fails`() = runTest {
        // Given
        val requestParam = TestUtils.createTestSaveUserParams()
        whenever(apiService.saveUserInfo(requestParam)).thenThrow(RuntimeException("Network error"))
        
        // When & Then
        repository.saveUserInfo(requestParam).test {
            val result = awaitItem()
            assertNotNull(result)
            awaitComplete()
        }
    }
    
    // ========== 本地存储功能测试 ==========
    // 注意：本地存储功能现在通过 core/data 模块的其他 Repository 处理
    // DefaultSettingsRepository 主要负责网络 API 调用
    // 本地存储相关的测试应该在对应的 Repository 测试中进行
}