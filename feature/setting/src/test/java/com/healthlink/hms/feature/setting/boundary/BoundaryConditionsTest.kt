package com.healthlink.hms.feature.setting.boundary

import app.cash.turbine.test

import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.UserInfoDTO
import com.healthlink.hms.core.network.api.ApiServiceKot
import com.healthlink.hms.feature.setting.di.DefaultSettingsRepository
import com.healthlink.hms.feature.setting.di.SettingsRepository
import com.healthlink.hms.feature.setting.utils.MainDispatcherRule
import com.healthlink.hms.feature.setting.utils.TestUtils
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.whenever
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * 边界条件和异常场景测试
 * 测试各种边界情况、异常处理和错误恢复
 */
@ExperimentalCoroutinesApi
@RunWith(MockitoJUnitRunner::class)
class BoundaryConditionsTest {
    
    @Mock
    private lateinit var apiService: ApiServiceKot
    

    
    private lateinit var repository: SettingsRepository
    
    @get:Rule
    private val mainDispatcherRule = MainDispatcherRule()
    
    @Before
    fun setup() {
        repository = DefaultSettingsRepository(apiService)
    }
    
    // ========== 空值和 null 处理测试 ==========
    
    @Test
    fun `test empty string user ID handling`() = runTest {
        // Given
        val emptyUserId = ""
        val errorResponse = TestUtils.createErrorResponse<Boolean>("400", "Invalid user ID")
        whenever(apiService.deleteUser(emptyUserId)).thenReturn(errorResponse)
        
        // When & Then
        repository.deleteUser(emptyUserId).test {
            val result = awaitItem()
            assertEquals("400", result.code)
            awaitComplete()
        }
    }
    
    @Test
    fun `test null data in response handling`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val responseWithNullData = BaseResponse<UserInfoDTO>().apply {
            code = "0"
            msg = "success"
            data = null
        }
        whenever(apiService.getUserInfo(userId)).thenReturn(responseWithNullData)

        // When & Then
        repository.getUserInfo(userId).test {
            val result = awaitItem()
            assertEquals("0", result.code)
            assertEquals("success", result.msg)
            assertEquals(null, result.data)
            awaitComplete()
        }
    }
    
    @Test
    fun `test empty map parameters handling`() = runTest {
        // Given
        val emptyParams = emptyMap<String, Any>()
        val errorResponse = TestUtils.createErrorResponse<Boolean>("400", "Empty parameters")
        whenever(apiService.saveUserInfo(emptyParams)).thenReturn(errorResponse)
        
        // When & Then
        repository.saveUserInfo(emptyParams).test {
            val result = awaitItem()
            assertEquals("400", result.code)
            awaitComplete()
        }
    }
    
    // ========== 网络异常测试 ==========
    
    @Test
    fun `test network timeout exception handling`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        whenever(apiService.deleteUser(userId)).thenThrow(SocketTimeoutException("Connection timeout"))

        // When & Then
        repository.deleteUser(userId).test {
            val result = awaitItem()
            // 验证返回了默认的 BaseResponse
            assertNotNull(result)
            awaitComplete()
        }
    }
    
    @Test
    fun `test unknown host exception handling`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        whenever(apiService.unbindAccount(userId)).thenThrow(UnknownHostException("Unable to resolve host"))

        // When & Then
        repository.unbindAccount(userId).test {
            val result = awaitItem()
            // 验证返回了默认的 BaseResponse
            assertNotNull(result)
            awaitComplete()
        }
    }
    
    @Test
    fun `test IO exception handling`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        whenever(apiService.getUserInfo(userId)).thenThrow(IOException("IO error"))

        // When & Then
        repository.getUserInfo(userId).test {
            val result = awaitItem()
            // 验证返回了默认的 BaseResponse
            assertNotNull(result)
            awaitComplete()
        }
    }
    
    // ========== 服务器错误响应测试 ==========
    
    @Test
    fun `test server error 500 response`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val serverErrorResponse = TestUtils.createErrorResponse<Boolean>("500", "Internal server error")
        whenever(apiService.deleteUser(userId)).thenReturn(serverErrorResponse)

        // When & Then
        repository.deleteUser(userId).test {
            val result = awaitItem()
            assertEquals("500", result.code)
            assertEquals("Internal server error", result.msg)
            awaitComplete()
        }
    }
    
    @Test
    fun `test unauthorized 401 response`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val unauthorizedResponse = TestUtils.createErrorResponse<Boolean>("401", "Unauthorized")
        whenever(apiService.unbindAccount(userId)).thenReturn(unauthorizedResponse)

        // When & Then
        repository.unbindAccount(userId).test {
            val result = awaitItem()
            assertEquals("401", result.code)
            assertEquals("Unauthorized", result.msg)
            awaitComplete()
        }
    }
    
    @Test
    fun `test malformed response handling`() = runTest {
        // Given
        val userId = TestUtils.TEST_USER_ID
        val malformedResponse = BaseResponse<UserInfoDTO>().apply {
            code = null // 异常的 null code
            msg = null
            data = null
        }
        whenever(apiService.getUserInfo(userId)).thenReturn(malformedResponse)

        // When & Then
        repository.getUserInfo(userId).test {
            val result = awaitItem()
            assertNull(result.code)
            assertNull(result.msg)
            assertNull(result.data)
            awaitComplete()
        }
    }
    
    // ========== 极端数据测试 ==========
    
    @Test
    fun `test very long user ID handling`() = runTest {
        // Given
        val veryLongUserId = "a".repeat(1000) // 1000 字符的用户ID
        val successResponse = TestUtils.createSuccessResponse(true)
        whenever(apiService.deleteUser(veryLongUserId)).thenReturn(successResponse)
        
        // When & Then
        repository.deleteUser(veryLongUserId).test {
            val result = awaitItem()
            assertEquals("0", result.code)
            awaitComplete()
        }
    }
    
    @Test
    fun `test special characters in user data`() = runTest {
        // Given
        val specialCharParams = mapOf(
            "userId" to "user@#$%^&*()_+",
            "nickName" to "用户名包含特殊字符!@#$%",
            "gender" to 1,
            "birthYear" to 1990,
            "birthMonth" to 5,
            "height" to 175,
            "weight" to 70.0f
        )
        val successResponse = TestUtils.createSuccessResponse(true)
        whenever(apiService.saveUserInfo(specialCharParams)).thenReturn(successResponse)
        
        // When & Then
        repository.saveUserInfo(specialCharParams).test {
            val result = awaitItem()
            assertEquals("0", result.code)
            awaitComplete()
        }
    }
    

}