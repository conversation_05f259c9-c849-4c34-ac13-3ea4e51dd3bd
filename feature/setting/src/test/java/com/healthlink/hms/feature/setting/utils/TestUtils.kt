package com.healthlink.hms.feature.setting.utils

import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.UserInfoDTO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

/**
 * 测试工具类，提供通用的测试数据和工具方法
 */
object TestUtils {
    
    /**
     * 创建成功的 BaseResponse
     */
    fun <T> createSuccessResponse(data: T): BaseResponse<T> {
        return BaseResponse<T>().apply {
            code = "0"
            msg = "success"
            this.data = data
        }
    }
    
    /**
     * 创建失败的 BaseResponse
     */
    fun <T> createErrorResponse(errorCode: String = "1", errorMessage: String = "error"): BaseResponse<T> {
        return BaseResponse<T>().apply {
            code = errorCode
            msg = errorMessage
            data = null
        }
    }
    
    /**
     * 创建成功的 Flow
     */
    fun <T> createSuccessFlow(data: T): Flow<BaseResponse<T>> {
        return flowOf(createSuccessResponse(data))
    }
    
    /**
     * 创建失败的 Flow
     */
    fun <T> createErrorFlow(errorCode: String = "1", errorMessage: String = "error"): Flow<BaseResponse<T>> {
        return flowOf(createErrorResponse<T>(errorCode, errorMessage))
    }
    
    /**
     * 创建测试用的 UserInfoDTO
     */
    fun createTestUserInfo(
        userId: String = "test_user_123",
        nickName: String = "测试用户",
        gender: Int = 1,
        birthYear: Int = 1990,
        birthMonth: Int = 5,
        height: Int = 175,
        weight: Float = 70.0f
    ): UserInfoDTO {
        return UserInfoDTO(
            userId = userId,
            nickName = nickName,
            gender = gender,
            birthYear = birthYear,
            birthMonth = birthMonth,
            height = height,
            weight = weight
        )
    }
    
    /**
     * 创建测试用的用户信息保存参数
     */
    fun createTestSaveUserParams(
        userId: String = "test_user_123",
        nickName: String = "测试用户",
        gender: Int = 1,
        birthYear: Int = 1990,
        birthMonth: Int = 5,
        height: Int = 175,
        weight: Float = 70.0f
    ): Map<String, Any> {
        return mapOf(
            "userId" to userId,
            "nickName" to nickName,
            "gender" to gender,
            "birthYear" to birthYear,
            "birthMonth" to birthMonth,
            "height" to height,
            "weight" to weight
        )
    }
    
    /**
     * 常用的测试用户ID
     */
    const val TEST_USER_ID = "test_user_123"
    
    /**
     * 常用的错误消息
     */
    const val NETWORK_ERROR_MESSAGE = "网络错误"
    const val SERVER_ERROR_MESSAGE = "服务器错误"
    const val UNKNOWN_ERROR_MESSAGE = "未知错误"
}