package com.healthlink.hms.feature.setting.di

import android.annotation.SuppressLint
import android.util.Log

import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.BaseResponseCallback
import com.healthlink.hms.core.model.dto.UserInfoDTO
import com.healthlink.hms.core.network.NetworkApi
import com.healthlink.hms.core.network.api.ApiService
import com.healthlink.hms.core.network.api.ApiServiceKot
import com.healthlink.hms.core.network.rx.BaseObserver
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DefaultSettingsRepository @Inject constructor(
    private val apiService: ApiServiceKot
) : SettingsRepository {
    companion object {
        private const val TAG = "DefaultSettingsRepository"
    }

    /**
     * 注销用户
     */
    override suspend fun deleteUser(userId: String): Flow<BaseResponse<Boolean>> = flow {
        try {
            val result = apiService.deleteUser(userId)
            emit(result)
        } catch (e: Exception) {
            Log.d(TAG,"deleteUser Error: " + e.toString())
            // TODO: 没有精准的处理网络接口返回的数据
            emit(BaseResponse())
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 解绑用户
     */
    override suspend fun unbindAccount(userId: String): Flow<BaseResponse<Boolean>> = flow {
        try {
            val result = apiService.unbindAccount(userId)
            emit(result)
        } catch (e: Exception) {
            Log.d(TAG,"unbindAccount Error: " + e.toString())
            emit(BaseResponse())
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 获取用户信息
     */
    override suspend fun getUserInfo(userId: String): Flow<BaseResponse<UserInfoDTO>> = flow {
        try {
            val result = apiService.getUserInfo(userId)
            emit(result)
        } catch (e: Exception) {
            Log.d(TAG,"getUserInfo Error: " + e.toString())
            emit(BaseResponse())
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 保存用户信息
     */
    override suspend fun saveUserInfo(requestParam: Map<String, Any>): Flow<BaseResponse<Boolean>> = flow {
        try {
            val result = apiService.saveUserInfo(requestParam)
            emit(result)
        } catch (e: Exception) {
            Log.d(TAG,"saveUserInfo Error: " + e.toString())
            emit(BaseResponse())
        }
    }.flowOn(Dispatchers.IO)


}