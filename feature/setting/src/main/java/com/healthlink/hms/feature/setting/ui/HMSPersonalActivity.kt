package com.healthlink.hms.feature.setting.ui

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.content.res.ColorStateList
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.LinearLayout
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.activity.viewModels
import androidx.annotation.IdRes
import androidx.annotation.LayoutRes
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ClickUtils
import com.healthlink.hms.core.common.base.BaseVBVMActivity
import com.healthlink.hms.core.common.extensions.dp
import com.healthlink.hms.core.common.utils.AppContext
import com.healthlink.hms.core.common.utils.BaseContext
import com.healthlink.hms.core.common.utils.Constants
import com.healthlink.hms.core.common.utils.DataTrackUtil
import com.healthlink.hms.core.common.utils.HMSDialogUtils
import com.healthlink.hms.core.common.utils.ToastUtil
// 移除UserUtils和SystemUtils的导入，现在通过ViewModel处理数据
import com.healthlink.hms.core.model.dto.UserInfoDTO
import com.healthlink.hms.feature.setting.BuildConfig
import com.healthlink.hms.feature.setting.R
import com.healthlink.hms.core.ui.R.*
import com.healthlink.hms.feature.setting.databinding.FeatureSettingActivityPersonalBinding
import com.healthlink.hms.feature.setting.viewmodels.SettingsUiEvent
import com.healthlink.hms.feature.setting.viewmodels.SettingsUiState
import com.healthlink.hms.feature.setting.viewmodels.SettingsViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import javax.inject.Inject
import kotlin.jvm.java
import kotlin.text.isNullOrEmpty

@AndroidEntryPoint
class HMSPersonalActivity : BaseVBVMActivity<FeatureSettingActivityPersonalBinding>() {

    private var mTag = "HMSPersonalActivity"
    private var isLoggin = true
    private val viewModel: SettingsViewModel by viewModels()
    private var nickNameShow = ""
    private var birthYearShow = 0
    private var birthMonthShow = 0
    private var heightShow = 0
    private var weightShow = 0f
    private var genderShow = 2
    private var personalMap = hashMapOf<String, Any>(
        Pair("userId", ""),
        Pair("nickName", ""),
        Pair("birthYear", 0),
        Pair("birthMonth", 0),
        Pair("height", 0),
        Pair("weight", 0f),
        Pair("gender", 2)
    )
    private var bundle: Bundle = Bundle()

    private var weakActivity: WeakReference<HMSPersonalActivity>? = WeakReference(this)
    
    // 移除UserUtils和SystemUtils的注入，现在通过ViewModel处理数据

    override fun getLayoutId(): Int {
        return R.layout.feature_setting_activity_personal
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(mTag, "onCreate ${this}")
        // 埋点和初始化将在观察userId后执行
        // 获取状态栏的高度
        val topHeight = getStatusBarHeight() + 8f.dp
        // 左侧布局页面高度动态调整
        Log.d("TOPBAR", "topHeight -> " + topHeight)
        val leftView = findViewById<View>(R.id.left_view)
        val leftLayoutParams = leftView.layoutParams
        leftLayoutParams.height = topHeight.toInt()
        leftView.layoutParams = leftLayoutParams
        // 右侧布局页面高度动态调整
        val rightView = findViewById<View>(R.id.right_view)
        val rightLayoutParams = rightView.layoutParams
        rightLayoutParams.height = topHeight.toInt()
        rightView.layoutParams = rightLayoutParams
        // 左侧内容区域高度动态调整
//        val params = binding.rlLeftContent.layoutParams
//        params.height = (984F.dp - topHeight.toInt()).toInt()
//        binding.rlLeftContent.layoutParams = params
        // TODO 补充代码说明
        val root = findViewById<LinearLayout>(R.id.lo_setting)
        root.systemUiVisibility =
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        // 初始化MVVM的模型
//        mainViewModel = ViewModelProvider(this).get(SettingsViewModel::class.java)
        // 生命周期观察对象
        binding.lifecycleOwner = this
        // 初始化MVVM的监听动作
//        initLiveDataObserve()
        
        // personalMap的userId将通过观察ViewModel的userId自动更新
        setupObserver()
        setupClickListeners()
        // 初始化UI
        initUI()
        // 初始化数据
        initData()
        //receiveBroadcast()
    }

    override fun onStop() {
        DataTrackUtil.dtExitPage(
            "Health_Set_Close",
            DataTrackUtil.userIDMap(viewModel.userId.value)
        )
        //清除数据
//        this.mainViewModel?.deleteuserResult?.value = null

        super.onStop()
    }

    override fun onDestroy() {
        super.onDestroy()
        if(weakActivity?.get() != null){
            weakActivity = null
        }
        Log.d(mTag, "onDestroy $this")
    }

    /**
     * 初始化设置页面的UI
     */
    fun initUI() {
        // 软件版本的设置
        var versionInfo = "软件版本V" + BuildConfig.VERSION_NAME+"." + BuildConfig.BUILD_TIME + " "
        if (!BuildConfig.BUILD_TYPE.equals("release")) {
            versionInfo += "-" + BuildConfig.PLATFORM_CODE
            versionInfo += "-" + BuildConfig.BUILD_TYPE
//            versionInfo += "-" + BuildConfig.BUILD_TIME + " "
        }else{

        }
        binding.version.text = versionInfo
    }

    private fun getNotificationBgColor(isOn: Boolean): ColorStateList {
        if (isOn) {
            return ColorStateList.valueOf(
                ContextCompat.getColor(
                    baseContext,
                    com.healthlink.hms.core.ui.R.color.notification_switch_on_bg
                )
            )
        } else {
            return ColorStateList.valueOf(
                ContextCompat.getColor(
                    baseContext,
                    com.healthlink.hms.core.ui.R.color.notification_switch_off_bg
                )
            )
        }
    }

    /**
     * 初始化数据
     */
    private fun initData() {
        // 用户信息现在通过ViewModel的响应式数据流获取
        if (BaseContext.checkNetWork()) {
            lifecycleScope.launch {
                val userId = viewModel.userId.value
                if (userId.isNotEmpty()) {
                    viewModel.getUserInfo(userId)
                }
            }
        }
    }

    /**
     * 设置观察者 状态管理的核心
     */
    private fun setupObserver(){
        // 观察UI状态变化 - 使用StateFlow管理持续性状态
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                handleUiStateChange(state)
            }
        }

        // 观察UI事件 - 使用SharedFlow管理一次性事件
        lifecycleScope.launch {
            viewModel.uiEvent.collect { event ->
                handleUiEvent(event)
            }
        }
        
        // 观察用户ID变化（用于埋点和默认昵称生成）
        lifecycleScope.launch {
            viewModel.userId.collect { userId ->
                if (userId.isNotEmpty()) {
                    // 埋点数据追踪
                    DataTrackUtil.dtEnterPage(
                        "Health_Set_PV",
                        DataTrackUtil.userIDMap(userId)
                    )
                    
                    // 初始化默认用户名
                    initDefaultUserName(userId)
                    
                    // 更新personalMap的userId
                    personalMap["userId"] = userId
                }
            }
        }
        
        // 观察通知状态变化
        lifecycleScope.launch {
            viewModel.notificationState.collect { isOn ->
                updateNotificationUI(isOn)
            }
        }
    }

    /**
     * 设置点击事件
     */
    private fun setupClickListeners(){
        // 返回按钮
        binding.settingBack.setOnClickListener {
            getSafeActivity()?.let {
                if (isLoggin) {
                    DataTrackUtil.dtClick(
                        "Health_Set_Return_Click",
                        DataTrackUtil.userIDMap(viewModel.userId.value)
                    )
                }
                backToMain()
            }
        }

        // 个人信息
        ClickUtils.applySingleDebouncing(binding.settingPersonalInfo, 1000) {
            it.isEnabled = false
            DataTrackUtil.dtClick(
                "Health_Set_Personalinformation_Click",
                DataTrackUtil.userIDMap(viewModel.userId.value)
            )
            val intent = Intent(this, DialogTransparentActivity::class.java)
            bundle.putFloat("weight", weightShow)
            bundle.putInt("height", heightShow)
            bundle.putInt("birthMonth", birthMonthShow)
            bundle.putInt("birthYear", birthYearShow)
            bundle.putInt("gender", genderShow)
            bundle.putString("nickName", nickNameShow)
            bundle.putString("userId", viewModel.userId.value)
            intent.putExtras(bundle)
            requestDataLauncher.launch(intent)
            overridePendingTransition(com.healthlink.hms.core.ui.R.anim.activity_enter_dialog, com.healthlink.hms.core.ui.R.anim.activity_stay)
            it.postDelayed({ it.isEnabled = true }, 500)
        }

        // 退出登录
        binding.settingLogout.setOnClickListener {
            DataTrackUtil.dtClick(
                "Health_Set_Logoff_Click",
                DataTrackUtil.userIDMap(viewModel.userId.value)
            )

            getSafeActivity()?.let { safeActivity ->
                val msg = "确定退出当前账号？"
                showConfirmDialog(safeActivity,layoutId = com.healthlink.hms.core.ui.R.layout.hms_dialog_confirm_auto_align,
                    message = msg){ isPositive ->
                    if (isPositive){
                        doLogout()
                        backToMain()
                    }
                }
            }
        }

        // 注销
        binding.settingLogoffAccount.setOnClickListener {
            //注销
            DataTrackUtil.dtClick(
                "Health_Set_Logout_Click",
                DataTrackUtil.userIDMap(viewModel.userId.value)
            )
            if (isLoggin) {
                val msg = "确定注销健康账户？注销后将解绑华为账户和删除您的健康历史数据"
                DataTrackUtil.dtEnterPage(
                    "Health_Set_LogoutPrompt_PV",
                    DataTrackUtil.userIDMap(viewModel.userId.value)
                )

                getSafeActivity()?.let { safeActivity ->
                    showConfirmDialog(safeActivity,com.healthlink.hms.core.ui.R.layout.hms_dialog_confirm_logoff,
                        message = msg) { isPositive ->
                        if (isPositive) {
                            Log.i(mTag, "confirm to delete user.")
                            val userId = viewModel.userId.value
                            if (userId.isNotEmpty()) {
                                DataTrackUtil.dtClick(
                                    "Health_Set_LogoutPrompt_Agree_Click",
                                    DataTrackUtil.userIDMap(userId)
                                )
                                viewModel.deleteUser(userId)
                            }
                            binding.tvDoctorNumber.text = ""
                        } else {
                            DataTrackUtil.dtClick(
                                "Health_Set_LogoutPrompt_Disagree_Click",
                                DataTrackUtil.userIDMap()
                            )
                        }
                        DataTrackUtil.dtExitPage(
                            "Health_Set_LogoutPrompt_Close",
                            DataTrackUtil.userIDMap()
                        )
                    }
                }
            }
        }

        // 解绑华为账号
        binding.settingUnbindAccount.setOnClickListener {
            //关联账户解绑
            DataTrackUtil.dtClick(
                "Health_Set_Unbind_Click",
                DataTrackUtil.userIDMap(viewModel.userId.value)
            )
            if (isLoggin) {
                DataTrackUtil.dtEnterPage(
                    "Health_Set_UnbindPrompt_PV",
                    DataTrackUtil.userIDMap(viewModel.userId.value)
                )
                val msg = resources.getString(com.healthlink.hms.core.ui.R.string.unbind_huawei_account)

                getSafeActivity()?.let { safeActivity ->
                    showConfirmDialog(safeActivity,
                        layoutId = com.healthlink.hms.core.ui.R.layout.hms_dialog_confirm_auto_align,
                        message = msg) { isPositive ->
                        if (isPositive) {
                            val userId = viewModel.userId.value
                            if (userId.isNotEmpty()) {
                                DataTrackUtil.dtClick(
                                    "Health_Set_UnbindPrompt_Agree_Click",
                                    DataTrackUtil.userIDMap(userId)
                                )
                                viewModel.unbindAccount(userId)
                            }
                        } else {
                            DataTrackUtil.dtClick(
                                "Health_Set_UnbindPrompt_Disagree_Click",
                                DataTrackUtil.userIDMap(viewModel.userId.value)
                            )
                        }
                        DataTrackUtil.dtExitPage(
                            "Health_Set_UnbindPrompt_Close",
                            DataTrackUtil.userIDMap()
                        )
                    }
                }
            }

        }

        // 数据使用说明
        binding.rlDataUseIntro.setOnClickListener {
            DataTrackUtil.dtClick(
                "Health_Set_Datadescription_Click",
                DataTrackUtil.userIDMap(viewModel.userId.value)
            )
            showPrivacyDialog()
        }

        // 通知开关
        binding.settingNotiSwitcher.setOnCheckedChangeListener() { buttonView, isChecked ->
            viewModel.updateNotificationState(isChecked)

            DataTrackUtil.dtClick(
                "Health_Set_Message_Click",
                DataTrackUtil.userIDAnNotifyMap(
                    viewModel.userId.value,
                    if (isChecked) "1" else "2"
                )
            )
        }
    }

    private fun handleUiStateChange(state: SettingsUiState) {
        // 处理加载状态
        if (state.isLoading) {
            showLoadingDialog()
        } else {
            hideLoadingDialog()
        }

        // 处理用户信息更新 - 更新UI
        state.userInfo?.let { userInfo ->
            updateUserInfoDisplay(userInfo)
            binding.settingUsername.text = userInfo.nickName
        }

        // 处理通知设置状态
        updateNotificationSwitch(state.isNotificationEnabled)

        // 处理错误状态
        state.error?.let { error ->
            showToast(error)
            viewModel.clearError() // 清除错误状态，避免重复显示
        }
    }
    
    /**
     * 更新通知UI状态
     */
    private fun updateNotificationUI(isOn: Boolean) {
        // 更新开关状态（避免触发监听器）
        binding.settingNotiSwitcher.setOnCheckedChangeListener(null)
        binding.settingNotiSwitcher.isChecked = isOn
        
        // 更新背景颜色
        binding.settingNotiSwitcher.backColor = getNotificationBgColor(isOn)
        
        // 重新设置监听器
        binding.settingNotiSwitcher.setOnCheckedChangeListener() { buttonView, isChecked ->
            viewModel.updateNotificationState(isChecked)

            DataTrackUtil.dtClick(
                "Health_Set_Message_Click",
                DataTrackUtil.userIDAnNotifyMap(
                    viewModel.userId.value,
                    if (isChecked) "1" else "2"
                )
            )
        }
    }

    private fun handleUiEvent(event: SettingsUiEvent) {
        when(event) {
            is SettingsUiEvent.ShowToast -> { showToast(event.message) }
            is SettingsUiEvent.DeleteUserSuccess -> {
                doLogout(true)
                backToMain()
            }

            is SettingsUiEvent.UnbindAccountSuccess -> {
                doLogout(true)
                backToMain()
            }

            is SettingsUiEvent.SaveUserInfoSuccess -> {
                //更新用户暂存信息
                personalMap.put("nickName", nickNameShow)
                personalMap.put("birthYear", birthYearShow)
                personalMap.put("birthMonth", birthMonthShow)
                personalMap.put("height", heightShow)
                personalMap.put("weight", weightShow)
                personalMap.put("gender", genderShow)

                personalMap.get("nickName")?.let { nickName ->
                    <EMAIL> = nickName.toString()
                    binding.settingUsername.text = nickName.toString()
                    // TODO: 通过ViewModel存储用户昵称
            // userUtils.storeUserinfoNicknameSync(nickName.toString())
                }
                personalMap.get("gender")?.let { gender ->
                    gender.toIntNumber()?.let {
                        <EMAIL> = it
                        // TODO: 通过ViewModel存储用户性别
                // userUtils.storeUserinfoGenderSync(it)
                    }

                }
                personalMap.get("birthYear")?.let { birthYear ->
                    birthYear.toIntNumber()?.let {
                        <EMAIL> = it
                        // TODO: 通过ViewModel存储用户出生年份
                // userUtils.storeUserinfoBirthYearSync(it)
                    }

                }
                personalMap.get("birthMonth")?.let { birthMonth ->
                    birthMonth.toIntNumber()?.let {
                        <EMAIL> = it
                        // TODO: 通过ViewModel存储用户出生月份
                // userUtils.storeUserinfoBirthMonthSync(it)
                    }

                }
                personalMap.get("height")?.let { height ->
                    height.toIntNumber()?.let {
                        <EMAIL> = it
                        // TODO: 通过ViewModel存储用户身高
                // userUtils.storeUserinfoHeightSync(it)
                    }

                }
                personalMap.get("weight")?.let { weight ->
                    <EMAIL> = weight.toString().toFloat()
                    // TODO: 通过ViewModel存储用户体重
                    // userUtils.storeUserinfoWeightSync(weight.toString().toFloat())
                }
            }

            is SettingsUiEvent.SaveUserInfoFailed -> {
                rollbackInfo()
            }
        }
    }

    // =========================== UI辅助方法 ===========================

    private fun showToast(string: String) {
        Toast.makeText(this, string, Toast.LENGTH_SHORT).show()
    }

    private fun showLoadingDialog() {}

    private fun hideLoadingDialog() {}

    private fun updateUserInfoDisplay(dTO: UserInfoDTO) {
        val personInfo = dTO
        personInfo.nickName?.let { nickName ->
            personalMap.put("nickName", nickName)
            // TODO: 通过ViewModel存储用户数据
            // userUtils.storeDataSync(KEY_NIKE_NAME, nickName)
            <EMAIL> = nickName
            // userUtils.storeUserinfoNicknameSync(nickName)
        }
        personInfo.gender?.let { gender ->
            personalMap.put("gender", gender)
            <EMAIL> = gender
            // TODO: 通过ViewModel存储用户性别
            // userUtils.storeUserinfoGenderSync(gender)
        }
        personInfo.birthYear?.let { birthYear ->
            personalMap.put("birthYear", birthYear)
            <EMAIL> = birthYear
            // TODO: 通过ViewModel存储用户出生年份
            // userUtils.storeUserinfoBirthYearSync(birthYear)
        }
        personInfo.birthMonth?.let { birthMonth ->
            personalMap.put("birthMonth", birthMonth)
            // TODO: 通过ViewModel存储用户出生月份
            // userUtils.storeUserinfoBirthMonthSync(birthMonth)
            <EMAIL> = birthMonth
        }
        personInfo.height?.let { height ->
            personalMap.put("height", height)
            // TODO: 通过ViewModel存储用户身高
            // userUtils.storeUserinfoHeightSync(height)
            <EMAIL> = height
        }
        personInfo.weight?.let { weight ->
            personalMap.put("weight", weight)
            // TODO: 通过ViewModel存储用户体重
            // userUtils.storeUserinfoWeightSync(weight)
            <EMAIL> = weight
        }
    }

    /**
     * 更新通知开关状态的实现
      */
    private fun updateNotificationSwitch(isEnabled: Boolean) {

        if (isEnabled) {
            binding.settingNotiSwitcher.isChecked = true
            binding.settingNotiSwitcher.backColor = getNotificationBgColor(true)
        } else {
            binding.settingNotiSwitcher.isChecked = false
            binding.settingNotiSwitcher.backColor = getNotificationBgColor(false)
        }
    }

    private fun showConfirmDialog(content: Context,@LayoutRes layoutId: Int, message: String, positiveBtnText: String? = "确定", negativeBtnText: String? = "取消", onConfirm: ((isPositive: Boolean) -> Unit)?){
        HMSDialogUtils.showHMSDialog(
            content,
            layoutId,
            message,
            positiveBtnText!!,
            negativeBtnText!!
        ) { isPositive ->
            if (onConfirm != null) onConfirm(isPositive)
        }
    }

    // 移除getCurrentUserId方法，现在直接使用viewModel.userId.value

    private fun getSafeActivity(): HMSPersonalActivity? {
        val activity = weakActivity?.get()
        if (activity != null && !activity.isFinishing) {
            return activity
        }
        return null
    }

    private fun rollbackInfo() {
        nickNameShow = personalMap.get("nickName") as String
        birthYearShow = personalMap.get("birthYear") as Int
        birthMonthShow = personalMap.get("birthMonth") as Int
        heightShow = personalMap.get("height") as Int
        weightShow = personalMap.get("weight") as Float
        genderShow = personalMap.get("gender") as Int
    }

    private fun showPrivacyDialog() {
        val activity = weakActivity?.get()
        if (activity != null && !activity.isFinishing) {
            HMSDialogUtils.showDataUseIntroDialog(activity, "数据使用说明", "知道了") {
            }
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(mTag, "onResume ${this}")
//        sendQueryDoctorService()
    }

    private fun doLogout(isKeepAgreed: Boolean = true) {
        isLoggin = false
        // 清除用户缓存数据 - 现在通过ViewModel处理
        viewModel.clearUserData(isKeepAgreed)
        //更新桌面卡片状态
        // TODO 更新 widget 状态
//        var hmsWidgetProvider = HMSWidgetProvider()
//        val activity = weakActivity?.get()
//        if (activity != null && !activity.isFinishing) {
////            hmsWidgetProvider.updateHmsWidgetForNoLogin(activity)
//            // 获取 AppWidgetManager 实例
//            val appWidgetManager = AppWidgetManager.getInstance(ContextWrapper.getApplicationContext)
//            // 获取当前小部件的所有 AppWidgetId
//            val appWidgetIds = appWidgetManager.getAppWidgetIds(
//                ComponentName(ContextWrapper.getApplicationContext, HMSWidgetProvider::class.java)
//            )
//            hmsWidgetProvider.updateHmsWidget(ContextWrapper.getApplicationContext,appWidgetIds)
//        }
    }

    private fun saveData(map: Map<String, Any>) {
//        mainViewModel.saveUserData(map)
        viewModel.saveUserInfo(map)
    }

    private val requestDataLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            var map = hashMapOf<String, Any>()
            map.put("userId", viewModel.userId.value)
            if (result.resultCode == Activity.RESULT_OK || result.resultCode == Activity.RESULT_CANCELED) {
                result.data?.let {
                    it.getStringExtra("nickName")?.let { nName ->
                        map.put("nickName", nName)
                        nickNameShow = nName
                        binding.settingUsername.text = nName
                    }
                    if (it.getIntExtra("birthYear", 0) != 0) {
                        map.put("birthYear", it.getIntExtra("birthYear", 0))
                        birthYearShow = it.getIntExtra("birthYear", 0)
                    }
                    if (it.getIntExtra("birthMonth", 0) != 0) {
                        map.put("birthMonth", it.getIntExtra("birthMonth", 0))
                        birthMonthShow = it.getIntExtra("birthMonth", 0)
                    }
                    if (it.getIntExtra("height", 0) != 0) {
                        map.put("height", it.getIntExtra("height", 0))
                        heightShow = it.getIntExtra("height", 0)
                    }
                    if (it.getFloatExtra("weight", 0f) != 0f) {
                        map.put("weight", it.getFloatExtra("weight", 0f))
                        weightShow = it.getFloatExtra("weight", 0f)
                    }
                    map.put("gender", it.getIntExtra("gender", 2))
                    genderShow = it.getIntExtra("gender", 2)
                }
                if (result.resultCode == Activity.RESULT_OK) saveData(map.toMap())
            }
        }

    fun Any.toOneDecimalStr(): String? {
        //显示身高一位小数
        if (this is Float) return String.format("%.1f", this)
        if (this is String) {
            try {
                return String.format("%.1f", this.toFloat())
            } catch (e: NumberFormatException) {
                return null
            }
        }
        return null
    }

    fun Any.toIntNumber(): Int? {
        if (this is Int) return this
        if (this is Float) return this.toInt()
        if (this is String) {
            try {
                return this.toFloat().toInt()
            } catch (e: NumberFormatException) {
                return null
            }
        }
        return null
    }

    private fun initDefaultUserName(userID: String?) {
        // 默认使用ViewModel中的用户信息；如无缓存，则使用默认规则生成昵称
        // 注意：昵称将通过ViewModel的用户信息流获取，这里只是设置默认值
        userID?.let {
            if (userID.length > 6) {
                nickNameShow = "CH${it.substring(it.length - 6)}"
            } else {
                nickNameShow = "CH$userID"
            }
        }
        personalMap.put("nickName", nickNameShow)
    }

    private fun getStatusBarHeight(): Int {
        var statusBarHeight = 0
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            statusBarHeight = resources.getDimensionPixelSize(resourceId)
        }
        return statusBarHeight
    }

    companion object {
        // 用户昵称缓存KEY
        const val KEY_NIKE_NAME = "NIKE_NAME"
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString("nickNameShowSave", nickNameShow)
        outState.putInt("birthYearShowSave", birthYearShow)
        outState.putInt("birthMonthShowSave", birthMonthShow)
        outState.putInt("heightShowSave", heightShow)
        outState.putFloat("weightShowSave", weightShow)
        outState.putInt("genderShowSave", genderShow)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        nickNameShow = savedInstanceState.getString("nickNameShowSave", nickNameShow)
        birthYearShow = savedInstanceState.getInt("birthYearShowSave", birthYearShow)
        birthMonthShow = savedInstanceState.getInt("birthMonthShowSave", birthMonthShow)
        heightShow = savedInstanceState.getInt("heightShowSave", heightShow)
        weightShow = savedInstanceState.getFloat("weightShowSave", weightShow)
        genderShow = savedInstanceState.getInt("genderShowSave", genderShow)
    }

    // 移除initUserFromMMKV方法，用户信息现在通过ViewModel的响应式数据流获取
    // 用户信息的初始化将在setupObserver中通过观察ViewModel的数据流完成

    /**
     * 注销用户结果的观察者
     */
//    var deleteUserResultObserver = Observer<BaseResponse<Boolean>>{
//        if (it.code == "0" && it.data != null) {
//            if (it.data!! != null) {
//                Log.i(mTag, "delete user success ")
//                ToastUtil.makeText(this, "注销账户成功", Toast.LENGTH_SHORT).show()
//                doLogout(false)
//                finish()
//                overridePendingTransition(
//                    R.anim.activity_enter_slide_in_left,
//                    R.anim.activity_enter_slide_out_right
//                )
//            } else{
//                Log.i(mTag, "delete user success fail because no data")
//                ToastUtil.makeText(this, "注销账户失败", Toast.LENGTH_SHORT).show()
//            }
//        } else {
//            Log.i(mTag, "delete user success fail ${it.msg}")
//            ToastUtil.makeText(this, "注销账户失败", Toast.LENGTH_SHORT).show()
//        }
//    }

    private fun backToMain() {
        getSharedPreferences(
            Constants.SHARE_IS_USER_RETURN_TO_MAIN,
            Context.MODE_PRIVATE
        ).edit().putBoolean(
            Constants.BACK_TO_MAIN, true).apply()
        finish()
        overridePendingTransition(
            com.healthlink.hms.core.ui.R.anim.activity_enter_slide_in_left,
            com.healthlink.hms.core.ui.R.anim.activity_enter_slide_out_right
        )
    }

}