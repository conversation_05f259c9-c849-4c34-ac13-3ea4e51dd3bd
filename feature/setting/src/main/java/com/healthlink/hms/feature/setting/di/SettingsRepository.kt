package com.healthlink.hms.feature.setting.di

import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.UserInfoDTO
import kotlinx.coroutines.flow.Flow

interface SettingsRepository {
    // 网络请求相关 - 只处理远程API调用
    suspend fun deleteUser(userId: String): Flow<BaseResponse<Boolean>>
    suspend fun unbindAccount(userId: String): Flow<BaseResponse<Boolean>>
    suspend fun getUserInfo(userId: String): Flow<BaseResponse<UserInfoDTO>>
    suspend fun saveUserInfo(requestParam: Map<String, Any>): Flow<BaseResponse<Boolean>>
}