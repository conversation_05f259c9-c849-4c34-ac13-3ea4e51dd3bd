package com.healthlink.hms.feature.setting.viewmodels


import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.healthlink.hms.core.data.repository.SystemRepository
import com.healthlink.hms.core.data.repository.UserRepository
import com.healthlink.hms.core.model.BaseResponse
import com.healthlink.hms.core.model.dto.UserInfoDTO
import com.healthlink.hms.feature.setting.di.SettingsRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch

import javax.inject.Inject

/**
 * 设置页面UI状态
 */
data class SettingsUiState(
    val isLoading: Boolean = false,
    val userInfo: UserInfoDTO? = null,
    val isNotificationEnabled: Boolean = false,
    val error: String? = null
)

/**
 * 设置页面UI事件
 */
sealed class SettingsUiEvent {
    data class ShowToast(val message: String) : SettingsUiEvent()
//    data class NavigateToLogin(val shouldClearData: Boolean = false) : SettingsUiEvent()
    object DeleteUserSuccess : SettingsUiEvent()
    object UnbindAccountSuccess : SettingsUiEvent()
    object SaveUserInfoSuccess : SettingsUiEvent()
    object SaveUserInfoFailed : SettingsUiEvent()
}

/**
 * Created by zwb on 2024/10/20
 * 设置页面的ViewModel类
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val settingsRepository: SettingsRepository,
    private val systemRepository: SystemRepository,
    private val userRepository: UserRepository
) : ViewModel() {

    // UI状态管理 - 使用StateFlow管理持续性状态
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

    // UI事件管理 - 使用SharedFlow管理一次性事件
    private val _uiEvent = MutableSharedFlow<SettingsUiEvent>()
    val uiEvent: SharedFlow<SettingsUiEvent> = _uiEvent.asSharedFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 响应式通知状态
    val notificationState: StateFlow<Boolean> = systemRepository.getNotificationState()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false
        )
    
    // 响应式用户ID
    val userId: StateFlow<String> = userRepository.getUserId()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = ""
        )

    /**
     * 注销用户
     */
    fun deleteUser(userId: String) {
        viewModelScope.launch {
            updateLoadingState(true)
            try {
                settingsRepository.deleteUser(userId)
                    .catch { e ->
                        handleError("注销失败: ${e.message}")
                    }
                    .collect { response ->
                        if (response.code == "0" && response.data == true) {
                            _uiEvent.emit(SettingsUiEvent.DeleteUserSuccess)
                            _uiEvent.emit(SettingsUiEvent.ShowToast("注销账户成功"))
                        } else {
                            _uiEvent.emit(SettingsUiEvent.ShowToast("注销账户失败"))
                        }
                    }
            } catch (e: Exception) {
                handleError("注销失败: ${e.message}")
            } finally {
                updateLoadingState(false)
            }
        }
    }

    /**
     * 解绑账户 - 现代化实现
     */
    fun unbindAccount(userId: String) {
        viewModelScope.launch {
            updateLoadingState(true)
            try {
                settingsRepository.unbindAccount(userId)
                    .catch { e ->
                        handleError("解绑失败: ${e.message}")
                    }
                    .collect { response ->
                        if (response.code == "0" && response.data == true) {
                            _uiEvent.emit(SettingsUiEvent.UnbindAccountSuccess)
                            _uiEvent.emit(SettingsUiEvent.ShowToast("解绑成功"))
                        } else {
                            _uiEvent.emit(SettingsUiEvent.ShowToast("解绑失败"))
                        }
                    }
            } catch (e: Exception) {
                handleError("解绑失败: ${e.message}")
            } finally {
                updateLoadingState(false)
            }
        }
    }

    /**
     * 获取用户信息
     */
    fun getUserInfo(userId: String) {
        viewModelScope.launch {
            updateLoadingState(true)
            try {
                settingsRepository.getUserInfo(userId)
                    .catch { e ->
                        handleError("获取用户信息失败: ${e.message}")
                    }
                    .collect { response ->
                        if (response.code == "0" && response.data != null) {
                            // TODO：此处处理成 UI 状态 还是 UI 事件?
                            _uiState.value = _uiState.value.copy(userInfo = response.data)
                        } else {
                            _uiEvent.emit(SettingsUiEvent.ShowToast("获取用户信息失败"))
                        }
                    }
            } catch (e: Exception) {
                handleError("获取用户信息失败: ${e.message}")
            } finally {
                updateLoadingState(false)
            }
        }
    }

    /**
     * 保存用户信息
     */
    fun saveUserInfo(requestParam: Map<String, Any>) {
        viewModelScope.launch {
            updateLoadingState(true)
            try {
                settingsRepository.saveUserInfo(requestParam)
                    .catch { e ->
                        handleError("保存用户信息失败: ${e.message}")
                    }
                    .collect { response ->
                        if (response.code == "0" && response.data != null) {
                            _uiEvent.emit(SettingsUiEvent.SaveUserInfoSuccess)
                        } else {
                            _uiEvent.emit(SettingsUiEvent.SaveUserInfoFailed)
                        }
                    }
            } catch (e: Exception) {
                handleError("保存用户信息失败: ${e.message}")
            } finally {
                updateLoadingState(false)
            }
        }
    }

    /**
     * 更新本地通知开关
     */
    fun updateNotificationState(isEnabled: Boolean) {
        viewModelScope.launch {
            try {
                systemRepository.storeNotificationOpen(isEnabled)
                _uiState.value = _uiState.value.copy(isNotificationEnabled = isEnabled)
            } catch (e: Exception) {
                handleError("更新通知设置失败: ${e.message}")
            }
        }
    }



    /**
     * 更新加载状态
     */
    private fun updateLoadingState(isLoading: Boolean) {
        _isLoading.value = isLoading
        _uiState.value = _uiState.value.copy(isLoading = isLoading)
    }

    /**
     * 处理错误
     */
    private suspend fun handleError(message: String) {
        _uiEvent.emit(SettingsUiEvent.ShowToast(message))
        _uiState.value = _uiState.value.copy(error = message)
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    /**
     * 清除用户数据
     */
    fun clearUserData(isKeepAgreed: Boolean = true) {
        viewModelScope.launch {
            try {
                // 通过Repository清除用户数据
                userRepository.clearAllUserData()
                if (!isKeepAgreed) {
                    systemRepository.resetSystemSettings()
                }
            } catch (e: Exception) {
                handleError("清除用户数据失败: ${e.message}")
            }
        }
    }

    override fun onCleared() {
        Log.d("ViewModel","onCleared")
        super.onCleared()
    }

}
