package com.healthlink.hms.feature.setting.di

import com.healthlink.hms.core.network.api.ApiServiceKot

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object SettingsModule {
    @Provides
    @Singleton
    fun provideSettingsRepository(
        apiService: ApiServiceKot
    ): SettingsRepository = DefaultSettingsRepository(apiService)
}