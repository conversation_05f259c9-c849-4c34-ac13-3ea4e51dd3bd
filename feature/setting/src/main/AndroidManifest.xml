<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.healthlink.hms.feature.setting">
    <application>
    <activity
    android:name=".ui.HMSPersonalActivity"
    android:exported="true"
    android:launchMode="singleTask"
    android:theme="@style/Theme.HMS" />

    <activity
        android:name=".ui.DialogTransparentActivity"
        android:exported="false"
        android:launchMode="singleTask"
        android:theme="@style/TransparentDialogActivity"
        android:windowSoftInputMode="stateVisible" />
    </application>
</manifest>