<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ResourceName">

    <data>

        <variable
            name="viewModel"
            type="com.healthlink.hms.feature.setting.viewmodels.SettingsViewModel" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/dialog_personal_container"
        android:background="@android:color/transparent">
        <RelativeLayout
            android:id="@+id/personal_dialog_content"
            android:background="@drawable/dialog_bg"
            android:layout_gravity="center"
            android:layout_width="960dp"
            android:layout_height="700dp">

            <TextView
                android:id="@+id/setting_personal_info_title"
                android:layout_width="match_parent"
                android:layout_height="90dp"
                android:layout_alignParentTop="true"
                android:gravity="center"
                android:text="个人信息"
                android:textColor="@color/text_color_fc_100"
                android:textSize="26sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="158dp"
                android:layout_marginTop="129dp"
                android:text="昵称"
                android:textColor="@color/personal_setting_text_60"
                android:textSize="26sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="106dp"
                android:layout_marginTop="225dp"
                android:text="出生年月"
                android:textColor="@color/personal_setting_text_60"
                android:textSize="26sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="158dp"
                android:layout_marginTop="321dp"
                android:text="身高"
                android:textColor="@color/personal_setting_text_60"
                android:textSize="26sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="158dp"
                android:layout_marginTop="417dp"
                android:text="体重"
                android:textColor="@color/personal_setting_text_60"
                android:textSize="26sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="158dp"
                android:layout_marginTop="513dp"
                android:text="性别"
                android:textColor="@color/personal_setting_text_60"
                android:textSize="26sp" />

            <LinearLayout
                android:layout_width="560dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/setting_personal_info_title"
                android:layout_marginLeft="242dp"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/lo_personal_info_username"
                    android:layout_width="match_parent"
                    android:layout_height="72dp"
                    android:layout_marginTop="24dp"
                    android:background="@drawable/setting_item_selector"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:id="@+id/tv_setting_personal_info_username"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="40dp"
                        android:textSize="26sp"
                        android:textColor="@color/personal_setting_text_100"
                        android:layout_gravity="center_vertical"
                        android:text="CH7710"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/lo_personal_info_birth"
                    android:layout_width="match_parent"
                    android:layout_height="72dp"
                    android:layout_marginTop="24dp"
                    android:background="@drawable/setting_item_selector"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:id="@+id/tv_setting_personal_info_birth"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="40dp"
                        android:textSize="26sp"
                        android:textColor="@color/personal_setting_text_100"
                        android:layout_gravity="center_vertical"
                        android:text=""/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/lo_personal_info_height"
                    android:layout_width="match_parent"
                    android:layout_height="72dp"
                    android:layout_marginTop="24dp"
                    android:background="@drawable/setting_item_selector"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:id="@+id/tv_setting_personal_info_height"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="40dp"
                        android:textSize="26sp"
                        android:textColor="@color/personal_setting_text_100"
                        android:layout_gravity="center_vertical"
                        android:text=""/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/lo_personal_info_weight"
                    android:layout_width="match_parent"
                    android:layout_height="72dp"
                    android:layout_marginTop="24dp"
                    android:background="@drawable/setting_item_selector"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:id="@+id/tv_setting_personal_info_weight"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="40dp"
                        android:textSize="26sp"
                        android:textColor="@color/personal_setting_text_100"
                        android:layout_gravity="center_vertical"
                        android:text=""/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lo_personal_info_gender"
                    android:layout_width="match_parent"
                    android:layout_height="72dp"
                    android:layout_marginTop="24dp"
                    android:orientation="horizontal"
                    >
                    <RadioGroup
                        android:id="@+id/rg_gender_group"
                        android:layout_width="394dp"
                        android:layout_height="42dp"
                        android:layout_gravity="center_vertical"
                        android:orientation="horizontal">
                        <RadioButton
                            android:id="@+id/rb_gender_male"
                            android:layout_width="wrap_content"
                            android:layout_height="42dp"
                            android:background="@color/personal_setting_radio_trans"
                            style="@style/CustomRadioButton"
                            />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:text="男"
                            android:textSize="22sp"
                            android:layout_marginLeft="20dp"
                            android:textColor="@color/personal_setting_text_100"/>
                        <RadioButton
                            android:id="@+id/rb_gender_female"
                            android:layout_width="wrap_content"
                            android:layout_height="42dp"
                            style="@style/CustomRadioButton"
                            android:background="@color/personal_setting_radio_trans"
                            android:layout_marginLeft="67dp"
                            />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:text="女"
                            android:textSize="22sp"
                            android:layout_marginLeft="23dp"
                            android:textColor="@color/personal_setting_text_100"/>
                        <RadioButton
                            android:id="@+id/rb_gender_privacy"
                            android:layout_width="wrap_content"
                            android:layout_height="42dp"
                            android:layout_marginLeft="60dp"
                            android:background="@color/personal_setting_radio_trans"
                            style="@style/CustomRadioButton"
                            />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:text="隐私"
                            android:textSize="22sp"
                            android:layout_marginLeft="23dp"
                            android:textColor="@color/personal_setting_text_100"/>
                    </RadioGroup>

                </LinearLayout>



            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="88dp"
                android:layout_alignParentBottom="true"
                android:orientation="horizontal"
                android:background="@drawable/bg_dialog_w_960_button"
                >

                <Button
                    android:id="@+id/positiveButton"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="完成"
                    android:textColor="@color/hms_color_primary"
                    android:background="#00000000"
                    android:textSize="26sp" />
            </LinearLayout>


        </RelativeLayout>

    </FrameLayout>
</layout>