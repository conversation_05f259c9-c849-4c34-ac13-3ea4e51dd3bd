import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

plugins {
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.hms.android.library)
    alias(libs.plugins.hms.hilt)
}

android {
    namespace = "com.healthlink.hms.feature.setting"
    val timestamp = SimpleDateFormat("yyMMdd", Locale.US).run {
        format(Date())
    }
    buildFeatures{
        buildConfig = true
        viewBinding = true
        dataBinding = true
    }

    defaultConfig{
        val versionName = libs.versions.versionName.get()
        buildConfigField("String", "VERSION_NAME", "\"${versionName}\"")
    }

//    compileSdk = 34
//
//    defaultConfig {
//        minSdk = 28
//
//        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
//        consumerProguardFiles("consumer-rules.pro")
//    }
//
    buildTypes {
        debug {
            isMinifyEnabled = false
            buildConfigField("String", "BUILD_TIME", "\"${timestamp}\"")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
        release {
            isMinifyEnabled = true
            buildConfigField("String", "BUILD_TIME", "\"${timestamp}\"")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    //渠道配置
    flavorDimensions += "version"
    productFlavors {

        create("V4") {
            buildConfigField("String", "PLATFORM_CODE", "\"V4\"")
//            signingConfig = signingConfigs.getByName("V4")
        }

        create("V35") {
            buildConfigField("String", "PLATFORM_CODE", "\"V35\"")
//            signingConfig = signingConfigs.getByName("V4")
        }
    }

//    compileOptions {
//        sourceCompatibility = JavaVersion.VERSION_17
//        targetCompatibility = JavaVersion.VERSION_17
//    }
//    kotlinOptions {
//        jvmTarget = "17"
//    }
}

dependencies {
    // core:model 基础模型
    implementation(project(":core:model"))
    // core:common 通用模块
    api(project(":core:common"))
    // core:network 网络模块
    implementation(project(":core:network"))
    // core:data 数据模块
    implementation(project(":core:data"))
    // core:ui 通用UI模块
    api(project(":core:ui"))

    implementation(libs.androidx.activity)
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    // lifecycle
    implementation(libs.androidx.lifecycle.runtime)
    implementation(libs.lifecycle.extensions)
    implementation(libs.lifecycle.compiler)
    // 加入协程
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.jetbrains.kotlinx.coroutines.android)

    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.lifecycle.livedata.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.savedstate)

    // RxJava for Hilt DI
    implementation(libs.rxjava2.rxandroid)
    implementation(libs.rxjava2.rxjava)

    // 界面适配
    implementation(libs.androidautosize)

    // 单元测试依赖
    testImplementation(libs.junit)
    testImplementation("org.junit.jupiter:junit-jupiter:5.10.0")
    testImplementation("org.junit.jupiter:junit-jupiter-api:5.10.0")
    testImplementation("org.junit.jupiter:junit-jupiter-engine:5.10.0")
    
    // Mockito 依赖
    testImplementation("org.mockito:mockito-core:5.7.0")
    testImplementation("org.mockito:mockito-inline:5.2.0")
    testImplementation("org.mockito.kotlin:mockito-kotlin:5.2.1")
    
    // 协程测试依赖
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3")
    
    // Turbine - Flow 测试库
    testImplementation("app.cash.turbine:turbine:1.0.0")
    
    // Hilt 测试依赖
    testImplementation(libs.hilt.android.testing)
    
    // AndroidX 测试依赖
    testImplementation("androidx.arch.core:core-testing:2.2.0")
    
    // 集成测试依赖
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(libs.hilt.android.testing)
}